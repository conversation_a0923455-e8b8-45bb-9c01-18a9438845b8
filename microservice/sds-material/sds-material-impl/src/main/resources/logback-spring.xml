<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <property name="log_base_dir" value="/data/logs"/>
    <property name="maxHistory" value="1"/>
    <property name="app_name" value="service-material"></property>
    <property name="env" value="${NACOS_ENV:-null}"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}  [%thread] [traceId=%X{X-B3-TraceId:-}] [SpanId=%X{X-B3-SpanId:-}] %-5level %logger - %msg%n
            </pattern>
        </encoder>
    </appender>
    <appender name="INFO_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-./logs}/${SPRING_PROFILES_ACTIVE:-local}/${app_name}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-./logs}/${SPRING_PROFILES_ACTIVE:-local}/${app_name}/info.%d{yyyy-MM-dd}.log.gz
            </fileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}  [%thread] [traceId=%X{X-B3-TraceId:-}] [SpanId=%X{X-B3-SpanId:-}] %-5level %logger - %msg%n
            </pattern>
        </encoder>
    </appender>
    <appender name="Sentry" class="io.sentry.logback.SentryAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <logger name="RocketmqClient" additivity="false">
        <level value="error"/>
        <appender-ref ref="STDOUT"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>

    </root>


</configuration>