rocketmq:
  listenerEnable: false
  producer:
    group: GID_PRODUCER_MATERIAL
spring:
  datasource:
    dynamic:
      seata: true
      primary: common
      datasource:
        common:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: *****************************************************************************************************************************************************************************
          username: read_user
          password: 5zEOdlpTyj4ARUGM
        master:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: *********************************************************************************************************************************************************************************
          username: read_user
          password: 5zEOdlpTyj4ARUGM
  cloud:
    alibaba:
      seata:
        tx-service-group: my_test_tx_group

sds:
  mq-topic-suffix: _dev

sqs_env: ${SQS_ENV}

aes_endpoint: ${ORDER_ES_ENDPOINT:es-cn-2wb3s4xrx0002ue79.public.elasticsearch.aliyuncs.com}