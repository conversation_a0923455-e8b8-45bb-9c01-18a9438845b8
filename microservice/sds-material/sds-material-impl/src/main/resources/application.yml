server:
  port: 9011
  tomcat:
    max-threads: 200
    min-spare-threads: 20
spring:
  cloud:
    alibaba:
      seata:
        tx-service-group: my_test_tx_group
  application:
    name: service-material
  profiles:
    active: @spring.profiles.active@
  jackson:
    time-zone: GMT+8
  datasource:
    dynamic:
      seata: true
      primary: common
      datasource:
        common:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ${COMMON_DATABASE_URL}
          username: ${COMMON_DATABASE_USERNAME}
          password: ${COMMON_DATABASE_PASSWORD}
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initialSize: 5
            minIdle: 10
            maxActive: 1000
            #配置获取连接等待超时的时间
            maxWait: 60000
            #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            #配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            #验证连接是否有效。此参数必须设置为非空字符串，下面三项设置成true才能生效
            validationQuery: SELECT 1
            #指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
            testWhileIdle: true
            #指明是否在从池中取出连接前进行检验,如果检验失败,则从池中去除连接并尝试取出另一个
            testOnBorrow: true
            #指明是否在归还到池中前进行检验
            testOnReturn: false
            #打开PSCache，并且指定每个连接上PSCache的大小
            poolPreparedStatements: true
            maxPoolPreparedStatementPerConnectionSize: 20
            #通过connectProperties属性来打开mergeSql功能；慢SQL记录
            connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=1000;druid.stat.logSlowSql=true
            #合并多个DruidDataSource的监控数据
            useGlobalDataSourceStat: true
        master:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ${MATERIAL_DATABASE_URL}
          username: ${MATERIAL_DATABASE_USERNAME}
          password: ${MATERIAL_DATABASE_PASSWORD}
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initialSize: 5
            minIdle: 10
            maxActive: 1000
            #配置获取连接等待超时的时间
            maxWait: 60000
            #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            #配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            #验证连接是否有效。此参数必须设置为非空字符串，下面三项设置成true才能生效
            validationQuery: SELECT 1
            #指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
            testWhileIdle: true
            #指明是否在从池中取出连接前进行检验,如果检验失败,则从池中去除连接并尝试取出另一个
            testOnBorrow: true
            #指明是否在归还到池中前进行检验
            testOnReturn: false
            #打开PSCache，并且指定每个连接上PSCache的大小
            poolPreparedStatements: true
            maxPoolPreparedStatementPerConnectionSize: 20
            #配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
            connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=1000;druid.stat.logSlowSql=true
            #合并多个DruidDataSource的监控数据
            useGlobalDataSourceStat: true
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    timeout: 1000000
  main:
    allow-bean-definition-overriding: true
sds:
  mq-topic-suffix: ${KAFKA_TOPIC_SUFFIX}
  env-suffix: ${ENV_SUFFIX}

rocketmq:
  producer:
    group: GID_PRODUCER_PRODUCT
management:
  endpoints:
    web:
      exposure:
        include: '*'

logging:
  level:
    com:
      sdsdiy:
        materialimpl:
          mapper: debug

#  level:
#    org.springframework.boot.autoconfigure: ERROR

# 分布式任务调度配置
xxl:
  job:
    enable: ${XXL_JOB_EXCUTE_ENABLE:false}
    admin:
      addresses: ${XXL_JOB_HOST}
    executor:
      address: http://sds-material:9999

es_suffix: ${ELSICSEARCH_SUFFIX}
seata:
  enable-auto-data-source-proxy: false
  registry:
    type: file
  config:
    type: file
    file:
      name: file.conf
  service:
    vgroupMapping:
      default_tx_group: default
      my_test_tx_group: default
    grouplist:
      default: seata-server:8091
sqs_env: ${SQS_ENV}

