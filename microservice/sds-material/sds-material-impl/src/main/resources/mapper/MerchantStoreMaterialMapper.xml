<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdsdiy.materialimpl.mapper.MerchantStoreMaterialMapper">

    <resultMap type="com.sdsdiy.materialimpl.entity.po.MerchantStoreMaterial" id="MerchantStoreMaterialMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="merchantId" column="merchant_id" jdbcType="INTEGER"/>
        <result property="merchantStoreId" column="merchant_store_id" jdbcType="INTEGER"/>
        <result property="materialId" column="material_id" jdbcType="INTEGER"/>
        <result property="merchantStoreMaterialCategoryId" column="merchant_store_material_category_id"
                jdbcType="INTEGER"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createUid" column="create_uid" jdbcType="INTEGER"/>
        <result property="updateUid" column="update_uid" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="copyMaterial">
     INSERT INTO `merchant_store_material` ( `merchant_id`, `merchant_store_id`, `material_id`, `merchant_store_material_category_id`, `create_uid`, `update_uid` )
        SELECT
            merchant_id,
            #{toMerchantStoreId},
            material_id,
            #{task.materialCategoryId},
            #{task.createUid},
            #{task.createUid}
        FROM
            merchant_store_material
        WHERE
            merchant_id = #{task.merchantId}
           AND merchant_store_material_category_id = #{task.originalMaterialCategoryId}
           AND (is_delete=0 or (is_delete=1 and update_time> #{task.createTime}))
           AND create_time <![CDATA[ < ]]> #{task.createTime}
    </insert>
    <select id="countByCategoryIdGroup" resultType="com.sdsdiy.materialimpl.bo.StoreMaterialCountBo">
        select count(*) as num,merchant_store_material_category_id from merchant_store_material where
        merchant_store_material_category_id in
        <foreach collection="categoryIds" item="categoryId" index="index" open="(" close=")" separator=",">
            #{categoryId}
        </foreach>
        and is_delete=0
        group by merchant_store_material_category_id ;
    </select>

</mapper>