<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdsdiy.materialimpl.mapper.MerchantOfficialMaterialRelationRecordsMapper">

    <select id="checkOfficialMaterialIdsWithDeleted" resultType="java.lang.Long">
        SELECT official_material_id
        FROM merchant_official_material_relation_records
        WHERE official_material_id in
        <foreach collection="bean.materialIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND merchant_id = #{bean.merchantId}
        <if test="bean.officialMaterialInfoType!=null and bean.officialMaterialInfoType!= ''">
            AND official_material_info_type = #{bean.officialMaterialInfoType}
        </if>
        GROUP BY official_material_id
    </select>
</mapper>
