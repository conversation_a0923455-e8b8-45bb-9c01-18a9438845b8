<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdsdiy.materialimpl.mapper.MerchantStoreMaterialCategoryMapper">

    <resultMap type="com.sdsdiy.materialimpl.entity.po.MerchantStoreMaterialCategory" id="MerchantStoreMaterialCategoryMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="merchantId" column="merchant_id" jdbcType="INTEGER"/>
        <result property="merchantStoreId" column="merchant_store_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="num" column="num" jdbcType="INTEGER"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createUid" column="create_uid" jdbcType="INTEGER"/>
        <result property="updateUid" column="update_uid" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


</mapper>