<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdsdiy.materialimpl.mapper.theme.ThemeRelationMaterialMapper">
    <select id="countMaterialNum" resultType="com.sdsdiy.common.base.entity.dto.SqlLongCount">
        select count(id) as qty,theme_relation_material_category_id as `key`
        from theme_relation_material
        where theme_relation_material_category_id in
        <foreach collection="categoryIds" item="categoryId" index="index" open="(" close=")" separator=",">
            #{categoryId}
        </foreach>
        and is_delete=0
        group by theme_relation_material_category_id ;
    </select>
</mapper>
