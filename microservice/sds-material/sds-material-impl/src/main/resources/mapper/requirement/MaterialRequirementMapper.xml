<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdsdiy.materialimpl.mapper.requirement.MaterialRequirementMapper">

    <select id="pageMyContribute" resultType="com.sdsdiy.materialimpl.entity.po.requirement.MaterialRequirement">
        SELECT r.*
        FROM material_requirement r, material_requirement_sign s
        WHERE r.id = s.material_requirement_id
        <if test="queryDTO.designAccountId != null">
            AND s.design_account_id = #{queryDTO.designAccountId}
        </if>
        <if test="queryDTO.designMerchantId != null">
            AND s.design_merchant_id = #{queryDTO.designMerchantId}
        </if>
        <if test="queryDTO.statusList != null">
            AND r.`status` in
            <foreach collection="queryDTO.statusList" item="state" index="index" open="(" close=")" separator=",">
                #{state}
            </foreach>
        </if>
        GROUP BY r.id
        ORDER BY r.id desc
    </select>
    <!--r.`status`, r.deadline, -->
    <select id="countMyContribute" resultType="java.lang.Integer">
        SELECT count(1) from (SELECT r.id
        FROM material_requirement r, material_requirement_sign s
        WHERE r.id = s.material_requirement_id
        <if test="queryDTO.designAccountId != null">
            AND s.design_account_id = #{queryDTO.designAccountId}
        </if>
        <if test="queryDTO.designMerchantId != null">
            AND s.design_merchant_id = #{queryDTO.designMerchantId}
        </if>
        <if test="queryDTO.statusList != null">
            AND r.`status` in
            <foreach collection="queryDTO.statusList" item="state" index="index" open="(" close=")" separator=",">
                #{state}
            </foreach>
        </if>
        GROUP BY r.id) a
    </select>

</mapper>
