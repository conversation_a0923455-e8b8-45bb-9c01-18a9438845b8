package com.sdsdiy.materialimpl.controller;


import com.es.pojo.EsResponse;
import com.es.pojo.PageInfo;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.BasePageSelect;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.materialapi.api.MaterialApi;
import com.sdsdiy.materialapi.dto.base.MaterialSaveReqDto;
import com.sdsdiy.materialapi.dto.base.materials.*;
import com.sdsdiy.materialapi.dto.resp.OfficialMaterialLikeDelRespDto;
import com.sdsdiy.materialdata.dto.MerchantOfficialMaterialRelationRecordDTO;
import com.sdsdiy.materialdata.dto.material.*;
import com.sdsdiy.materialdata.util.MaterialUtils;
import com.sdsdiy.materialimpl.entity.po.Material;
import com.sdsdiy.materialimpl.service.MaterialEsService;
import com.sdsdiy.materialimpl.service.MaterialService;
import com.sdsdiy.materialimpl.service.material.MaterialSaveService;
import com.sdsdiy.materialimpl.task.MaterialTask;
import com.sdsdiy.userdata.dto.UpdateUserIdReqDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * (Material)表控制层
 *
 * <AUTHOR>
 * @since 2020-11-13 16:46:06
 */
@Api(tags = "素材")
@RestController
@RequiredArgsConstructor
public class MaterialController implements MaterialApi {
    private final MaterialEsService materialEsService;
    @Resource
    private MaterialService materialService;
    @Resource
    private MaterialSaveService materialSaveService;

    @Override
    public List<MaterialRespDto> findByIds(IdsSearchHelper ids) {
        return this.materialService.byIds(ids);
    }

    @Override
    public List<MaterialRespDto> findByFileCodes(MaterialsFileCodesReqDto dto) {
        return this.materialService.findByFileCodes(dto);
    }

    @Override
    public MaterialRespDto findById(IdSearchHelper idsSearchHelper) {
        return this.materialService.getOneById(idsSearchHelper);
    }

    @Override
    public List<MaterialsFoldersRespDto> folderDirectory(MaterialsFoldersReqDto materialsFoldersReqDto) {
        return this.materialService.folderDirectory(materialsFoldersReqDto);
    }

    @Override
    public List<MaterialsUsersSharedDirectoryDto> usersSharedDirectory(Long merchantId) {
        return this.materialService.usersSharedDirectory(merchantId);
    }

    @Override
    public MaterialsBatchRespDto specifyShareBatch(MaterialsSpecifyShareBatchResqDto materialsShareBatchResDto) {
        return this.materialService.specifyShareBatch(materialsShareBatchResDto);
    }

    @Override
    public void archiveFolder(MatarialArchiveFolderDto matarialArchiveFolderDto) {
        this.materialService.archiveFolder(matarialArchiveFolderDto);
    }

    @Override
    public MaterialSpecifyShareDto specifyShareNum(Long userId) {
        return this.materialService.specifyShareNum(userId);
    }

    @Override
    public void newShareBatch(MaterialsShareBatchResDto materialsShareBatchResDto) {
        this.materialService.newShareBatch(materialsShareBatchResDto);
    }

    @Override
    public MaterialInfringementDto infringement(MaterialInfringementResqDto materialInfringementResqDto) {
        return this.materialService.infringement(materialInfringementResqDto);
    }

    @Override
    public MaterialsBatchRespDto batchShare(MaterialsShareBatchResDto materialsShareBatchResDto) {
        return this.materialService.batchShare(materialsShareBatchResDto);
    }

    @Override
    public void batchMove(MaterialBatchMoveDto materialFolderBatchDto) {
        this.materialService.batchMove(materialFolderBatchDto);
    }

    @Override
    public MaterialsBatchRespDto batchLike(MatarialBatchLikeDto matarialBatchLikeDto) {
        return this.materialService.batchLike(matarialBatchLikeDto);
    }

    @Override
    public void batchDel(MatarialDelBatchDto dto) {
        this.materialService.batchDel(dto);
    }

    @Override
    public void syncEs(Collection<Long> ids) {
        this.materialService.materialUpdateNotify(ids);
    }

    @Override
    public OfficialMaterialLikeDto officialMaterialLike(OfficialMaterialLikeReqDto materialLikeDto) {
        return this.materialService.officialMaterialLike(materialLikeDto);
    }

    @Override
    public Map<Long, MerchantOfficialMaterialRelationRecordDTO> getLongMerchantOfficialMaterialRelationRecordsMap(OfficialMaterialLikeReqDto materialLikeDto) {
        return this.materialService.getLongMerchantOfficialMaterialRelationRecordDTOMap(materialLikeDto.getOfficialMaterialIds(), materialLikeDto.getMerchantId());
    }

    @Override
    public OfficialMaterialLikeDto officialMaterialLikeDel(OfficialMaterialLikeDelRespDto materialLikeDto) {
        return this.materialService.officialMaterialLikeDel(materialLikeDto);
    }

    @Override
    public Long saveMaterial(MaterialRespDto materialDto) {
        return this.materialService.save(materialDto);
    }

    @Override
    public PageListResultRespDto<MaterialSimpleRespDTO> sceneList(@RequestBody BasePageSelect basePageSelect, @RequestParam Long merchantId) {
        PageInfo<MaterialEsDto> pageInfo = new PageInfo<>();
        pageInfo.setPage(basePageSelect.getPage());
        pageInfo.setSize(basePageSelect.getSize());
        EsResponse<MaterialEsDto> materialEsDtoEsResponse = this.materialEsService.pageScene(pageInfo, merchantId);
        List<MaterialSimpleRespDTO> materialSimpleRespDTOS = BeanUtils.toList(materialEsDtoEsResponse.getList(), MaterialSimpleRespDTO.class);
        materialSimpleRespDTOS.forEach(m -> {
            m.setImg1000ThumbUrl(MaterialUtils.get1000ThumbImages(m.getImgUrl()));
            m.setImgThumbUrl(MaterialUtils.getThumbImages(m.getImgUrl()));
        });
        PageListResultRespDto<MaterialSimpleRespDTO> respDto = new PageListResultRespDto<>();
        respDto.setItems(materialSimpleRespDTOS);
        respDto.setTotalCount((int) materialEsDtoEsResponse.getTotal());
        return respDto;
    }

    @Override
    public List<MaterialRespDto> getFolderByUserId(MaterialFolderReqDTO reqDTO) {
        List<Material> list = this.materialService.getFolderByUserId(reqDTO);
        return ListUtil.copyProperties(list, MaterialRespDto.class);
    }

    @Override
    public MaterialRespDto getAmazonCustomDataFolder(Long merchantId, Long userId) {
        return this.materialService.getAmazonCustomDataFolder(merchantId, userId);
    }

    @Override
    public void updateUserId(UpdateUserIdReqDto dto) {
        this.materialService.updateUserId(dto);
    }

    @Override
    public Map<Long, BigDecimal> countOccupyLengthMapByMerchantId(Long merchantId) {
        return this.materialService.countOccupyLengthMapByMerchantId(merchantId);
    }

    @Override
    public BigDecimal countOccupyLengthByMerchantIdUserId(Long merchantId, Long userId) {
        return this.materialService.countOccupyLengthByMerchantIdUserId(merchantId, userId);
    }

    @Override
    public void updateReplaceToAliYunOssUrl(Integer limit) {
        MaterialTask.MaterialReplaceOssUrlBo materialReplaceOssUrlBo = new MaterialTask.MaterialReplaceOssUrlBo();
        while (materialReplaceOssUrlBo.getMaxId() == null || materialReplaceOssUrlBo.getMaxId() != 0) {
            this.materialSaveService.updateReplaceToAliYunOssUrl(materialReplaceOssUrlBo, limit);
        }
    }

    @Override
    public Map<String, String> getDesignUrlFromEsByNames(MaterialUrlQueryDTO dto) {
        return this.materialService.getDesignUrlFromEsByNames(dto);
    }

    @Override
    public Map<String, MaterialUrlDTO> getMaterialUrlDtoFromEsByNames(MaterialUrlQueryDTO dto) {
        return this.materialService.getMaterialUrlDtoFromEsByNames(dto);
    }

    @Override
    public List<Long> getMaterialIdsFromEsByNames(MaterialUrlQueryDTO dto) {
        return this.materialService.getMaterialIdsFromEsByNames(dto);
    }

    @Override
    public Map<String, List<Long>> mapMaterialIdsFromEsByNames(MaterialUrlQueryDTO dto) {
        return this.materialService.mapMaterialIdsFromEsByNames(dto);
    }

    @Override
    public void updateMaterialEs(BaseListDto<Long> dto) {
        this.materialEsService.updateEs(dto.getList());
    }
}