package com.sdsdiy.materialimpl.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.constant.TimeConstant;
import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.core.redis.util.RedisUtil;
import com.sdsdiy.materialapi.api.MaterialSyncApi;
import com.sdsdiy.materialapi.dto.base.materials.MatarialArchiveFolderDto;
import com.sdsdiy.materialapi.dto.base.materials.MatarialOfficialFolderFolderDto;
import com.sdsdiy.materialimpl.entity.po.Material;
import com.sdsdiy.materialimpl.service.MaterialService;
import com.sdsdiy.materialimpl.sqs.*;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.base.MerchantSysUserRespDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class MaterialInitController implements MaterialSyncApi {
    private static final Logger logger = LoggerFactory.getLogger(MaterialInitController.class);


    @Resource
    private SqsSender sqsSender;
    @Resource
    private MaterialService materialService;
    @Autowired
    protected RedisUtil redisUtil;

    private static final Long numL0 = 0L;

    @Override
    @Async
    public void syncMaterialOrderCountInit(Long id) {
        String key = "syncMaterialOrderCountInit" + id;
        if (checkIsDuplicate(key)) {
            logger.info("syncMaterialOrderCountInit 脚本已执行 id " + id);
            return;
        }
        redisUtil.set(key, id, TimeConstant.CACHE_EXPIRE_1_MINUTE);
        boolean bl = true;
        logger.info("syncMaterialOrderCountInit send id={}", id);
        while (bl) {
            List<Material> list = materialService.syncMaterialOrderCountInit(id);
            if (CollectionUtils.isNotEmpty(list)) {
                for (Material material : list) {
                    MaterialOrderCountMsgBo msgBo = new MaterialOrderCountMsgBo();
                    msgBo.setMaterialId(material.getId());
                    msgBo.setOrderCount(material.getOrderCount());
                    msgBo.setParentFolderId(material.getParentFolderId());
                    id = material.getId();
                    sqsSender.send("sync_material_order_count_init", msgBo);
                }
                logger.info("syncMaterialOrderCountInit implement  maxId={}", id);
            } else {
                bl = false;
                logger.info("syncMaterialOrderCountInit end  maxId={}", id);
            }
        }

    }

    @Override
    @Async
    public void syncMaterialShareCountInit(Long id, Long merchantId) {
        String key = "syncMaterialShareCountInit" + id;
        if (checkIsDuplicate(key)) {
            logger.info("syncMaterialShareCountInit 脚本已执行 id " + id);
            return;
        }
        redisUtil.set(key, id, TimeConstant.CACHE_EXPIRE_1_MINUTE);
        boolean bl = true;
        logger.info("syncMaterialShareCountInit send id={}", id);
        if (id <= 1){
            if (null != merchantId) {
                //更新商户共享文件夹共享素材数为0
                materialService.updateShareCountByMerchantId(merchantId);
            } else {
                materialService.updateShareCount(id);
            }
        }

        while (bl) {
            List<Material> list;
            if (null != merchantId) {
                list = materialService.syncMaterialShareCountInit(id, merchantId);
            } else {
                list = materialService.syncMaterialShareCountInit(id);
            }

            if (CollectionUtils.isNotEmpty(list)) {
                List<MaterialShareCountMsgBo> materialShareCountMsgBos = Lists.newArrayList();
                for (Material material : list) {
                    MaterialShareCountMsgBo msgBo = new MaterialShareCountMsgBo();
                    msgBo.setMaterialId(material.getId());
                    msgBo.setParentFolderId(material.getParentFolderId());
                    msgBo.setOnlineTime(material.getOnlineTime());
                    materialShareCountMsgBos.add(msgBo);
                    id = material.getId();
                    redisUtil.set("syncMaterialShareCountInitCuId", id, TimeConstant.CACHE_EXPIRE_1_HOUR);
                }
                sqsSender.send("sync_material_share_count_init", materialShareCountMsgBos);

                logger.info("syncMaterialShareCountInit implement  maxId={}", id);
            } else {
                bl = false;
                materialService.updateOnLineStatusByMerchantId(merchantId);

                logger.info("syncMaterialShareCountInit end  maxId={}", id);
            }
        }
    }

    @Override
    @Async
    public void syncMaterialShapeInit(Long id) {
        String key = "syncMaterialShapeInit" + id;
        if (checkIsDuplicate(key)) {
            logger.info("syncMaterialShapeInit 脚本已执行 id " + id);
            return;
        }
        redisUtil.set(key, id, TimeConstant.CACHE_EXPIRE_1_MINUTE);
        boolean bl = true;
        logger.info("syncMaterialShareCountInit send id={}", id);
        while (bl) {
            List<Material> list = materialService.syncMaterialShapeInit(id);
            if (CollectionUtils.isNotEmpty(list)) {
                for (Material material : list) {
                    MaterialShapeMsgBo msgBo = new MaterialShapeMsgBo();
                    msgBo.setMaterialId(material.getId());
                    msgBo.setHeight(material.getHeight());
                    msgBo.setWidth(material.getWidth());
                    id = material.getId();
                    redisUtil.set("syncMaterialShapeInitInitId", id, TimeConstant.SEC_1_DAY);
                    sqsSender.send("sync_material_init", msgBo);
                }
                logger.info("syncMaterialShapeInit implement  maxId={}", id);
            } else {
                bl = false;
                logger.info("syncMaterialShapeInit end  maxId={}", id);
            }
        }
    }


    public void archiveFolder() {
/*		List<Long> merchantIdList = Lists.newArrayList();
		merchantIdList.add(2743L);
		merchantIdList.add(2577L);
		merchantIdList.add(30L);
		merchantIdList.add(2372L);
		merchantIdList.add(696L);
		merchantIdList.add(2749L);*/
        List<MerchantSysUserRespDto> merchantSysUserRespDtos = materialService.getListNotBoss();
        for (MerchantSysUserRespDto merchantSysUserRespDto : merchantSysUserRespDtos) {
            logger.info("archiveFolder send  userId={}", merchantSysUserRespDto.getId());
            MatarialArchiveFolderDto matarialArchiveFolderDto = new MatarialArchiveFolderDto();
            matarialArchiveFolderDto.setUserId(merchantSysUserRespDto.getId());
            matarialArchiveFolderDto.setMerchantId(merchantSysUserRespDto.getMerchantId());
            materialService.archiveFolder(matarialArchiveFolderDto);
            materialService.isShareMaterial(merchantSysUserRespDto.getId());
            logger.info("archiveFolder end  userId={}", merchantSysUserRespDto.getId());

        }
    }

    @Override
    public void initOfficialFolder(IdsSearchHelper IdsSearchHelper) {
        IdsSearchHelper.setFields("id");
        List<MerchantRespDto> merchantRespDtoList = materialService.getListMerchant(IdsSearchHelper);

        for (MerchantRespDto merchantRespDto : merchantRespDtoList) {
            logger.info("archiveFolder send  userId={}", merchantRespDto.getId());
            MatarialOfficialFolderFolderDto matarialOfficialFolderFolderDto = new MatarialOfficialFolderFolderDto();
            matarialOfficialFolderFolderDto.setMerchantId(merchantRespDto.getId());
            materialService.queryAndAddOfficialMaterialFolder(matarialOfficialFolderFolderDto.getMerchantId());
            logger.info("archiveFolder end  userId={}", merchantRespDto.getId());
        }
    }
    //ES同步所有素材
    @Override
    public void initEsAll(Long id) {

        boolean bl = true;
        while (bl) {
            logger.info("initEsAll send  id={}", id);
            List<Material> materialList = materialService.initEsAll(id);
            if (CollectionUtils.isEmpty(materialList)) {
                bl = false;
            } else {
                for (Material material : materialList) {
                    id = material.getId();
                }
                materialService.updateInitOnlineStatus(materialList);
            }
            logger.info("initEsAll end  id={}", id);
        }
    }

    @Override
    public void initEsDelete(Long maxId) {
        materialService.initEsDelete(maxId);
    }


    //同步文件夹状态
    public void initOnlineStatus() {

        boolean bl = true;
        Long id = 0L;
        while (bl) {
            logger.info("initOnlineStatus send  id={}", id);
            List<Material> materialList = materialService.initOnlineStatus(id);
            if (CollectionUtils.isEmpty(materialList)) {
                bl = false;
            } else {
                for (Material material : materialList) {
                    id = material.getId();
                }
                materialService.updateInitOnlineStatus(materialList);
            }
            logger.info("initOnlineStatus end  id={}", id);
        }
    }






    @Override
    @Async
    //初始化祖先信息表
    public void materialAncestorsInfo(Long id, Long merchantId) {
        String key = "materialAncestorsInfo" + id;
        if (checkIsDuplicate(key)) {
            logger.info("materialAncestorsInfo 脚本已执行 id " + id);
            return;
        }
        redisUtil.set(key, id, TimeConstant.CACHE_EXPIRE_1_MINUTE);
        boolean bl = true;
        logger.info("materialAncestorsInfo send id={}", id);
        while (bl) {
            List<Material> list = materialService.materialAncestorsInit(id, merchantId);
            if (CollectionUtils.isNotEmpty(list)) {
                MaterialAncestorsInfoMsgBo msgBo = new MaterialAncestorsInfoMsgBo();
                List<Long> materialIds = Lists.newArrayList();
                for (Material material : list) {
                    materialIds.add(material.getId());
                    id = material.getId();
                }
                msgBo.setMaterialIds(materialIds);
                sqsSender.send("material_ancestors_info_data_processing", msgBo);

                logger.info("materialAncestorsInfo implement  maxId={}", id);
            } else {
                bl = false;
                logger.info("materialAncestorsInfo end  maxId={}", id);
            }
        }
    }


    /**
     * 判断消息是否重复
     *
     * @return
     */
    protected boolean checkIsDuplicate(String key) {
        boolean isDuplicate = false;
        //判断redis缓存是否存在，存在，重复
        logger.info("judge key " + key);
        boolean hasKey = redisUtil.hasKey(key);
        if (hasKey) {
            logger.info("redis已存在key【" + key + "】");
            isDuplicate = true;
        } else {
            //不存在
            logger.info("redis no exist key " + key);
        }
        return isDuplicate;
    }
}

