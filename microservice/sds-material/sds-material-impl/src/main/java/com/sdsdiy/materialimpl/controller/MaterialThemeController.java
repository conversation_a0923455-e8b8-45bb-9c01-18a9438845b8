package com.sdsdiy.materialimpl.controller;

import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.materialapi.api.MaterialThemeApi;

import com.sdsdiy.materialapi.dto.param.ThemeEditReqDto;
import com.sdsdiy.materialapi.dto.param.ThemePageReqDto;

import com.sdsdiy.materialapi.dto.resp.MarterialThemePageRespDto;
import com.sdsdiy.materialdata.dto.officialmaterial.DesignMaterialThemeAddReqDTO;
import com.sdsdiy.materialimpl.service.DesignMaterialThemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/25
 */

@RestController
public class MaterialThemeController implements MaterialThemeApi {

    @Autowired
    private DesignMaterialThemeService themeService;

    @Override
    public PageListResultRespDto<MarterialThemePageRespDto> page(@SpringQueryMap ThemePageReqDto reqDto) {
        return themeService.getPage(reqDto.getThemeName(), reqDto.getIsRecommended(), reqDto.getPage(), reqDto.getSize());
    }

    @Override
    public List<MarterialThemePageRespDto> list(@SpringQueryMap ThemePageReqDto reqDto) {
        return themeService.getList(reqDto.getIsRecommended());
    }

    @Override
    public void edit(ThemeEditReqDto reqDto) {
        themeService.edit(reqDto);
    }

    @Override
    public void add(DesignMaterialThemeAddReqDTO addReqDTO) {
        themeService.add(addReqDTO);
    }

    @Override
    public void repeat(String name) {

    }


}


