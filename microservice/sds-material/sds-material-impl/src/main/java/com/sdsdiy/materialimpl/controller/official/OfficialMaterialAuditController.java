package com.sdsdiy.materialimpl.controller.official;


import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.materialapi.api.official.OfficialMaterialAuditApi;
import com.sdsdiy.materialapi.constant.enums.OfficialMaterialAuditStatus;
import com.sdsdiy.materialapi.dto.official.OfficialMaterialAuditBatchDTO;
import com.sdsdiy.materialapi.dto.official.OfficialMaterialAuditDTO;
import com.sdsdiy.materialapi.dto.official.OfficialMaterialBatchDTO;
import com.sdsdiy.materialdata.dto.officialmaterial.OfficialMaterialAuditResultDTO;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialAuditService;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialEsService;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialService;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 素材审批表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
@RestController
@RequiredArgsConstructor
public class OfficialMaterialAuditController implements OfficialMaterialAuditApi {
    private final OfficialMaterialEsService officialMaterialEsService;
    @Autowired
    private OfficialMaterialAuditService auditService;
    @Autowired
    private OfficialMaterialService officialMaterialService;
    @Autowired
    private MaterialRequirementService materialRequirementService;

    /**
     * 提交审核
     */
    @Override
    public void add(OfficialMaterialAuditDTO dto, Long uId) {
        auditService.add(dto, uId);
    }

    /**
     * 批量审核
     * 同时更新素材表状态
     */
    @Override
    @GlobalTransactional
    public void batchAudit(OfficialMaterialAuditBatchDTO dto, Long uId) {
        if (dto.getAuditStatus() == null) {
            throw new BusinessException("审核结果不能为空");
        }
        OfficialMaterialAuditResultDTO resultDTO = officialMaterialService.batchAudit(dto, uId);
        auditService.batchAudit(dto, uId);
        if (OfficialMaterialAuditStatus.isPass(dto.getAuditStatus())) {
            if (!CollectionUtils.isEmpty(resultDTO.getOpenMarketIds())) {
                // 开放市场，更新es
                this.officialMaterialEsService.sendSyncMq(resultDTO.getOpenMarketIds());
            }
            if (!CollectionUtils.isEmpty(resultDTO.getCustomRequirementIds())) {
                // 定制需求，更新投稿数
                materialRequirementService.updateMaterialNum(resultDTO.getCustomRequirementIds());
            }
        }
    }

    /**
     * 批量提交
     *
     * @return 成功提交数量
     */
    @Override
    @GlobalTransactional
    public int batchSubmit(OfficialMaterialBatchDTO dto, Long uId) {
        dto.setUpdateUid(uId);
        // 过滤信息不完整的
        List<Long> ids = officialMaterialService.submitAuditCheck(dto);
        dto.setMaterialIds(ids);
        return auditService.addBatch(dto);
    }

    /**
     * 获取最近一次审核不通过原因
     */
    @Override
    public String getLastAuditRefuseRemark(Long materialId) {
        return auditService.getLastAuditRefuseRemark(materialId);
    }

}

