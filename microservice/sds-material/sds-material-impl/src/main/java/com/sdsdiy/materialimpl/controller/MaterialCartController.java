package com.sdsdiy.materialimpl.controller;


import com.sdsdiy.materialapi.api.MaterialCartApi;
import com.sdsdiy.materialapi.dto.official.MaterialCartPlaceOrderPrepareRespDTO;
import com.sdsdiy.materialdata.dto.*;
import com.sdsdiy.materialimpl.service.materialcart.MaterialCartListService;
import com.sdsdiy.materialimpl.service.materialcart.MaterialCartPaymentPrepareService;
import com.sdsdiy.materialimpl.service.materialcart.MaterialCartService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * (Material)表控制层
 *
 * <AUTHOR>
 * @since 2020-11-13 16:46:06
 */
@RestController
public class MaterialCartController implements MaterialCartApi {

	@Resource
	MaterialCartService materialCartService;

	@Resource
	MaterialCartListService materialCartListService;
	@Resource
	MaterialCartPaymentPrepareService materialCartPaymentPrepareService;

	@Override
	public void add(MaterialCartAddDTO materialCartAddDTO) {
		materialCartService.add(materialCartAddDTO);
	}

	@Override
	public List<MaterialCartRespDTO> list(MaterialCartListDTO queryParam) {
		return materialCartListService.list(queryParam.getMerchantId(),queryParam.getUserId());
	}

	@Override
	public MaterialCartPlaceOrderRespDTO orderPreview(MaterialCartOrderPreviewReqDTO materialCartOrderPreviewReqDTO) {
		return materialCartListService.placeOrderPreview(materialCartOrderPreviewReqDTO.getMerchantId(),materialCartOrderPreviewReqDTO.getUserId(),materialCartOrderPreviewReqDTO.getCartIds());
	}

	@Override
	public MaterialCartPlaceOrderPrepareRespDTO orderPrepare(MaterialCartOrderPreviewReqDTO materialCartOrderPreviewReqDTO) {
		return materialCartPaymentPrepareService.add(materialCartOrderPreviewReqDTO.getMerchantId(),materialCartOrderPreviewReqDTO.getUserId(),materialCartOrderPreviewReqDTO.getCartIds());
	}

	@Override
	public void delete(MaterialCartDeleteDTO materialCartDeleteDTO) {
		materialCartService.delete(materialCartDeleteDTO);
	}
}