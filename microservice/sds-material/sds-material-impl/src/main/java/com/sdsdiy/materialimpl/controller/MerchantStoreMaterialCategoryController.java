package com.sdsdiy.materialimpl.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.materialapi.api.MerchantStoreMaterialCategoryApi;
import com.sdsdiy.materialapi.dto.param.AddMerchantStoreMaterialCategoryParam;
import com.sdsdiy.materialapi.dto.param.UpdateMerchantStoreMaterialCategoryParam;
import com.sdsdiy.materialapi.dto.resp.MerchantStoreMaterialCategoryResp;
import com.sdsdiy.materialapi.dto.resp.SumMaterialNumByStoreIdGroupResp;
import com.sdsdiy.materialimpl.service.MerchantStoreMaterialCategoryService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 店铺素材分类表(MerchantStoreMaterialCategory)表控制层
 *
 * <AUTHOR>
 * @since 2020-11-24 09:52:17
 */
@RestController
public class MerchantStoreMaterialCategoryController implements MerchantStoreMaterialCategoryApi {
    /**
     * 服务对象
     */
    @Resource
    private MerchantStoreMaterialCategoryService merchantStoreMaterialCategoryService;

    @Override
    public void initStoreCategory(Long merchantId, Long userId, Long merchantStoreId) {
        merchantStoreMaterialCategoryService.initCategory(merchantId, merchantStoreId);
    }

    @Override
    public List<MerchantStoreMaterialCategoryResp> list(Long merchantId, Long userId, Long merchantStoreId) {
        return merchantStoreMaterialCategoryService.list(merchantId, merchantStoreId);
    }

    @Override
    public List<SumMaterialNumByStoreIdGroupResp> sumMaterialNumByStoreIdGroup(Long merchantId, Long userId, BaseListDto<Long> merchantStoreIds) {
        List<SumMaterialNumByStoreIdGroupResp> respList = merchantStoreMaterialCategoryService.sumMaterialNumByStoreIdGroup(merchantId, merchantStoreIds.getList());
        if (CollectionUtil.isEmpty(respList)) {
            respList = Lists.newArrayList();
        }
        return respList;
    }

    @Override
    public void add(Long merchantId, Long userId, Long merchantStoreId, AddMerchantStoreMaterialCategoryParam addParam) {
        merchantStoreMaterialCategoryService.add(merchantId, userId, merchantStoreId, addParam);
    }

    @Override
    public void delete(Long merchantId, Long userId, Long merchantStoreId, Long id) {
        merchantStoreMaterialCategoryService.delete(merchantId, userId, id);
    }

    @Override
    public void update(Long merchantId, Long userId, Long merchantStoreId, UpdateMerchantStoreMaterialCategoryParam updateParam) {
        merchantStoreMaterialCategoryService.update(merchantId, userId, merchantStoreId, updateParam);
    }

    @Override
    public List<Long> getSyncIdList(Long merchantId, Long userId) {
        return merchantStoreMaterialCategoryService.getSyncIdList();
    }

    @Override
    public void updateSync(Long merchantId, Long userId, BaseListDto baseListDto) {
        merchantStoreMaterialCategoryService.updateSync(baseListDto.getList());
    }
}