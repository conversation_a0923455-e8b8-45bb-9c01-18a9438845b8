package com.sdsdiy.materialimpl.controller;

import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.materialapi.api.MaterialClassificationApi;
import com.sdsdiy.materialapi.dto.param.EditReqDto;
import com.sdsdiy.materialapi.dto.resp.MarterialClassificationPageRespDto;
import com.sdsdiy.materialdata.dto.officialmaterial.DesignMaterialClassificationAddReqDTO;
import com.sdsdiy.materialdata.dto.officialmaterial.DesignMaterialDeleteReqDTO;
import com.sdsdiy.materialimpl.service.DesignMaterialClassificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/26
 */

@RestController
public class MaterialClassificationController implements MaterialClassificationApi {

    @Autowired
    private DesignMaterialClassificationService classificationService;

    @Override
    public PageListResultRespDto<MarterialClassificationPageRespDto> page(Integer page, Integer size) {
        return classificationService.getPage(page,size);

    }

    @Override
    public List<MarterialClassificationPageRespDto> list() {
        return classificationService.getList();
    }

    @Override
    public void edit(EditReqDto reqDto) {
         classificationService.edit(reqDto);

    }

    @Override
    public void add(DesignMaterialClassificationAddReqDTO reqDTO) {
        classificationService.add(reqDTO);
    }

    @Override
    public void repeat(Long id, String name) {
        classificationService.checkName(id,name);
    }

    @Override
    public void delete(DesignMaterialDeleteReqDTO reqDTO) {
        classificationService.delete(reqDTO.getIds(),reqDTO.getUserId());
    }

}
