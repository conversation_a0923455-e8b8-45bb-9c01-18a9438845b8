package com.sdsdiy.materialimpl.controller;

import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.materialapi.api.ThirdPlatformMaterialApi;
import com.sdsdiy.materialapi.dto.base.ThirdPlatformMaterialReq;
import com.sdsdiy.materialapi.dto.base.ThirdPlatformMaterialResp;
import com.sdsdiy.materialapi.dto.base.ThirdPlatformMaterialTaskMaterialResp;
import com.sdsdiy.materialapi.dto.param.thirdplatformmaterial.ThirdPlatformMaterialDto;
import com.sdsdiy.materialapi.dto.param.thirdplatformmaterial.ThirdPlatformMaterialTaskReq;
import com.sdsdiy.materialapi.dto.resp.ThirdPlatformMaterialFlodResp;
import com.sdsdiy.materialdata.dto.ThirdPlatformMaterialTaskResp;
import com.sdsdiy.materialimpl.service.ThirdPlatformMaterialService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 第三方平台素材表(ThirdPlatformMaterial)表控制层
 *
 * <AUTHOR>
 * @since 2022-02-21 16:27:53
 */
@RestController
public class ThirdPlatformMaterialController implements ThirdPlatformMaterialApi {
    /**
     * 服务对象
     */
    @Resource
    private ThirdPlatformMaterialService thirdPlatformMaterialService;


    @Override
    public ThirdPlatformMaterialTaskResp addThirdPlatformMaterialBatch(ThirdPlatformMaterialDto param) {
        return thirdPlatformMaterialService.addThirdPlatformMaterialBatch(param);
    }

    @Override
    public List<ThirdPlatformMaterialTaskMaterialResp> getTaskStatus(@Valid ThirdPlatformMaterialTaskReq param) {
        return thirdPlatformMaterialService.getTaskStatus(param.getTaskId());
    }

    @Override
    public Map<String, Long> getThirdIdKeyMaterialIdValueMap(Long merchantId, BaseListDto<String> thirdPlatformMaterialIds) {
        return thirdPlatformMaterialService.getThirdIdKeyMaterialIdValueMap(merchantId, thirdPlatformMaterialIds.getList());
    }

    @Override
    public List<ThirdPlatformMaterialResp> getByMaterialIds(ThirdPlatformMaterialReq req) {
        return thirdPlatformMaterialService.getByMaterialIds(req.getMerchantId(),req.getMaterialIds());
    }

    @Override
    public ThirdPlatformMaterialFlodResp getMaterialAndFold(Long id) {
        return thirdPlatformMaterialService.getFoldOrSaveFold(id);
    }

    @Override
    public void syncMaterialSucceed(Long id, Long materialId) {
        thirdPlatformMaterialService.syncMaterialSucceed(id, materialId);
    }
}