package com.sdsdiy.materialimpl.controller;


import com.sdsdiy.materialapi.api.MaterialCartPayApi;
import com.sdsdiy.materialdata.dto.*;
import com.sdsdiy.materialimpl.service.materialcart.MaterialCartPayService;
import com.sdsdiy.orderdata.dto.PaymentRespDTO;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * (Material)表控制层
 *
 * <AUTHOR>
 * @since 2020-11-13 16:46:06
 */
@RestController
public class MaterialCartPayController implements MaterialCartPayApi {

	@Resource
	MaterialCartPayService materialCartPayService;

	@Override
	public MaterialCartPayRespDTO pay(MaterialCartPayReqDTO reqDTO) {
		return materialCartPayService.pay(reqDTO.getMerchantId(),reqDTO.getUserId(),reqDTO.getMaterialOrderPrepareId(),reqDTO.getPaymentMethod(),reqDTO.getTotalAmount());
	}
	@Override
	public void alipaySuccess(PaymentDto paymentRespDTO) {
		materialCartPayService.alipaySuccess(paymentRespDTO);
	}
}