package com.sdsdiy.materialimpl.controller.aidrawing;

import com.sdsdiy.common.base.entity.dto.PageResult;
import com.sdsdiy.materialapi.api.aidrawing.PodAiAccountApi;
import com.sdsdiy.materialapi.vo.aidrawing.pod.PodAiAccountGroupDetailVO;
import com.sdsdiy.materialapi.vo.aidrawing.pod.PodAiAccountGroupPageVO;
import com.sdsdiy.materialdata.req.aiaccount.EditAiAccountGroupReq;
import com.sdsdiy.materialdata.req.aiaccount.AddAiAccountReq;
import com.sdsdiy.materialdata.req.aiaccount.EditAiAccountReq;
import com.sdsdiy.materialdata.req.aiaccount.EditAiGroupNameReq;
import com.sdsdiy.materialimpl.service.aidrawing.PodAiAccountGroupService;
import com.sdsdiy.materialimpl.service.aidrawing.PodAiAccountService;
import com.sdsdiy.materialimpl.service.aidrawing.PodAiMerchantService;
import com.sdsdiy.userapi.dto.MerchantSimpleDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
public class InnerPodAiAccountController implements PodAiAccountApi {

    private final PodAiAccountGroupService podAiAccountGroupService;
    private final PodAiAccountService podAiAccountService;
    private final PodAiMerchantService podAiMerchantService;

    @Override
    public PageResult<PodAiAccountGroupPageVO> page(String groupName, Long tenantId, Long merchantId, Integer page, Integer size) {

        return podAiAccountGroupService.page(groupName, tenantId, merchantId, page, size);
    }

    @Override
    public List<MerchantSimpleDto> bindMerchantList(Long tenantId) {
        return podAiMerchantService.bindMerchantList(tenantId);
    }

    @Override
    public List<MerchantSimpleDto> unBindMerchantList(Long groupId, Long tenantId) {
        return podAiMerchantService.unBindMerchantList(groupId, tenantId);
    }

    @Override
    public void addGroup(Long tenantId, EditAiGroupNameReq req) {
        podAiAccountGroupService.addGroup(tenantId, req);
    }

    @Override
    public void editGroup(Long id, Long tenantId, EditAiGroupNameReq req) {
        podAiAccountGroupService.editGroup(id, tenantId, req);
    }

    @Override
    public void deleteGroup(Long id) {
        podAiAccountGroupService.deleteGroup(id);
    }

    @Override
    public PodAiAccountGroupDetailVO groupDetail(Long id) {
        return podAiAccountGroupService.groupDetail(id);
    }

    @Override
    public void editGroupDetail(Long id, Long tenantId, EditAiAccountGroupReq req) {
        podAiAccountGroupService.editGroupDetail(id, tenantId, req.getMerchantId());
    }

    @Override
    public void addAccount(AddAiAccountReq req) {
        podAiAccountService.addAccount(req);
    }

    @Override
    public void editAccount(Long id, EditAiAccountReq req) {
        podAiAccountService.editAccount(id, req);
    }

    @Override
    public void deleteAccount(Long id) {
        podAiAccountService.deleteAccount(id);
    }

    @Override
    public void failTaskByIds(List<Long> taskIds) {
        podAiAccountService.failOldTask(taskIds);
    }
}