package com.sdsdiy.materialimpl.controller.requirement;


import cn.hutool.core.util.StrUtil;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.materialapi.api.requirement.MaterialRequirementAuditApi;
import com.sdsdiy.materialapi.constant.enums.OfficialMaterialAuditStatus;
import com.sdsdiy.materialdata.dto.requirement.MaterialRequirementAuditDTO;
import com.sdsdiy.materialdata.dto.requirement.MaterialRequirementEsDTO;
import com.sdsdiy.materialimpl.fegin.MaterialRequirementStatFeign;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementAuditManager;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementService;
import com.sdsdiy.statapi.dto.material.MaterialRequirementStatDTO;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 素材需求审批表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@RestController
public class MaterialRequirementAuditController implements MaterialRequirementAuditApi {
    @Autowired
    private MaterialRequirementAuditManager materialRequirementAuditManager;
    @Autowired
    private MaterialRequirementService materialRequirementService;
    @Autowired
    private MaterialRequirementStatFeign materialRequirementStatFeign;

    @GlobalTransactional
    @Override
    public void audit(MaterialRequirementAuditDTO dto) {
        boolean pass = OfficialMaterialAuditStatus.isPass(dto.getAuditStatus());
        if (!pass && StrUtil.isBlank(dto.getAuditRemark())) {
            Assert.wrong("审核不通过原因不能为空");
        }
        Long materialRequirementId = materialRequirementAuditManager.audit(dto);
        MaterialRequirementEsDTO esDTO = materialRequirementService.auditResult(materialRequirementId, pass, dto.getUpdateUid());
        if (pass && esDTO != null) {
            // 统计表新增记录
            materialRequirementStatFeign.create(new MaterialRequirementStatDTO(materialRequirementId,
                    esDTO.getMerchantId(), dto.getUpdateUid()));
        }
    }

}

