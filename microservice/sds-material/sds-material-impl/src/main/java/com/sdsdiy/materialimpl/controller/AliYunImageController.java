package com.sdsdiy.materialimpl.controller;

import com.sdsdiy.materialapi.api.AliYunImageApi;
import com.sdsdiy.materialapi.dto.resp.ImageSegmentHeadDto;
import com.sdsdiy.materialapi.dto.resp.ImageSegmentHeadParam;
import com.sdsdiy.materialimpl.service.AliYunImageService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * (Abc)表控制层
 *
 * <AUTHOR>
 * @since 2021-02-03 16:06:39
 */
@RestController
public class AliYunImageController implements AliYunImageApi {

    @Resource
    private AliYunImageService aliyunImageService;

    @Override
    @ApiOperation("头像抠图")
    @PostMapping("/image/segmentHead")
    public ImageSegmentHeadDto segmentHead(@RequestBody ImageSegmentHeadParam param) {
        return aliyunImageService.segmentHeadDto(param);
    }
}
