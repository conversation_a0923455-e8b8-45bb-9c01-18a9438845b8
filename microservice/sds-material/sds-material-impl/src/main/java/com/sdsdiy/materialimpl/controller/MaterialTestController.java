package com.sdsdiy.materialimpl.controller;


import com.es.pojo.EsResponse;
import com.es.pojo.PageInfo;
import com.sdsdiy.common.base.entity.dto.BasePageSelect;
import com.sdsdiy.common.base.helper.BeanUtils;
import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.core.aws.s3.S3Util;
import com.sdsdiy.materialapi.api.MaterialApi;
import com.sdsdiy.materialapi.api.MaterialTestApi;
import com.sdsdiy.materialapi.dto.base.MaterialSaveReqDto;
import com.sdsdiy.materialapi.dto.base.materials.*;
import com.sdsdiy.materialapi.dto.resp.OfficialMaterialLikeDelRespDto;
import com.sdsdiy.materialdata.dto.MerchantOfficialMaterialRelationRecordDTO;
import com.sdsdiy.materialdata.dto.material.MaterialEsDto;
import com.sdsdiy.materialdata.dto.material.MaterialFolderReqDTO;
import com.sdsdiy.materialdata.dto.material.MaterialRespDto;
import com.sdsdiy.materialdata.dto.material.MaterialSimpleRespDTO;
import com.sdsdiy.materialdata.util.MaterialUtils;
import com.sdsdiy.materialimpl.entity.po.Material;
import com.sdsdiy.materialimpl.service.MaterialEsService;
import com.sdsdiy.materialimpl.service.MaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * (Material)表控制层
 *
 * <AUTHOR>
 * @since 2020-11-13 16:46:06
 */
@Api(tags = "素材")
@RestController
@Log4j2
public class MaterialTestController implements MaterialTestApi {

    @Override
    public void specifyShareBatch(List<MaterialRespDto> materialRespDtos) {
        int i = 1;
        for (MaterialRespDto materialRespDto : materialRespDtos) {
            try{
                log.info("index {} url {}",i,materialRespDto.getName());
                S3Util.download(materialRespDto.getImgUrl(),materialRespDto.getName().replace(".jpg",""));

            }catch (Exception e){

            }
            i ++;
        }
    }
}