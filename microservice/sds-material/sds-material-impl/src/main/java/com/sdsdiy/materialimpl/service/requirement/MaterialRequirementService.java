package com.sdsdiy.materialimpl.service.requirement;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdsdiy.common.base.constant.CommonConstant;
import com.sdsdiy.common.base.entity.dto.EsSearchResHelper;
import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.common.base.enums.SexEnum;
import com.sdsdiy.common.base.helper.MoneyHelper;
import com.sdsdiy.core.aws.es.ElasticsearchUtils;
import com.sdsdiy.core.aws.es.EsSearchHelper;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.materialapi.constant.enums.OfficialMaterialAuditStatus;
import com.sdsdiy.materialdata.dto.requirement.*;
import com.sdsdiy.materialdata.enums.MaterialEsSort;
import com.sdsdiy.materialdata.enums.MaterialRequirementStatus;
import com.sdsdiy.materialdata.enums.RequirementSaleAge;
import com.sdsdiy.materialdata.enums.RequirementSaleMarket;
import com.sdsdiy.materialdata.util.MaterialUtils;
import com.sdsdiy.materialimpl.entity.po.requirement.MaterialRequirement;
import com.sdsdiy.materialimpl.fegin.*;
import com.sdsdiy.materialimpl.service.DesignMaterialClassificationService;
import com.sdsdiy.materialimpl.service.DesignMaterialStyleService;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialEsService;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialService;
import com.sdsdiy.productdata.dto.CategoryNameDTO;
import com.sdsdiy.statapi.dto.material.MaterialCustomDynamicDTO;
import com.sdsdiy.statapi.dto.material.MaterialRequirementStatDTO;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.MerchantSimpleDto;
import com.sdsdiy.userapi.dto.merchant.MerchantSysUserSimpleDto;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/9
 */
@Log4j2
@Service
public class MaterialRequirementService {
    private static final String ES_INDEX = "sds_material_requirement";
    @Autowired
    private ElasticsearchUtils elasticsearchUtils;
    @Autowired
    private DesignMaterialStyleService designMaterialStyleService;
    @Autowired
    private DesignMaterialClassificationService designMaterialClassificationService;
    @Autowired
    private CategoryFeign categoryFeign;
    @Autowired
    private MerchantFeign merchantFeign;
    @Autowired
    private MaterialRequirementManager materialRequirementManager;
    @Autowired
    private MaterialRequirementSignManager materialRequirementSignManager;
    @Autowired
    private MaterialRequirementStatFeign materialRequirementStatFeign;
    @Autowired
    private MaterialRequirementAuditManager materialRequirementAuditManager;
    @Autowired
    private OfficialMaterialService officialMaterialService;
    @Autowired
    private OfficialMaterialEsService officialMaterialEsService;
    @Autowired
    private OfficialMaterialStatFeign officialMaterialStatFeign;
    @Autowired
    private MerchantSysUserFeign merchantSysUserFeign;

    // es=========================start

    public EsSearchResHelper<MaterialRequirementEsDTO> pageFromEs(MaterialRequirementEsQueryDTO queryDTO) {
        if (queryDTO.getPage() < 1 || queryDTO.getSize() < 1) {
            return null;
        }
        queryDTO.setSortStr(MaterialEsSort.convertSort(queryDTO.getSort()));
        EsSearchHelper esSearchHelper = new EsSearchHelper();
        esSearchHelper.setPage(queryDTO.getPage());
        esSearchHelper.setPageSize(queryDTO.getSize());
        // 状态
        esSearchHelper.addQuery("status", MaterialRequirementStatus.CONTRIBUTE.status);
        // 销售市场
        if (StrUtil.isNotBlank(queryDTO.getSaleMarket())) {
            esSearchHelper.addQuery("saleMarket", queryDTO.getSaleMarket());
        }
        // 销售人群
        if (StrUtil.isNotBlank(queryDTO.getSaleAge())) {
            esSearchHelper.addQuery("saleAge", queryDTO.getSaleAge());
        }
        // 销售性别
        if (StrUtil.isNotBlank(queryDTO.getSaleSex())) {
            esSearchHelper.addQuery("saleSex", queryDTO.getSaleSex());
        }
        // 适用品类/产品分类
        if (StrUtil.isNotBlank(queryDTO.getProductCategory())) {
            esSearchHelper.addQuery("productCategory", queryDTO.getProductCategory());
        }
        // 素材分类id
        if (StrUtil.isNotBlank(queryDTO.getMaterialCategory())) {
            esSearchHelper.addQuery("materialCategory", queryDTO.getMaterialCategory());
        }
        // 设计风格id
        if (StrUtil.isNotBlank(queryDTO.getDesignStyle())) {
            esSearchHelper.addQuery("designStyle", queryDTO.getDesignStyle());
        }
        // 排序
        if (StrUtil.isNotBlank(queryDTO.getSortStr())) {
            esSearchHelper.addSort(queryDTO.getSortStr());
        } else {
            esSearchHelper.addSort("-id");
        }
        return elasticsearchUtils
                .searchList(ES_INDEX, ElasticsearchUtils.ES_INDEX_TYPE, esSearchHelper, MaterialRequirementEsDTO.class);
    }

    public MaterialRequirementEsDTO saveEs(Long materialRequirementId) {
        MaterialRequirementDTO requirementDTO = materialRequirementManager.getOneDto(materialRequirementId);
        MaterialRequirementEsDTO esDTO = requirementResp2EsDTO(requirementDTO);
        save2Es(Collections.singletonList(esDTO));
        return esDTO;
    }

    public void saveEs(List<Long> ids) {
        List<MaterialRequirementDTO> dtoList = materialRequirementManager.listDto(ids);
        save2Es(dtoList.stream().map(this::requirementResp2EsDTO).collect(Collectors.toList()));
    }

    private void save2Es(List<MaterialRequirementEsDTO> esDTOList) {
        elasticsearchUtils.save(ES_INDEX, ElasticsearchUtils.ES_INDEX_TYPE, esDTOList);
        log.info("save MaterialRequirementEsDTOList  completed  for json : " + JSON.toJSONString(esDTOList));
    }

    public MaterialRequirementEsDTO requirementResp2EsDTO(MaterialRequirementDTO requirementDTO) {
        MaterialRequirementEsDTO esDTO = new MaterialRequirementEsDTO();
        BeanUtil.copyProperties(requirementDTO, esDTO);
        // 销售市场
        esDTO.setSaleMarket(StrUtil.replace(requirementDTO.getSaleMarket(), StrUtil.COMMA, StrUtil.SPACE));
        // 销售人群
        esDTO.setSaleAge(StrUtil.replace(requirementDTO.getSaleAge(), StrUtil.COMMA, StrUtil.SPACE));
        // 适用品类
        esDTO.setProductCategory(StrUtil.replace(requirementDTO.getProductCategory(), StrUtil.COMMA, StrUtil.SPACE));
        // 素材分类id
        esDTO.setMaterialCategory(StrUtil.replace(requirementDTO.getMaterialCategory(), StrUtil.COMMA, StrUtil.SPACE));
        // 适用品类
        esDTO.setDesignStyle(StrUtil.replace(requirementDTO.getDesignStyle(), StrUtil.COMMA, StrUtil.SPACE));
        // 截稿日期
        esDTO.setDeadlineLong(requirementDTO.getDeadline().getTime());
        return esDTO;
    }

    public void deleteEs(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        elasticsearchUtils.delete(ES_INDEX, ElasticsearchUtils.ES_INDEX_TYPE, ids);
    }
    // es======================end

    @Async
    public void updateMaterialNum(List<Long> materialIds) {
        Map<Long, Long> requirementIdCountMap = officialMaterialService.getRequirementIdAndCount(materialIds);
        requirementIdCountMap.forEach((requirementId, count) -> {
            materialRequirementStatFeign.addMaterialNum(requirementId, count.intValue());
        });
    }

    @Async
    public void putCustomDynamic(Long requirementId, Long merchantId, Long bidAmount) {
        MerchantRespDto merchant = merchantFeign.getMerchantById(merchantId);
        MaterialCustomDynamicDTO dynamicDTO = new MaterialCustomDynamicDTO();
        dynamicDTO.setDate(DateUtil.today());
        dynamicDTO.setMerchantNo(merchant.getMerchantNo());
        dynamicDTO.setPrice(bidAmount == null ? BigDecimal.ZERO : new BigDecimal(bidAmount));
        dynamicDTO.setRequirementId(requirementId);
        officialMaterialStatFeign.putCustomDynamic(dynamicDTO);
    }

    /**
     * 截止时间-到期事件
     */
    public void deadlineOver() {
        DateTime today = DateUtil.date();
        // 投稿截止
        List<Long> contributeIds = materialRequirementManager.contributeDeadline(today);
        // 删投稿中需求es
        deleteEs(contributeIds);
        // 选稿截止
        materialRequirementManager.selectionDeadline(today);
        // 开放素材需求
        List<Long> openRequirementIds = materialRequirementManager.openMaterial(today);
        // 更新素材并上架
        List<Long> materialIds = officialMaterialService.openMaterialAndOnShelf(openRequirementIds, null);
        // 更新素材es
        officialMaterialEsService.saveBatch(materialIds);
    }

    /**
     * 报名，并更新已报名数统计
     */
    public boolean sign(MaterialRequirementSignDTO dto) {
        boolean sign = materialRequirementSignManager.sign(dto);
        if (sign) {
            int signNum = materialRequirementSignManager.signCount(new MaterialRequirementSignDTO(dto.getMaterialRequirementId()));
            materialRequirementStatFeign.updateSignNum(dto.getMaterialRequirementId(), signNum);
        }
        return sign;
    }

    /**
     * 审核结果更新
     */
    public MaterialRequirementEsDTO auditResult(Long materialRequirementId, boolean pass, Long updateUid) {
        boolean update = materialRequirementManager.auditResult(materialRequirementId, pass, updateUid);
        if (pass && update) {
            // 添加到ES
            return saveEs(materialRequirementId);
        }
        return null;
    }

    public MaterialRequirementRespDTO getOneResp(Long id) {
        MaterialRequirementDTO one = materialRequirementManager.getOneDto(id);
        if (one == null) {
            return null;
        }
        return translate(Collections.singletonList(one)).get(0);
    }

    /**
     * 【设计师端】
     * 分页查询
     */
    public MaterialRequirementDesignerRespDTO<MaterialRequirementRespDTO> pageForDesigner(MaterialRequirementEsQueryDTO pageSelect) {
        MaterialRequirementDesignerRespDTO<MaterialRequirementDTO> tmpDTO;
        if (pageSelect.getMyContribute() == 1) {
            // 我的投稿
            MaterialRequirementMyContributeQueryDTO queryDTO = new MaterialRequirementMyContributeQueryDTO();
            if ("processing".equalsIgnoreCase(pageSelect.getRequirementStatus())) {
                queryDTO.setStatusList(MaterialRequirementStatus.processing());
            } else if ("completed".equalsIgnoreCase(pageSelect.getRequirementStatus())) {
                queryDTO.setStatusList(MaterialRequirementStatus.completed());
            }
            queryDTO.setDesignAccountId(pageSelect.getDesignAccountId());
            queryDTO.setDesignMerchantId(pageSelect.getDesignMerchantId());
            queryDTO.setPage(pageSelect.getPage());
            queryDTO.setSize(pageSelect.getSize());
            tmpDTO = materialRequirementManager.pageMyContribute(queryDTO);
        } else {
            // 需求池
            pageSelect.setStatus(MaterialRequirementStatus.CONTRIBUTE.status);
            // 先es查出
            EsSearchResHelper<MaterialRequirementEsDTO> esPage = pageFromEs(pageSelect);
            List<MaterialRequirementDTO> requirementDtoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(esPage.getItems())) {
                // 再id查询
                requirementDtoList = materialRequirementManager.listForDesigner(esPage.getItems().stream()
                        .map(MaterialRequirementEsDTO::getId).collect(Collectors.toList()), pageSelect.getSortStr());
            }
            tmpDTO = new MaterialRequirementDesignerRespDTO<>(esPage.getTotalCount(), requirementDtoList);
        }
        MaterialRequirementDesignerRespDTO<MaterialRequirementRespDTO> respDTO = new MaterialRequirementDesignerRespDTO<>();
        respDTO.setTotalCount(tmpDTO.getTotalCount());
        if (pageSelect.getMyContribute() == 1 && "processing".equalsIgnoreCase(pageSelect.getRequirementStatus())) {
            respDTO.setMyContributeNum(tmpDTO.getTotalCount());
        } else {
            MaterialRequirementMyContributeQueryDTO queryCount = new MaterialRequirementMyContributeQueryDTO();
            queryCount.setDesignAccountId(pageSelect.getDesignAccountId());
            queryCount.setDesignMerchantId(pageSelect.getDesignMerchantId());
            queryCount.setStatusList(MaterialRequirementStatus.processing());
            respDTO.setMyContributeNum(materialRequirementManager.countMyContribute(queryCount));
        }
        if (CollectionUtils.isEmpty(tmpDTO.getList())) {
            respDTO.setList(new ArrayList<>());
            return respDTO;
        }
        // 是否中标
        Set<Long> checkWinBid = officialMaterialService.checkWinBid(tmpDTO.getList().stream()
                        .map(MaterialRequirementDTO::getId).collect(Collectors.toList()),
                pageSelect.getDesignMerchantId(), pageSelect.getDesignAccountId());
        List<MaterialRequirementRespDTO> respDtoList = translate(RelationsBinder.convertAndBind(tmpDTO.getList(), MaterialRequirementDTO.class));
        respDtoList.forEach(r -> r.setWinBid(checkWinBid.contains(r.getId()) ? 1 : 0));
        respDTO.setList(respDtoList);
        return respDTO;
    }

    /**
     * 【商户端】
     * 分页查询
     */
    public PageListResultDTO<MaterialRequirementRespDTO> pageForMerchant(MaterialRequirementQueryDTO pageSelect) {
        Page<MaterialRequirement> page = materialRequirementManager.pageForMerchant(pageSelect);
        return new PageListResultDTO<>(page.getTotal(),
                translate(RelationsBinder.convertAndBind(page.getRecords(), MaterialRequirementDTO.class)));
    }

    /**
     * 【运营端】
     * 分页查询
     */
    public PageListResultDTO<MaterialRequirementRespDTO> pageForAdmin(MaterialRequirementQueryDTO pageSelect) {
        if (StrUtil.isNotBlank(pageSelect.getMerchantNo())) {
            List<Long> merchantIds = merchantFeign.getMerchantIdByNameOrNo(pageSelect.getMerchantNo());
            if (CollectionUtils.isEmpty(merchantIds)) {
                return new PageListResultDTO<>(0, new ArrayList<>());
            }
            pageSelect.setMerchantIds(merchantIds);
        }
        Page<MaterialRequirement> page = materialRequirementManager.pageForAdmin(pageSelect);
        return new PageListResultDTO<>(page.getTotal(),
                translate(RelationsBinder.convertAndBind(page.getRecords(), MaterialRequirementDTO.class)));
    }


    private List<MaterialRequirementRespDTO> translate(List<MaterialRequirementDTO> dtoList) {
        List<MaterialRequirementRespDTO> respDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(dtoList)) {
            return respDtoList;
        }
        Set<Long> merchantIds = new HashSet<>();
        Set<Long> merchantAccountIds = new HashSet<>();
        List<Long> requirementIds = new ArrayList<>();
        List<Long> auditRefuseRequirementIds = new ArrayList<>();
        Set<Long> productCategoryIds = new HashSet<>();
        Set<Long> designStyleIds = new HashSet<>();
        Set<Long> materialCategoryIds = new HashSet<>();
        dtoList.stream().forEach(r -> {
            if (StrUtil.isNotBlank(r.getProductCategory())) {
                productCategoryIds.addAll(Arrays.stream(r.getProductCategory().split(CommonConstant.COMMA))
                        .map(Long::parseLong).collect(Collectors.toList()));
            }
            if (StrUtil.isNotBlank(r.getDesignStyle())) {
                designStyleIds.addAll(Arrays.stream(r.getDesignStyle().split(CommonConstant.COMMA))
                        .map(Long::parseLong).collect(Collectors.toList()));
            }
            if (StrUtil.isNotBlank(r.getMaterialCategory())) {
                materialCategoryIds.addAll(Arrays.stream(r.getMaterialCategory().split(CommonConstant.COMMA))
                        .map(Long::parseLong).collect(Collectors.toList()));
            }
            if (OfficialMaterialAuditStatus.REFUSE.status.equals(r.getAuditStatus())) {
                auditRefuseRequirementIds.add(r.getId());
            }
            merchantIds.add(r.getMerchantId());
            merchantAccountIds.add(r.getCreateUid());
            requirementIds.add(r.getId());
        });
        // 统计=========start
        List<MaterialRequirementStatDTO> statDtoList = materialRequirementStatFeign.listDtoByIds(requirementIds);
        Map<Long, MaterialRequirementStatDTO> statDtoMap = statDtoList.stream().collect(Collectors.toMap(MaterialRequirementStatDTO::getId, r -> r));
        // 统计=========end
        Map<Long, MerchantSimpleDto> merchantSimpleDtoMap = merchantFeign.mapMerchantSimpleDtoByIds(new ArrayList<>(merchantIds));
        Map<Long, MerchantSysUserSimpleDto> merchantSysUserSimpleDtoMap = merchantSysUserFeign.mapMerchantSysUserSimpleDtoByIds(new ArrayList<>(merchantAccountIds));
        Map<Long, String> designStyleNameMap = designMaterialStyleService.getDesignStyleNameMap(new ArrayList<>(designStyleIds));
        Map<Long, String> materialCategoryNameMap = designMaterialClassificationService.getMaterialCategoryNameMap(new ArrayList<>(materialCategoryIds));
        // 产品分类/适用品类=========start
        List<CategoryNameDTO> categoryNameDTOList = categoryFeign.queryCategoryNameWithParent(new ArrayList<>(productCategoryIds));
        Map<Long, String> categoryNameMap = categoryNameDTOList.stream().collect(Collectors.toMap(CategoryNameDTO::getId, CategoryNameDTO::getName));
        // 产品分类/适用品类=========end
        Map<Long, String> auditRemarkMap = materialRequirementAuditManager.getAuditRemarkMap(auditRefuseRequirementIds);
        Date now = new Date();
        dtoList.forEach(dto -> {
            MaterialRequirementRespDTO resp = new MaterialRequirementRespDTO();
            BeanUtil.copyProperties(dto, resp);
            // 审核状态、不通过原因
            resp.setAuditStatusDesc(OfficialMaterialAuditStatus.getByStatus(dto.getAuditStatus()).desc)
                    .setAuditRemark(auditRemarkMap.get(dto.getId()));
            // 进度
            MaterialRequirementStatus status = MaterialRequirementStatus.getByStatus(dto.getStatus());
            resp.setStatusDesc(status.desc);
            if (status.status <= MaterialRequirementStatus.CONTRIBUTE.status) {
                // 投稿中,或之前
                resp.setDeadlineDesc(deadlineDate2Desc(dto.getDeadline(), now, status));
            } else if (MaterialRequirementStatus.SELECTION.equals(status)) {
                // 选稿中,投稿结束后的三天的23:59:59结束
                resp.setSelectionEndDesc(deadlineDate2Desc(DateUtil.endOfDay(DateUtil.offsetDay(new Date(dto.getContributeEndTime()), 3)), now, status));
            } else {
                // 已结束
                resp.setDeadlineDesc(deadlineDate2Desc(null, now, status));
            }
            // 中标金额,分转元
            resp.setBidAmount(MoneyHelper.changeF2Y(dto.getBidAmount()));
            // 商户信息
            resp.setMerchantInfo(merchantSimpleDtoMap.get(dto.getMerchantId()));
            // 需求发起人信息
            MerchantSysUserSimpleDto merchantSysUserSimpleDto = merchantSysUserSimpleDtoMap.get(dto.getCreateUid());
            resp.setCreateName(merchantSysUserSimpleDto == null ? "" : merchantSysUserSimpleDto.getUsername());
            // 图片路径补全
            if (StrUtil.isNotBlank(dto.getImageUrl())) {
                resp.setImageUrl(Arrays.stream(dto.getImageUrl().split(StrUtil.COMMA))
                        .map(MaterialUtils::fileCode2ImagesUrl).collect(Collectors.toList()));
            }
            // 适用品类
            if (StrUtil.isBlank(dto.getProductCategory())) {
                resp.setProductCategory(new ArrayList<>());
                resp.setProductCategoryDesc(new ArrayList<>());
            } else if ("0".equals(dto.getProductCategory())) {
                resp.setProductCategory(Collections.singletonList(0L));
                resp.setProductCategoryDesc(Collections.singletonList("全部"));
            } else {
                resp.setProductCategory(Arrays.stream(dto.getProductCategory().split(CommonConstant.COMMA))
                        .map(Long::parseLong).collect(Collectors.toList()));
                resp.setProductCategoryDesc(resp.getProductCategory()
                        .stream().map(categoryNameMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            // 素材分类/元素
            if (StrUtil.isBlank(dto.getMaterialCategory())) {
                resp.setMaterialCategory(new ArrayList<>());
                resp.setMaterialCategoryDesc(new ArrayList<>());
            } else {
                resp.setMaterialCategory(Arrays.stream(dto.getMaterialCategory().split(CommonConstant.COMMA))
                        .map(Long::parseLong).collect(Collectors.toList()));
                resp.setMaterialCategoryDesc(resp.getMaterialCategory()
                        .stream().map(materialCategoryNameMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            // 设计/图案风格
            if (StrUtil.isBlank(dto.getDesignStyle())) {
                resp.setDesignStyle(new ArrayList<>());
                resp.setDesignStyleDesc(new ArrayList<>());
            } else {
                resp.setDesignStyle(Arrays.stream(dto.getDesignStyle().split(CommonConstant.COMMA))
                        .map(Long::parseLong).collect(Collectors.toList()));
                resp.setDesignStyleDesc(resp.getDesignStyle()
                        .stream().map(designStyleNameMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            // 销售性别
            resp.setSaleSexDesc(SexEnum.valueOf(dto.getSaleSex()).desc);
            // 销售市场
            if (StrUtil.isBlank(dto.getSaleMarket())) {
                resp.setSaleMarket(new ArrayList<>());
                resp.setSaleMarketDesc(new ArrayList<>());
            } else {
                resp.setSaleMarket(Arrays.asList(dto.getSaleMarket().split(CommonConstant.COMMA)));
                resp.setSaleMarketDesc(RequirementSaleMarket.translate(resp.getSaleMarket()));
            }
            // 销售人群
            if (StrUtil.isBlank(dto.getSaleAge())) {
                resp.setSaleAge(new ArrayList<>());
                resp.setSaleAgeDesc(new ArrayList<>());
            } else {
                resp.setSaleAge(Arrays.asList(dto.getSaleAge().split(CommonConstant.COMMA)));
                resp.setSaleAgeDesc(RequirementSaleAge.translate(resp.getSaleAge()));
            }
            // 统计
            MaterialRequirementStatDTO statDTO = statDtoMap.get(dto.getId());
            if (statDTO != null) {
                resp.setWinBidNum(statDTO.getWinBidNum())
                        .setMaterialNum(statDTO.getMaterialNum())
                        .setSignNum(statDTO.getSignNum());
            }
            respDtoList.add(resp);
        });
        return respDtoList;
    }

    private static String deadlineDate2Desc(Date deadline, Date now, MaterialRequirementStatus status) {
        if (!MaterialRequirementStatus.deadlineShow().contains(status.status)) {
            // 不显示截稿时间
            return status.deadlineStr;
        }
        if (deadline == null) {
            return "";
        }
        long time = (deadline.getTime() - now.getTime()) / 1000;
        if (time <= 0) {
            return "已结束";
        }
        long hour = DateUtil.between(now, deadline, DateUnit.HOUR, false);
        if (hour > 24) {
            long day = hour / 24;
            hour = hour % 24;
            if (hour > 0) {
                return day + "天" + hour + "小时" + status.deadlineStr;
            } else {
                return day + "天" + status.deadlineStr;
            }
        } else if (hour > 0) {
            return hour + "小时" + status.deadlineStr;
        } else {
            long minute = DateUtil.between(now, deadline, DateUnit.MINUTE, false);
            return (minute > 0 ? minute : 1) + "分钟" + status.deadlineStr;
        }

    }
}
