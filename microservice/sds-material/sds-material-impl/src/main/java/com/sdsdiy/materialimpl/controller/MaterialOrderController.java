package com.sdsdiy.materialimpl.controller;


import com.google.common.collect.Lists;
import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.materialapi.api.MaterialOrderApi;
import com.sdsdiy.materialdata.dto.officialorder.MaterialOrderPageReqDTO;
import com.sdsdiy.materialdata.dto.officialorder.MaterialOrderRespDTO;
import com.sdsdiy.materialimpl.listener.PlaceOrderEventListener;
import com.sdsdiy.materialimpl.service.materialorder.FactoryOrderConfirmListenService;
import com.sdsdiy.materialimpl.service.materialorder.MaterialOrderPayListenService;
import com.sdsdiy.materialimpl.service.materialorder.MaterialOrderService;
import com.sdsdiy.materialimpl.service.materialorder.OfficialMaterialPlaceOrderListenService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * (Material)表控制层
 *
 * <AUTHOR>
 * @since 2020-11-13 16:46:06
 */
@RestController
public class MaterialOrderController implements MaterialOrderApi {

	@Resource
	MaterialOrderService materialOrderService;

	@Resource
	MaterialOrderPayListenService materialOrderPayListenService;

	@Resource
	FactoryOrderConfirmListenService factoryOrderConfirmListenService;
	@Resource
	OfficialMaterialPlaceOrderListenService officialMaterialPlaceOrderListenService;

	@Override
	public PageListResultDTO<MaterialOrderRespDTO> pageList(MaterialOrderPageReqDTO reqDTO) {
		return materialOrderService.page(reqDTO);
	}

	@Override
	public void test(Long mid, Long omid) {
		officialMaterialPlaceOrderListenService.execute(mid,omid);
	}


	@Override
	public List<Long> distinctDesignerMerchantId(Long merchantId) {
		return materialOrderService.distinctDesignerMerchantId(merchantId);
	}
}