package com.sdsdiy.materialimpl.controller.official;


import com.sdsdiy.materialapi.api.official.OfficialMaterialPriceConfigApi;
import com.sdsdiy.materialdata.dto.OfficialMaterialPriceConfigRespDTO;
import com.sdsdiy.materialdata.dto.OfficialMaterialPriceConfigUpdateDTO;
import com.sdsdiy.materialimpl.service.OfficialMaterialPriceConfigService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 素材审批表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
@RestController
public class OfficialMaterialPriceConfigController implements OfficialMaterialPriceConfigApi {


    @Resource
    OfficialMaterialPriceConfigService officialMaterialPriceConfigService;


    @Override
    public void update(OfficialMaterialPriceConfigUpdateDTO updateDTO) {
        officialMaterialPriceConfigService.update(updateDTO);
    }

    @Override
    public OfficialMaterialPriceConfigRespDTO get() {
        return officialMaterialPriceConfigService.getRespConfig();
    }


}

