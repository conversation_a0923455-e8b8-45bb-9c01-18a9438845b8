package com.sdsdiy.materialimpl.controller.aidrawing;

import com.sdsdiy.materialapi.api.aidrawing.MerchantAiAccountApi;
import com.sdsdiy.materialimpl.service.aidrawing.MerchantAiAccountService;
import com.sdsdiy.userapi.dto.aidrawing.AiDrawingRemainTimeRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class InnerMerchantAiAccountController implements MerchantAiAccountApi {

    private final MerchantAiAccountService merchantAiAccountService;

    @Override
    public AiDrawingRemainTimeRespDTO getRemindTimesDtoByMerchantId(Long tenantId, Long merchantId) {
        return merchantAiAccountService.getRemainTime(tenantId, merchantId);
    }
}