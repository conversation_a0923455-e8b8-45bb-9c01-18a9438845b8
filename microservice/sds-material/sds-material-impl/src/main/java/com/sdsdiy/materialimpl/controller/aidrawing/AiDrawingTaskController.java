package com.sdsdiy.materialimpl.controller.aidrawing;

import com.sdsdiy.materialapi.api.aidrawing.AiDrawingTaskApi;
import com.sdsdiy.materialapi.dto.param.aidrawing.SaveToFolderReqDTO;
import com.sdsdiy.materialapi.vo.aidrawing.AiDrawingSubTaskRespVO;
import com.sdsdiy.materialimpl.service.aidrawing.AiDrawingTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @BelongsProject: a4-sdsdiy-microservice
 * @BelongsPackage: com.sdsdiy.materialimpl.controller.aidrawing
 * @Author: lujp
 * @CreateTime: 2023-05-29
 * @Description:
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class AiDrawingTaskController implements AiDrawingTaskApi {

    private final AiDrawingTaskService aiDrawingTaskService;
    @Override
    public void saveToMaterialWithGroupIdLock(Long tenantId, Long merchantId, Long userId, SaveToFolderReqDTO reqDTO) {
        this.aiDrawingTaskService.saveToMaterialWithGroupIdLock(tenantId, merchantId, userId, reqDTO);
    }

    @Override
    public void retryInResultWithGroupIdLock(Long tenantId, Long merchantId, Long userId, Long id) {
        this.aiDrawingTaskService.retryInResultWithGroupIdLock(tenantId, merchantId, userId, id);
    }

    @Override
    public void variationWithGroupIdLock(Long tenantId, Long merchantId, Long userId, Long variantImageId) {
        this.aiDrawingTaskService.variationWithGroupIdLock(tenantId, merchantId, userId, variantImageId);
    }

    @Override
    public List<AiDrawingSubTaskRespVO> subTaskList(String no, Long currUserId) {
        return this.aiDrawingTaskService.subTaskList(no, currUserId);
    }
}