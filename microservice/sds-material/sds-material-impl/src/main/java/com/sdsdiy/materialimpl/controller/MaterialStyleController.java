package com.sdsdiy.materialimpl.controller;

import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.materialapi.api.MaterialStyleApi;
import com.sdsdiy.materialapi.dto.param.EditReqDto;
import com.sdsdiy.materialapi.dto.resp.MarterialStylePageRespDto;
import com.sdsdiy.materialdata.dto.officialmaterial.DesignMaterialStyleAddReqDTO;
import com.sdsdiy.materialdata.dto.officialmaterial.DesignMaterialDeleteReqDTO;
import com.sdsdiy.materialimpl.service.DesignMaterialStyleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/25
 */

@RestController
public class MaterialStyleController implements MaterialStyleApi {

    @Autowired
    private DesignMaterialStyleService styleService;

    @Override
    public PageListResultRespDto<MarterialStylePageRespDto> page(Integer page, Integer size) {
        return styleService.getPage(page, size);
    }

    @Override
    public List<MarterialStylePageRespDto> list() {
        return styleService.getList();
    }

    @Override
    public void edit(EditReqDto reqDto) {
        styleService.edit(reqDto);
    }

    @Override
    public void add(DesignMaterialStyleAddReqDTO reqDTO) {
        styleService.add(reqDTO);
    }

    @Override
    public void repeat(Long id,String name) {
        styleService.checkName(id,name);
    }

    @Override
    public void delete(DesignMaterialDeleteReqDTO reqDTO) {
        styleService.delete(reqDTO.getIds(),reqDTO.getUserId());
    }


}
