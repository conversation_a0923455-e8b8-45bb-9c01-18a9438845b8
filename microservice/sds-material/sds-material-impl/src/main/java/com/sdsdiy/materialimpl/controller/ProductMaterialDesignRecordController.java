package com.sdsdiy.materialimpl.controller;

import com.sdsdiy.materialapi.api.ProductMaterialDesignRecordApi;
import com.sdsdiy.materialapi.dto.base.InitProductMaterialDesignRecordDto;
import com.sdsdiy.materialapi.dto.base.IsRepetitiveDesignDto;
import com.sdsdiy.materialapi.dto.base.IsRepetitiveDesignResqDto;
import com.sdsdiy.materialapi.dto.base.IsRepetitiveDesignResqItemDto;
import com.sdsdiy.materialimpl.service.ProductMaterialDesignRecordService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 产品素材合成记录表(ProductMaterialDesignRecord)表控制层
 *
 * <AUTHOR>
 * @since 2021-02-20 17:35:24
 */
@RestController
public class ProductMaterialDesignRecordController implements ProductMaterialDesignRecordApi {
    /**
     * 服务对象
     */
    @Resource
    private ProductMaterialDesignRecordService productMaterialDesignRecordService;

    @Override
    public List<IsRepetitiveDesignResqItemDto> isRepetitiveDesign(IsRepetitiveDesignResqDto isRepetitiveDesignResqDto) {
        return productMaterialDesignRecordService.isRepetitiveDesign(isRepetitiveDesignResqDto);
    }

    @Override
    public void initProductMaterialDesignRecord(InitProductMaterialDesignRecordDto initProductMaterialDesignRecordDto) {
        productMaterialDesignRecordService.initProductMaterialDesignRecord(initProductMaterialDesignRecordDto.getMaterialIds(),initProductMaterialDesignRecordDto.getDesignRecordDto());
    }


}
