package com.sdsdiy.materialimpl.controller.requirement;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.common.base.enums.SdsAggregationEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.materialapi.api.requirement.MaterialRequirementApi;
import com.sdsdiy.materialapi.dto.base.materials.MerchantOfficialMaterialRelationRecordsResqDto;
import com.sdsdiy.materialapi.dto.official.OfficialMaterialRespDTO;
import com.sdsdiy.materialdata.dto.officialmaterial.OfficialMaterialRequirementReqDTO;
import com.sdsdiy.materialdata.dto.requirement.*;
import com.sdsdiy.materialimpl.fegin.MaterialRequirementStatFeign;
import com.sdsdiy.materialimpl.service.MerchantOfficialMaterialRelationRecordsService;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialManager;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementAuditManager;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementManager;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementService;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementSignManager;
import com.sdsdiy.statapi.dto.material.MaterialRequirementStatDTO;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 素材定制需求 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@RestController
public class MaterialRequirementController implements MaterialRequirementApi {
    @Autowired
    private OfficialMaterialManager officialMaterialManager;
    @Autowired
    private MaterialRequirementManager materialRequirementManager;
    @Autowired
    private MaterialRequirementSignManager materialRequirementSignManager;
    @Autowired
    private MaterialRequirementAuditManager materialRequirementAuditManager;
    @Autowired
    private MaterialRequirementService materialRequirementService;
    @Autowired
    private MaterialRequirementStatFeign materialRequirementStatFeign;
    @Resource
    private MerchantOfficialMaterialRelationRecordsService merchantOfficialMaterialRelationRecordsService;

    @GlobalTransactional
    @Override
    public Long add(MaterialRequirementReqDTO reqDTO) {
        if (StrUtil.isBlank(reqDTO.getDeadline())) {
            throw new BusinessException("截稿时间不能为空");
        }
        long day = DateUtil.betweenDay(new Date(), DateUtil.parseDateTime(reqDTO.getDeadline() + " 23:59:59"), false);
        if (day < 7 || day > 30) {
            throw new BusinessException("截止时间不得少于7天，不得高于30天");
        }
        Long requirementId = materialRequirementManager.saveReqDto(reqDTO);
        // 同步新增审核数据
        MaterialRequirementAuditDTO auditDTO = new MaterialRequirementAuditDTO();
        auditDTO.setMerchantId(reqDTO.getMerchantId()).setUpdateUid(reqDTO.getUpdateUid())
                .setMaterialRequirementId(requirementId);
        materialRequirementAuditManager.add(auditDTO);
        // 增加首页需求动态
        materialRequirementService.putCustomDynamic(requirementId, reqDTO.getMerchantId(), reqDTO.getBidAmount());
        return requirementId;
    }

    @Override
    public PageListResultDTO<MaterialRequirementRespDTO> pageForMerchant(MaterialRequirementQueryDTO pageSelect) {
        return materialRequirementService.pageForMerchant(pageSelect);
    }

    @Override
    public PageListResultDTO<MaterialRequirementRespDTO> pageForAdmin(MaterialRequirementQueryDTO pageSelect) {
        return materialRequirementService.pageForAdmin(pageSelect);
    }

    @Override
    public MaterialRequirementDesignerRespDTO<MaterialRequirementRespDTO> pageForDesigner(MaterialRequirementEsQueryDTO pageSelect) {
        return materialRequirementService.pageForDesigner(pageSelect);
    }

    @Override
    public MaterialRequirementRespDTO getOneResp(Long id) {
        return materialRequirementService.getOneResp(id);
    }

    @Override
    public MaterialRequirementOtherDTO getMaterialRequirementOtherDTO(OfficialMaterialRequirementReqDTO reqDTO) {
        MaterialRequirementOtherDTO otherDTO = new MaterialRequirementOtherDTO();
        MaterialRequirementStatDTO statDTO = materialRequirementStatFeign.getDto(reqDTO.getMaterialRequirementId());
        if (statDTO == null) {
            statDTO = new MaterialRequirementStatDTO();
        }
        otherDTO.setMaterialNum(statDTO.getMaterialNum()).setSignNum(statDTO.getSignNum())
                .setVisitNum(statDTO.getVisitNum()).setWinBidNum(statDTO.getWinBidNum());
        // 是否报名
        MaterialRequirementSignDTO signDTO = new MaterialRequirementSignDTO();
        BeanUtil.copyProperties(reqDTO, signDTO);
        boolean sign = materialRequirementSignManager.checkSign(signDTO);
        otherDTO.setSign(sign ? 1 : 0);
        // 设计师端-设置其他值
        if (reqDTO.getPlatform() == SdsAggregationEnum.DESIGNER) {
            officialMaterialManager.setMaterialRequirementOtherDTO(otherDTO, reqDTO);
            // 增加浏览数pv
            materialRequirementStatFeign.addVisitNum(reqDTO.getMaterialRequirementId(), 1);
        }
        return otherDTO;
    }

    @Override
    public PageListResultDTO<OfficialMaterialRespDTO> getPageForRequirement(OfficialMaterialRequirementReqDTO dto) {
        PageListResultDTO<OfficialMaterialRespDTO> page = officialMaterialManager.getPageForRequirement(dto);
        if (dto.getPlatform() == SdsAggregationEnum.MERCHANT && page.getList().size() > 0) {
            // 收藏标记
            List<Long> materialIds = page.getList().stream().map(OfficialMaterialRespDTO::getId).collect(Collectors.toList());
            Set<Long> likeSet = merchantOfficialMaterialRelationRecordsService.checkLikeSet(new MerchantOfficialMaterialRelationRecordsResqDto(materialIds, dto.getMerchantId()));
            page.getList().forEach(m -> m.setCollection(BasePoConstant.yesOrNo(likeSet.contains(m.getId()))));
        }
        return page;
    }

    @Override
    public void materialRequirementDeadline() {
        materialRequirementService.deadlineOver();
    }

    @Override
    public void updateTimeForTest(MaterialRequirementTestUpdateDTO updateDTO) {
        materialRequirementManager.updateTimeForTest(updateDTO);
    }
}

