package com.sdsdiy.materialimpl.controller;

import com.sdsdiy.materialapi.api.MaterialAncestorsInfoApi;
import com.sdsdiy.materialapi.dto.base.MaterialAncestorsInfoDto;
import com.sdsdiy.materialimpl.entity.po.MaterialAncestorsInfo;
import com.sdsdiy.materialimpl.service.MaterialAncestorsInfoService;

import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 素材祖先信息表(MaterialAncestorsInfo)表控制层
 *
 * <AUTHOR>
 * @since 2021-02-20 17:19:01
 */
@RestController
public class MaterialAncestorsInfoController implements MaterialAncestorsInfoApi {
    /**
     * 服务对象
     */
    @Resource
    private MaterialAncestorsInfoService materialAncestorsInfoService;


    @Override
    public void save(MaterialAncestorsInfoDto materialAncestorsInfoDto) {
        materialAncestorsInfoService.save(materialAncestorsInfoDto);
    }

    @Override
    public void saveBatch(List<MaterialAncestorsInfoDto> materialAncestorsInfoDto) {
        materialAncestorsInfoService.saveBatch(materialAncestorsInfoDto);
    }
}
