package com.sdsdiy.materialimpl.controller.aidrawing;

import com.sdsdiy.common.base.entity.dto.PageResult;
import com.sdsdiy.materialapi.api.aidrawing.AiDrawingBatchApi;
import com.sdsdiy.materialapi.vo.aidrawing.AiDrawingMainTaskRespVO;
import com.sdsdiy.materialimpl.service.aidrawing.AiDrawingBatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * ai绘图任务批次表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class AiDrawingBatchController implements AiDrawingBatchApi {
    private final AiDrawingBatchService aiDrawingBatchService;
    @Override
    public PageResult<AiDrawingMainTaskRespVO> page(List<Long> userIds, String key, Integer page, Integer size, Long currUserId) {
        return this.aiDrawingBatchService.page(userIds, key, page, size, currUserId);
    }

    @Override
    public void createTaskByKeywords(Long tenantId, Long merchantId, Long userId, String keywords, Long materialId) {
        this.aiDrawingBatchService.createTaskByKeywordsWithGroupIdLock(tenantId, merchantId, userId, keywords, materialId);
    }

    @Override
    public void createTaskByMaterialId(Long tenantId, Long merchantId, Long userId, Long materialId) {
        this.aiDrawingBatchService.createTaskByMaterialIdWithGroupIdLock(tenantId, merchantId, userId, materialId);

    }

    @Override
    public void imagineMore(Long tenantId, Long merchantId, Long userId, String no, Integer randomParam, String keyWords) {
        this.aiDrawingBatchService.imagineMoreWithGroupIdLock(tenantId, merchantId, userId, no, randomParam, keyWords);
    }

    @Override
    public void deleteAiDrawingTask(Long userId, String no) {
        this.aiDrawingBatchService.deleteAiDrawingTask(userId, no);
    }

    @Override
    public void retry(Long tenantId, Long merchantId, Long userId, String no) {
        this.aiDrawingBatchService.retryWithGroupIdLock(tenantId, merchantId, userId, no);
    }
}