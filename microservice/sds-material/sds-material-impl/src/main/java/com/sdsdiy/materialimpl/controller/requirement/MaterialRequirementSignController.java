package com.sdsdiy.materialimpl.controller.requirement;


import com.sdsdiy.materialapi.api.requirement.MaterialRequirementSignApi;
import com.sdsdiy.materialdata.dto.requirement.MaterialRequirementSignDTO;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementService;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementSignManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 素材需求-设计师报名(按子账号) 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-06
 */
@RestController
public class MaterialRequirementSignController implements MaterialRequirementSignApi {
    @Resource
    private MaterialRequirementSignManager materialRequirementSignManager;
    @Autowired
    private MaterialRequirementService materialRequirementService;

    @Override
    public boolean sign(MaterialRequirementSignDTO dto) {
        return materialRequirementService.sign(dto);
    }

    @Override
    public boolean checkSign(MaterialRequirementSignDTO dto) {
        return materialRequirementSignManager.checkSign(dto);
    }

    @Override
    public int signCount(MaterialRequirementSignDTO dto) {
        return materialRequirementSignManager.signCount(dto);
    }


}

