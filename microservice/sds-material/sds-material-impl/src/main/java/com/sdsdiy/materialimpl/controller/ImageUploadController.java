package com.sdsdiy.materialimpl.controller;

import com.sdsdiy.common.base.entity.dto.ResDTO;
import com.sdsdiy.materialapi.api.ImageUploadApi;
import com.sdsdiy.materialapi.dto.base.ImageUploadDto;
import com.sdsdiy.materialimpl.service.ImageUploadService;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/9/3
 */
@RestController
public class ImageUploadController implements ImageUploadApi {

    @Resource
    private ImageUploadService imageUploadService;

    @Override
    public ImageUploadDto imageUpload(MultipartFile mf, Long userId) {
        return imageUploadService.save(mf, userId);
    }

    @Override
    public ImageUploadDto imageUploadByUrl(ImageUploadDto imageUploadDto) {
        try {
            return imageUploadService.saveByUrl(imageUploadDto.getUrl());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public ResDTO<Boolean> cleanOssImage(ImageUploadDto imageUploadDto) {
        Boolean res ;
        try {
            res = imageUploadService.cleanOssImage(imageUploadDto.getUrl());
        } catch (IOException e) {
            res = false;
        }
        ResDTO<Boolean> resDTO = new ResDTO<>();
        resDTO.setData(res);
        return resDTO;
    }


    @Override
    public ImageUploadDto findByFileCode(String fileCode) {
        return imageUploadService.findByFileCode(fileCode);
    }

}
