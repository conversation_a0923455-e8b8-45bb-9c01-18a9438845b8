package com.sdsdiy.materialimpl.controller.official;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import com.beust.jcommander.internal.Maps;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.materialapi.api.official.OfficialMaterialFileApi;
import com.sdsdiy.materialapi.dto.official.OfficialMaterialBooleanDTO;
import com.sdsdiy.materialapi.dto.official.OfficialMaterialFileReqDTO;
import com.sdsdiy.materialapi.dto.official.OfficialMaterialUploadImgDTO;
import com.sdsdiy.materialdata.dto.officialmaterial.OfficialMaterialCreateDTO;
import com.sdsdiy.materialdata.dto.officialmaterial.OfficialMaterialFileDTO;
import com.sdsdiy.materialdata.dto.requirement.MaterialRequirementDTO;
import com.sdsdiy.materialdata.dto.requirement.MaterialRequirementSignDTO;
import com.sdsdiy.materialdata.enums.MaterialRequirementStatus;
import com.sdsdiy.materialimpl.entity.po.official.OfficialMaterialFile;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialFileService;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialManager;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialService;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementManager;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementService;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 官方素材文件表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@RestController
public class OfficialMaterialFileController implements OfficialMaterialFileApi {
    @Autowired
    private OfficialMaterialFileService fileService;
    @Resource
    private OfficialMaterialService officialMaterialService;
    @Autowired
    private OfficialMaterialManager officialMaterialManager;
    @Autowired
    private MaterialRequirementManager materialRequirementManager;
    @Autowired
    private MaterialRequirementService materialRequirementService;

    @GlobalTransactional(noRollbackFor = BusinessException.class)
    @Override
    public Long add(OfficialMaterialUploadImgDTO dto, Long uId) {
        OfficialMaterialCreateDTO createDTO = new OfficialMaterialCreateDTO();
        if (QueryParamHelper.checkQueryId(dto.getMaterialRequirementId())) {
            MaterialRequirementDTO requirementDTO = materialRequirementManager.getOneDto(dto.getMaterialRequirementId());
            if (requirementDTO == null
                    || !MaterialRequirementStatus.canContribute(requirementDTO.getStatus())
                    || requirementDTO.getDeadline().getTime() < DateTime.now().getTime()) {
                throw new BusinessException("投稿失败，该需求已截止投稿");
            }
            createDTO.setRequirementType(requirementDTO.getType())
                    .setMaterialRequirementId(dto.getMaterialRequirementId());
            // 设计师投稿的时候自动报名，即使图片上传失败
            materialRequirementService.sign(new MaterialRequirementSignDTO(dto.getMaterialRequirementId(), dto.getDesignMerchantId(), uId));
        }
        Long materialId = fileService.saveFile(dto, uId);
        createDTO.setMaterialId(materialId).setDesignAccountId(uId)
                .setDesignMerchantId(dto.getDesignMerchantId());
        officialMaterialService.createMaterial(createDTO);
        return materialId;
    }

    @Override
    public List<OfficialMaterialFileDTO> getByIds(OfficialMaterialFileReqDTO reqDTO) {
        OfficialMaterialBooleanDTO booleanDTO = OfficialMaterialBooleanDTO.build();
        booleanDTO.setNeedBaseThumb(reqDTO.getNeedBaseImg())
                .setFileCode2Url(reqDTO.getToUrl());
        return officialMaterialManager.getFileListByIds(reqDTO.getMaterialIds(), booleanDTO);
    }

    @Override
    public Map<String, OfficialMaterialFileDTO> getFileCodeKeyMap(List<String> fileCodes) {
        if (CollectionUtil.isEmpty(fileCodes)) {
            return Maps.newHashMap();
        }
        List<OfficialMaterialFile> list = fileService.lambdaQuery()
                .in(OfficialMaterialFile::getFileCode, fileCodes).list();
        if (CollectionUtil.isEmpty(list)) {
            return Maps.newHashMap();
        }
        return BeanUtil.copyToList(list, OfficialMaterialFileDTO.class)
                .stream()
                .collect(Collectors.toMap(OfficialMaterialFileDTO::getFileCode, Function.identity()));
    }

//    @Override
//    public OfficialMaterialFileDTO get(Long id) {
//        return fileService.getOneDto(id);
//    }

//    @Override
//    public PageListResultDTO<OfficialMaterialFileDTO> page(BasePageSelect pageSelect) {
//        return fileService.getPage(pageSelect);
//    }

}

