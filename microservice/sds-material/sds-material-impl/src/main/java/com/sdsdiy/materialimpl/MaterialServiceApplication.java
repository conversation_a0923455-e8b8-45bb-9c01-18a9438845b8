package com.sdsdiy.materialimpl;


import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.sdsdiy.core.annotation.EnableBigolloSync;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class})
@EnableFeignClients
@EnableAsync
@MapperScan("com.sdsdiy.materialimpl.mapper")
@EnableBigolloSync
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
public class MaterialServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(MaterialServiceApplication.class, args);
    }
}
