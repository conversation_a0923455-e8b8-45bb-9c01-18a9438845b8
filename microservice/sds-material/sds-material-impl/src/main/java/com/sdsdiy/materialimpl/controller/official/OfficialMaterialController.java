package com.sdsdiy.materialimpl.controller.official;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.EsSearchResHelper;
import com.sdsdiy.common.base.entity.dto.PageListResultDTO;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.common.base.helper.StringHelper;
import com.sdsdiy.core.base.util.StringUtils;
import com.sdsdiy.materialapi.api.official.OfficialMaterialApi;
import com.sdsdiy.materialapi.constant.enums.OfficialMaterialBatchSetType;
import com.sdsdiy.materialapi.constant.enums.OfficialMaterialShelfStatus;
import com.sdsdiy.materialapi.constant.official.OfficialMaterialInfoTypeConstant;
import com.sdsdiy.materialapi.dto.base.materials.MerchantOfficialMaterialRelationRecordsResqDto;
import com.sdsdiy.materialapi.dto.base.materials.OfficialMaterialLikeDto;
import com.sdsdiy.materialapi.dto.base.materials.OfficialMaterialLikeReqDto;
import com.sdsdiy.materialapi.dto.official.*;
import com.sdsdiy.materialapi.dto.official.bill.MaterialCountOfPassDto;
import com.sdsdiy.materialapi.dto.official.es.OfficialMaterialEsQueryDTO;
import com.sdsdiy.materialapi.dto.official.show.OfficialMaterialShowDTO;
import com.sdsdiy.materialapi.dto.official.show.RecommendMaterialReqDTO;
import com.sdsdiy.materialapi.dto.resp.MarterialClassificationPageRespDto;
import com.sdsdiy.materialapi.dto.resp.MarterialThemePageRespDto;
import com.sdsdiy.materialapi.dto.resp.OfficialMaterialLikeDelRespDto;
import com.sdsdiy.materialdata.dto.officialmaterial.*;
import com.sdsdiy.materialdata.enums.OfficialMaterialImgFolder;
import com.sdsdiy.materialimpl.service.*;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialAuditService;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialFileService;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialManager;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialService;
import com.sdsdiy.materialimpl.service.requirement.MaterialRequirementManager;
import com.sdsdiy.productdata.dto.CategoryTreeDTO;
import com.sdsdiy.productdata.dto.content.ContentRecommendReqDto;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 官方素材表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@RestController
@Log4j2
public class OfficialMaterialController implements OfficialMaterialApi {
    @Autowired
    private OfficialMaterialService officialMaterialService;
    @Autowired
    private OfficialMaterialManager officialMaterialManager;
    @Autowired
    private OfficialMaterialAuditService officialMaterialAuditService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private DesignMaterialThemeService designMaterialThemeService;
    @Autowired
    private OfficialMaterialFileService officialMaterialFileService;
    @Autowired
    private MaterialRequirementManager materialRequirementManager;
    @Resource
    private DesignMaterialClassificationService designMaterialClassificationService;
    @Resource
    private MerchantOfficialMaterialRelationRecordsService merchantOfficialMaterialRelationRecordsService;
    @Autowired
    private MerchantStoreMaterialService merchantStoreMaterialService;

    @Override
    public void updateMaterial(OfficialMaterialReqDTO dto, Long uId) {
        if (dto.getId() == null) {
            throw new BusinessException("更新id不能为null");
        }
        officialMaterialService.updateDto(dto, uId);
        officialMaterialManager.onShelfUpdateEs(Collections.singletonList(dto.getId()));
    }

    @Override
    @GlobalTransactional
    public void batchSave(OfficialMaterialBatchSaveDTO dto) {
        boolean audit = "SAVE_AUDIT".equalsIgnoreCase(dto.getOperate());
        dto.getMaterialList().forEach(m -> {
            if (m.getMaterialCategoryId() == null
                    || m.getDesignStyleId() == null
                    || CollectionUtils.isEmpty(m.getProcessType())
                    || StrUtil.isBlank(m.getDesignArea())
                    || CollectionUtils.isEmpty(CollectionUtil.removeNull(m.getProductCategory()))) {
                throw new BusinessException("必填信息不完整，请补全信息");
            }
            m.setMaterialRequirementId(dto.getMaterialRequirementId());
        });
        officialMaterialService.updateBatchDto(dto.getMaterialList(), dto.getUpdateUid(), audit);
        if (audit) {
            // 自动提交审核
            OfficialMaterialAuditBatchDTO auditBatchDTO = new OfficialMaterialAuditBatchDTO();
            auditBatchDTO.setMaterialIds(dto.getMaterialList()
                    .stream().map(OfficialMaterialReqDTO::getId).collect(Collectors.toList()));
            officialMaterialAuditService.addBatch(auditBatchDTO);
        }
    }

    @Override
    public void onShelf(List<Long> officialMaterialIds, Long uId) {
        officialMaterialService.onShelf(officialMaterialIds, uId, false);
        officialMaterialManager.onShelfUpdateEs(officialMaterialIds);
    }

    @Override
    @GlobalTransactional
    public void offShelf(List<Long> officialMaterialIds, Long uId) {
        officialMaterialService.offShelf(officialMaterialIds, uId);
        merchantStoreMaterialService.deleteByOfficialMaterialIds(uId, officialMaterialIds);
        officialMaterialManager.offShelfDelEs(officialMaterialIds);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void batchSet(OfficialMaterialBatchSetDTO dto) {
        if (dto.getValue() == null) {
            throw new BusinessException("批量设置值不能为空");
        }
        officialMaterialService.batchSetValue(dto);
        if (dto.getBatchSetType() == OfficialMaterialBatchSetType.OUT_SITE_STATUS
                && !BasePoConstant.YES.equals(Integer.parseInt(dto.getValue().toString()))) {
            merchantStoreMaterialService.deleteByOfficialMaterialIds(dto.getUpdateUid(), dto.getMaterialIds());
        }
        officialMaterialManager.onShelfUpdateEs(dto.getMaterialIds());
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void batchDel(OfficialMaterialBatchDTO dto) {
        List<Long> delIds = officialMaterialService.delete(dto);
        officialMaterialFileService.delByIds(delIds);
        officialMaterialAuditService.delByOfficialMaterialIds(delIds);
    }

    @Override
    public Integer batchCollection(OfficialMaterialCollectionDTO dto) {
        // 是否定制需求
        boolean customRequirement = QueryParamHelper.checkQueryId(dto.getMaterialRequirementId());
        if (customRequirement) {
            List<Long> processing = materialRequirementManager.checkProcessing(Collections.singletonList(dto.getMaterialRequirementId()));
            if (processing.size() == 0) {
                Assert.wrong("需求已结束无法操作");
            }
        }
        // 是否收藏
        boolean collection = "1".equals(dto.getOperate());
        boolean single = dto.getMaterialIds().size() == 1;
        List<Long> successIds;
        if (collection) {
            if (customRequirement) {
                successIds = officialMaterialService.checkCustomRequirementOnShelfPass(dto.getMaterialIds(), dto.getMerchantId());
            } else {
                successIds = officialMaterialService.checkOpenMarketOnShelfPass(dto.getMaterialIds());
            }
        } else {
            successIds = dto.getMaterialIds();
        }
        if (CollectionUtils.isEmpty(successIds)) {
            Assert.wrong(collection ? "素材已下架无法收藏" : "取消失败");
        }
        OfficialMaterialLikeDto likeDto;
        if (collection) {
            OfficialMaterialLikeReqDto likeResqDto = new OfficialMaterialLikeReqDto();
            likeResqDto.setMerchantId(dto.getMerchantId());
            likeResqDto.setUserId(dto.getUpdateUid());
            likeResqDto.setOfficialMaterialInfoType(OfficialMaterialInfoTypeConstant.BILL_PAYMENT);
            likeResqDto.setOfficialMaterialIds(successIds);
            likeDto = materialService.officialMaterialLike(likeResqDto);
        } else {
            OfficialMaterialLikeDelRespDto delRespDto = new OfficialMaterialLikeDelRespDto();
            delRespDto.setOfficialMaterialIds(successIds);
            delRespDto.setMerchantId(dto.getMerchantId());
            delRespDto.setUserId(dto.getUpdateUid());
            // 老板号不鉴权
            delRespDto.setIsAuthorityValidation(!dto.getBoss());
            likeDto = materialService.officialMaterialLikeDel(delRespDto);
        }
        int success = likeDto.getSuccessNum();
        if (success > 0) {
            if (customRequirement) {
                // 进行中的才能变更中标
                officialMaterialManager.winBid(likeDto.getSuccessOfficialMaterialIds(),
                        dto.getMaterialRequirementId(), collection);
            }
            officialMaterialManager.putCollectionOfficialMaterialIdCache(likeDto.getSuccessOfficialMaterialIds());
            return success;
        }
        if (single) {
            if (likeDto.getHadUseAuth()) {
                Assert.wrong("您已购买该素材使用授权，" + (collection ? "无需重复收藏!" : "无法取消收藏!"));
            }
            Assert.wrong(collection ? "无法重复收藏" : "已取消收藏");
        }
        return 0;
    }

    @Override
    public List<OfficialMaterialRespDTO> getListWithBaseNotUrl(List<Long> officialMaterialIds) {
        return officialMaterialManager.getMaterialInfoByIds(officialMaterialIds,
                OfficialMaterialBooleanDTO.build().needBaseThumb().not2Url());
    }

    @Override
    public List<OfficialMaterialRespDTO> getListNotBaseWithUrl(List<Long> officialMaterialIds) {
        return officialMaterialManager.getMaterialInfoByIds(officialMaterialIds, OfficialMaterialBooleanDTO.build());
    }

    @Override
    public OfficialMaterialDTO getOneWithBaseWithUrl(Long id) {
        return officialMaterialManager.getById(id, OfficialMaterialBooleanDTO.build().needBaseThumb());
    }

    @Override
    public OfficialMaterialDTO getOne(Long id, OfficialMaterialBooleanDTO officialMaterialBooleanDTO) {
        return officialMaterialManager.getById(id,officialMaterialBooleanDTO);
    }

    @Override
    public OfficialMaterialFileUrlDTO getFileUrl(Long id, OfficialMaterialBooleanDTO officialMaterialBooleanDTO) {
        OfficialMaterialFileDTO fileDTO = officialMaterialManager.getById(id,officialMaterialBooleanDTO).getFileInfo();
        return BeanUtil.toBean(fileDTO, OfficialMaterialFileUrlDTO.class);
    }


    @Override
    public OfficialMaterialRespDTO getOneNotBaseWithUrl(Long id, Integer isDesignPlatform) {
        OfficialMaterialBooleanDTO booleanDTO = OfficialMaterialBooleanDTO.build();
        if (isDesignPlatform != null && isDesignPlatform == 1) {
            booleanDTO.designPlatform();
        }
        return officialMaterialManager.getMaterialInfoById(id, booleanDTO);
    }

    @Override
    public PageListResultDTO<OfficialMaterialRespDTO> getPageForDesigner(OfficialMaterialDesignListReqDTO dto) {
        return officialMaterialManager.getPageForDesigner(dto);
    }

    @Override
    public PageListResultDTO<OfficialMaterialRespDTO> getPageForAdmin(OfficialMaterialAdminListReqDTO dto) {
        if (dto.getDesignMerchantId() == null) {
            dto.setDesignMerchantId(dto.getDesignStudioId());
        }
        return officialMaterialManager.getPageForAdmin(dto);
    }

    @Override
    public PageListResultDTO<OfficialMaterialSimpleDTO> getPageForMarketHomeNewest(OfficialMaterialMerchantReqDTO select) {
        PageListResultDTO<OfficialMaterialSimpleDTO> page = officialMaterialManager.getPageForMarketHomeNewest(select);
        if (CollUtil.isEmpty(page.getList())) {
            return page;
        }
        if (QueryParamHelper.checkQueryId(select.getMerchantId())) {
            List<Long> materialIds = page.getList().stream().map(OfficialMaterialSimpleDTO::getId).collect(Collectors.toList());
            Set<Long> likeSet = merchantOfficialMaterialRelationRecordsService.checkLikeSet(new MerchantOfficialMaterialRelationRecordsResqDto(materialIds, select.getMerchantId()));
            page.getList().forEach(m -> m.setCollection(BasePoConstant.yesOrNo(likeSet.contains(m.getId()))));
        }

        return page;
    }

    @Override
    public EsSearchResHelper<OfficialMaterialRespDTO> getPageForMerchantMaterialMarket(OfficialMaterialEsQueryDTO dto) {
        return officialMaterialManager.getPageForMerchantMaterialMarket(dto);
    }

    @Override
    public Map<Long, List<OfficialMaterialRecommendDTO>> calendar() {
        return officialMaterialManager.getMapByProductCategory();
    }

    @Override
    public List<OfficialMaterialSimpleDTO> getRecommendMaterial(Integer num) {
        if (num == null) {
            return new ArrayList<>();
        }
        return officialMaterialManager.getRecommendMaterial(num);
    }

    @Override
    public Map<Long, List<OfficialMaterialCalendarOfficialMaterialDto>> officialMaterialCalendar() {
        return officialMaterialManager.officialMaterialCalendar();
    }

    @Override
    public MarketHomeTopicDTO getOfficialMaterialByTopic(Long topicId, Long merchantId) {
        MarketHomeTopicDTO topicDTO = new MarketHomeTopicDTO();
        if (topicId == 0) {
            List<MarterialThemePageRespDto> topicList = designMaterialThemeService.getList(1);
            topicDTO.setTopicList(topicList);
            if (!CollectionUtils.isEmpty(topicList)) {
                topicId = topicList.get(0).getId();
            }
        }
        if (topicId > 0) {
            List<OfficialMaterialSimpleDTO> materialList = officialMaterialManager.getOfficialMaterialByTopic(topicId);
            topicDTO.setMaterialList(materialList);
            if (QueryParamHelper.checkQueryId(merchantId)) {
                List<Long> materialIds = materialList.stream().map(OfficialMaterialSimpleDTO::getId).collect(Collectors.toList());
                Set<Long> likeSet = merchantOfficialMaterialRelationRecordsService.checkLikeSet(new MerchantOfficialMaterialRelationRecordsResqDto(materialIds, merchantId));
                materialList.forEach(m -> m.setCollection(BasePoConstant.yesOrNo(likeSet.contains(m.getId()))));
            }
        } else {
            topicDTO.setMaterialList(new ArrayList<>());
        }
        return topicDTO;
    }

    @Override
    public Integer materialCountOfOnShelf() {
        return officialMaterialService.materialCountOfOnShelf();
    }

    @Override
    public Integer materialCountOfPass(MaterialCountOfPassDto select) {
        return officialMaterialService.materialCountOfPass(select);
    }

    @Override
    public List<CategoryTreeDTO> findProductCategoryByMaterialCategoryId(Long materialCategoryId) {
        return officialMaterialManager.findProductCategoryByMaterialCategoryId(materialCategoryId);
    }

    @Override
    public List<Long> listMaterialMarketNewestIds(MaterialMarketNewestReqDTO reqDTO) {
        if (reqDTO.getNum() == null || reqDTO.getNum() < 1) {
            return new ArrayList<>();
        }
        return officialMaterialManager.listMaterialMarketNewestIds(reqDTO.getNum(), reqDTO.getExcludeIds());
    }

    @Override
    public OfficialMaterialShowDTO getMaterialMarketShowDTO(Long officialMaterialId, Long merchantId) {
        return officialMaterialManager.getMaterialMarketShowDTO(officialMaterialId, merchantId);
    }

    @Override
    public PageListResultDTO<OfficialMaterialRespDTO> listShowRecommendMaterial(RecommendMaterialReqDTO reqDTO) {
        if (!QueryParamHelper.checkQueryId(reqDTO.getTopicId())) {
            return new PageListResultDTO<>(0, new ArrayList<>());
        }
        return officialMaterialManager.listRecommendMaterialByDesignerAndTopic(reqDTO);
    }

    @Override
    public List<Long> getIdsListForAdminDataAnalysis(OfficialMaterialDataAnalysisReqDTO officialMaterialDataAnalysisReqDTO) {
        return officialMaterialManager.getIdsListForAdminDataAnalysis(officialMaterialDataAnalysisReqDTO);
    }

    @Override
    public OfficialMaterialDTO getOneOnlyInfo(Long id) {
        return officialMaterialService.getOneDto(id);
    }

    @Override
    public List<OfficialMaterialStaticDTO> designMaterialStatic(List<Long> designMerchantIds) {
        return officialMaterialService.getDesignMaterialNumByParams(designMerchantIds);
    }

    @Override
    public List<Long> checkOpenMarketOnShelfPass(OfficialMaterialBatchDTO dto) {
        return officialMaterialService.checkOpenMarketOnShelfPass(dto.getMaterialIds());
    }

    @Override
    public List<Long> checkShelfStatus(OfficialMaterialBatchDTO dto) {
        OfficialMaterialShelfStatus shelfStatus = OfficialMaterialShelfStatus.valueOf(dto.getOperate());
        Assert.validateNull(shelfStatus, "错误状态");
        return officialMaterialService.checkShelfStatus(dto.getMaterialIds(), shelfStatus);
    }

    @Override
    public List<OfficialMaterialBaseSimpleDto> getLatestMaterialByDesignMerchantId(Long designMerchantId, Integer num) {
        return officialMaterialManager.getLatestMaterialByDesignMerchantId(designMerchantId, num);
    }

    @Override
    public MaterialHomePageTopicDto getMaterialHomePageTopic(String ids) {
        // 获取主题信息
        MaterialHomePageTopicDto homePageTopicDto = new MaterialHomePageTopicDto();
        List<MarterialThemePageRespDto> topicList = designMaterialThemeService.getList(1);
        List<MarterialThemePageRespDto> subList ;
        if(StringUtils.isNotBlank(ids)){
            subList = Lists.newArrayList();
            List<Long> idList = StringUtils.stringToLongList(ids);
            for (MarterialThemePageRespDto marterialThemePageRespDto : topicList) {
                if(idList.contains(marterialThemePageRespDto.getId())){
                    subList.add(marterialThemePageRespDto);
                }
            }
        }else {
            subList = topicList.subList(0, 4);
        }
        // 取4条推荐的数据
        homePageTopicDto.setTopicList(subList);

        // 获取素材信息
        List<OfficialMaterialBaseSimpleDto> materialList = new ArrayList<>();
        for (MarterialThemePageRespDto marterialThemePageRespDto : subList) {
            // 每个topic获取4条
            List<OfficialMaterialBaseSimpleDto> newMaterial = officialMaterialManager.getNewMaterial(marterialThemePageRespDto.getId(), 10);
            if (CollUtil.isNotEmpty(newMaterial)) {
                materialList.addAll(newMaterial);
            }
        }
        homePageTopicDto.setMaterialList(materialList);
        return homePageTopicDto;
    }

    @Override
    public List<MarterialClassificationPageRespDto> getMaterialCategory() {
        List<Long> materialCatogoryIds = officialMaterialService.getMaterialCategoryId();
        return designMaterialClassificationService.getOrderList(materialCatogoryIds);
    }

    @Override
    public List<OfficialMaterialBaseSimpleDto> getLatestMaterialByDesignMerchantId(String designMerchantIds, Integer num) {
        List<Long> designMerchantIdList = StringHelper.stringToLongList(designMerchantIds);
        return officialMaterialManager.getLatestMaterialByDesignMerchantId(designMerchantIdList, num);
    }

    @Override
    public List<DesignNumDto> getDesignNum(String designMerchants) {
        List<Long> designMerchantIdList = StringHelper.stringToLongList(designMerchants);
        return officialMaterialManager.getDesignNum(designMerchantIdList);
    }

    @Override
    public List<OfficialMaterialBaseSimpleDto> getOfficialMaterialByIds(String ids) {
        List<Long> materialIds = StringHelper.stringToLongList(ids);
        return officialMaterialManager.getByIds(materialIds);
    }

    @Override
    public EsSearchResHelper<OfficialMaterialRespDTO> getPageForMaterialHomePage(OfficialMaterialEsQueryDTO dto) {
        EsSearchResHelper<OfficialMaterialRespDTO> materialMarket = officialMaterialManager.getPageForMerchantMaterialMarket(dto);
        materialMarket.getItems().forEach(m -> {
            String fileCode = OfficialMaterialImgFolder.WATERMARK_IMG.getFileCode(m.getFileInfo().getWatermarkImg());
            OfficialMaterialImgDTO materialImgDTO = OfficialMaterialImgFolder.getByBase(fileCode, true);
            m.getFileInfo().setBaseThumb1000(materialImgDTO.getBaseThumb1000());
        });
        return materialMarket;
    }

    @Override
    public void updateAllOldDataEs() {
        officialMaterialManager.updateAllOldDataEs();
    }

    @Override
    public PageListResultDTO<OfficialMaterialRespDTO> pageForContent(OfficialMaterialContentRecommendReqDTO dto) {
        return officialMaterialManager.pageForContent(dto);
    }

    @Override
    public EsSearchResHelper<OfficialMaterialRespDTO> getPageForContentOfficialMaterial(ContentRecommendReqDto dto) {
        return officialMaterialManager.getPageForContentOfficialMaterial(dto);
    }

    @Override
    public List<OfficialMaterialSimpleDTO> getByIds(List<Long> ids) {
        return officialMaterialManager.getOfficialMaterialByIds(ids);
    }
}

