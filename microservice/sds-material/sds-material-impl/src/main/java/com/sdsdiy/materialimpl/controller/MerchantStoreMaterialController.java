package com.sdsdiy.materialimpl.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.materialapi.api.MerchantStoreMaterialApi;
import com.sdsdiy.materialapi.constant.official.OfficialMaterialInfoTypeConstant;
import com.sdsdiy.materialapi.dto.base.MerchantStoreMaterialRespDto;
import com.sdsdiy.materialapi.dto.base.materials.OfficialMaterialLikeDto;
import com.sdsdiy.materialapi.dto.base.materials.OfficialMaterialLikeReqDto;
import com.sdsdiy.materialapi.dto.param.AddMerchantStoreMaterialParam;
import com.sdsdiy.materialapi.dto.resp.AddStoreMaterialResp;
import com.sdsdiy.materialimpl.entity.po.MerchantStoreMaterial;
import com.sdsdiy.materialimpl.service.MaterialService;
import com.sdsdiy.materialimpl.service.MerchantStoreMaterialService;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialService;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商家店铺素材表(MerchantStoreMaterial)表控制层
 *
 * <AUTHOR>
 * @since 2020-11-24 09:51:24
 */
@RestController
public class MerchantStoreMaterialController implements MerchantStoreMaterialApi {
    /**
     * 服务对象
     */
    @Resource
    private MerchantStoreMaterialService merchantStoreMaterialService;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private OfficialMaterialService officialMaterialService;

    @Override
    public void copy(Long merchantId, Long userId, Long merchantStoreId, BaseListDto<Long> list) {
        merchantStoreMaterialService.copy(merchantId, userId, merchantStoreId, list);
    }

    @Override
    public PageListResultRespDto<MerchantStoreMaterialRespDto> page(Long merchantId, Long userId, Long merchantStoreId, Long merchantStoreMaterialCategoryId, QueryParamHelper query) {
        Page<MerchantStoreMaterial> page = merchantStoreMaterialService.page(merchantId, merchantStoreId, merchantStoreMaterialCategoryId, query);
        PageListResultRespDto<MerchantStoreMaterialRespDto> resultRespDto = new PageListResultRespDto<>();
        List<MerchantStoreMaterialRespDto> merchantStoreMaterialRespList = RelationsBinder.convertAndBind(page.getRecords(), MerchantStoreMaterialRespDto.class);
        merchantStoreMaterialService.formatUser(merchantStoreMaterialRespList);
        //fix商户店铺素材有的素材，但是素材表里没有的素材 过滤掉 不返回给前端
        merchantStoreMaterialRespList = merchantStoreMaterialRespList.stream().filter(a -> Objects.nonNull(a.getMaterialRespDto())).collect(Collectors.toList());
        resultRespDto.setItems(merchantStoreMaterialRespList);
        resultRespDto.setTotalCount((int) page.getTotal());
        resultRespDto.setSize((long) query.getSize());
        resultRespDto.setPage((long) query.getPage());
        return resultRespDto;
    }

    @Override
    public AddStoreMaterialResp add(Long merchantId, Long userId, Long merchantStoreId, AddMerchantStoreMaterialParam addParam) {
        return merchantStoreMaterialService.addBatch(merchantId, userId, merchantStoreId, addParam);
    }

    @Override
    @GlobalTransactional
    public AddStoreMaterialResp addOfficial(Long merchantId, Long userId, Long merchantStoreId, AddMerchantStoreMaterialParam addParam) {
        // 校验
        List<Long> successIds = officialMaterialService.checkOpenMarketOnShelfPassOutSite(addParam.getList());
        OfficialMaterialLikeReqDto likeReqDto = new OfficialMaterialLikeReqDto();
        likeReqDto.setMerchantId(merchantId).setUserId(userId).setOfficialMaterialIds(successIds)
                .setOfficialMaterialInfoType(OfficialMaterialInfoTypeConstant.BILL_PAYMENT);
        // 先收藏
        OfficialMaterialLikeDto likeDto = materialService.officialMaterialLike(likeReqDto);
        // 用素材表的id添加
        addParam.setList(new ArrayList<>(likeDto.getMaterialIds()));
        AddStoreMaterialResp resp = merchantStoreMaterialService.addBatch(merchantId, userId, merchantStoreId, addParam);
        resp.setMaterialIds(new ArrayList<>(likeDto.getMaterialIds()));
        return resp;
    }

    @Override
    public void delete(Long merchantId, Long userId, Long merchantStoreId, Long id) {
        merchantStoreMaterialService.deleteById(merchantId, userId, merchantStoreId, id);
    }

    @Override
    public void deleteByMaterialIds(BaseListDto<Long> baseListDto, Long merchantId, Long uid) {
        merchantStoreMaterialService.deleteByMaterialIds(Collections.singletonList(merchantId), uid, baseListDto.getList());
    }

    @Override
    public List<Long> getSyncIdList(Long merchantId, Long userId) {
        return merchantStoreMaterialService.getSyncIdList();
    }

    @Override
    public void updateSync(Long merchantId, Long userId, BaseListDto baseListDto) {
        merchantStoreMaterialService.updateSync(baseListDto.getList());
    }

    @Override
    public List<Long> getMaterialIds(BaseListDto<Long> baseListDto) {
        return merchantStoreMaterialService.getMaterialIdsByMaterialIds(baseListDto.getList());
    }
}