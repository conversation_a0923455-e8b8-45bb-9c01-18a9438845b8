package com.sdsdiy.materialimpl.controller;

import com.sdsdiy.materialapi.api.ai.AiSaveImageRecordApi;
import com.sdsdiy.materialimpl.service.ai.AiSaveImageRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * ai 保存图片记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class AiSaveImageRecordController implements AiSaveImageRecordApi {

    private final AiSaveImageRecordService aiSaveImageRecordService;

}