package com.sdsdiy.materialimpl.controller;


import com.sdsdiy.common.base.helper.IdSearchHelper;
import com.sdsdiy.materialapi.api.MaterialCartPreparePaymentApi;
import com.sdsdiy.materialdata.dto.MaterialCartPaymentPrepareRespDTO;
import com.sdsdiy.materialimpl.service.materialcart.MaterialCartPaymentPrepareService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * (Material)表控制层
 *
 * <AUTHOR>
 * @since 2020-11-13 16:46:06
 */
@RestController
public class MaterialCartPreparePaymentController implements MaterialCartPreparePaymentApi {

	@Resource
	MaterialCartPaymentPrepareService materialCartPaymentPrepareService;

	@Override
	public MaterialCartPaymentPrepareRespDTO get(IdSearchHelper idSearchHelper) {
		return materialCartPaymentPrepareService.get(idSearchHelper.getId(),idSearchHelper.getFields());
	}
}