package com.sdsdiy.materialimpl.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.materialapi.api.MerchantOfficialMaterialRelationRecordsApi;
import com.sdsdiy.materialapi.dto.base.MerchantOfficialMaterialRelationRecordsDto;
import com.sdsdiy.materialapi.dto.base.materials.MerchantOfficialMaterialRelationRecordsResqDto;
import com.sdsdiy.materialapi.dto.official.OfficialMaterialDTO;
import com.sdsdiy.materialimpl.service.MerchantOfficialMaterialRelationRecordsService;
import com.sdsdiy.materialimpl.service.official.OfficialMaterialService;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商户素材官方素材关系记录表(MerchantOfficialMaterialRelationRecords)表控制层
 *
 * <AUTHOR>
 * @since 2021-06-22 11:49:07
 */
@RestController
@Log4j2
public class MerchantOfficialMaterialRelationRecordsController implements MerchantOfficialMaterialRelationRecordsApi {
    /**
     * 服务对象
     */
    @Resource
    private MerchantOfficialMaterialRelationRecordsService merchantOfficialMaterialRelationRecordsService;

    @Resource
    OfficialMaterialService officialMaterialService;

    @Override
    public List<MerchantOfficialMaterialRelationRecordsDto> findByMaterialIds(MerchantOfficialMaterialRelationRecordsResqDto bean) {
        return RelationsBinder.convertAndBind(merchantOfficialMaterialRelationRecordsService.findByMaterialIds(bean.getMaterialIds())
                , MerchantOfficialMaterialRelationRecordsDto.class);
    }

    @Override
    public List<MerchantOfficialMaterialRelationRecordsDto> findByOfficialMaterialIds(MerchantOfficialMaterialRelationRecordsResqDto bean) {
        if (CollectionUtils.isEmpty(bean.getMaterialIds())) {
            return new ArrayList<>();
        }
        List<MerchantOfficialMaterialRelationRecordsDto> merchantOfficialMaterialRelationRecordsDtos = RelationsBinder.convertAndBind(merchantOfficialMaterialRelationRecordsService.findByOfficialMaterialIds(bean.getMaterialIds(), bean.getMerchantId()), MerchantOfficialMaterialRelationRecordsDto.class);
        List<Long> officeialMaterialIds = merchantOfficialMaterialRelationRecordsDtos.stream().map(MerchantOfficialMaterialRelationRecordsDto::getOfficialMaterialId).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(officeialMaterialIds)){
            return merchantOfficialMaterialRelationRecordsDtos;
        }
        List<OfficialMaterialDTO> officialMaterialDTOS =officialMaterialService.getByIds(officeialMaterialIds);
        if(CollectionUtil.isEmpty(officialMaterialDTOS)){
            return merchantOfficialMaterialRelationRecordsDtos;
        }
        Map<Long,OfficialMaterialDTO> officialMaterialDTOMap = officialMaterialDTOS.stream().collect(Collectors.toMap(OfficialMaterialDTO::getId, Function.identity()));
        for (MerchantOfficialMaterialRelationRecordsDto merchantOfficialMaterialRelationRecordsDto : merchantOfficialMaterialRelationRecordsDtos) {
            merchantOfficialMaterialRelationRecordsDto.setOfficialMaterialDTO(officialMaterialDTOMap.get(merchantOfficialMaterialRelationRecordsDto.getOfficialMaterialId()));
        }
        return merchantOfficialMaterialRelationRecordsDtos;

    }

    @Override
    public void delFindByMaterialIds(MerchantOfficialMaterialRelationRecordsResqDto merchantOfficialMaterialRelationRecordsResqDto) {
        merchantOfficialMaterialRelationRecordsService.delFindByMaterialIds(merchantOfficialMaterialRelationRecordsResqDto);
    }

    @Override
    public Set<Long> checkLikeSet(MerchantOfficialMaterialRelationRecordsResqDto bean) {
        return merchantOfficialMaterialRelationRecordsService.checkLikeSet(bean);
    }

    @Override
    public List<Long> checkOfficialMaterialIdsWithDeleted(MerchantOfficialMaterialRelationRecordsResqDto bean) {
        return merchantOfficialMaterialRelationRecordsService.checkOfficialMaterialIdsWithDeleted(bean);
    }
}
