package com.sdsdiy.materialimpl.controller;

import com.sdsdiy.materialapi.api.PingMaterialApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public class PingMaterialController implements PingMaterialApi {

    @Override
    public Map<String, Object> serverCheck() {
        Map<String, Object> result = new HashMap<>();
        result.put("msg", "mc-sds-material");
        result.put("date", "20201124");
        result.put("version", "0.0.33");
        return result;
    }
}

