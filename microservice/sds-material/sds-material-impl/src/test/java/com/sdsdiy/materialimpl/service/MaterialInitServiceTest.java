package com.sdsdiy.materialimpl.service;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import com.sdsdiy.materialimpl.task.MaterialTask;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
class MaterialInitServiceTest {


	@Resource
	private MaterialTask materialTask;
	@Resource
	private MaterialService materialService;

	@Test
	void updateEsTask() {
		materialService.initialization(30007005L,2659L);
	}


	@Test
	void materialTask() {
		long beginTime = System.currentTimeMillis();
		MaterialTask.MaterialReplaceOssUrlBo materialReplaceOssUrlBo = new MaterialTask.MaterialReplaceOssUrlBo();
		materialReplaceOssUrlBo.setTime(5);
		materialReplaceOssUrlBo.setMaxId(354811550L);
		materialReplaceOssUrlBo.setLimit(10000);
		materialTask.updateReplaceToAliYunOssUrl(JSON.toJSONString(materialReplaceOssUrlBo));
		System.out.println(System.currentTimeMillis() - beginTime);
	}

}