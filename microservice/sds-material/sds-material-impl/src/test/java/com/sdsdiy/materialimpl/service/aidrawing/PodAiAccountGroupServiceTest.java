package com.sdsdiy.materialimpl.service.aidrawing;

import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.common.base.entity.dto.PageResult;
import com.sdsdiy.materialapi.vo.aidrawing.pod.PodAiAccountGroupDetailVO;
import com.sdsdiy.materialapi.vo.aidrawing.pod.PodAiAccountGroupPageVO;
import com.sdsdiy.materialdata.req.aiaccount.EditAiGroupNameReq;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class PodAiAccountGroupServiceTest {

    @Resource
    private PodAiAccountGroupService podAiAccountGroupService;
    @Test
    public void testPage() {
        PageResult<PodAiAccountGroupPageVO> page =
                podAiAccountGroupService.page("", 1L,4184L, 1, 10);
        System.out.println(JSONObject.toJSONString(page));
    }

    @Test
    public void testAddGroup() {
        EditAiGroupNameReq req = new EditAiGroupNameReq();
        req.setName("ljptest");
        podAiAccountGroupService.addGroup(1L, req);
    }

    @Test
    public void testEditGroup() {
        EditAiGroupNameReq req = new EditAiGroupNameReq();
        req.setName("ljptest");
        podAiAccountGroupService.editGroup(1L, 1L, req);
    }


    @Test
    public void testDeleteGroup() {
        podAiAccountGroupService.deleteGroup(1L);
    }

    @Test
    public void testGroupDetail() {
        PodAiAccountGroupDetailVO vo = podAiAccountGroupService.groupDetail(1L);
        System.out.println(JSONObject.toJSONString(vo));
    }

    @Test
    public void testEditGroupDetail() {
        podAiAccountGroupService.editGroupDetail(1L, 1L, 0L);
    }
}