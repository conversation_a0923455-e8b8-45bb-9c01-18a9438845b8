package com.sdsdiy.materialimpl.service.aidrawing;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.materialapi.constant.enums.aidrawing.AiDrawingStatusEnum;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import com.sdsdiy.materialimpl.bo.YouChuanGetTargetAccountBO;
import com.sdsdiy.materialimpl.entity.po.aidrawing.AiYouChuanRel;
import com.sdsdiy.materialimpl.entity.po.aidrawing.YouChuanGroup;
import com.sdsdiy.materialimpl.manager.aidrawing.AiYouChuanRelManager;
import com.sdsdiy.materialimpl.manager.aidrawing.YouChuanGroupManager;
import com.sdsdiy.userapi.dto.aidrawing.AiDrawingRemainTimeRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class MerchantAiAccountServiceTest {
    @Resource
    private MerchantAiAccountService merchantAiAccountService;
    @Resource
    private AiYouChuanRelManager aiYouChuanRelManager;
    @Resource
    private YouChuanGroupManager youChuanGroupManager;
    @Test
    public void testGetTargetAccountIdByMerchantId() {
        YouChuanGroup targetGroup = youChuanGroupManager.getTargetGroup(1L, 11090L);
        YouChuanGetTargetAccountBO targetAccountIdByMerchantId = merchantAiAccountService.getTargetAccountIdByMerchantId(1L, new BigDecimal(1), targetGroup);
        targetAccountIdByMerchantId.setIsVip(NumberUtils.greaterZero(targetGroup.getMerchantId()));
        System.out.println(JSONObject.toJSONString(targetAccountIdByMerchantId));
    }

    @Test
    public void updateAiYouChuanRel() {
        LambdaUpdateWrapper<AiYouChuanRel> youChuanRelWrapper = new LambdaUpdateWrapper<>();
        youChuanRelWrapper.eq(AiYouChuanRel::getId, 1);
        youChuanRelWrapper.set(AiYouChuanRel::getTaskStatus, AiDrawingStatusEnum.QUEUING.getStatus());
        LocalDateTime now = LocalDateTime.now();
        youChuanRelWrapper.set(AiYouChuanRel::getTaskTime, now);
        youChuanRelWrapper.set(AiYouChuanRel::getUpdateTime, now);
        youChuanRelWrapper.set(AiYouChuanRel::getJobId, "");
        aiYouChuanRelManager.update(youChuanRelWrapper);
    }

    @Test
    public void findOccupyUsageTasksByAccountIds() {

        List<AiYouChuanRel> occupyUsageTasksByAccountIds = aiYouChuanRelManager.findOccupyUsageTasksByAccountIds(Collections.singletonList(10086L));
        System.out.println(JSONObject.toJSONString(occupyUsageTasksByAccountIds));
    }

    @Test
    public void testGetTaskAndRank() {
        List<Long> accountIds = new ArrayList<>();
        accountIds.add(4L);
        List<AiYouChuanRel> relList = new ArrayList<>();
        AiYouChuanRel rel = new AiYouChuanRel();
        rel.setId(1299L);
        rel.setYouChuanAccountId(4L);
        relList.add(rel);
        Map<Long, Integer> taskAndRank = merchantAiAccountService.getTaskAndRank(accountIds, relList);
        System.out.println(JSONObject.toJSONString(taskAndRank));
    }
}