package com.sdsdiy.materialimpl.service.ai.swap;

import com.google.common.collect.Lists;
import com.sdsdiy.materialdata.dto.ai.swap.AiSwapCreateParam;
import com.sdsdiy.materialdata.dto.ai.swap.AiSwapCreateTaskParam;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;


/**
 * @description:
 * @Author: zmy
 * @Date: 2024/7/26 17:47
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class AiSwapBatchServiceTest {
    @Resource
    AiSwapBatchService aiSwapBatchService;

    @Test
    public void getByDesignProductParentIds() {
        List<Long> designProductParentIds = aiSwapBatchService.getByDesignProductParentIds(Lists.newArrayList(670740447789555712L));
        System.out.println(designProductParentIds);
    }

    @Test
    public void testCreate() {
        AiSwapCreateParam param = new AiSwapCreateParam();
        param.setMerchantId(2577L);
        param.setType(1);
        param.setModelMarketId("1782673239593299968");
        param.setDesignProductId(670837412682506240L);
        param.setDesignProductParentId(670837461919440896L);
        param.setUserId(30006711L);
        List<AiSwapCreateTaskParam> taskList = new ArrayList<>();
        AiSwapCreateTaskParam taskParam = new AiSwapCreateTaskParam();
        taskParam.setDesignProductId(670837412682506240L);
        taskParam.setDesignProductImageId(670837408689528832L);
        taskParam.setImageIndex(3);
        taskParam.setImageUrl("http://static-photo-center-prov.oss-cn-hangzhou.aliyuncs.com/out/2577/202407/ceef6206bb8ca083901adda15b40abd5.jpg");
        taskParam.setPrototypeId(657352633712836609L);
        taskList.add(taskParam);
        param.setTaskList(taskList);
        aiSwapBatchService.create(param);
    }
}