package com.sdsdiy.materialimpl.service;

import com.sdsdiy.materialapi.dto.resp.ImageSegmentHeadDto;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.net.MalformedURLException;

/**
 * @description:
 * @Author: zmy
 * @Date: 2024/2/20 12:17
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class AliYunImageServiceTest {
    @Resource
    AliYunImageService aliyunImageService;

    @Test
    public void getImageSegmentHeadDto() throws MalformedURLException {
        String url="https://static-photo-center-prov.oss-cn-hangzhou.aliyuncs.com/imagesThumbs/91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/1d19b36acdcf11eb05d7eabe97c9e7df.jpeg";
        ImageSegmentHeadDto imageSegmentHeadDto = aliyunImageService.getImageSegmentHeadDto(url);
        System.out.println(imageSegmentHeadDto);
    }
}