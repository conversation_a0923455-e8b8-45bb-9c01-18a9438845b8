package com.sdsdiy.materialimpl.service.ai.swap;

import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.materialdata.enums.AiSwapTypeEnum;
import com.sdsdiy.materialdata.enums.LinkFoxConstant;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class AiSwapTaskServiceTest {
    @Resource
    private AiSwapTaskService aiSwapTaskService;

    @MockBean
    private RocketMQTemplate rocketMQTemplate;

    @Before
    public void mockBean() {
        Mockito.doReturn(null).when(rocketMQTemplate).sendNormal(Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void testSwapTask() {
        aiSwapTaskService.swapTask(AiSwapTypeEnum.SWAP_MODEL.getType());
    }

    @Test
    public void testGetResult() {
        aiSwapTaskService.getResult(4L);
    }

    @Test
    public void testTestSubmitTask() {
        aiSwapTaskService.submitTask(32L);
    }
}