package com.sdsdiy.materialimpl.service.ai.text;

import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeAll;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class AiCreativeTextServiceTest {
    @Resource
    private AiCreativeTextService aiCreativeTextService;

    @MockBean
    private RocketMQTemplate rocketMQTemplate;

    @Before
    public void mockMq() {
        Mockito.doReturn(null).when(rocketMQTemplate).sendNormal(Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void testSubmitTask() {
        aiCreativeTextService.submitTask(2L);
    }

    @Test
    public void testGetResult() {
        aiCreativeTextService.getResult(2L);
    }

    @Test
    public void testCreativeTextTask() {
        aiCreativeTextService.creativeTextTask();
    }
}