package com.sdsdiy.materialimpl.service.aidrawing;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.materialdata.enums.LinkFoxConstant;
import com.sdsdiy.materialdata.req.linkfox.CreativeTextReq;
import com.sdsdiy.materialdata.req.linkfox.SwapBackGroundReq;
import com.sdsdiy.materialdata.req.linkfox.SwapModelReq;
import com.sdsdiy.materialdata.resp.linkfox.*;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
class LinkFoxApiServiceTest {
    @Resource
    private LinkFoxApiService linkFoxApiService;
    @Test
    void imageToPrompt() {
        String s = linkFoxApiService.imageToPrompt(10086L, "https://jobs-1318297550.cos.ap-shanghai.myqcloud.com/bdbdf5fe-0ad3-4214-8ed5-8ea99af6e453_0_3.png");
        System.out.println("imgToPromptId = " + s);
    }

    @Test
    void imageToPromptResult() {
        ImageToPromptResultResp imageToPromptResultResp = linkFoxApiService.imageToPromptResult(10086L, "277108");
        System.out.println(JSONObject.toJSONString(imageToPromptResultResp));
    }

    @Test
    void modelPromptPropertiesTest() {
        LinkFoxModelPromptPropertiesResp linkFoxModelPromptPropertiesResp = linkFoxApiService.modelPromptProperties();
        System.out.println(JSONObject.toJSONString(linkFoxModelPromptPropertiesResp));
    }

    @Test
    void modelListTest() {
        LinkFoxModelListResp linkFoxModelListResp = linkFoxApiService.modelList();
        System.out.println(JSONObject.toJSONString(linkFoxModelListResp));
    }
    @Test
    void aiSwapModelSceneStyleListTest() {

        LinkFoxStyleListResp linkFoxStyleListResp = linkFoxApiService.styleList(LinkFoxConstant.LinkFoxStyleTypeEnum.AI_SWAP_MODEL_SCENE_TYPE.getType());
        System.out.println(JSON.toJSONString(linkFoxStyleListResp));
    }
    @Test
    void aiSwapBackgroundSceneStyleListTest() {

        LinkFoxStyleListResp linkFoxStyleListResp = linkFoxApiService.styleList(LinkFoxConstant.LinkFoxStyleTypeEnum.AI_SWAP_BACKGROUND_SCENE_TYPE.getType());
        System.out.println(JSON.toJSONString(linkFoxStyleListResp));
    }

    @Test
    void mattingPictureTest() {


        String url = "https://cdn.sdspod.com/out/17231/202407/facde310c3de9208bb45ec06604f8805.jpg";
        LinkFoxMattingPictureResp resp = linkFoxApiService.mattingPicture(url, LinkFoxConstant.LinkFoxSubTypeEnum.DRESS.getType());
        System.out.println(JSON.toJSONString(resp));
    }
    @Test
    void mattingPictureResultTest() {

        MattingPictureResultResp resp = linkFoxApiService.mattingPictureResult("1813774593648603136");
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    void swapModelTest() {
        SwapModelReq req = new SwapModelReq();
        req.setImageUrl("https://cdn.sdspod.com/out/17231/202407/facde310c3de9208bb45ec06604f8805.jpg");
        SwapModelReq.ExtendFieldsDTO extendFieldsDTO = new SwapModelReq.ExtendFieldsDTO();

        extendFieldsDTO.setImageSegUrl("https://cdn.sdspod.com/images/91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/8a0393d926029a03769edfa6e156d4fb.png");
        extendFieldsDTO.setModelMarketId("1783382068347740160");
        extendFieldsDTO.setModelMarketType("2");
        req.setOutputNum(2);
        extendFieldsDTO.setScene("健身房");
        extendFieldsDTO.setSceneType("1");
        extendFieldsDTO.setModelPrompt("微笑");
        req.setExtendFields(extendFieldsDTO);
        SwapModelResp swapModelResp = linkFoxApiService.swapModel(req);

        System.out.println(JSONObject.toJSONString(swapModelResp));
    }

    @Test
    void getSwapModelResultTest() {
        LinkFoxSwapResultResp linkFoxSwapResultResp = linkFoxApiService.swapModelResult("1818477424097402880");
        System.out.println(JSONObject.toJSONString(linkFoxSwapResultResp));
    }

    @Test
    void swapBackGroundTest() {
        SwapBackGroundReq req = new SwapBackGroundReq();
        req.setImageUrl("https://cdn.sdspod.com/imagesThumbs/91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/9268b73f56ba6c365cfb402bab37fbab.jpg");
        req.setStyle("泳池");
        req.setOutputNum(2);
        SwapBackGroundReq.ExtendFieldsDTO extendFieldsDTO = new SwapBackGroundReq.ExtendFieldsDTO();
        extendFieldsDTO.setImageSegUrl("https://cdn.sdspod.com/imagesThumbs/91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/41113049a8f53ba49e9a21977ae3d0a8.png");
        extendFieldsDTO.setIsDepth(Boolean.TRUE);
        req.setExtendFields(extendFieldsDTO);
        SwapBackGroundResp swapBackGroundResp = linkFoxApiService.swapBackGround(req);
        System.out.println(JSONObject.toJSONString(swapBackGroundResp));
    }

    @Test
    void getSwapBackGroundTest() {
        LinkFoxSwapResultResp linkFoxSwapResultResp = linkFoxApiService.swapBackGroupResult("1813107467549876224");
        System.out.println(JSONObject.toJSONString(linkFoxSwapResultResp));
    }

    @Test
    void creativeTextStyleListTest() {

        LinkFoxStyleListResp linkFoxStyleListResp = linkFoxApiService.styleList(LinkFoxConstant.LinkFoxStyleTypeEnum.CREATIVE_TEXT.getType());
        System.out.println(JSON.toJSONString(linkFoxStyleListResp));
    }

    @Test
    public void testCreativeText() {
        CreativeTextReq req = new CreativeTextReq();
        req.setPrompt("super hero");
        CreativeTextReq.ExtendFieldsDTO extendFieldsDTO = new CreativeTextReq.ExtendFieldsDTO();
        extendFieldsDTO.setRatio("16:10");
        extendFieldsDTO.setMode(1);
        extendFieldsDTO.setModelName("方块像素");
        extendFieldsDTO.setModelId("766");
        req.setExtendFields(extendFieldsDTO);
        CreativeTextResp creativeTextResp = linkFoxApiService.creativeText(req);
        System.out.println(JSONObject.toJSONString(creativeTextResp));
    }

    @Test
    public void testCreativeTextResult() {
        CreativeTextResultResp creativeTextResultResp = linkFoxApiService.creativeTextResult("1814119988543205376");
        System.out.println(JSONObject.toJSONString(creativeTextResultResp));
    }
}