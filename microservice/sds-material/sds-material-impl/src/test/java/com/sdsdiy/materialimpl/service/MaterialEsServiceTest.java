package com.sdsdiy.materialimpl.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.sdsdiy.materialdata.dto.material.MaterialEsDto;
import com.sdsdiy.materialdata.dto.material.MaterialUrlQueryDTO;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import com.sdsdiy.materialimpl.entity.po.Material;
import com.sdsdiy.materialimpl.mapper.MaterialMapper;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
class MaterialEsServiceTest {

    @Resource
    private MaterialEsService materialEsService;
    @Resource
    private MaterialMapper materialMapper;
/*    @Resource
    private MerchantStoreMaterialCopyTaskService merchantStoreMaterialCopyTaskService;
    @Resource
    private MerchantStoreMaterialTask merchantStoreMaterialTask;
    @Resource
    private MerchantStoreMaterialCategoryService merchantStoreMaterialCategoryService;*/

    private static final String ES_INDEX = "sds_material";

    @Test
    void updateEsTask() {

        List<Material> materials = materialMapper.selectBatchIds(Lists.newArrayList(146519756));
/*        for (Material material:materials){
            material.setOriginalImg("https://i0.wp.com/wallpapertag.com/wallpaper/full/6/d/7/434909-baby-panda-wallpapers-1080x1920-for-4k-monitor.jpg?w=350");
            material.setOfficialMaterialInfoType("BILL_PAYMENT");
        }*/


        materialEsService.updateEsTask(materials);
    }
    @Test
    void getByName() {
        MaterialUrlQueryDTO dto=new MaterialUrlQueryDTO();
        dto.setMerchantId(15321L);
        dto.setNames(Lists.newArrayList("20050Z95302"));
        List<MaterialEsDto> fromEsByNames = materialEsService.getFromEsByNames(dto);
        System.out.println(JSON.toJSONString(fromEsByNames));
    }

    @Test
    void updateEs() {
        LambdaQueryWrapper<Material> eq = Wrappers.<Material>lambdaQuery()
                .eq(Material::getMerchantId, 4242L);
        List<Material> materials = materialMapper.selectList(eq);
        List<List<Material>> partition = Lists.partition(materials, 500);
        for(List<Material> materialList:partition){
            materialEsService.updateEsTask(materialList);
        }

    }

    @Test
    void countOccupyLengthByMerchantId() {
        Map<Long, BigDecimal> longBigDecimalMap = materialEsService.countOccupyLengthByMerchantId(1L);
        System.out.println(longBigDecimalMap);
    }
/*    void test() {
        List<Long> syncIdList = merchantStoreMaterialCategoryService.getSyncIdList();
        System.out.println(syncIdList);
    }

    void test2() {
        merchantStoreMaterialCategoryService.updateSync(Lists.newArrayList(1L, 2L, 9L));
    }


    void task() {
        merchantStoreMaterialTask.merchantStoreMaterialCopyTask(null);
    }*/

}