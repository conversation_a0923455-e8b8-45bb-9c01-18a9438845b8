package com.sdsdiy.materialimpl.service.aidrawing;

import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.materialimpl.MaterialServiceApplication;

import com.sdsdiy.userapi.dto.MerchantSimpleDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class PodAiMerchantServiceTest {
    @Resource
    private PodAiMerchantService podAiMerchantService;

    @Test
    public void testBindMerchantList() {
        List<MerchantSimpleDto> merchantSimpleDtos = podAiMerchantService.bindMerchantList(1L);
        System.out.println(JSONObject.toJSONString(merchantSimpleDtos));
        List<MerchantSimpleDto> merchantSimpleDtos2 = podAiMerchantService.unBindMerchantList(1L, 1L);
        System.out.println(JSONObject.toJSONString(merchantSimpleDtos2));
    }

}