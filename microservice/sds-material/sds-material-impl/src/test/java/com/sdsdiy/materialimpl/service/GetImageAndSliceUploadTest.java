package com.sdsdiy.materialimpl.service;

import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Encodes;
import com.sdsdiy.common.base.enums.S3ModuleEnum;
import com.sdsdiy.core.aws.s3.S3Util;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import com.sdsdiy.materialimpl.entity.po.aidrawing.AiDrawingTaskImg;
import com.sdsdiy.materialimpl.util.ImageUtils;
import org.apache.commons.lang.ArrayUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class GetImageAndSliceUploadTest {

    @Resource
    private S3Util s3Util;

    private static String getMessageHash(String url) {
        int i = url.lastIndexOf("_");
        return url.substring(i + 1);
    }
    private static String getContentType(String url) {
        if (url.endsWith("png")) {
            return "image/png";
        } else {
            return "image/webp";
        }
    }

    private static String getFileName(String url) {
        int i = url.lastIndexOf("/");
        return url.substring(i + 1);
    }

    private static String getFormatName(String url) {
        int i = url.lastIndexOf(".");
        return url.substring(i + 1);
    }

}
