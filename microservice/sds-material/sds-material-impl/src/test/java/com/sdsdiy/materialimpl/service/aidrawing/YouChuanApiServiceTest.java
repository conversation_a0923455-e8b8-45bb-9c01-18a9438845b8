package com.sdsdiy.materialimpl.service.aidrawing;

import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.materialdata.resp.youchuan.*;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class YouChuanApiServiceTest {
    @Resource
    private YouChuanApiService youChuanApiService;


    private static final String TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ4aWFvY2h1YW4iLCJzdWIiOiJ4aWFvY2h1YW4tY24iLCJhdWQiOlsiMTgwMDQ0ODQwMjA5MDA5ODY4OSIsIjEzODExMTk4NTEzIiwiYkxEbk1ZakMiXSwiZXhwIjoxNzE5OTc5MjAwLCJuYmYiOjE3MTk0MDQ1NTgsImlhdCI6MTcxOTQwNDU1OCwianRpIjoiOTA4YTRiYzgtMDMxYS00Y2IzLTkyOGUtMDdhZmMwZDQ4NTA1In0.i8ViELOVGTCkaJoPCRCu5LYmxBHX8WsXlaDXrBFdIaM";

    @Test
    public void getInfo() {
        YouChuanInfoResp info = youChuanApiService.getInfo(TOKEN);
        System.out.println(JSONObject.toJSONString(info));
    }
    @Test
    public void getUsage() {
        YouChuanUsageResp usage = youChuanApiService.getUsage(TOKEN);
        System.out.println(JSONObject.toJSONString(usage));
    }
    @Test
    public void testGetQueue() {

        YouChuanQueueResp queue = youChuanApiService.getQueue(TOKEN);

        System.out.println(JSONObject.toJSONString(queue));
    }

    @Test
    public void testYouChuanLogin() {
        YouChuanLoginResp loginResp = youChuanApiService.youChuanLogin("13811198513", "sdsdiy8888");
        System.out.println(JSONObject.toJSONString(loginResp));
    }

}