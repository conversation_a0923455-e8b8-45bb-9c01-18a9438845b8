package com.sdsdiy.materialimpl.service.aidrawing;

import com.sdsdiy.materialdata.req.aiaccount.AddAiAccountReq;
import com.sdsdiy.materialdata.req.aiaccount.EditAiAccountReq;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class PodAiAccountServiceTest {
    @Resource
    private PodAiAccountService podAiAccountService;

    @Test
    public void testAddAccount() {
        AddAiAccountReq req = new AddAiAccountReq();
        req.setPhone("***********");
        req.setPassword("sdsdiy8888");
        req.setGroupId(1L);
        podAiAccountService.addAccount(req);
    }

    @Test
    public void testEditAccount() {
        EditAiAccountReq req = new EditAiAccountReq();
        req.setPhone("***********");
        req.setPassword("10086");
        podAiAccountService.editAccount(1L, req);
    }

    @Test
    public void testDeleteAccount() {
        podAiAccountService.deleteAccount(1L);
    }
}