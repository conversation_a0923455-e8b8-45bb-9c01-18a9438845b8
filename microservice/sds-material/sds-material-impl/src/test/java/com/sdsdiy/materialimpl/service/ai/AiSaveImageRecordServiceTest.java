package com.sdsdiy.materialimpl.service.ai;

import com.sdsdiy.common.base.entity.dto.McContentDTO;
import com.sdsdiy.common.base.helper.McContentHelper;
import com.sdsdiy.materialdata.dto.ai.text.AiCreativeTextBatchResultSaveDTO;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class AiSaveImageRecordServiceTest {
    @Resource
    private AiSaveImageRecordService aiSaveImageRecordService;

    @Test
    public void testMattingTextAndSave() {
        AiCreativeTextBatchResultSaveDTO saveDTO = new AiCreativeTextBatchResultSaveDTO();
        saveDTO.setIsMatting(1);
        saveDTO.setName("aabb123");
        saveDTO.setImgId(13L);
        saveDTO.setKeywordList(Arrays.asList("keyword1", "keyword2"));
        saveDTO.setMaterialFolderId(146522383L);
        McContentHelper.setCurrentContent(new McContentDTO().setMerchantId(4184L).setUserId(30011446L));
        this.aiSaveImageRecordService.mattingTextAndSave(saveDTO, null);
    }
}