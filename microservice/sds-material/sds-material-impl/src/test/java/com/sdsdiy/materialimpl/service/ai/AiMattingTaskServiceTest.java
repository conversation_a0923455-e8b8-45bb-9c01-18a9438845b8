package com.sdsdiy.materialimpl.service.ai;

import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class AiMattingTaskServiceTest {

    @Resource
    private AiMattingTaskService aiMattingTaskService;

    @MockBean
    private RocketMQTemplate rocketMQTemplate;

    @Before
    public void mockBean() {
        Mockito.doReturn(null).when(rocketMQTemplate).sendNormal(Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void testMattingTask() {
        aiMattingTaskService.mattingTask();
    }

    @Test
    public void testSubmitMatting() {
        aiMattingTaskService.submitMatting(21L);
    }

    @Test
    public void testMattingResult() {
        aiMattingTaskService.syncMattingResult(21L);
    }
}