package com.sdsdiy.materialimpl.service.aidrawing;

import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.materialdata.dto.aidrawing.AiDrawingGetKeywordsMsgDTO;
import com.sdsdiy.materialdata.dto.aidrawing.YouChuanAccountMsgDTO;
import com.sdsdiy.materialdata.dto.aidrawing.YouChuanJobFinishMsgDTO;
import com.sdsdiy.materialdata.dto.aidrawing.YouChuanSubmitSingleTaskMsgDTO;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class YouChuanAsyncServiceTest {

    @Resource
    private YouChuanAsyncService youChuanAsyncService;


    @Test
    public void testSubmitTaskByAccount() {

        YouChuanAccountMsgDTO dto = new YouChuanAccountMsgDTO();
        dto.setAccountId(4L);
        youChuanAsyncService.submitTaskByAccount(dto);
    }

    @Test
    public void testSubmitSingleTask() {
        YouChuanSubmitSingleTaskMsgDTO dto = new YouChuanSubmitSingleTaskMsgDTO();
        dto.setTaskId(1296L);
        dto.setAccountId(4L);
        youChuanAsyncService.submitSingleTask(dto);
    }

    @Test
    public void testGetKeywordsByLinkFox() {
        AiDrawingGetKeywordsMsgDTO msgDTO = new AiDrawingGetKeywordsMsgDTO();
        msgDTO.setUuid("aabbabab");
        msgDTO.setMaterialId(357576207L);
        msgDTO.setTaskId(1299L);
        youChuanAsyncService.getKeywordsByLinkFox(msgDTO);
    }

    @Test
    public void testGetResult() {
        YouChuanAccountMsgDTO msgDTO = new YouChuanAccountMsgDTO();
        msgDTO.setAccountId(44L);
        youChuanAsyncService.getResult(msgDTO);
    }

    @Test
    public void testYouChuanJobFinish() {
        String s = "{\"imgList\":[{\"no\":0,\"status\":\"ok\",\"url\":\"https://jobs-**********.cos.ap-shanghai.myqcloud.com/cfca7840-42dc-45c7-a2f9-209820f67024_0_0.png\"}],\"jobId\":\"66839810e34a4cf7c9d8d872\",\"taskId\":1296}";
        YouChuanJobFinishMsgDTO msg = JSONObject.parseObject(s, YouChuanJobFinishMsgDTO.class);
        youChuanAsyncService.youChuanJobFinish(msg);
    }


    @Test
    public void testYouChuanJobError() {
        YouChuanJobFinishMsgDTO msg = new YouChuanJobFinishMsgDTO();
        msg.setErrorMsg("文本审核未通过(生成失败的任务不消耗GPU时间)");
        msg.setJobId("66a751bfee056ac48299910b");
        msg.setTaskId(1607L);
        youChuanAsyncService.youChuanJobError(msg);
    }
}

