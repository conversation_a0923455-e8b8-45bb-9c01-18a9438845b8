package com.sdsdiy.materialimpl.listener;

import com.google.common.collect.Lists;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import com.sdsdiy.userapi.dto.user.message.MerchantUserDeleteMaterialDeleteMsg;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * @description:
 * @Author: zmy
 * @Date: 2023/5/30 14:45
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class MerchantUserDeleteMaterialDeleteS3ListenerTest {

    @Resource
    private MerchantUserDeleteMaterialDeleteS3Listener s3Listener;
    @Test
    public void onMessage() {
        MerchantUserDeleteMaterialDeleteMsg msg=new MerchantUserDeleteMaterialDeleteMsg();
        msg.setMaterialIds(Lists.newArrayList(146519785L));
        msg.setMerchantId(4184l);
        msg.setDataHistoryId(1l);
        s3Listener.onMessage(msg);
    }
}