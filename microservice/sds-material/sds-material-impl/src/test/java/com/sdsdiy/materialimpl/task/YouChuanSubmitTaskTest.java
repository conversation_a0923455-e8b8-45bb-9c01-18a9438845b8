package com.sdsdiy.materialimpl.task;

import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class YouChuanSubmitTaskTest {

    @Resource
    private YouChuanSubmitTask task;
//    @MockBean
//    private MqTemplate mqTemplate;

    @Test
    public void testYouChuanSubmitTask() {
//        Mockito.doNothing().when(mqTemplate).sendMessage(Mockito.any(), Mockito.any());
//        task.youChuanSubmitTask(null);
    }

    @Test
    public void testYouChuanGetResult() {
    }
}
