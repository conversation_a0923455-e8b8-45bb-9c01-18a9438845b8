package com.sdsdiy.materialimpl.util;

import com.sdsdiy.common.base.enums.S3ModuleEnum;
import com.sdsdiy.common.base.helper.Encodes;
import com.sdsdiy.core.aws.s3.S3Util;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.apache.commons.lang.ArrayUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
public class ImageUtilsTest {

    @Resource
    private S3Util s3Util;
    @Test
    public void testGetInvertImage() throws IOException {
        String originUrl = "https://cdn.sdspod.com/out/17231/202407/facde310c3de9208bb45ec06604f8805.jpg";
        String revertUrl = "https://cdn.sdspod.com/images/91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/ecdde98e42b277da5c9015aa999aa1fc.png";

        BufferedImage invertImage = ImageUtils.mattingBackGroundImage(originUrl, revertUrl);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(invertImage, "png", outputStream);
        String contentType = "image/png";
        InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        String encodeFilename = Encodes.md5ToString(DigestUtils.md5DigestAsHex(ArrayUtils.addAll(outputStream.toByteArray(), "ljptest07152".getBytes())));
        String fileCode = encodeFilename + "." + "png";
        String s = s3Util.uploadToS3(S3ModuleEnum.IMAGES, fileCode, contentType, inputStream);

        System.out.println(s);
    }

    @Test
    public void getImageExceptHumanBody() throws IOException {
        String originUrl = "https://cdn.sdspod.com/out/17231/202407/facde310c3de9208bb45ec06604f8805.jpg";
        String revertUrl = "https://cdn.sdspod.com/images/91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/ecdde98e42b277da5c9015aa999aa1fc.png";
        String coverImageUrl = "https://cdn.sdspod.com/images/91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/8a0393d926029a03769edfa6e156d4fb.png";
        BufferedImage invertImage = ImageUtils.mattingBackGroundImage(originUrl, revertUrl);
        BufferedImage coverImage = ImageIO.read(new URL(coverImageUrl));
        ImageUtils.imageCover(invertImage, originUrl, coverImage);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(invertImage, "png", outputStream);
        String contentType = "image/png";
        InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        String encodeFilename = Encodes.md5ToString(DigestUtils.md5DigestAsHex(ArrayUtils.addAll(outputStream.toByteArray(), "ljptest07182".getBytes())));
        String fileCode = encodeFilename + "." + "png";
        String s = s3Util.uploadToS3(S3ModuleEnum.IMAGES, fileCode, contentType, inputStream);
        System.out.println(s);
    }
}