//package com.sdsdiy.materialimpl.service.aidrawing;
//
//import com.sdsdiy.materialdata.dto.aidrawing.AiDrawingGetKeywordsMsgDTO;
//import com.sdsdiy.materialdata.dto.aidrawing.AiDrawingGetResultMsgDTO;
//import com.sdsdiy.materialdata.dto.aidrawing.AiDrawingLinkFoxMessageDTO;
//import com.sdsdiy.materialimpl.MaterialServiceApplication;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
//
///**
// * @BelongsProject: a4-sdsdiy-microservice
// * @BelongsPackage: com.sdsdiy.materialimpl.service.aidrawing
// * @Author: lujp
// * @CreateTime: 2023-05-29
// * @Description:
// * @Version: 1.0
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {MaterialServiceApplication.class})
//class AiDrawingTaskServiceTest {
//
//    @Resource
//    private AiDrawingTaskService aiDrawingTaskService;
//
//    @Test
//    void syncAiDrawingTaskResult() {
//        aiDrawingTaskService.syncAiDrawingTaskResult();
//    }
//
//    @Test
//    void getKeyword() {
//        AiDrawingGetKeywordsMsgDTO aiDrawingGetKeywordsMsgDTO = new AiDrawingGetKeywordsMsgDTO();
//        aiDrawingGetKeywordsMsgDTO.setMaterialId(146520966L);
//        aiDrawingGetKeywordsMsgDTO.setUuid("aabdsgsdahsjfhdfajadj");
//        aiDrawingTaskService.getKeyword(aiDrawingGetKeywordsMsgDTO);
//    }
//
//    @Test
//    void dealTaskSuccessMessage() {
//        AiDrawingGetResultMsgDTO msg = new AiDrawingGetResultMsgDTO();
//        msg.setAiDrawingTaskId(1071L);
//        msg.setUuid("asgasg5s4ah6");
//        aiDrawingTaskService.dealTaskGetResultMessage(msg);
//    }
//
//    @Test
//    void linkFoxMultiExecute() {
//        AiDrawingLinkFoxMessageDTO msg = new AiDrawingLinkFoxMessageDTO();
//        msg.setImageUrl("https://static-photo-center-prov.oss-cn-hangzhou.aliyuncs.com/images/91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/6133bebbd36abaf853d7822b8ceacf32.jpg");
//        msg.setRatioStr("1920:1200");
//        msg.setTaskId(1068L);
//        msg.setType(1);
//        msg.setUuid("2f65a488-11e3-486e-99f9-b4d4484cab51");
//        msg.setKeywords("a lovely cat");
//        aiDrawingTaskService.linkFoxMultiExecute(msg);
//    }
//}