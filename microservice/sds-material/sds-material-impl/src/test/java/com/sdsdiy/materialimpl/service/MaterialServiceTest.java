package com.sdsdiy.materialimpl.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sdsdiy.materialapi.dto.base.materials.MatarialUpdateOrderCountDto;
import com.sdsdiy.materialapi.dto.resp.ThirdPlatformMaterialFlodResp;
import com.sdsdiy.materialdata.dto.material.MaterialUrlQueryDTO;
import com.sdsdiy.materialimpl.MaterialServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MaterialServiceApplication.class})
class MaterialServiceTest {

	@Resource
	private MaterialService materialService;

	@Resource
	private ThirdPlatformMaterialService thirdPlatformMaterialService;

	@Test
	void updateEsTask() {
		MatarialUpdateOrderCountDto matarialUpdateOrderCountDto = new MatarialUpdateOrderCountDto();
		matarialUpdateOrderCountDto.setMatarialIds(Lists.newArrayList(47587664L));
		materialService.updateOrderCount(matarialUpdateOrderCountDto);
	}

	@Test
	void getFoldOrSaveFold() {
		ThirdPlatformMaterialFlodResp foldOrSaveFold = thirdPlatformMaterialService.getFoldOrSaveFold(430L);
		System.out.println(foldOrSaveFold);
	}
	@Test
	void getDesignUrlFromEsByNames() {
		MaterialUrlQueryDTO dto=new MaterialUrlQueryDTO();
		dto.setMerchantId(4184L);
		dto.setNames(Lists.newArrayList("20050Z95302","3333"));
		Map<String, String> map = materialService.getDesignUrlFromEsByNames(dto);
		System.out.println(JSON.toJSONString(map));
	}

}