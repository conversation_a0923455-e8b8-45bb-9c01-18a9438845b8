package com.sdsdiy.materialapi.dto.official;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 官方素材表-前端请求对象
 * 【不要加状态相关字段】
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "官方素材")
public class OfficialMaterialReqDTO implements Serializable {

    private static final long serialVersionUID = -6979447811918056830L;
    private Long id;

    @ApiModelProperty(value = "素材标题、名称")
    private String title;

    @ApiModelProperty(value = "素材分类id")
    private Long materialCategoryId;

    @ApiModelProperty(value = "素材主题id")
    private Long topicId;

    @ApiModelProperty(value = "设计风格id")
    private Long designStyleId;

    @ApiModelProperty(value = "多选，适合工艺：UV直喷，热转印，刺绣，烫画，数码打印，3D打印")
    private List<String> processType;

    @ApiModelProperty(value = "适合设计区域：全印，半印")
    private String designArea;

    @ApiModelProperty(value = "多选，适用品类，category.id")
    private List<Long> productCategory;

    public List<Long> getProductCategory() {
        if (CollectionUtils.isEmpty(productCategory)) {
            return productCategory;
        }
        // 0=全部，如果有0，就过滤掉其他的
        return productCategory.contains(0L) ? Collections.singletonList(0L) : productCategory;
    }

    @ApiModelProperty(value = "设计理念，50字以内")
    private String designIdea;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "版权证明：无，版权登记，作品存证")
    private String copyrightType;

    @ApiModelProperty(value = "版权登记号")
    private String copyrightNo;

    @ApiModelProperty(value = "作品存证平台")
    private String preservationPlatform;

    @ApiModelProperty(value = "作品存证号")
    private String preservationPlatformNo;

    @ApiModelProperty(value = "需求表id")
    private Long materialRequirementId;

    @ApiModelProperty(value = "设计师账号id")
    private Long designAccountId;

    @ApiModelProperty(value = "设计师商户id")
    private Long designMerchantId;

    @ApiModelProperty(value = "关键字")
    private String keyword;

}
