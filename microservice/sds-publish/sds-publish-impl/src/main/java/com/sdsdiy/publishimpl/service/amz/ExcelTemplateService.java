package com.sdsdiy.publishimpl.service.amz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.common.base.helper.McContentHelper;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.core.aws.es.ElasticsearchUtils;
import com.sdsdiy.core.base.util.ConvertUtil;
import com.sdsdiy.publishapi.api.template.ExcelUploadContent;
import com.sdsdiy.publishapi.api.template.ExcelUploadContentValue;
import com.sdsdiy.publishapi.constant.CustomProductTemplateConstant;
import com.sdsdiy.publishapi.constant.ExcelTemplateConstant;
import com.sdsdiy.publishapi.dto.ProductTemplatePlatformEnum;
import com.sdsdiy.publishapi.dto.template.*;
import com.sdsdiy.publishapi.myenum.PlatformTemplateCode;
import com.sdsdiy.publishapi.param.ProductTemplateHandleParam;
import com.sdsdiy.publishdata.dto.MerchantTemplateNumberDTO;
import com.sdsdiy.publishimpl.dto.ExcelTemplateEsDto;
import com.sdsdiy.publishimpl.entity.po.amz.ExcelTemplate;
import com.sdsdiy.publishimpl.entity.po.amz.ExcelTemplateOffice;
import com.sdsdiy.publishimpl.entity.po.template.CustomProductTemplate;
import com.sdsdiy.publishimpl.feign.MerchantFeign;
import com.sdsdiy.publishimpl.feign.MerchantStoreFeign;
import com.sdsdiy.publishimpl.feign.MerchantSysUserFeign;
import com.sdsdiy.publishimpl.manager.amz.ExcelTemplateManager;
import com.sdsdiy.publishimpl.manager.template.ProductTemplatePublishValueMapManager;
import com.sdsdiy.publishimpl.service.MerchantTemplateNumberService;
import com.sdsdiy.publishimpl.service.template.CustomProductTemplateService;
import com.sdsdiy.publishimpl.service.template.TemplateProductRelService;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.base.MerchantStoreRespDto;
import com.sdsdiy.userapi.dto.merchant.MerchantSysUserSimpleDto;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/05/25 15:09
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class ExcelTemplateService {
    private final ExcelTemplateManager excelTemplateManager;
    private final ElasticsearchUtils elasticsearchUtils;
    private final MerchantSysUserFeign merchantSysUserFeign;
    private final ExcelTemplateOfficeService excelTemplateOfficeService;
    private final CustomProductTemplateService customProductTemplateService;
    private final MerchantFeign merchantFeign;
    private final MerchantTemplateNumberService merchantTemplateNumberService;
    private final TemplateProductRelService templateProductRelService;
    private final MerchantStoreFeign merchantStoreFeign;
    private final ProductTemplatePublishValueMapManager productTemplatePublishValueMapManager;
    private final String attrValueMapFormat = "<" + CustomProductTemplateConstant.ValueOriginEnum.ATTR_VALUE_MAP.getCode() + "-%s>";

    private static final String ES_INDEX = "sds_excel_template";
    private static final String UNLIMITED_PLATFORM_CODE = "0";

    public void batchDeleteByUser(ProductTemplateHandleParam param) {
        excelTemplateManager.batchDeleteByUser(param.getUserId());
        deleteEsByUserId(param.getUserId());
    }

    public void deleteEsByUserId(Long userId) {
        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("createUid", userId);
        String type = elasticsearchUtils.index(ES_INDEX);
        elasticsearchUtils.deleteByQuery(ES_INDEX, type, termQueryBuilder);

    }

    public void transfer(ProductTemplateHandleParam param) {
        String suffixName=param.getUserName()+"转移";
        List<ExcelTemplate> excelTemplates = excelTemplateManager.getSelectByUserId(param.getUserId());
        if (CollUtil.isEmpty(excelTemplates)) {
            return;
        }
        List<ExcelTemplate> updates=Lists.newArrayList();
        for (ExcelTemplate excelTemplate : excelTemplates) {
            ExcelTemplate updateExcelTemplate=new ExcelTemplate();
            updateExcelTemplate.setId(excelTemplate.getId());
            updateExcelTemplate.setCreateUid(param.getTransferUserId());
            updateExcelTemplate.setFileName(excelTemplate.getFileName()+suffixName);
            updates.add(updateExcelTemplate);
        }
        excelTemplateManager.updateBatchById(updates);
        List<Long> ids = excelTemplates.stream().map(i -> i.getId()).collect(Collectors.toList());
        syncToEs(ids);
    }

    public void syncToEs(List<Long> ids) {
        List<ExcelTemplate> excelTemplates = excelTemplateManager.listByIds(ids);
        List<ExcelTemplateOffice> excelTemplateOffices = excelTemplateOfficeService.findByTemplateIds(ids);
        List<Long> userIds = excelTemplates.stream().map(ExcelTemplate::getCreateUid).collect(Collectors.toList());
        Map<Long, MerchantSysUserSimpleDto> userMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(userIds)) {
            userMap = merchantSysUserFeign.mapMerchantSysUserSimpleDtoByIds(userIds);
        }
        List<ExcelTemplateEsDto> esDtos = Lists.newArrayList();
        for (ExcelTemplate excelTemplate : excelTemplates) {
            ExcelTemplateEsDto excelTemplateEsDto = ConvertUtil.dtoConvert(excelTemplate, ExcelTemplateEsDto.class);
            MerchantSysUserSimpleDto user = userMap.get(excelTemplate.getCreateUid());
            if (null != user) {
                excelTemplateEsDto.setMerchantId(user.getMerchantId());
            }
            excelTemplateEsDto.setCreatedTime(excelTemplate.getCreateDate().getTime());
            excelTemplateEsDto.setLastUsedTime(excelTemplate.getLastUseTime() == null ? 0L : excelTemplate.getLastUseTime().getTime());
            List<Long> officeIds = Lists.newArrayList();
            for (ExcelTemplateOffice excelTemplateOffice : excelTemplateOffices) {
                if (excelTemplateOffice.getExcelTemplateId().equals(excelTemplate.getId())) {
                    officeIds.add(excelTemplateOffice.getOfficeId());
                }
            }
            excelTemplateEsDto.setOfficeIds(officeIds);
            esDtos.add(excelTemplateEsDto);


        }
        String type = elasticsearchUtils.index(ES_INDEX);
        elasticsearchUtils.save(ES_INDEX, type, esDtos);
    }

    public void deleteByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            elasticsearchUtils.delete(ES_INDEX, ids);
        }
    }

    public ExcelTemplateDto getById(Long id) {
        if (!NumberUtils.greaterZero(id)) {
            return null;
        }
        ExcelTemplate excelTemplate = excelTemplateManager.findById(id, BasePoConstant.NO);
        return BeanUtil.copyProperties(excelTemplate, ExcelTemplateDto.class);
    }

    @GlobalTransactional
    public Long customCreate(CustomProductTemplateSaveDto dto) {
        checkValue(dto.getTemplateInfos());
        Long userId = McContentHelper.getCurrentUserId();
        // 自定义模版
        Long customProductTemplateId = saveCustomProductTemplate(userId, dto);
        // excelTemplate
        Long id = saveExcelTemplate(McContentHelper.getCurrentMerchantId(), userId, dto, customProductTemplateId);
        // 关联产品
        saveRelProductIds(dto.getProductIds(), userId, id);
        // 更新汇出值映射
        setAttrValueMapTemplateId(id, dto.getTemplateInfos());
        return id;
    }

    private void setAttrValueMapTemplateId(Long id, List<CustomProductTemplateContentDto> templateInfos) {
        if (CollUtil.isEmpty(templateInfos)) {
            return;
        }
        List<CustomProductTemplateAttrValueMapDto> attrValueMapDtos = templateInfos.stream().filter(i -> null != i.getAttrValueMap()).map(i -> i.getAttrValueMap()).collect(Collectors.toList());
        productTemplatePublishValueMapManager.setExcelTemplateId(id, attrValueMapDtos);
    }

    private void saveRelProductIds(List<Long> productIds, Long userId, Long id) {
        templateProductRelService.saveRelProducts(productIds, userId, id, ProductTemplatePlatformEnum.OTHER.getCode());
    }

    private static void checkValue(List<CustomProductTemplateContentDto> templateInfos) {
        if (CollUtil.isEmpty(templateInfos)) {
            return;
        }
        List<CustomProductTemplateContentDto> titleInfos = templateInfos.stream().filter(i -> StrUtil.isNotBlank(i.getValue()) && i.getValue().contains("<title-")).collect(Collectors.toList());
        Assert.validateTrue(CollUtil.isNotEmpty(titleInfos) && titleInfos.size() > 1, "标题模板不可插入多个模板字段");
    }

    private String getMerchantStorePlatformCode(Long merchantStoreId) {
        if(!NumberUtils.greaterZero(merchantStoreId)){
            return "";
        }
        List<MerchantStoreRespDto> merchantStoreRespDtos = merchantStoreFeign.findByIds(IdsSearchHelper.of(merchantStoreId));
        return CollUtil.isNotEmpty(merchantStoreRespDtos) ? merchantStoreRespDtos.get(0).getMerchantStorePlatformCode() : "";
    }

    private Long saveCustomProductTemplate(Long userId, CustomProductTemplateSaveDto dto) {
        CustomProductTemplate customProductTemplate = BeanUtil.copyProperties(dto, CustomProductTemplate.class);
        customProductTemplate.setContent(JSONUtil.toJsonStr(dto.getTemplateInfos()));
        customProductTemplate.setCreateUid(userId);
        customProductTemplate.setUpdateUid(userId);
        Long customProductTemplateId = customProductTemplateService.save(customProductTemplate);
        return customProductTemplateId;
    }

    private static void formatAttrValue(List<CustomProductTemplateContentDto> templateInfos) {
        if (CollUtil.isEmpty(templateInfos)) {
            return;
        }
        List<CustomProductTemplateContentDto> attrValueMapInfos = templateInfos.stream().filter(i -> null != i.getAttrValueMap()).collect(Collectors.toList());
        String attrValueMapFormat = "<%s-%s>";
        for (CustomProductTemplateContentDto attrValueMapInfo : attrValueMapInfos) {
            CustomProductTemplateAttrValueMapDto attrValueMap = attrValueMapInfo.getAttrValueMap();
            attrValueMapInfo.setValue(String.format(attrValueMapFormat, CustomProductTemplateConstant.ValueOriginEnum.ATTR_VALUE_MAP.getCode(), attrValueMap.getId()));
        }
    }

    private Long saveExcelTemplate(Long merchantId, Long userId, CustomProductTemplateSaveDto dto, Long customProductTemplateId) {
        String nextTemplateNumber = getNextTemplateNumber(merchantId);
        ExcelTemplate excelTemplate = new ExcelTemplate();
        excelTemplate.setResourceTemplateId(customProductTemplateId);
        excelTemplate.setMerchantStoreId(dto.getMerchantStoreId());
        excelTemplate.setCountryCode(dto.getCountryCode());
        excelTemplate.setFileName(dto.getTemplateName());
        excelTemplate.setMerchantStorePlatformCode(getDbPlatformCode(dto.getMerchantStorePlatformCode()));
        excelTemplate.setFileCode(dto.getFileCode());
        excelTemplate.setTemplateNumber(nextTemplateNumber);
        excelTemplate.setMerchantId(merchantId);
        excelTemplate.setCreateUid(userId);
        excelTemplate.setContent(getContent(dto.getSheetNumber(),dto.getDataStartNumber(),dto.getTemplateInfos()));
        excelTemplate.setTplType(ExcelTemplateConstant.TPL_TYPE_CUSTOM);
        excelTemplateManager.save(excelTemplate);
        Long id = excelTemplate.getId();
        return id;
    }

    public String getContent(Integer sheetNumber,Integer dataStartNumber,List<CustomProductTemplateContentDto> customProductTemplateContentDtos){
        ExcelUploadContent excelUploadContent=new ExcelUploadContent();
        excelUploadContent.setRowNum(dataStartNumber-1);
        excelUploadContent.setSheetNum(sheetNumber-1);
        List<ExcelUploadContentValue> contentValues=Lists.newArrayList();
        for (CustomProductTemplateContentDto templateInfo : customProductTemplateContentDtos) {
            ExcelUploadContentValue contentValue=new ExcelUploadContentValue();
            contentValue.setCode(templateInfo.getHead());
            contentValue.setCellNum(templateInfo.getCellNum());
            contentValue.setValue(templateInfo.getValue());
            contentValue.setDataFormat(templateInfo.getDataFormat());
            contentValues.add(contentValue);
        }
        excelUploadContent.setContents(contentValues);
        return JSONUtil.toJsonStr(Collections.singletonList(excelUploadContent));
    }

    private String getNextTemplateNumber(Long merchantId) {
        MerchantRespDto merchant = merchantFeign.getMerchantById(merchantId);
        String merchantNo = merchant.getMerchantNo();
        String platformTemplateCode = PlatformTemplateCode.IMPORT.getCode();
        MerchantTemplateNumberDTO templateNumber = merchantTemplateNumberService.getTemplateNumberAndIncrease(merchantNo, platformTemplateCode);
        long newNum = templateNumber.getLastNumber() + 1L;
        return merchantNo + platformTemplateCode + newNum;
    }

    public String getDbPlatformCode(String merchantStorePlatformCode){
        return StrUtil.isBlank(merchantStorePlatformCode)||merchantStorePlatformCode.equalsIgnoreCase(UNLIMITED_PLATFORM_CODE)?"":merchantStorePlatformCode;
    }

    public String getShowPlatformCode(String merchantStorePlatformCode){
        return StrUtil.isBlank(merchantStorePlatformCode)?UNLIMITED_PLATFORM_CODE:merchantStorePlatformCode;
    }

    @GlobalTransactional
    public void customEdit(Long id, CustomProductTemplateEditDto dto) {
        ExcelTemplate excelTemplate = getExcelTemplate(id);
        CustomProductTemplateRespDto customProductTemplateRespDto = getCustomProductTemplateRespDto(excelTemplate.getResourceTemplateId());
        // excelTemplate更新
        ExcelTemplate updateExcelTemplate = new ExcelTemplate();
        updateExcelTemplate.setId(id);
        updateExcelTemplate.setMerchantStoreId(dto.getMerchantStoreId());
        updateExcelTemplate.setCountryCode(dto.getCountryCode());
        updateExcelTemplate.setMerchantStorePlatformCode(getDbPlatformCode(dto.getMerchantStorePlatformCode()));
        updateExcelTemplate.setFileName(dto.getTemplateName());
        updateExcelTemplate.setContent(getContent(customProductTemplateRespDto.getSheetNumber(),customProductTemplateRespDto.getDataStartNumber(),dto.getTemplateInfos()));
        excelTemplateManager.updateById(updateExcelTemplate);
        // 关联产品
        saveRelProductIds(dto.getProductIds(), McContentHelper.getCurrentUserId(), id);
        // 自定义模版更新
        customProductTemplateService.updateById(excelTemplate.getResourceTemplateId(), dto);
        // 更新汇出值映射
        setAttrValueMapTemplateId(id, dto.getTemplateInfos());
    }

    private CustomProductTemplateRespDto getCustomProductTemplateRespDto(Long customProductTemplateId) {
        CustomProductTemplateRespDto customProductTemplateRespDto = customProductTemplateService.getById(customProductTemplateId);
        Assert.validateNull(customProductTemplateRespDto, "自定义模版不存在");
        return customProductTemplateRespDto;
    }

    public CustomProductTemplateDetailDto customDetail(Long id) {
        ExcelTemplate excelTemplate = getExcelTemplate(id);

        Long resourceTemplateId = excelTemplate.getResourceTemplateId();
        CustomProductTemplateRespDto customProductTemplateRespDto = getCustomProductTemplateRespDto(resourceTemplateId);
        CustomProductTemplateDetailDto customProductTemplateDetailDto = getCustomTemplateRespDto(excelTemplate, customProductTemplateRespDto);
        return customProductTemplateDetailDto;
    }

    private CustomProductTemplateDetailDto getCustomTemplateRespDto(ExcelTemplate excelTemplate,
                                                                    CustomProductTemplateRespDto customProductTemplateRespDto) {
        CustomProductTemplateDetailDto customProductTemplateDetailDto = new CustomProductTemplateDetailDto();
        customProductTemplateDetailDto.setId(excelTemplate.getId());
        customProductTemplateDetailDto.setFileCode(excelTemplate.getFileCode());
        customProductTemplateDetailDto.setFileName(excelTemplate.getFileName());
        customProductTemplateDetailDto.setMerchantStoreId(excelTemplate.getMerchantStoreId());
        customProductTemplateDetailDto.setCountryCode(excelTemplate.getCountryCode());
        customProductTemplateDetailDto.setTemplateName(excelTemplate.getFileName());
        customProductTemplateDetailDto.setProductIds(getRelProductIds(excelTemplate.getId(), ProductTemplatePlatformEnum.OTHER.getCode()));
        customProductTemplateDetailDto.setMerchantStorePlatformCode(getShowPlatformCode(excelTemplate.getMerchantStorePlatformCode()));

        customProductTemplateDetailDto.setCustomProductTemplateId(customProductTemplateRespDto.getId());
        customProductTemplateDetailDto.setSheetNumber(customProductTemplateRespDto.getSheetNumber());
        customProductTemplateDetailDto.setHeadNumber(customProductTemplateRespDto.getHeadNumber());
        customProductTemplateDetailDto.setGroupHeadNumber(customProductTemplateRespDto.getGroupHeadNumber());
        customProductTemplateDetailDto.setDataStartNumber(customProductTemplateRespDto.getDataStartNumber());
        customProductTemplateDetailDto.setExportParent(customProductTemplateRespDto.getExportParent());

        customProductTemplateDetailDto.setTemplateInfos(JSONUtil.toList(customProductTemplateRespDto.getContent(), CustomProductTemplateContentDto.class));
        return customProductTemplateDetailDto;
    }

    public List<Long> getRelProductIds(Long templateId, String platformCode) {
        TemplateProductRelSearchDto productRelSearchDto = new TemplateProductRelSearchDto();
        productRelSearchDto.setTemplateIds(Collections.singletonList(templateId));
        productRelSearchDto.setPlatformCodes(Lists.newArrayList(platformCode));
        List<TemplateProductRelRespDto> templateProductRelRespDtos = templateProductRelService.getDtoByPlatformTemplateIds(productRelSearchDto);
        return templateProductRelRespDtos.stream().map(i -> i.getProductId()).distinct().collect(Collectors.toList());
    }

    private ExcelTemplate getExcelTemplate(Long id) {
        ExcelTemplate excelTemplate = excelTemplateManager.findById(id, BasePoConstant.NO);
        Assert.validateNull(excelTemplate, "模版不存在");
        return excelTemplate;
    }

    public CustomProductTemplateDetailDto customCopyDetail(Long id) {
        CustomProductTemplateDetailDto customProductTemplateDetailDto = customDetail(id);
        customProductTemplateDetailDto.setId(null);
        customProductTemplateDetailDto.setCustomProductTemplateId(null);
        Map<Long, Long> oldAttrValueMapIdNewIdMap = productTemplatePublishValueMapManager.copyAndCreateValueMap(id);
        for (CustomProductTemplateContentDto attrValueMapInfo : customProductTemplateDetailDto.getTemplateInfos()) {
            CustomProductTemplateAttrValueMapDto attrValueMap = attrValueMapInfo.getAttrValueMap();
            if (null == attrValueMap) {
                continue;
            }
            Long newId = oldAttrValueMapIdNewIdMap.get(attrValueMap.getId());
            attrValueMap.setId(newId);
            if(attrValueMap.getMapSwitch().equals(BasePoConstant.YES)){
                String newValue = String.format(attrValueMapFormat, newId);
                attrValueMapInfo.setValue(newValue);
            }
        }

        return customProductTemplateDetailDto;
    }
}
