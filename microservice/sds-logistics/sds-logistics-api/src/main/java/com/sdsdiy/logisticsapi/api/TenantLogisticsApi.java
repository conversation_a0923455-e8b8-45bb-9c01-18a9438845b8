package com.sdsdiy.logisticsapi.api;

import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.logisticsapi.dto.DeclarationTaxResp;
import com.sdsdiy.logisticsapi.dto.LogisticsListParam;
import com.sdsdiy.logisticsapi.dto.LogisticsSimpleResp;
import com.sdsdiy.logisticsapi.dto.base.LogisticsChannelRespDto;
import com.sdsdiy.logisticsapi.dto.base.TenantAddressRespDto;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsapi.dto.logistics.CopyLogisticsParam;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsEditParam;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsFBASaveParam;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsFbmBatchSaveParam;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsUpdateParam;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.LogisticsAndLogisticsSourceResp;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.TenantLogisticsAsConditionParam;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.TenantLogisticsSaasPageResp;
import com.sdsdiy.logisticsdata.dto.base.LogisticsRespDto;
import com.sdsdiy.logisticsdata.vo.logistics.LogisticsGroupToSelectResp;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 物流渠道表(TenantLogistics)表api接口
 *
 * <AUTHOR>
 * @since 2021-11-01 10:38:35
 */
@RequestMapping("/microservice/tenantLogistics")
public interface TenantLogisticsApi {

    @GetMapping("/tenant/{tenantId}/page")
    PageListResultRespDto<TenantLogisticsRespDto> page(@PathVariable("tenantId") Long tenantId,
                                                       @RequestParam(value = "issuingBayAreaId") Long issuingBayAreaId,
                                                       @RequestParam(value = "keyword", required = false) String keyword,
                                                       @RequestParam(value = "deliveryMethod") String deliveryMethod,
                                                       @SpringQueryMap QueryParamHelper queryParamHelper);

    @GetMapping("/tenant/{tenantId}/saasPage")
    PageListResultRespDto<TenantLogisticsSaasPageResp> saasPage(@PathVariable("tenantId") Long tenantId,
                                                                @RequestParam(value = "issuingBayAreaId") Long issuingBayAreaId,
                                                                @RequestParam(value = "keyword", required = false) String keyword,
                                                                @RequestParam(value = "deliveryMethod") String deliveryMethod,
                                                                @SpringQueryMap QueryParamHelper queryParamHelper);

    @PostMapping("/tenant/{tenantId}/tenantServiceProviders/{tenantServiceProviderId}/fbm/batch")
    void fbmBatch(@PathVariable("tenantId") Long tenantId,
                  @PathVariable("tenantServiceProviderId") Long tenantServiceProviderId,
                  @RequestBody @Valid TenantLogisticsFbmBatchSaveParam param);

    @PostMapping("/tenant/{tenantId}/fba/one")
    void saveFbaOne(@PathVariable("tenantId") Long tenantId,
                    @RequestBody @Valid TenantLogisticsFBASaveParam param);

    @PostMapping("/tenant/{tenantId}/fba/zt/one")
    void saveFbaZtOne(@PathVariable("tenantId") Long tenantId,
                      @RequestBody @Valid TenantLogisticsFBASaveParam param);

    @PostMapping("/tenant/{tenantId}/fbm/zt/one")
    void saveFbmZtOne(@PathVariable("tenantId") Long tenantId,
                      @RequestBody @Valid TenantLogisticsFBASaveParam param);

    @PutMapping("{id}/tenant/{tenantId}")
    void edit(@PathVariable("tenantId") Long tenantId,
              @PathVariable("id") Long id,
              @RequestBody @Valid TenantLogisticsEditParam param);

    @PutMapping("{id}/status/{status}/tenant/{tenantId}")
    void updateStatus(@PathVariable("tenantId") Long tenantId,
                      @PathVariable("id") Long id,
                      @PathVariable("status") Integer status);

    @GetMapping("{id}/tenant/{tenantId}/countryExpressInfoNews")
    TenantLogisticsRespDto getOneDetail(@PathVariable("tenantId") Long tenantId,
                                        @PathVariable("id") Long id);

    @ApiOperation("获取租户下所有物流")
    @GetMapping("/tenants/{tenantId}/allSimple")
    List<TenantLogisticsRespDto> all(@PathVariable("tenantId") Long tenantId);

    @GetMapping("/findByIds")
    List<TenantLogisticsRespDto> findByIds(@SpringQueryMap BaseListReqDto reqDto);

    @ApiOperation("获取物流")
    @GetMapping("/getDtoById")
    TenantLogisticsRespDto getDtoById(@RequestParam Long id);

    @GetMapping("/findDtoById")
    TenantLogisticsRespDto findDtoById(@RequestParam Long id
            , @RequestParam(required = false) String fields);

    @ApiOperation("获取物流")
    @GetMapping("/getWithServiceProviderById")
    TenantLogisticsRespDto getWithServiceProviderById(@RequestParam Long id);

    @GetMapping("/tenants/{tenantId}/logistics/issuingBayAreas")
    List<Long> getLogisticsIdsByIssuingBayAreaIds(@PathVariable("tenantId") Long tenantI, @SpringQueryMap BaseListReqDto reqDto);

    @GetMapping("/logistics/issuingBayAreas")
    List<TenantLogisticsRespDto> getLogisticsByIssuingBayAreaIds(@SpringQueryMap BaseListReqDto reqDto);


    @GetMapping("/{id}/tenant/{tenantId}")
    TenantLogisticsRespDto getOne(@PathVariable("tenantId") Long tenantId,
                                  @PathVariable("id") Long id);

    @GetMapping("/{id}/logisticsResp")
    LogisticsRespDto getOneLogisticsResp(@PathVariable("id") Long id);

    @GetMapping("/haveLogisticsAccount/{tenantId}/{issuingBayAreaId}")
    List<Long> getHaveLogisticsAccountLogisticIdsMainIssuingBay(@PathVariable Long tenantId, @PathVariable Long issuingBayAreaId);

    @GetMapping("/logisticsRespList")
    @ApiOperation("根据id获取物流")
    List<LogisticsRespDto> list(@SpringQueryMap BaseListReqDto reqDto);

    @GetMapping("/logisticsRespList/all")
    @ApiOperation("根据id获取物流")
    List<LogisticsRespDto> all();

    @ApiOperation("根据仓位获取及需要物流追踪的物流信息")
    @GetMapping("/issuingBayArea/{issuingBayAreaId}/track/{isTrackInfo}")
    List<LogisticsRespDto> listByIssuingBayIdAndIsTrackInfo(@PathVariable(value = "issuingBayAreaId") Long issuingBayAreaId,
                                                            @PathVariable(value = "isTrackInfo") Integer isTrackInfo);

    @GetMapping("/simple")
    @ApiOperation("获取物流下拉列表")
    List<LogisticsSimpleResp> list(@SpringQueryMap LogisticsListParam param);

    @ApiOperation("根据仓位取物流信息")
    @GetMapping("/getByIssuingBayAreaIdAndType/{issuingBayAreaId}/{type}")
    List<TenantLogisticsRespDto> getByIssuingBayAreaIdAndType(@PathVariable(value = "issuingBayAreaId") Long issuingBayAreaId,
                                                              @PathVariable(value = "type") String type);

    @ApiOperation("根据仓位取物流信息")
    @GetMapping("/logistics/issuingBay/{issuingBayAreaId}/{type}")
    List<LogisticsRespDto> getLogisticsListByIssuingBayAreaIdAndType(@PathVariable(value = "issuingBayAreaId") Long issuingBayAreaId,
                                                                     @PathVariable(value = "type") String type,@RequestParam Long merchantId)
            ;

    @GetMapping("/tenants/{tenantId}/asCondition")
    List<TenantLogisticsRespDto> getAsCondition(@PathVariable(value = "tenantId") Long tenantId, @SpringQueryMap TenantLogisticsAsConditionParam param);

    @PostMapping("/logisticsGroupToSelect")
    @ApiOperation("获取分组的物流信息")
    List<LogisticsGroupToSelectResp> logisticsGroupToSelect(@RequestBody Collection<Long> ids);

    @GetMapping("/tenants/{tenantId}/SDS/logisticsChannel")
    @ApiOperation("获取租户物流渠道信息")
    List<LogisticsChannelRespDto> getLogisticsChannelContainSDS(@PathVariable(value = "tenantId") Long tenantId,
                                                                @RequestParam(value = "serviceProviderId", required = false) Long serviceProviderId,
                                                                @RequestParam(value = "nonServiceProviderId", required = false) Long nonServiceProviderId);

    @GetMapping("/init/tenantLogistics")
    void initTenantLogistics();

    @GetMapping("/init/initAccount")
    void initAccount();


    @GetMapping("/test")
    void test();
    @GetMapping("/initManualAccount")
    void initManualAccount();

    @GetMapping("/tenantServiceProvider")
    void tenantServiceProvider();

    @GetMapping("/fbmOfflineHandle")
    void fbmOfflineHandle();

    @GetMapping("/fbmAliHandle")
    void fbmAliHandle();

    @GetMapping("/bayAccountsConfig")
    void bayAccountsConfig();

    @GetMapping("/init/dhlFaraway")
    void initDhlFaraway();


    @PutMapping("/copyLogistics")
    void copyLogistics(@RequestBody CopyLogisticsParam copyLogisticsParam);

    @PutMapping("/copyAccount/tenant/{tenantId}")
    void copyAccount(@PathVariable("tenantId") Long tenantId, @RequestBody List<Long> accountIds);

    @PutMapping("/bindAccount/tenant/{tenantId}/{issuingAreaId}")
    void bindAccount(@PathVariable("tenantId") Long tenantId, @PathVariable("issuingAreaId") Long issuingAreaId, @RequestBody List<Long> issuingBayIds);

    @GetMapping("/tenant/{tenantId}/logisticsSource")
    Map<Long, LogisticsAndLogisticsSourceResp> getLogisticsAndLogisticsSource(@PathVariable("tenantId") Long tenantId,
                                                                              @SpringQueryMap BaseListReqDto reqDto);

    @GetMapping("/tenant/{tenantId}/issuingArea/{issuingAreaId}logisticsSource")
    Map<Long, LogisticsAndLogisticsSourceResp> getLogisticsAndLogisticsSource(@PathVariable("tenantId") Long tenantId,
                                                                              @PathVariable("issuingAreaId") Long issuingAreaId,
                                                                              @SpringQueryMap BaseListReqDto reqDto);


    @GetMapping("/tenant/{tenantId}/spanTenantArea")
    List<LogisticsSimpleResp> getSpanTenantAreaLogisticsAndNoAllProduct(@PathVariable("tenantId") Long tenantId);

    @GetMapping("/tenants/spanAreaLogisticsNumMap")
    Map<Long, Long> spanAreaLogisticsNumMap(@SpringQueryMap BaseListReqDto reqDto);

    @GetMapping("/tenant/{tenantId}/issuingAreas/{issuingAreaId}/spanTenantArea/all")
    List<LogisticsRespDto> getSpanTenantAreaLogistics(@PathVariable("tenantId") Long tenantId,
                                                      @PathVariable("issuingAreaId") Long issuingAreaId);

    @GetMapping("/spanTenantArea")
    List<TenantLogisticsRespDto> getSpanTenantAreaLogistics();

    @PostMapping("/findNameByIds")
    List<BaseIdAndNameDTO> findNameByIds(@RequestBody Collection<Long> ids);

    @GetMapping("/findNameByIds")
    String findNameById(@RequestParam("id") Long id);

    @GetMapping("/cnSelfSend/list")
    @ApiOperation("sprint_6.2.3 自寄物流渠道列表")
    List<TenantLogisticsRespDto> getCNSelfSend(@RequestParam("tenantId") Long tenantId,
                                               @RequestParam("issuingAreaId") Long issuingAreaId,
                                               @RequestParam("issuingBayId") Long issuingBayId,
                                               @RequestParam(value = "isTemuFullySelfSend", required = false) Integer isTemuFullySelfSend);

    @GetMapping("/taxInfos")
    @ApiOperation("获取申报税信息")
    DeclarationTaxResp getTaxInfos(@RequestParam("logisticsId") Long logisticsId,
                                   @RequestParam("countryCode") String countryCode);

    @PutMapping("{id}/update")
    void update(@PathVariable("id") Long id, @RequestBody @Valid TenantLogisticsUpdateParam param);

    @GetMapping("tenantAddress")
    TenantAddressRespDto getTenantAddress(@RequestParam Long logisticsId, @RequestParam Long issuingBayId);

}
