package com.sdsdiy.logisticsapi.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 运单申请记录表(CarriageNoRecodeReqDto)ReqDto类
 *
 * <AUTHOR>
 * @since 2020-09-04 19:22:36
 */
@Data
public class CarriageNoRecodeInitReqDto implements Serializable {
    @ApiModelProperty(value = "订单状态 1未付款 2待确认 3备货中 4已完成 98取消 99删除")
    private Integer orderStatus;
    @ApiModelProperty(value = "请求状态 1.待生成 2.生成成功 3.同步运单 4.生成失败")
    private Integer status;
    @ApiModelProperty(value = "运单编号")
    private String carriageNo;
    @ApiModelProperty(value = "申请号（自己生成的")
    private String extendNo;
    @ApiModelProperty(value = "面单地址")
    private String labelUrl;
    @ApiModelProperty(value = "订单完成时间")
    private Date orderFinishTime;

    private CarriageNoRepDto carriageNoRepDto;
}