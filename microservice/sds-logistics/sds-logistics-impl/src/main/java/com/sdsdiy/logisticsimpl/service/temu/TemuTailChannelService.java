package com.sdsdiy.logisticsimpl.service.temu;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.common.base.constant.task.ScheduledTaskConstant;
import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import com.sdsdiy.common.base.enums.ScanFormStatusEnum;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.CompareUtils;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.logistics.LogisticsTagConst;
import com.sdsdiy.core.util.ExceptionUtils;
import com.sdsdiy.coreconfig.util.DingDingUtil;
import com.sdsdiy.logisticsapi.constant.CarriageNoRecodeConstant;
import com.sdsdiy.logisticsapi.dto.CarriageNoRepDto;
import com.sdsdiy.logisticsapi.dto.CarriageNoRespDto;
import com.sdsdiy.logisticsapi.dto.base.LogisticsChannelRespDto;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsapi.event.CarriageNoEventMessage;
import com.sdsdiy.logisticsdata.dto.temu.TemuTailApplyCarriageNoMessageDTO;
import com.sdsdiy.logisticsdata.dto.temu.TemuTailApplyCarriageNoReqDTO;
import com.sdsdiy.logisticsimpl.entity.po.CarriageNoRecode;
import com.sdsdiy.logisticsimpl.feign.OrderCarriageFeign;
import com.sdsdiy.logisticsimpl.feign.OrderFeign;
import com.sdsdiy.logisticsimpl.feign.OrderProgressManagerFeign;
import com.sdsdiy.logisticsimpl.feign.order.OrderParcelFeign;
import com.sdsdiy.logisticsimpl.feign.order.OrderTailLogisticsFeign;
import com.sdsdiy.logisticsimpl.feign.outeco.TemuOrderFeign;
import com.sdsdiy.logisticsimpl.feign.stat.CommonScheduledTaskFeign;
import com.sdsdiy.logisticsimpl.manager.CarriageDeclarationInfoMapperManager;
import com.sdsdiy.logisticsimpl.service.*;
import com.sdsdiy.logisticsimpl.service.support.impl.TemuOnlineServiceImpl;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.constant.event.message.admin.LogisticsLabelProgressMessage;
import com.sdsdiy.orderapi.constant.event.message.system.SystemFailGenerateWaybillNumberMessage;
import com.sdsdiy.orderapi.constant.event.message.system.SystemSuccessGenerateWaybillNumberMessage;
import com.sdsdiy.orderapi.dto.OrderDTO;
import com.sdsdiy.orderapi.dto.order.OrderCarriageUpdateParam;
import com.sdsdiy.orderdata.dto.parcel.OrderParcelDTO;
import com.sdsdiy.orderdata.util.TemuUtil;
import com.sdsdiy.outecodata.dto.temu.TemuOrderDetailDto;
import com.sdsdiy.statdata.dto.task.CommonScheduledTaskCreateDTO;
import com.sdsdiy.statdata.dto.task.CommonScheduledTaskDTO;
import com.sdsdiy.statdata.dto.task.CommonScheduledTaskDeleteDTO;
import com.sdsdiy.statdata.dto.task.CommonScheduledTaskQueryDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.sdsdiy.common.base.constant.BasePoConstant.NO;
import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.orderapi.constant.OrderProgressTypeConstant.LOGISTICS_LABEL_PDF_NO_MANIFEST;

/**
 * <AUTHOR>
 * @date 2025/5/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemuTailChannelService {
    private final TemuOnlineServiceImpl temuOnlineService;
    private final TenantLogisticsService tenantLogisticsService;
    private final CarriageNoRecodeService carriageNoRecodeService;
    private final CarriageDeclarationInfoService carriageDeclarationInfoService;
    private final OrderParcelFeign orderParcelFeign;
    private final OrderFeign orderFeign;
    private final TemuOrderFeign temuOrderFeign;
    private final OrderTailLogisticsFeign orderTailLogisticsFeign;
    private final CommonScheduledTaskFeign commonScheduledTaskFeign;
    private final LogisticsChannelService logisticsChannelService;
    private final OrderCarriageFeign orderCarriageFeign;
    private final CarriageDeclarationInfoMapperManager carriageDeclarationInfoMapperManager;
    private final RocketMQTemplate rocketMQTemplate;
    private final CarriageService carriageService;
    private final OrderProgressManagerFeign orderProgressManagerFeign;
    public final static String TAIL_EXTRA_INFO = "tailExtraInfo";

    public void tailChannelTask() {
        // 申请运单号
        List<CommonScheduledTaskDTO> applyTaskList = this.commonScheduledTaskFeign.queryTask(new CommonScheduledTaskQueryDTO()
                .setDataType(ScheduledTaskConstant.DataType.TEMU_TAIL_APPLY_CARRIAGE_NO)
                .setMaxVersion(3)
                .setLimitNum(200)
                .setUpdateNextExecuteTime(true));
        log.info("temuTailChannelTask applyTaskList:{}", applyTaskList.size());
        applyTaskList.forEach(i -> this.rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_LOGISTICS
                , LogisticsTagConst.TEMU_TAIL_APPLY_CARRIAGE_NO_TAG, new TemuTailApplyCarriageNoMessageDTO(i.getId())));
        // 查询结果
        List<CommonScheduledTaskDTO> resultTaskList = this.commonScheduledTaskFeign.queryTask(new CommonScheduledTaskQueryDTO()
                .setDataType(ScheduledTaskConstant.DataType.TEMU_TAIL_CARRIAGE_NO_RESULT)
                .setMaxVersion(30)
                .setLimitNum(200)
                .setUpdateNextExecuteTime(true));
        log.info("temuTailChannelTask resultTaskList:{}", resultTaskList.size());
        resultTaskList.forEach(i -> this.rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_LOGISTICS
                , LogisticsTagConst.TEMU_TAIL_CARRIAGE_NO_RESULT_TAG, new BaseIdAndNameDTO(i.getId())));
        // 同步给物流
        List<CommonScheduledTaskDTO> syncTaskList = this.commonScheduledTaskFeign.queryTask(new CommonScheduledTaskQueryDTO()
                .setDataType(ScheduledTaskConstant.DataType.SYNC_TAIL_CARRIAGE_NO)
                .setMaxVersion(10).setTimeFactor(2)
                .setLimitNum(100)
                .setUpdateNextExecuteTime(true));
        log.info("temuTailChannelTask syncTaskList:{}", syncTaskList.size());
        List<Long> delTaskIds = new ArrayList<>();
        syncTaskList.forEach(i -> {
            boolean taskFinish = this.syncTail2Logistics(Long.parseLong(i.getDataId()), i.getVersion());
            if (i.getVersion() >= 10) {
                taskFinish = true;
            }
            if (taskFinish) {
                delTaskIds.add(i.getId());
            }
        });
        if (!delTaskIds.isEmpty()) {
            this.commonScheduledTaskFeign.deleteTask(new CommonScheduledTaskDeleteDTO(delTaskIds));
        }
    }

    public void applyCarriageNoAndUpdateParcel(TemuTailApplyCarriageNoReqDTO reqDTO) {
        this.orderTailLogisticsFeign.updateOrderTailChannelId(reqDTO);
        this.applyCarriageNoHandler(reqDTO.getOrderParcelId(), true, null);
    }

    public void applyCarriageNoHandler(Long orderParcelId, boolean checkTemuTime, Boolean isNeedScanForm) {
        OrderParcelDTO parcelDTO = this.orderParcelFeign.findDtoByParcelId(orderParcelId, "tailLogisticsChannelId,logisticsId");
        if (parcelDTO == null || !NumberUtils.greaterZero(parcelDTO.getTailLogisticsChannelId())) {
            return;
        }
        CarriageNoRecode recode = this.carriageNoRecodeService.getOneByOrderIdAndOrderParcelId(parcelDTO.getOrderId(), parcelDTO.getId());
        if (recode == null || CarriageNoRecodeConstant.STATUS_SUCCESS.equals(recode.getTransferCarriageStatus())) {
            return;
        }
        OrderDTO orderDTO = this.orderFeign.findById(parcelDTO.getOrderId(), "outOrderNo");
        if (orderDTO == null) {
            return;
        }
//        if (checkTemuTime) {
//            // 校验temu时间
//            TemuOrderDetailDto detailDto = this.temuOrderFeign.getOrderBaseInfo(orderDTO.getOutOrderNo());
//            if (detailDto != null) {
//                long earliestTime = TemuUtil.getEarliestTimeBuyShippingLabel(JSON.parseObject(detailDto.getOrderJson())) * 1000;
//                if (earliestTime > 0 && earliestTime > System.currentTimeMillis()) {
//                    Assert.wrong("该订单为Y2订单，当前时间小于该打印面单时间，请在"
//                            + TimeUtil.format(earliestTime, null) + "后再获取");
//                }
//            }
//        }

        OrderCarriageUpdateParam carriageUpdateParam = new OrderCarriageUpdateParam();
        CarriageNoRecode updateRecode = new CarriageNoRecode();
        try {
            LogisticsChannelRespDto tailChannel = this.logisticsChannelService.getDtoById(parcelDTO.getTailLogisticsChannelId());
            Assert.validateNull(tailChannel, "尾程物流渠道数据异常");
            CarriageNoRepDto carriageNoRepDto = this.carriageDeclarationInfoService.getReqDtoById(recode.getDeclarationInfoId());
            carriageNoRepDto.setExtendNo(recode.getExtendNo());
            carriageNoRepDto.setOutOrderNo(orderDTO.getOutOrderNo());
            CarriageNoRespDto tailRespDto = this.temuOnlineService.temuApplyCarriageNo(carriageNoRepDto, tailChannel);
            JSONObject extraInfo;
            if (JSONUtil.isJsonObj(recode.getExtraInfo())) {
                extraInfo = JSON.parseObject(recode.getExtraInfo());
            } else {
                extraInfo = new JSONObject();
            }
            extraInfo.put(TAIL_EXTRA_INFO, tailRespDto.getExtraInfo());
            updateRecode.setExtraInfo(extraInfo.toJSONString());
            updateRecode.setIsImmediatelyScanForm(Boolean.TRUE.equals(tailRespDto.getImmediatelyScanForm()) ? 1 : 0);
            if (isNeedScanForm != null) {
                if (isNeedScanForm) {
                    updateRecode.setIsImmediatelyScanForm(YES);
                } else {
                    updateRecode.setIsImmediatelyScanForm(NO);
                }
            }
            // 创建查询结果任务
            this.commonScheduledTaskFeign.createTask(CommonScheduledTaskCreateDTO
                    .ofOne(ScheduledTaskConstant.DataType.TEMU_TAIL_CARRIAGE_NO_RESULT, orderParcelId)
                    .setExcludeExists(true));
            carriageUpdateParam.setTransferCarriageNo(tailRespDto.getCarriageNo());
            carriageUpdateParam.setTransferCarriageStatus(CarriageNoRecodeConstant.STATUS_STAY);
            carriageUpdateParam.setTransferCarriageMsg("");
            carriageUpdateParam.setScanFormStatus(tailRespDto.getScanFormStatus());
            if (tailRespDto.getImmediatelyScanForm() != null &&
                    tailRespDto.getImmediatelyScanForm() &&
                    ScanFormStatusEnum.PENDING.getCode().equals(tailRespDto.getScanFormStatus())) {
                rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_LOGISTICS, LogisticsTagConst.EVENT_CARRIAGE_SCAM_FORM, String.valueOf(recode.getId()));
            }
        } catch (Exception e) {
            ExceptionUtils.printErrOneLine(e);
            carriageUpdateParam.setTransferCarriageStatus(CarriageNoRecodeConstant.STATUS_FAIL);
            carriageUpdateParam.setTransferCarriageMsg(StrUtil.subPre(ExceptionUtils.getFeignExceptionMessage(e), 1000));
            // 订单进度
            this.sendOrderProgressMessage(parcelDTO, recode, carriageUpdateParam, null);
        } finally {
            updateRecode.setId(recode.getId());
            updateRecode.setTransferCarriageNo(carriageUpdateParam.getTransferCarriageNo());
            updateRecode.setTransferCarriageStatus(carriageUpdateParam.getTransferCarriageStatus());
            updateRecode.setTransferCarriageMsg(carriageUpdateParam.getTransferCarriageMsg());
            this.carriageNoRecodeService.updatePo(updateRecode);
            this.orderCarriageFeign.updateByParcelId(parcelDTO.getOrderId(), parcelDTO.getId(), carriageUpdateParam);
        }
    }


    public boolean carriageNoResultAndLabelHandler(Long orderParcelId, Integer retryNum) {
        OrderParcelDTO parcelDTO = this.orderParcelFeign.findDtoByParcelId(orderParcelId, "tailLogisticsChannelId,logisticsId");
        if (parcelDTO == null) {
            return true;
        }
        CarriageNoRecode recode = this.carriageNoRecodeService.getOneByOrderIdAndOrderParcelId(parcelDTO.getOrderId(), parcelDTO.getId());
        if (recode == null || CarriageNoRecodeConstant.STATUS_SUCCESS.equals(recode.getTransferCarriageStatus())) {
            return true;
        }
        retryNum = CompareUtils.aElseB(retryNum, 0);
        OrderCarriageUpdateParam carriageUpdateParam = new OrderCarriageUpdateParam();
        Integer transferCarriageStatus = CarriageNoRecodeConstant.STATUS_STAY;
        CarriageNoRespDto respDto = new CarriageNoRespDto();
        try {
            respDto = this.getCarriageNoResultAndLabel(recode);

            transferCarriageStatus = respDto.getTransferCarriageStatus();
            carriageUpdateParam.setTransferCarriageNo(respDto.getTransferCarriageNo());
            carriageUpdateParam.setTransferCarriageLabel(respDto.getTransferCarriageLabel());
            carriageUpdateParam.setTransferCarriageMsg("");
        } catch (Exception e) {
            ExceptionUtils.printErrOneLine(e);
            transferCarriageStatus = CarriageNoRecodeConstant.STATUS_FAIL;
            carriageUpdateParam.setTransferCarriageMsg(StrUtil.subPre(ExceptionUtils.getFeignExceptionMessage(e), 1000));
        } finally {
            if (CarriageNoRecodeConstant.STATUS_STAY.equals(transferCarriageStatus) && retryNum > 20) {
                transferCarriageStatus = CarriageNoRecodeConstant.STATUS_FAIL;
                carriageUpdateParam.setTransferCarriageMsg(StrUtil.isBlank(carriageUpdateParam.getTransferCarriageNo())
                        ? "temu下call结果查询失败" : "temu面单查询失败");
            }
            if (CarriageNoRecodeConstant.isUSPSNoScanFormErrorMsg(carriageUpdateParam.getTransferCarriageMsg())) {
                transferCarriageStatus = CarriageNoRecodeConstant.STATUS_FAIL;
            }
            carriageUpdateParam.setTransferCarriageStatus(transferCarriageStatus);
            CarriageNoRecode updateRecode = new CarriageNoRecode();
            updateRecode.setId(recode.getId());
            updateRecode.setTransferCarriageNo(carriageUpdateParam.getTransferCarriageNo());
            updateRecode.setTransferCarriageLabel(carriageUpdateParam.getTransferCarriageLabel());
            updateRecode.setTransferCarriageStatus(carriageUpdateParam.getTransferCarriageStatus());
            updateRecode.setTransferCarriageMsg(carriageUpdateParam.getTransferCarriageMsg());
            this.carriageNoRecodeService.updatePo(updateRecode);
            this.orderCarriageFeign.updateByParcelId(recode.getOrderId(), recode.getOrderParcelId(), carriageUpdateParam);
        }
        if (CarriageNoRecodeConstant.STATUS_SUCCESS.equals(transferCarriageStatus)) {
            // 创建同步尾程运单到急速的任务
            this.commonScheduledTaskFeign.createTask(CommonScheduledTaskCreateDTO
                    .ofOne(ScheduledTaskConstant.DataType.SYNC_TAIL_CARRIAGE_NO, recode.getId())
                    .setExcludeExists(true));
            this.rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_LOGISTICS
                    , LogisticsTagConst.TAIL_APPLY_CARRIAGE_RESULT_TAG
                    , new CarriageNoEventMessage().setOrderId(parcelDTO.getOrderId()).setStatus(transferCarriageStatus).setIsTailCarriageNo(Boolean.TRUE));
        }
        // 订单进度
        this.sendOrderProgressMessage(parcelDTO, recode, carriageUpdateParam, respDto.getOrderProgressType());
        // 成功、失败都删除任务不再继续
        return !CarriageNoRecodeConstant.STATUS_STAY.equals(transferCarriageStatus);
    }

    private void sendOrderProgressMessage(OrderParcelDTO parcelDTO, CarriageNoRecode recode, OrderCarriageUpdateParam carriageUpdateParam, String orderProgressType) {
        Integer transferCarriageStatus = carriageUpdateParam.getTransferCarriageStatus();
        if (CarriageNoRecodeConstant.STATUS_SUCCESS.equals(transferCarriageStatus)) {
            SystemSuccessGenerateWaybillNumberMessage message = new SystemSuccessGenerateWaybillNumberMessage();
            message.setOrderId(parcelDTO.getOrderId());
            message.setOrderParcelName(parcelDTO.getParcelName());
            message.setOrderParcelId(parcelDTO.getId());
            message.setEid(parcelDTO.getOrderId());
            message.setSendingTime(new Date());
            message.setOperatorUid(0L);
            message.setLogisticsId(parcelDTO.getLogisticsId());
            message.setLogisticsNo(recode.getCarriageNo());
            message.setTransferCarriageNo(carriageUpdateParam.getTransferCarriageNo());
            message.setIsTransferType(true);
            message.setOrderProgressType(orderProgressType);
            message.setTailLogisticsChannelId(parcelDTO.getTailLogisticsChannelId());
            this.rocketMQTemplate.sendNormal(RocketMqTopicConst.ORDER_PROGRESS_TOPIC
                    , OrderProgressConstant.SYSTEM_SUCCESS_GENERATE_WAYBILL_NUMBER
                    , message);
        } else if (CarriageNoRecodeConstant.STATUS_FAIL.equals(transferCarriageStatus)) {
            if (CarriageNoRecodeConstant.isUSPSNoScanFormErrorMsg(carriageUpdateParam.getTransferCarriageMsg())) {
                LogisticsLabelProgressMessage labelProgressMessage = new LogisticsLabelProgressMessage();
                labelProgressMessage.setOrderId(parcelDTO.getOrderId());
                labelProgressMessage.setErrorMsg("");
                labelProgressMessage.setSuccess(false);
                labelProgressMessage.setOrderProgressType(LOGISTICS_LABEL_PDF_NO_MANIFEST);
                orderProgressManagerFeign.sendLogisticsLabelMsg(labelProgressMessage);
                return;
            }
            SystemFailGenerateWaybillNumberMessage message = new SystemFailGenerateWaybillNumberMessage();
            message.setOrderId(parcelDTO.getOrderId());
            message.setOrderParcelName(parcelDTO.getParcelName());
            message.setOrderParcelId(parcelDTO.getId());
            message.setEid(parcelDTO.getOrderId());
            message.setSendingTime(new Date());
            message.setOperatorUid(0L);
            message.setReasonsFailure(carriageUpdateParam.getTransferCarriageMsg());
            message.setIsTransferType(true);
            this.rocketMQTemplate.sendNormal(RocketMqTopicConst.ORDER_PROGRESS_TOPIC
                    , OrderProgressConstant.SYSTEM_FAIL_GENERATE_WAYBILL_NUMBER
                    , message);
        }

    }

    public CarriageNoRespDto getCarriageNoResultAndLabel(CarriageNoRecode recode) {
        CarriageNoRespDto respDto = new CarriageNoRespDto();
        JSONObject extraInfo = JSON.parseObject(recode.getExtraInfo());
        String tailExtraInfo = extraInfo.getString(TAIL_EXTRA_INFO);
        // 尾程运单
        String transferCarriageNo = recode.getTransferCarriageNo();
        if (StrUtil.isBlank(transferCarriageNo)) {
            CarriageNoRespDto tailNoResp = this.temuOnlineService.syncTemuCarriageNo(recode.getOrderId(), tailExtraInfo);
            respDto.setOrderProgressType(tailNoResp.getOrderProgressType());
            if (!CarriageNoRecodeConstant.STATUS_SUCCESS.equals(tailNoResp.getStatus())
                    || StrUtil.isBlank(tailNoResp.getCarriageNo())) {
                respDto.setTransferCarriageStatus(CarriageNoRecodeConstant.STATUS_STAY);
                return respDto;
            }
            transferCarriageNo = tailNoResp.getCarriageNo();
        }
        // 尾程面单
        String transferCarriageLabel = recode.getTransferCarriageLabel();
        if (StrUtil.isBlank(transferCarriageLabel)) {
            CarriageNoRespDto tailLabelResp = this.temuOnlineService.temuSheetLabel(recode.getOrderId(), tailExtraInfo, recode.getExtendNo());
            respDto.setOrderProgressType(tailLabelResp.getOrderProgressType());
            if (!CarriageNoRecodeConstant.SHEET_LABEL_STATUS_SUCCESS.equals(tailLabelResp.getSheetLabelStatus())
                    || StrUtil.isBlank(tailLabelResp.getLabelUrl())) {
                respDto.setTransferCarriageStatus(CarriageNoRecodeConstant.STATUS_STAY);
                return respDto;
            }
            transferCarriageLabel = tailLabelResp.getLabelUrl();
        }
        respDto.setTransferCarriageNo(transferCarriageNo);
        respDto.setTransferCarriageLabel(transferCarriageLabel);
        respDto.setTransferCarriageStatus(CarriageNoRecodeConstant.STATUS_SUCCESS);
        return respDto;
    }

    public boolean syncTail2Logistics(Long recodeId, Integer retryNum) {
        CarriageNoRecode recode = this.carriageNoRecodeService.getById(recodeId);
        if (recode == null
                // 头程失败
                || CarriageNoRecodeConstant.STATUS_FAIL.equals(recode.getStatus())
                // 尾程未成功
                || !CarriageNoRecodeConstant.STATUS_SUCCESS.equals(recode.getTransferCarriageStatus())) {
            return true;
        }
        log.info("syncTail2Logistics :{},{}", recode.getCarriageNo(), retryNum);
        TenantLogisticsRespDto logistics = tenantLogisticsService.getLogisticsAndChannel(recode.getLogisticsId());
        try {
            this.carriageService.pushTailNoAndLabel(recode, logistics);
            return true;
        } catch (Exception e) {
            ExceptionUtils.printErrOneLine(e);
            if (retryNum >= 10) {
                this.sendDingDing(logistics, recode, ExceptionUtils.getFeignExceptionMessage(e));
                return true;
            }
            return false;
        }
    }

    private void sendDingDing(TenantLogisticsRespDto logistics, CarriageNoRecode recode, String reason) {
        String name = logistics.getLogisticsChannel().getName();
        String msg = StrUtil.format("订单号：{}\n头程运单：{}\n尾程运单：{}\n尾程面单：{}\n失败原因：{}"
                , recode.getOrderNo(), recode.getCarriageNo()
                , recode.getTransferCarriageNo(), recode.getTransferCarriageLabel()
                , reason);
        DingDingUtil.sendDDMessage(DingDingUtil.TAIL_LOGISTICS_ROBOT_URL
                , DingDingUtil.formatContent(msg, name + " 尾程同步失败")
                , false);
    }

    public void temuTailApplyCarriageNoTask(CarriageNoRepDto dto) {
        // 从订单json里解析最早面单时间，创建尾程运单任务
        OrderDTO orderDTO = this.orderFeign.findById(dto.getId(), "outOrderNo");
        TemuOrderDetailDto detailDto = this.temuOrderFeign.getOrderBaseInfo(orderDTO.getOutOrderNo());
        if (detailDto != null) {
            long earliestTime = TemuUtil.getEarliestTimeBuyShippingLabel(JSON.parseObject(detailDto.getOrderJson())) * 1000;
            if (earliestTime <= 0) {
                earliestTime = TimeUnit.MINUTES.toMillis(10);
            }
            this.commonScheduledTaskFeign.createTask(CommonScheduledTaskCreateDTO
                    .ofOne(ScheduledTaskConstant.DataType.TEMU_TAIL_APPLY_CARRIAGE_NO
                            , dto.getOrderParcelId(), null, earliestTime)
                    .setExcludeExists(true));
        }
    }
}
