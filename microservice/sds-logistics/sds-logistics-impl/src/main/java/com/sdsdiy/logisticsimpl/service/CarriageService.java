package com.sdsdiy.logisticsimpl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.enums.CountryEnum;
import com.sdsdiy.common.base.enums.EuropeUnionEnum;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.enums.ScanFormStatusEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.common.consts.MerchantStoreTypeEnum;
import com.sdsdiy.core.base.util.BeanTool;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.logistics.LogisticsTagConst;
import com.sdsdiy.core.redis.lock.annotation.DistributedLock;
import com.sdsdiy.core.util.ExceptionUtils;
import com.sdsdiy.logisticsapi.constant.CarriageNoRecodeConstant;
import com.sdsdiy.logisticsapi.constant.LogisticsConstant;
import com.sdsdiy.logisticsapi.dto.*;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum;
import com.sdsdiy.logisticsapi.enums.TrackMilestoneEnum;
import com.sdsdiy.logisticsdata.dto.CarriageScanFormDto;
import com.sdsdiy.logisticsdata.dto.CarriageScanFormParam;
import com.sdsdiy.logisticsdata.dto.temu.TemuTailApplyCarriageNoMessageDTO;
import com.sdsdiy.logisticsimpl.entity.po.*;
import com.sdsdiy.logisticsimpl.feign.MerchantStoreFeign;
import com.sdsdiy.logisticsimpl.feign.OrderCarriageFeign;
import com.sdsdiy.logisticsimpl.feign.OrderFeign;
import com.sdsdiy.logisticsimpl.feign.order.OrderExtendInfoFeign;
import com.sdsdiy.logisticsimpl.feign.order.OrderParcelFeign;
import com.sdsdiy.logisticsimpl.manager.CarriageSyncRecordManager;
import com.sdsdiy.logisticsimpl.service.support.LogisticsSupport;
import com.sdsdiy.logisticsimpl.service.support.LogisticsSupportFactory;
import com.sdsdiy.logisticsimpl.service.support.dto.*;
import com.sdsdiy.logisticsimpl.service.support.impl.TemuOnlineServiceImpl;
import com.sdsdiy.logisticsimpl.service.temu.TemuTailChannelService;
import com.sdsdiy.logisticsimpl.service.track.LogisticsProviderTrackService;
import com.sdsdiy.logisticsimpl.service.track.Tracking17ToCarriageNoRecodeService;
import com.sdsdiy.logisticsimpl.service.track.Tracking51Service;
import com.sdsdiy.orderapi.constant.OrderOriginType;
import com.sdsdiy.orderapi.dto.OrderDTO;
import com.sdsdiy.orderapi.dto.order.OrderCarriageUpdateParam;
import com.sdsdiy.orderapi.enums.OrderOriginEnum;
import com.sdsdiy.orderdata.dto.parcel.OrderParcelDTO;
import com.sdsdiy.userapi.dto.base.MerchantStoreRespDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.common.base.enums.EuropeUnionEnum.NL;
import static com.sdsdiy.logisticsapi.constant.CarriageNoRecodeConstant.*;
import static com.sdsdiy.logisticsapi.constant.LogisticsConstant.IOSS_TYPE_MUST;
import static com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum.SERVICE_PROVIDER_YZ;

/**
 * @author: bin_lin
 * @date: 2021/2/26 10:45
 * @desc: 物流功能
 */
@Slf4j
@Service
public class CarriageService {
    @Resource
    private TenantLogisticsService tenantLogisticsService;
    @Resource
    private CarriageNoRecodeService carriageNoRecodeService;
    @Resource
    private CarriageSheetLabelService carriageSheetLabelService;
    @Resource
    private OrderCarriageFeign orderCarriageFeign;
    @Resource
    private Tracking51Service tracking51Service;
    @Resource
    private Tracking17ToCarriageNoRecodeService tracking17ToCarriageNoRecodeService;
    @Resource
    private LogisticsSupportFactory logisticsSupportFactory;
    @Resource
    private CarriageRequestHistoryService requestHistoryService;
    @Resource
    private CarriageDeclarationInfoService carriageDeclarationInfoService;
    @Resource
    private OrderApplyCarriageNumService orderApplyCarriageNumService;
    @Resource
    private CustomLogisticOrderService customLogisticOrderService;
    @Resource
    private LogisticsProviderTrackService logisticsProviderTrackService;
    @Resource
    private CarriageSyncRecordManager carriageSyncRecordManager;
    @Resource
    private RocketMQTemplate rocketMQTemplate;
    @Resource
    private OrderFeign orderFeign;
    @Resource
    private OrderExtendInfoFeign orderExtendInfoFeign;
    @Resource
    private TenantIssuingBayLogisticsAccountService tenantIssuingBayLogisticsAccountService;
    @Resource
    private TenantServiceProviderAccountService tenantServiceProviderAccountService;
    @Resource
    private TenantAddressService tenantAddressService;
    @Resource
    private MerchantStoreFeign merchantStoreFeign;
    @Resource
    private CountryCodeMappingService countryCodeMappingService;
    @Resource
    private CarriageScanFormRecordService carriageScanFormRecordService;
    @Resource
    private TemuTailChannelService temuTailChannelService;
    @Resource
    private OrderParcelFeign orderParcelFeign;
    @Resource
    private TemuOnlineServiceImpl temuOnlineService;

    public void validate(ApplyCarriageNoParam param) {
        CarriageNoRepDto repDto = carriageDeclarationInfoService.getCarriageNoReqByParam(param);
        validate(repDto);
    }

    private void validate(CarriageNoRepDto dto) {
        TenantLogisticsRespDto logisticsAndChannel = tenantLogisticsService.getLogisticsAndChannel(dto.getLogisticsId());
        LogisticsSupport logisticsSupport = getLogisticsSupport(logisticsAndChannel.getId(), logisticsAndChannel);
        logisticsSupport.validate(dto, logisticsAndChannel, requestHistoryService.getEventNo());
    }

    public CarriageNoRespDto applyCarriageNo(ApplyCarriageNoParam param) {
        CarriageNoRepDto repDto = carriageDeclarationInfoService.getCarriageNoReqByParam(param);
        param.setExtendNo(orderApplyCarriageNumService.getExtendNo(param.getNo(), param.getId()));
        CarriageNoRecode oldCarriageNoRecode = carriageNoRecodeService.getOneByOrderIdAndOrderParcelId(param.getId(), param.getOrderParcelId());
        if (oldCarriageNoRecode != null && oldCarriageNoRecode.getCarriageVersion() >= param.getCarriageVersion() && !Objects.equals(oldCarriageNoRecode.getStatus(), STATUS_STAY)) {
            throw new BusinessException("版本号数据异常 新版本号小等于旧版本号 orderId : " + param.getId());
        }
        CarriageNoRecode noRecode = this.save(param, repDto.getAddress());
        repDto.setExtendNo(param.getExtendNo());
// 处理特殊的线上物流
        ArrayList<Long> specialProviderIdList = LogisticsConstant.specialProviderIdList();
        TenantLogistics tenantLogistics = tenantLogisticsService.getOneById(param.getLogisticsId());
        if (specialProviderIdList.contains(tenantLogistics.getServiceProviderId())) {
            return null;
        }
        return applyCarriageNo(repDto, noRecode);
    }

    public CarriageNoRecode save(ApplyCarriageNoParam param, CarriageNoAddressDto carriageNoAddressDto) {
        CarriageNoRecode po = new CarriageNoRecode();
        po.setMerchantStoreId(param.getMerchantStoreId());
        po.setCountryCode(carriageNoAddressDto.getCountryCode());
        po.setOrderStatus(param.getOrderStatus());
        po.setDeclarationInfoId(param.getCarriageDeclarationInfoId());
        po.setOperationMode(param.getOperationMode());
        po.setUpdateUid(param.getUserId());
        po.setCreateUid(param.getUserId());
        po.setMerchantId(param.getMerchantId());
        po.setOrderId(param.getId());
        po.setOrderParcelId(param.getOrderParcelId());
        po.setOrderNo(param.getNo());
        po.setCarriageVersion(param.getCarriageVersion());
        po.setLogisticsId(param.getLogisticsId());
        po.setExtendNo(param.getExtendNo());
        po.setOrderType(param.getOrderType());
        po.setBusinessId(param.getBusinessId());
        po.setEventNo(requestHistoryService.getEventNo());
        if (param.getOrderType() != null && param.getOrderType() == ORDER_TYPE_RETURN) {
            po.setTrackStatus(TRACK_STATUS_DELIVER_GOODS);
        }
        if (StringUtils.isBlank(po.getExtraInfo())) {
            po.setExtraInfo("");
        }
        carriageNoRecodeService.saveAndAbandon(po);
        return po;
    }

    private CarriageNoRespDto applyCarriageNo(CarriageNoRepDto dto, CarriageNoRecode po) {
        CarriageNoRecode updatePo = carriageNoRecodeService.getUpdateInitPo(po);
        CarriageNoRespDto respDto = new CarriageNoRespDto();
        try {
            this.paramCheck(dto);
            this.checkAddressEmailRequired(dto);
            dto.setCarriageNoRecodeId(updatePo.getId());
            respDto = this.applyCarriageNo(dto, po.getEventNo());
            updatePo.setCarriageNo(respDto.getCarriageNo());
            updatePo.setStatus(respDto.getStatus());
            updatePo.setSheetLabelStatus(respDto.getSheetLabelStatus());
            updatePo.setExtraInfo(respDto.getExtraInfo());
            updatePo.setLabelUrl(respDto.getLabelUrl());
            updatePo.setLogisticsBayAccountConfigId(respDto.getLogisticsBayAccountConfigId());
            updatePo.setTransferCarriageNo(respDto.getTransferCarriageNo());
            updatePo.setTransferCarriageLabel(respDto.getTransferCarriageLabel());
            updatePo.setLatestTrackMilestoneTime(new Date());
            updatePo.setLatestTrackMilestone(STATUS_SUCCESS.equals(respDto.getStatus()) ? TrackMilestoneEnum.NO_RECORD.name() : TrackMilestoneEnum.NONE.name());
            updatePo.setIsImmediatelyScanForm(Boolean.TRUE.equals(respDto.getImmediatelyScanForm()) ? 1 : 0);
            carriageNoRecodeService.updateAndSaveLabel(updatePo, respDto);
        } catch (BusinessException b) {
            String meg = b.getMessage().length() > 1000 ? b.getMessage().substring(0, 999) : b.getMessage();
            updatePo.setErrorInfo(meg);
            carriageNoRecodeService.updateById(updatePo);
            throw b;
        } catch (Exception e) {
            String errorMsg = "物流系统内部异常" + ExceptionUtils.getFeignExceptionMessage(e);
            if (e instanceof ResourceAccessException) {
                errorMsg = "请求超时,请稍后再试!!";
            }
            updatePo.setErrorInfo(errorMsg);
            carriageNoRecodeService.updateById(updatePo);
            ExceptionPrintUtil.printErrOneLine(e);
            throw new BusinessException(errorMsg);
        } finally {
            if (STATUS_SYNCING.equals(updatePo.getStatus()) && !YES.equals(respDto.getNotGenCarriageSyncRecord())) {
                LocalDateTime now = LocalDateTime.now().withSecond(0).withNano(0);
                if (NumberUtils.greaterZero(respDto.getSyncCarriageSyncRecordPlusMinutes())) {
                    now = now.plusMinutes(respDto.getSyncCarriageSyncRecordPlusMinutes());
                }
                this.carriageSyncRecordManager.genRecord(updatePo.getId(), now);
            }
            carriageNoRecodeService.sendCarriageEvent(updatePo, dto, respDto.getOrderProgressType(), respDto.getScanFormStatus(), MESSAGE_ORIGINAL_CARRIAGE);
        }
        return respDto;
    }

    private void checkAddressEmailRequired(CarriageNoRepDto dto) {
        boolean isUsAddress = dto.getAddress().getCountry().equals(CountryEnum.US.getCode());
        if (!isUsAddress) {
            return;
        }
        Long orderId = dto.getId();
        OrderDTO orderDTO = orderFeign.findById(orderId, "origin,merchantStorePlatformCode,merchantStoreId,issuingBayId");
        if (null == orderDTO) {
            return;
        }
        List<MerchantStoreRespDto> merchantStoreRespDtos = merchantStoreFeign.findByIds(IdsSearchHelper.of(orderDTO.getMerchantStoreId(), "id,merchantStorePlatformCode,isPopChoice,type"));
        Map<Long, MerchantStoreRespDto> idMerchantStoreMap = ListUtil.toMap(MerchantStoreRespDto::getId, merchantStoreRespDtos);
        MerchantStoreRespDto merchantStoreRespDto = idMerchantStoreMap.get(orderDTO.getMerchantStoreId());
        boolean needDealEmail = isNeedDealEmail(orderId, orderDTO, merchantStoreRespDto);
        if (!needDealEmail) {
            return;
        }
        TenantAddress tenantAddress = getTenantAddress(dto.getLogisticsId(), orderDTO.getIssuingBayId());
        if (null != tenantAddress) {
            boolean isCn = tenantAddress.getCountry().equals(CountryEnum.CN.getCode());
            if (isCn) {
                Assert.validateBlank(dto.getAddress().getEmail(), "收件人邮箱不能为空");
            }
        }
    }

    private boolean isNeedDealEmail(Long orderId, OrderDTO orderDTO, MerchantStoreRespDto merchantStoreRespDto) {
        Boolean isFba = OrderOriginEnum.isFba(orderDTO.getOrigin());
        boolean isTemuPopChoice = null != merchantStoreRespDto &&
                merchantStoreRespDto.getMerchantStorePlatformCode().equals(MerchantStorePlatformEnum.TEMU.getCode()) &&
                MerchantStoreTypeEnum.isSemiOrLocal(merchantStoreRespDto.getType());
        Boolean isJitOrder = orderExtendInfoFeign.isJitOrder(orderId);
        boolean sellFastAutoImport = OrderOriginType.AUTO_IMPORT.getValue().equals(orderDTO.getOrigin()) && orderDTO.getMerchantStorePlatformCode().equals(MerchantStorePlatformEnum.SELL_FAST.getCode());

        return !isFba && !isJitOrder && !isTemuPopChoice && !sellFastAutoImport;
    }

    @Nullable
    private TenantAddress getTenantAddress(Long logisticsId, Long issuingBayId) {
        if (!NumberUtils.greaterZero(logisticsId) || !NumberUtils.greaterZero(issuingBayId)) {
            return null;
        }
        TenantAddress tenantAddress = null;
        TenantIssuingBayLogisticsAccount tenantIssuingBayLogisticsAccount = tenantIssuingBayLogisticsAccountService.getByLogisticsIdAndIssuingBayId(logisticsId, issuingBayId, 1);
        if (null != tenantIssuingBayLogisticsAccount) {
            TenantServiceProviderAccount tenantServiceProviderAccount = tenantServiceProviderAccountService.getById(tenantIssuingBayLogisticsAccount.getTenantServiceProviderAccountId());
            if (null != tenantServiceProviderAccount) {
                tenantAddress = tenantAddressService.getById(tenantServiceProviderAccount.getTenantAddressId());
            }
        }
        return tenantAddress;
    }

    private void paramCheck(CarriageNoRepDto repDto) {
        TenantLogisticsRespDto logisticsAndChannel = tenantLogisticsService.getLogisticsAndChannel(repDto.getLogisticsId());
        // JIT 物流不用申报价等
        if (logisticsAndChannel == null || LogisticsServiceProviderEnum.SELL_FAST_JIT.equalsNumber(logisticsAndChannel.getServiceProviderId())
                || LogisticsServiceProviderEnum.TEMU_FULLY_MANAGED.equalsNumber(logisticsAndChannel.getServiceProviderId())) {
            return;
        }
        if (CountryEnum.CA.getCode().equals(repDto.getAddress().getCountryCode()) && NumberUtil.compare(repDto.getDeclarePrice(), 14.0D) > 0) {
            throw new BusinessException("加拿大申报总金额大于14美元,请修改后再试");
        }
        if (CountryEnum.US.getCode().equals(repDto.getAddress().getCountryCode()) && NumberUtil.compare(repDto.getDeclarePrice(), 200D) > 0) {
            throw new BusinessException("美国申报总金额大于200美元,请修改后再试");
        }
        if (!EuropeUnionEnum.isEuropeUnion(repDto.getAddress().getCountry())) {
            return;
        }
        if (!IOSS_TYPE_MUST.equals(logisticsAndChannel.getLogisticsChannel().getIossType())) {
            return;
        }
        if (YES.equals(repDto.getIsCollectVat())) {
            return;
        }
        if (StringUtils.isBlank(repDto.getIossTaxNumber())) {
            throw new BusinessException("申报信息里IOSS必填");
        }
        // 荷兰的电子邮箱必填
        if (NL.getCode().equals(repDto.getAddress().getCountryCode()) && SERVICE_PROVIDER_YZ.getNumber().equals(logisticsAndChannel.getServiceProviderId()) && repDto.getIossMail() == null) {
            throw new BusinessException("申报信息里电子邮箱必填");
        }
    }

    private CarriageNoRespDto applyCarriageNo(CarriageNoRepDto dto, String eventNo) {
        TenantLogisticsRespDto logisticsAndChannel = tenantLogisticsService.getLogisticsAndChannel(dto.getLogisticsId());
        if (TenantCommonConstant.isZiguangTenant(logisticsAndChannel.getTenantId())) {
            CarriageNoRespDto carriageNoRespDto = customLogisticOrderService.getCarriageNoResp(dto.getId(), dto.getLogisticsId(), dto);
            if (carriageNoRespDto != null) {
                return carriageNoRespDto;
            }
        }
        LogisticsSupport logisticsSupport = getLogisticsSupport(logisticsAndChannel.getId(), logisticsAndChannel);
        log.info("logistics applyCarriageNo orderNo={} param={}", dto.getNo(), JSONObject.toJSONString(dto));
        return logisticsSupport.applyCarriageNo(dto, logisticsAndChannel, eventNo);
    }

    private String getTenantAddress(CarriageNoRepDto dto, TenantLogisticsRespDto logisticsAndChannel) {
        TenantIssuingBayLogisticsAccount account = tenantIssuingBayLogisticsAccountService.getOne(dto.getLogisticsId(), dto.getIssuingBayId());
        if (account == null) {
            return "";
        }
        Long tenantServiceProviderAccountId = account.getTenantServiceProviderAccountId();
        TenantAddress addresses = tenantServiceProviderAccountService.getAddresses(logisticsAndChannel.getTenantId(), tenantServiceProviderAccountId);
        if (addresses == null) {
            return "";
        }
        return addresses.getCountry();
    }


    public CarriageNoRespDto syncCarriageNo(SyncCarriageNoBO dto, String eventNo) {
        TenantLogisticsRespDto logisticsAndChannel = tenantLogisticsService.getLogisticsAndChannel(dto.getLogisticsId());
        Assert.validateNull(logisticsAndChannel, "未找到对应物流信息！！！");
        LogisticsSupport logisticsSupport = getLogisticsSupport(logisticsAndChannel.getId(), logisticsAndChannel);
        log.info("logistics sync carriage no={}", JSONObject.toJSONString(dto));
        return logisticsSupport.syncCarriageNo(dto, logisticsAndChannel, eventNo);
    }


    public CarriageNoRespDto asyncApplyCarriageNo(ExpressReqDto dto) {
        CarriageNoRecode carriageNoRecode = carriageNoRecodeService.getOneByOrderIdAndVersion(dto.getOrderId(), dto.getOrderParcelId(), dto.getCarriageVersion());
        Assert.validateNull(carriageNoRecode, "未找到对应的运单信息！！");
        if (!CarriageNoRecodeConstant.STATUS_SYNCING.equals(carriageNoRecode.getStatus())) {
            throw new BusinessException("运动号非同步中");
        }
        SyncCarriageNoBO syncCarriageNoBO = BeanTool.copyBean(carriageNoRecode, SyncCarriageNoBO.class);
        CarriageNoRecode updateInitPo = carriageNoRecodeService.getUpdateInitPo(carriageNoRecode);
        CarriageDeclarationInfo info = carriageDeclarationInfoService.getInfoById(carriageNoRecode.getDeclarationInfoId());
        syncCarriageNoBO.setIssuingBayId(info.getIssuingBayId());
        syncCarriageNoBO.setRecode(carriageNoRecode)
                .setDeclarationInfo(info);
        CarriageNoRespDto respDto = new CarriageNoRespDto();
        try {
            respDto = syncCarriageNo(syncCarriageNoBO, carriageNoRecode.getEventNo());
            if (respDto != null) {
                if (CarriageNoRecodeConstant.STATUS_SYNCING.equals(respDto.getStatus())) {
                    return respDto;
                }
                updateInitPo.setIsImmediatelyScanForm(carriageNoRecode.getIsImmediatelyScanForm());
                updateInitPo.setCarriageNo(respDto.getCarriageNo());
                updateInitPo.setStatus(respDto.getStatus());
                updateInitPo.setSheetLabelStatus(respDto.getSheetLabelStatus());
                updateInitPo.setLabelUrl(respDto.getLabelUrl());
                updateInitPo.setLatestTrackMilestoneTime(new Date());
                updateInitPo.setLatestTrackMilestone(STATUS_SUCCESS.equals(respDto.getStatus()) ? TrackMilestoneEnum.NO_RECORD.name() : TrackMilestoneEnum.NONE.name());
                updateInitPo.setTransferCarriageNo(respDto.getTransferCarriageNo());
                updateInitPo.setTransferCarriageLabel(respDto.getTransferCarriageLabel());
            } else {
                respDto = new CarriageNoRespDto();
                respDto.setStatus(STATUS_SYNCING);
            }
        } catch (Exception e) {
            updateInitPo.setErrorInfo("同步运单号异常:" + e.getMessage());
            ExceptionPrintUtil.printErrOneLine("id为{} asyncApply运单号异常:" + carriageNoRecode.getId(), e);
        } finally {
            CarriageNoRepDto reqDto = carriageDeclarationInfoService.getReqDtoById(carriageNoRecode.getDeclarationInfoId());
            // 更改状态存入表中
            carriageNoRecodeService.updateAndSaveLabel(updateInitPo, respDto);
            // 处理成功后发送MQ
            carriageNoRecodeService.sendCarriageEvent(updateInitPo, reqDto, respDto.getOrderProgressType(), respDto.getScanFormStatus(), MESSAGE_ORIGINAL_CARRIAGE);
        }
        return respDto;
    }

    public String getExpressSheetUrl(ExpressReqDto dto) {
        CarriageNoRecode carriageNoRecode = carriageNoRecodeService.getOneByOrderIdAndVersion(dto.getOrderId(), dto.getOrderParcelId(), dto.getCarriageVersion());
        Assert.validateNull(carriageNoRecode, "未找到对应的运单信息！！");
        if (SHEET_LABEL_STATUS_SUCCESS.equals(carriageNoRecode.getSheetLabelStatus())) {
            return carriageNoRecode.getLabelUrl();
        }
        try {
            return getExpressSheetUrlByProvider(carriageNoRecode);
        } catch (Exception e) {
            throw new BusinessException(ExceptionUtils.getFeignExceptionMessage(e));
        }
    }

    public String getExpressSheetUrlByProvider(CarriageNoRecode carriageNoRecode) {
        ExpressSheetLabelDto labelDto = new ExpressSheetLabelDto();
        labelDto.setOrderId(carriageNoRecode.getOrderId());
        labelDto.setCarriageNo(carriageNoRecode.getCarriageNo());
        labelDto.setExtendNo(carriageNoRecode.getExtendNo());
        labelDto.setEventNo(carriageNoRecode.getEventNo());
        labelDto.setExtraInfo(carriageNoRecode.getExtraInfo());
        labelDto.setCountryCode(carriageNoRecode.getCountryCode());
        labelDto.setDeclarationInfoId(carriageNoRecode.getDeclarationInfoId());
        CarriageDeclarationInfo info = carriageDeclarationInfoService.getInfoById(carriageNoRecode.getDeclarationInfoId());
        labelDto.setIssuingBayId(info.getIssuingBayId());
        CarriageNoRespDto respDto = new CarriageNoRespDto();
        respDto.setEventNo(carriageNoRecode.getEventNo());
        try {
            respDto = getExpressSheetUrl(labelDto, carriageNoRecode.getLogisticsId());
            if (respDto.getSheetLabelStatus() == null) {
                respDto.setSheetLabelStatus(SHEET_LABEL_STATUS_FAIL);
            }
        } catch (Exception e) {
            String message = ExceptionUtils.getFeignExceptionMessage(e);
            log.error("eventNo为:{} 异常信息为：{}", carriageNoRecode.getEventNo(), message);
            respDto.setSheetLabelStatus(SHEET_LABEL_STATUS_FAIL);
            throw e;
        } finally {
            carriageNoRecodeService.updateNoAndLabel(carriageNoRecode.getId(), carriageNoRecode.getLogisticsId(), respDto);
            if (SHEET_LABEL_STATUS_SUCCESS.equals(respDto.getSheetLabelStatus())) {
                carriageSheetLabelService.sendLabelEvent(carriageNoRecode, respDto);
            }
        }
        return respDto.getLabelUrl();
    }

    private CarriageNoRespDto getExpressSheetUrl(ExpressSheetLabelDto dto, long logisticsId) {
        LogisticsSupport logisticsSupport = getLogisticsSupport(logisticsId, null);
        return logisticsSupport.expressSheetLabel(dto, logisticsId);
    }


    public List<TrackInfo> queryExpressInfo(ExpressReqDto dto) {
        CarriageNoRecode carriageNoRecode = carriageNoRecodeService.getOneByOrderIdAndVersion(dto.getOrderId(), dto.getOrderParcelId(), dto.getCarriageVersion());
        Assert.validateBool(STATUS_SUCCESS.equals(carriageNoRecode.getStatus()), "运单状态异常！！");
        Assert.validateNull(carriageNoRecode, "未找到对应的运单信息！！");
        return getTrackInfos(carriageNoRecode);
    }

    public List<TrackInfo> getTrackInfos(CarriageNoRecode carriageNoRecode) {
        if (TRACK_METHOD_TRACKING51.equals(carriageNoRecode.getTrackingMode())) {
            return tracking51Service.getResultByTrack51(carriageNoRecode.getCarriageNo(), carriageNoRecode.getEventNo());
        }
        if (TRACK_METHOD_TRACKING17.equals(carriageNoRecode.getTrackingMode())) {
            if (TRACK_METHOD_TRACKING17.equalsIgnoreCase(carriageNoRecode.getTransferTrackingMode())) {
                return tracking17ToCarriageNoRecodeService.trackHeadAndTail(carriageNoRecode);
            } else {
                Long logisticsId = carriageNoRecode.getLogisticsId();
                TenantLogisticsRespDto tenantLogisticsRespDto = tenantLogisticsService.findDtoById(logisticsId, "codeId");
                if ("HanJin".equalsIgnoreCase(tenantLogisticsRespDto.getCodeId()) && StrUtil.isNotBlank(carriageNoRecode.getCarriageNo())) {
                    Map<String,String> hanjinMap = new HashMap<>();
                    String replaceCarriageNo = carriageNoRecode.getCarriageNo().replace("-", "");
                    hanjinMap.put(replaceCarriageNo,carriageNoRecode.getCarriageNo());
                    // 韩进物流单号需要去除-
                    return tracking17ToCarriageNoRecodeService.getOneResult(replaceCarriageNo, carriageNoRecode.getEventNo(),hanjinMap);
                } else {
                    return tracking17ToCarriageNoRecodeService.getOneResult(carriageNoRecode.getCarriageNo(), carriageNoRecode.getEventNo(),null);

                }
            }
        }
        ExpressTrackInfoDto trackInfoDto = new ExpressTrackInfoDto();
        trackInfoDto.setCarriageNo(carriageNoRecode.getCarriageNo());
        trackInfoDto.setExtendNo(carriageNoRecode.getExtendNo());
        trackInfoDto.setLogisticsId(carriageNoRecode.getLogisticsId());
        trackInfoDto.setNo(carriageNoRecode.getOrderNo());
        trackInfoDto.setExtraInfo(carriageNoRecode.getExtraInfo());
        trackInfoDto.setEventNo(carriageNoRecode.getEventNo());
        trackInfoDto.setOrderId(carriageNoRecode.getOrderId());
        trackInfoDto.setCountryCode(carriageNoRecode.getCountryCode());
        CarriageDeclarationInfo info = carriageDeclarationInfoService.getInfoById(carriageNoRecode.getDeclarationInfoId());
        if(info != null){
            trackInfoDto.setIssuingBayId(info.getIssuingBayId());
        }
        TrackDto trackDto = queryExpressInfo(trackInfoDto);
        if (trackDto == null) {
            return Collections.emptyList();
        }
        logisticsProviderTrackService.trackInfoResultHandle(carriageNoRecode, trackInfoDto.getEventNo(), trackDto);
        return trackDto.getTrackInfoList();
    }

    private TrackDto queryExpressInfo(ExpressTrackInfoDto dto) {
        LogisticsSupport logisticsSupport = getLogisticsSupport(dto.getLogisticsId(), null);
        return logisticsSupport.queryExpressTrackInfo(dto);
    }

    public String getTransferCarriageNo(Long orderId, Long orderParcelId) {
        CarriageNoRecode carriageNoRecode = carriageNoRecodeService.getOneByOrderIdAndOrderParcelId(orderId, orderParcelId);
        Assert.validateNull(carriageNoRecode, "未找到对应的运单信息！！");
        return getTransferCarriageNo(carriageNoRecode);
    }

    public String getTransferCarriageNo(CarriageNoRecode carriageNoRecode) {
        CarriageDeclarationInfo info = carriageDeclarationInfoService.getInfoById(carriageNoRecode.getDeclarationInfoId());
        String eventNo = carriageNoRecode.getEventNo();
        TransferCarriageNoDto carriageNoDto = new TransferCarriageNoDto();
        carriageNoDto.setCarriageNo(carriageNoRecode.getCarriageNo());
        carriageNoDto.setExtendNo(carriageNoRecode.getExtendNo());
        carriageNoDto.setEventNo(eventNo);
        carriageNoDto.setIssuingBayId(info.getIssuingBayId());
        String transferCarriageNo = "";
        try {
            transferCarriageNo = getTransferCarriageNo(carriageNoDto, carriageNoRecode.getLogisticsId());
        } catch (Exception e) {
            log.error("eventNo为:{} 异常信息为：{}", eventNo, e.getMessage());
        }
        if (StringUtils.isNotBlank(transferCarriageNo)) {
            carriageNoRecodeService.updateTransferNo(carriageNoRecode.getId(), transferCarriageNo);
            OrderCarriageUpdateParam updateParam = new OrderCarriageUpdateParam();
            updateParam.setTransferCarriageNo(transferCarriageNo);
            orderCarriageFeign.updateByParcelId(carriageNoRecode.getOrderId(), carriageNoRecode.getOrderParcelId(), updateParam);
        }
        return transferCarriageNo;
    }

    private String getTransferCarriageNo(TransferCarriageNoDto dto, long logisticsId) {
        LogisticsSupport logisticsSupport = getLogisticsSupport(logisticsId, null);
        dto.setLogisticsId(logisticsId);
        return logisticsSupport.transferCarriageNo(dto);
    }


    private LogisticsSupport getLogisticsSupport(Long logisticsId, TenantLogisticsRespDto logistics) {
        if (logistics == null) {
            logistics = tenantLogisticsService.getLogisticsAndChannel(logisticsId);
        }
        LogisticsSupport logisticsSupport = logisticsSupportFactory.createLogisticsSupport(logistics.getServiceProviderId());
        Assert.validateNull(logisticsSupport, "物流供应商不支持！！！");
        return logisticsSupport;
    }

    public void confirmCarriage(Long orderId, Long parcelId, String carriageNo) {
        log.info("confirmCarriage orderId = {}, parcelId = {}, carriageNo = {}", orderId, parcelId, carriageNo);
        CarriageNoRecode recode = this.carriageNoRecodeService.getOneByOrderIdAndOrderParcelId(orderId, parcelId);
        Assert.validateNull(recode, "运单记录未找到！");
        // 如果是手动改的物流 没法确认
        if (!Objects.equals(carriageNo, recode.getCarriageNo())) {
            log.info("confirmCarriage parcelId = {}, manual change carriageNo, can't verifyWeight", parcelId);
            return;
        }
        CarriageDeclarationInfo info = carriageDeclarationInfoService.getInfoById(recode.getDeclarationInfoId());
        ConfirmCarriageDto dto = new ConfirmCarriageDto();
        dto.setLogisticsId(recode.getLogisticsId());
        dto.setCarriageNo(recode.getCarriageNo());
        dto.setExtendNo(recode.getExtendNo());
        dto.setEventNo(recode.getEventNo());
        dto.setIssuingBayId(info.getIssuingBayId());
        dto.setWeight(info.getWeight().longValue());
        dto.setExtraInfo(recode.getExtraInfo());
        dto.setAddress(JSON.parseObject(info.getAddressInfo(),CarriageNoAddressDto.class));
        String countryCode = countryCodeMappingService.countryCodeHandler(dto.getAddress().getCountryCode());
        dto.getAddress().setCountryCode(countryCode);

        LogisticsSupport logisticsSupport = getLogisticsSupport(dto.getLogisticsId(), null);
        logisticsSupport.confirmCarriage(dto);
    }

    @DistributedLock(value = "#carriageNoRecodeId")
    public void cancelCarriage(Long carriageNoRecodeId) {
        log.info("cancelCarriage carriageNoRecodeId = {}", carriageNoRecodeId);
        CarriageNoRecode recode = carriageNoRecodeService.getById(carriageNoRecodeId);
        if (Objects.isNull(recode)) {
            return;
        }

        if (YES.equals(recode.getIsCancelInThirdParty())) {
            return;
        }
        CarriageDeclarationInfo info = carriageDeclarationInfoService.getInfoById(recode.getDeclarationInfoId());

        CancelLabelDto dto = new CancelLabelDto();
        dto.setIssuingBayId(info.getIssuingBayId());
        dto.setLogisticsId(recode.getLogisticsId());
        dto.setExtendNo(recode.getExtendNo());
        dto.setCarriageNo(recode.getCarriageNo());
        dto.setEventNo(recode.getEventNo());
        dto.setExtraInfo(recode.getExtraInfo());
        dto.setCarriageNoRecodeId(carriageNoRecodeId);
        dto.setDeclarationInfoId(recode.getDeclarationInfoId());
        Boolean cancelSuccess = false;
        try {
            LogisticsSupport logisticsSupport = getLogisticsSupport(recode.getLogisticsId(), null);
            cancelSuccess = logisticsSupport.cancelLabel(dto);
        } catch (Exception e) {
            log.warn("cancelCarriage error, carriageNoRecodeId = {}, e = {}", carriageNoRecodeId, e.getMessage());
        }
        if (Boolean.TRUE.equals(cancelSuccess)) {
            carriageNoRecodeService.updateToCancelInThirdParty(carriageNoRecodeId);
        }
    }

    public List<Long> getNeedCancelCarriageByParcelIds(GetNeedCancelCarriageByParcelReq req) {
        List<Long> parcelIds = req.getParcelIds();
        if (CollUtil.isEmpty(parcelIds)) {
            return Collections.emptyList();
        }

        List<CarriageNoRecode> haveCarriageNoUnCancelRecodes = carriageNoRecodeService.findHaveCarriageNoUnCancelRecodeByParcelIds(parcelIds, req.getDonCancelCarriageNo(), req.getDonCancelBusinessIds());

        if (CollUtil.isEmpty(haveCarriageNoUnCancelRecodes)) {

            return Collections.emptyList();
        }

        List<Long> logisticsIds = haveCarriageNoUnCancelRecodes.stream().map(CarriageNoRecode::getLogisticsId).distinct().collect(Collectors.toList());

        List<TenantLogistics> needCancelOrderInThirdPartyLogistics = tenantLogisticsService.findNeedCancelOrderInThirdPartyLogisticsByIds(logisticsIds);

        if (CollUtil.isEmpty(needCancelOrderInThirdPartyLogistics)) {

            return Collections.emptyList();
        }

        Set<Long> needCancelOrderInThirdPartyLogisticsIds = needCancelOrderInThirdPartyLogistics.stream().map(TenantLogistics::getId).collect(Collectors.toSet());

        List<CarriageNoRecode> needCancelCarriageRecodes = haveCarriageNoUnCancelRecodes.stream().filter(a -> needCancelOrderInThirdPartyLogisticsIds.contains(a.getLogisticsId())).collect(Collectors.toList());

        if (CollUtil.isEmpty(needCancelCarriageRecodes)) {

            log.info("no needCancelCarriageRecodeIds, parcelIds = {}", JSONObject.toJSONString(parcelIds));
            return Collections.emptyList();

        } else {

            log.info("needCancelCarriageRecodeIds, parcelIds = {}, recodeIds = {}",
                    JSONObject.toJSONString(parcelIds),
                    JSONObject.toJSONString(needCancelCarriageRecodes.stream().map(CarriageNoRecode::getId).collect(Collectors.toList())));

            return needCancelCarriageRecodes.stream().map(CarriageNoRecode::getId).collect(Collectors.toList());
        }
    }

    public void pushTailNoAndLabel(CarriageNoRecode recode, TenantLogisticsRespDto logistics) {
        if (StrUtil.isBlank(recode.getCarriageNo())
                || !CarriageNoRecodeConstant.STATUS_SUCCESS.equals(recode.getStatus())) {
            Assert.wrong("头程运单未生成");
        }
        CarriageDeclarationInfo declarationInfo = this.carriageDeclarationInfoService.getInfoById(recode.getDeclarationInfoId());
        LogisticsSupport logisticsSupport = getLogisticsSupport(null, logistics);
        logisticsSupport.pushTailNoAndLabel(declarationInfo.getLogisticsId()
                , declarationInfo.getIssuingBayId(), recode);
    }

    public CarriageScanFormDto scanForm(CarriageScanFormParam param) {
        CarriageNoRecode recode = carriageNoRecodeService.getOneByOrderIdAndVersion(param.getOrderId(), param.getOrderParcelId(), param.getCarriageVersion());
        CarriageScanFormDto carriageScanFormDto = new CarriageScanFormDto();
        if (Objects.isNull(recode)) {
            carriageScanFormDto.setScanFormStatus(ScanFormStatusEnum.FAILED.getCode());
            carriageScanFormDto.setScanFormErrorMsg("未查到下单信息");
            return carriageScanFormDto;
        }
        if (param.getAsyncHandle() != null && param.getAsyncHandle()) {
            rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_LOGISTICS, LogisticsTagConst.EVENT_CARRIAGE_SCAM_FORM, String.valueOf(recode.getId()));
            return carriageScanFormDto;
        }
        return scanForm(recode, param.getIsTailChannel());
    }

    public void scanFormById(Long id) {
        CarriageNoRecode recode = carriageNoRecodeService.getById(id);
        Assert.validateNull(recode, "未查到下单信息");
        scanForm(recode, Boolean.FALSE);
    }

    public CarriageScanFormDto scanForm(CarriageNoRecode recode, Boolean isTailChannelNeedSendMessage) {
        log.info("scanForm real req recodeId : {}", recode.getId());
        ScanFormBo dto = new ScanFormBo();
        dto.setRecode(recode);
        OrderParcelDTO parcel = orderParcelFeign.findDtoByParcelId(recode.getOrderParcelId(), "tailLogisticsChannelId");
        boolean hasTailChannel = NumberUtils.greaterZero(parcel.getTailLogisticsChannelId());
        CarriageScanFormDto scanFormResp = temuOnlineService.scanForm(dto);
        if (ScanFormStatusEnum.NONE.getCode().equals(scanFormResp.getScanFormStatus())) {
            return scanFormResp;
        }
        scanFormResp.setOrderParcelId(recode.getOrderParcelId());
        scanFormResp.setOrderId(recode.getOrderId());
        scanFormResp.setCarriageVersion(recode.getCarriageVersion());
        if (ScanFormStatusEnum.SUCCESS.getCode().equals(scanFormResp.getScanFormStatus())) {
            if (isTailChannelNeedSendMessage != null && isTailChannelNeedSendMessage) {
                this.rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_LOGISTICS
                        , LogisticsTagConst.TEMU_TAIL_APPLY_CARRIAGE_NO_TAG
                        , new TemuTailApplyCarriageNoMessageDTO().setOrderParcelId(recode.getOrderParcelId()).setIsNeedScanForm(false));
            }
            if (!hasTailChannel) {
                carriageNoRecodeService.updateScanFormSuccess(recode.getId());
            }
        }
        carriageScanFormRecordService.save(recode, scanFormResp);
        orderCarriageFeign.updateScanFormStatus(scanFormResp);
        return scanFormResp;
    }

}
