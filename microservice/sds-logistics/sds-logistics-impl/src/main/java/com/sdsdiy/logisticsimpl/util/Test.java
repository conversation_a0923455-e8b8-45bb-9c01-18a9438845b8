package com.sdsdiy.logisticsimpl.util;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.nio.charset.StandardCharsets;

/**
 * @author: bin_lin
 * @date: 2021/10/9 17:43
 * @desc:
 */
public class Test {

    private static String file = "E:\\gitlabSpace\\A4-DATA-MODEL\\物流.chnr.json";

    public static void main(String[] args) {
        String string = FileUtil.readString(file, StandardCharsets.UTF_8);
        JSONObject db = JSON.parseObject(string);
        JSONArray entities = db.getJSONArray("entities");
        entities.toJavaList(JSONObject.class).forEach(entity -> {
            JSONArray headers = entity.getJSONArray("headers");
            if (headers == null || headers.size() < 1) {
                entity.put("headers", getHeaders());
            }
        });
        FileUtil.writeString(db.toJSONString(), file, StandardCharsets.UTF_8);

        System.out.println(db.toJSONString());
    }

    private static JSONArray getHeaders() {
        String h = "[{\n" +
                "refKey: \"hideInGraph\",\n" +
                "hideInGraph: true\n" +
                "},\n" +
                "{\n" +
                "refKey: \"defKey\",\n" +
                "hideInGraph: false\n" +
                "},\n" +
                "{\n" +
                "refKey: \"defName\",\n" +
                "hideInGraph: false\n" +
                "},\n" +
                "{\n" +
                "refKey: \"primaryKey\",\n" +
                "hideInGraph: false\n" +
                "},\n" +
                "{\n" +
                "refKey: \"notNull\",\n" +
                "hideInGraph: true\n" +
                "},\n" +
                "{\n" +
                "refKey: \"autoIncrement\",\n" +
                "hideInGraph: true\n" +
                "},\n" +
                "{\n" +
                "refKey: \"domain\",\n" +
                "hideInGraph: true\n" +
                "},\n" +
                "{\n" +
                "refKey: \"type\",\n" +
                "hideInGraph: false\n" +
                "},\n" +
                "{\n" +
                "refKey: \"len\",\n" +
                "hideInGraph: false\n" +
                "},\n" +
                "{\n" +
                "refKey: \"scale\",\n" +
                "hideInGraph: false\n" +
                "},\n" +
                "{\n" +
                "refKey: \"comment\",\n" +
                "hideInGraph: true\n" +
                "},\n" +
                "{\n" +
                "refKey: \"refDict\",\n" +
                "hideInGraph: true\n" +
                "},\n" +
                "{\n" +
                "refKey: \"defaultValue\",\n" +
                "hideInGraph: true\n" +
                "},\n" +
                "{\n" +
                "refKey: \"isStandard\",\n" +
                "hideInGraph: false\n" +
                "},\n" +
                "{\n" +
                "refKey: \"uiHint\",\n" +
                "hideInGraph: true\n" +
                "}]";
        return JSON.parseArray(h);
    }
}
