package com.sdsdiy.logisticsimpl.service.support.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sds.platform.sdk.temu.BaseResp;
import com.sds.platform.sdk.temu.constant.TemuShipCompanyEnum;
import com.sds.platform.sdk.temu.logistics.*;
import com.sds.platform.sdk.temu.order.OrderDetailsGetV2Resp;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.enums.ScanFormStatusEnum;
import com.sdsdiy.common.base.enums.onlineorder.TemuOrderStatusEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.DoubleUtils;
import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.common.consts.MerchantStoreTypeEnum;
import com.sdsdiy.core.base.util.DateUtil;
import com.sdsdiy.core.base.util.StringUtils;
import com.sdsdiy.core.redis.util.RedisUtil;
import com.sdsdiy.logisticsapi.constant.CarriageNoRecodeConstant;
import com.sdsdiy.logisticsapi.dto.*;
import com.sdsdiy.logisticsapi.dto.base.LogisticsChannelRespDto;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum;
import com.sdsdiy.logisticsdata.dto.CarriageScanFormDto;
import com.sdsdiy.logisticsimpl.entity.po.CarriageNoRecode;
import com.sdsdiy.logisticsimpl.entity.po.TemuShipmentRecord;
import com.sdsdiy.logisticsimpl.entity.po.TemuShipmentRecordItemRel;
import com.sdsdiy.logisticsimpl.exception.ProviderServiceException;
import com.sdsdiy.logisticsimpl.feign.MerchantStoreFeign;
import com.sdsdiy.logisticsimpl.feign.OrderFeign;
import com.sdsdiy.logisticsimpl.feign.order.PlatformOrderExtendFeign;
import com.sdsdiy.logisticsimpl.feign.outeco.OnlineLogisticsWarehouseFeign;
import com.sdsdiy.logisticsimpl.feign.outeco.TemuOrderFeign;
import com.sdsdiy.logisticsimpl.feign.outeco.TemuPdfFeign;
import com.sdsdiy.logisticsimpl.feign.outeco.TemuShipmentFeign;
import com.sdsdiy.logisticsimpl.manager.TemuShipmentRecordOrderRelManage;
import com.sdsdiy.logisticsimpl.service.CarriageNoRecodeService;
import com.sdsdiy.logisticsimpl.service.EcommerceLogisticsService;
import com.sdsdiy.logisticsimpl.service.TemuShipmentRecordRelService;
import com.sdsdiy.logisticsimpl.service.TemuShipmentRecordService;
import com.sdsdiy.logisticsimpl.service.support.LogisticsSupportAbstract;
import com.sdsdiy.logisticsimpl.service.support.dto.ScanFormBo;
import com.sdsdiy.logisticsimpl.service.support.dto.SyncCarriageNoBO;
import com.sdsdiy.logisticsimpl.service.temu.TemuOnlineOrderService;
import com.sdsdiy.logisticsimpl.util.TemuUtils;
import com.sdsdiy.orderapi.dto.OrderDTO;
import com.sdsdiy.orderdata.constant.order.PlatformOrderExtendValueEnum;
import com.sdsdiy.outecodata.dto.TemuOrderShipmentInfoDto;
import com.sdsdiy.outecodata.dto.TemuOrderShipmentItemDto;
import com.sdsdiy.outecodata.dto.UploadTemuPdfResp;
import com.sdsdiy.outecodata.param.LogisticsScanformCreateParam;
import com.sdsdiy.outecodata.param.UploadTemuPdfReq;
import com.sdsdiy.userapi.dto.base.MerchantStoreRespDto;
import com.sdsdiy.userdata.constant.MerchantStoreMailingDateTypeEnum;
import com.sdsdiy.userdata.dto.OnlineLogisticsWarehouseDto;
import com.sdsdiy.userdata.dto.TemuUsableWarehouseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static com.sds.platform.sdk.temu.constant.TemuShipCompanyEnum.*;
import static com.sdsdiy.logisticsapi.constant.CarriageNoRecodeConstant.*;
import static com.sdsdiy.logisticsapi.constant.TemuShipmentRecordConstant.ORIGIN_ALREADY_BOUGHT_LABEL;
import static com.sdsdiy.logisticsapi.constant.TemuShipmentRecordConstant.ORIGIN_CALL;
import static com.sdsdiy.logisticsimpl.service.temu.TemuTailChannelService.TAIL_EXTRA_INFO;
import static com.sdsdiy.userapi.constant.OnlineLogisticsIssuingBayConstant.TemuTypeEnum.TEMU_SEMI;

@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired, @Lazy})
@Slf4j
public class TemuOnlineServiceImpl extends LogisticsSupportAbstract<Object> {

    private final TemuShipmentFeign temuShipmentFeign;
    private final MerchantStoreFeign merchantStoreFeign;
    private final PlatformOrderExtendFeign platformOrderExtendFeign;

    private final TemuShipmentRecordService temuShipmentRecordService;
    private final TemuShipmentRecordRelService temuShipmentRecordRelService;
    private final CarriageNoRecodeService carriageNoRecodeService;

    private final OnlineLogisticsWarehouseFeign onlineLogisticsWarehouseFeign;
    private final TemuPdfFeign temuPdfFeign;
    private final OrderFeign orderFeign;
    private final TemuOrderFeign temuOrderFeign;
    private final static String MERCHANT_STORE_ID = "merchantStoreId";
    private final static String PACKAGE_SN = "packageSn";
    private final static String NEED_SCAN_FORM = "needScanForm";
    private final TemuShipmentRecordOrderRelManage temuShipmentRecordOrderRelManage;
    private final TemuOnlineOrderService temuOnlineOrderService;
    private final EcommerceLogisticsService ecommerceLogisticsService;

    @Resource
    private RedisUtil redisUtil;
    @Override
    public Long getServiceProviderId() {

        return LogisticsServiceProviderEnum.TEMU_ONLINE.getNumber();
    }

    @Override
    public CarriageNoRespDto applyCarriageNo(CarriageNoRepDto dto, TenantLogisticsRespDto logistics, String eventNo) {
        return this.temuApplyCarriageNo(dto, logistics.getId(), logistics.getLogisticsChannel(), null);
    }

    public CarriageNoRespDto temuApplyCarriageNo(CarriageNoRepDto dto, LogisticsChannelRespDto logisticsChannel) {
        LogisticsConfigProcessed processed = getLogisticsConfig(dto.getLogisticsId(), dto.getIssuingBayId());
        return temuApplyCarriageNo(dto, dto.getLogisticsId(), logisticsChannel, processed.getOnlineLogisticsIssuingBayId());
    }

    public CarriageNoRespDto temuApplyCarriageNo(CarriageNoRepDto dto, Long logisticsId
            , LogisticsChannelRespDto logisticsChannel, Long onlineLogisticsIssuingBayId) {

        String outOrderNo = dto.getOutOrderNo();
        Long orderId = dto.getId();
        log.info("temu online logistics, outOrderNo = {}, orderId = {}", outOrderNo.replaceAll("-", ""), orderId);
        Boolean needScanForm = isNeedScanForm(logisticsChannel.getStandardName());
        TemuShipmentRecord existRecord = this.temuShipmentRecordService.getTemuShipmentRecordByOutOrderNo(outOrderNo);
        CarriageNoRespDto respDto = new CarriageNoRespDto();
        respDto.setImmediatelyScanForm(dto.getImmediatelyScanForm());

        Long merchantStoreId = dto.getMerchantStoreId();
        MerchantStoreRespDto store = this.merchantStoreFeign.findById(merchantStoreId, "sellerId,onlineLogisticsLimitTime,type,mailingDateType");
        Assert.validateNull(store, "店铺不存在");

        MerchantStoreMailingDateTypeEnum mailingDateType = calculateLogisticsMailingDateOffsetType(logisticsChannel, store.getMailingDateType());
        if (MerchantStoreMailingDateTypeEnum.TEMU_IMMEDIATELY.equals(mailingDateType)) {
            respDto.setImmediatelyScanForm(true);
        }
        if (Objects.nonNull(existRecord)) {
            //如果已经存在了下单记录，且是成功或者失败的，直接拿结果
            CarriageNoRespDto existResp = this.getResultFromExistRecord(dto, orderId, merchantStoreId, existRecord, needScanForm);
            if (Objects.nonNull(existResp)) {
                existResp.setImmediatelyScanForm(respDto.getImmediatelyScanForm());
                return existResp;
            }
        }
        String temuWarehouseId = this.getWarehouseId(dto, onlineLogisticsIssuingBayId, merchantStoreId);

        TemuOrderShipmentInfoDto canShipQuantityInfo = this.temuOnlineOrderService.getUnCancelAndCanShipOrderInfo(orderId, outOrderNo);

        log.info("temu online logistics, unCancelAndCanShipQuantityInfo = {}", JSONObject.toJSONString(canShipQuantityInfo));

        boolean noExist = Objects.isNull(existRecord);

        String packageSn;
        log.info("temu online logistics, logisticsChannel = {}", JSONObject.toJSONString(logisticsChannel));

        if (noExist) {
            Long shipCompanyId = TemuShipCompanyEnum.getShipCompanyIdByChannelStandrdName(logisticsChannel.getStandardName());
            if (!NumberUtils.greaterZero(shipCompanyId)) {
                String shipCompanyIdStr = ecommerceLogisticsService.getServiceProviderId(MerchantStorePlatformEnum.TEMU.getCode(), logisticsChannel.getStandardName());
                if (StrUtil.isNotEmpty(shipCompanyIdStr) && StrUtil.isNumeric(shipCompanyIdStr)) {
                    shipCompanyId = Long.valueOf(shipCompanyIdStr);
                }
            }
            List<OrderSendInfoListDTO> orderSendInfoList = this.genOrderSendInfoListByTemuOrderShipmentInfoDto(canShipQuantityInfo, dto.getProductList());
            BaseResp<ShipmentCreateResp> shipmentResp = new BaseResp<>();
            String mallRegion = this.platformOrderExtendFeign.getOnlineOrderRegion(dto.getOutOrderNo(), orderId);
            String origin = ORIGIN_CALL;
            if (CollUtil.isEmpty(orderSendInfoList)) {
                packageSn = this.handleAlreadyBoughtLabels2(dto, store, logisticsChannel, mallRegion, outOrderNo, "没有可发货的子单");
                origin = ORIGIN_ALREADY_BOUGHT_LABEL;
            } else {
                ShipmentCreateReq req = this.genCreateShipmentReq(dto, orderId, store, temuWarehouseId, canShipQuantityInfo, logisticsChannel, shipCompanyId, mallRegion, orderSendInfoList);
                log.info("temu online logistics, createShipment sellerId = {}, req = {}", store.getSellerId(), JSONObject.toJSONString(req));

                shipmentResp = this.temuShipmentFeign.createShipment(store.getSellerId(), req);
                log.info("temu online logistics, createShipment resp = {}", shipmentResp);
                if (Boolean.FALSE.equals(shipmentResp.getSuccess())) {
                    if (shipmentResp.getErrorMsg().contains("You have already bought labels for some of your orders. Please check and try again")
                            || shipmentResp.getErrorMsg().contains("You have already requested by Temu integrated logistics. Please check whether request is")) {
                        packageSn = this.handleAlreadyBoughtLabels2(dto, store, logisticsChannel, mallRegion, outOrderNo, shipmentResp.getErrorMsg());
                        origin = ORIGIN_ALREADY_BOUGHT_LABEL;
                    } else {
                        if ("50005".equals(shipmentResp.getErrorCode())) {
                            throw new ProviderServiceException("该订单不支持使用该渠道发货，请更换其他线上物流");
                        }
                        if (shipmentResp.getErrorMsg().contains("Invalid logistics company ID")) {
                            throw new ProviderServiceException("该订单不支持使用该渠道发货,公司渠道问题，请更换其他线上物流");
                        }
                        if (shipmentResp.getErrorMsg().contains("The parameter warehouseId is invalid")) {
                            throw new ProviderServiceException("当前店铺绑定的temu仓库不存在或绑定的temu仓库所属站点不匹配");
                        }
                        throw new ProviderServiceException(shipmentResp.getErrorMsg());
                    }
                } else {
                    packageSn = shipmentResp.getResult().getPackageSnList().get(0);
                }
            }
            this.temuShipmentRecordService.saveShipmentRecord(logisticsId, outOrderNo, orderId, temuWarehouseId, logisticsChannel.getId(), shipCompanyId, packageSn, orderSendInfoList, origin);
        } else {
            //temu重新下单 先查第三方状态 如果成功，直接变为运单成功
            packageSn = existRecord.getPackageSn();
            String mallRegion = this.platformOrderExtendFeign.getOnlineOrderRegion(dto.getOutOrderNo(), orderId);
            ShipmentResultGetReq shipmentResultReq = new ShipmentResultGetReq();
            shipmentResultReq.setPackageSnList(Collections.singletonList(packageSn));
            BaseResp<ShipmentResultGetResp> respBaseResp = this.temuShipmentFeign.shipmentResult(store.getSellerId(), mallRegion, shipmentResultReq);
            getPackageNo(dto, logisticsChannel, "当前订单子单与temu包裹子单不一致或物流渠道不一致", respBaseResp);
            ShipmentResultGetResp shipmentResultGetResp = respBaseResp.getResult();
            if (Objects.nonNull(shipmentResultGetResp)) {
                List<PackageInfoResultListDTO> packageInfoResultList = shipmentResultGetResp.getPackageInfoResultList();
                if (CollUtil.isNotEmpty(packageInfoResultList)) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put(MERCHANT_STORE_ID, merchantStoreId);
                    jsonObject.put(PACKAGE_SN, packageSn);
                    jsonObject.put(NEED_SCAN_FORM, needScanForm);
                    respDto.setExtraInfo(jsonObject.toJSONString());

                    PackageInfoResultListDTO packageInfoResultListDTO = packageInfoResultList.get(0);
                    if (1 == packageInfoResultListDTO.getShippingLabelStatus() && StrUtil.isNotBlank(packageInfoResultListDTO.getTrackingNumber())) {
                        if (ORIGIN_ALREADY_BOUGHT_LABEL.equals(existRecord.getOrigin())) {
                            respDto.setOrderProgressType(ORDER_PROGRESS_TYPE_TUME_ALREADY_BOUGHT_LABEL);
                        }
                        respDto.setStatus(STATUS_SUCCESS);
                        if (needScanForm) {
                            respDto.setScanFormStatus(ScanFormStatusEnum.PENDING.getCode());
                        }
                        respDto.setCarriageNo(packageInfoResultListDTO.getTrackingNumber());
                        this.temuShipmentRecordService.updateCarriageNo(existRecord.getId(), packageInfoResultListDTO.getTrackingNumber());
                        return respDto;
                    } else if (0 == packageInfoResultListDTO.getShippingLabelStatus()) {
                        if (needScanForm) {
                            respDto.setScanFormStatus(ScanFormStatusEnum.PENDING.getCode());
                        }
                        respDto.setStatus(STATUS_SYNCING);
                        return respDto;
                    }
                }
            }
            List<TemuShipmentRecordItemRel> recordItemRelList = this.temuShipmentRecordRelService.TemuShipmentRecordRelListByPackageSn(existRecord.getPackageSn());

            List<OrderSendInfoListDTO> orderSendInfoList = new ArrayList<>();

            for (TemuShipmentRecordItemRel temuShipmentRecordItemRel : recordItemRelList) {
                OrderSendInfoListDTO orderSendInfoListDTO = new OrderSendInfoListDTO();
                orderSendInfoListDTO.setQuantity(temuShipmentRecordItemRel.getQuantity());
                orderSendInfoListDTO.setOrderSn(temuShipmentRecordItemRel.getOrderSn());
                orderSendInfoListDTO.setParentOrderSn(temuShipmentRecordItemRel.getParentOrderSn());
                orderSendInfoList.add(orderSendInfoListDTO);
            }

            ShipmentUpdateReq req = this.genUpdateShipmentReq(dto, store, temuWarehouseId, canShipQuantityInfo, logisticsChannel, mallRegion, existRecord, orderSendInfoList);

            log.info("temu online logistics, updateShipment sellerId = {}, req = {}", store.getSellerId(), JSONObject.toJSONString(req));
            BaseResp<Boolean> shipmentUpdateRespBaseResp = this.temuShipmentFeign.updateShipment(store.getSellerId(), req);
            log.info("temu online logistics, updateShipment resp = {}", JSONObject.toJSONString(shipmentUpdateRespBaseResp));

            if (Boolean.FALSE.equals(shipmentUpdateRespBaseResp.getSuccess())) {
                throw new ProviderServiceException(shipmentUpdateRespBaseResp.getErrorMsg());
            }

            existRecord.setPackageSn(packageSn);
            existRecord.setShippingLabelStatus(0);
            existRecord.setLogisticsId(logisticsId);
            this.temuShipmentRecordService.updateShipmentRecord(outOrderNo, orderId, existRecord, orderSendInfoList);
        }
        if (needScanForm) {
            respDto.setScanFormStatus(ScanFormStatusEnum.PENDING.getCode());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(MERCHANT_STORE_ID, merchantStoreId);
        jsonObject.put(PACKAGE_SN, packageSn);
        jsonObject.put(NEED_SCAN_FORM, needScanForm);
        respDto.setExtraInfo(jsonObject.toJSONString());
        respDto.setStatus(CarriageNoRecodeConstant.STATUS_SYNCING);

        return respDto;
    }


    private String handleAlreadyBoughtLabels2(CarriageNoRepDto dto,
                                              MerchantStoreRespDto merchantStore,
                                              LogisticsChannelRespDto logisticsChannel,
                                              String mallRegion,
                                              String outOrderNo,
                                              String errorMsg) {

        OrderDetailsGetV2Resp orderDetailResp = temuOrderFeign.getOrderDetailResp(merchantStore.getSellerId(), outOrderNo);
        log.info("handleAlreadyBoughtLabels orderDetailResp:{}", JSONObject.toJSONString(orderDetailResp));
        if (orderDetailResp == null) {
            throw new ProviderServiceException(errorMsg);
        }
        List<OrderDetailsGetV2Resp.OrderListDTO> orderList = orderDetailResp.getOrderList();
        if (CollUtil.isEmpty(orderList)) {
            throw new ProviderServiceException(errorMsg);
        }

        List<String> packageSnTmpList = new ArrayList<>();
        for (OrderDetailsGetV2Resp.OrderListDTO orderDto : orderList) {
            List<OrderDetailsGetV2Resp.OrderListDTO.PackageSnInfoDTO> packageSnInfoList = orderDto.getPackageSnInfo();
            if (CollUtil.isEmpty(packageSnInfoList)) {
                continue;
            }
            for (OrderDetailsGetV2Resp.OrderListDTO.PackageSnInfoDTO packageSnInfo : packageSnInfoList) {
                packageSnTmpList.add(packageSnInfo.getPackageSn());
            }
        }
        if (CollUtil.isEmpty(packageSnTmpList)) {
            log.info("handleAlreadyBoughtLabels 包裹数据是空的数据不成功");
            throw new ProviderServiceException(errorMsg);
        }

        ShipmentResultGetReq shipmentResultReq = new ShipmentResultGetReq();
        shipmentResultReq.setPackageSnList(packageSnTmpList);
        log.info("handleAlreadyBoughtLabels shipmentResult:{}", JSONObject.toJSONString(shipmentResultReq));
        BaseResp<ShipmentResultGetResp> respBaseResp = this.temuShipmentFeign.shipmentResult(merchantStore.getSellerId(), mallRegion, shipmentResultReq);
        log.info("handleAlreadyBoughtLabels shipmentResult:{}", JSONObject.toJSONString(respBaseResp));
        return getPackageNo(dto, logisticsChannel, errorMsg, respBaseResp);
    }

    private static String getPackageNo(CarriageNoRepDto dto, LogisticsChannelRespDto logisticsChannel, String errorMsg, BaseResp<ShipmentResultGetResp> respBaseResp) {
        if (Boolean.FALSE.equals(respBaseResp.getSuccess())) {
            log.info("handleAlreadyBoughtLabels 根据包裹获取的数据不成功");
            throw new ProviderServiceException(errorMsg);
        }
        List<PackageInfoResultListDTO> packageInfoResultList = respBaseResp.getResult().getPackageInfoResultList();
        if (CollUtil.isEmpty(packageInfoResultList)) {
            log.info("handleAlreadyBoughtLabels 包裹返回数据是空的");
            throw new ProviderServiceException(errorMsg);
        }
        //同物流商 同渠道
        packageInfoResultList = packageInfoResultList.stream()
                .filter(p -> p.getShipLogisticsType().equals(logisticsChannel.getMethodName()))
                .filter(p -> logisticsChannel.getStandardName().equals(p.getShipCompanyName())
                        || logisticsChannel.getStandardName().equals(p.getShippingCompanyName()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(packageInfoResultList)) {
            log.info("handleAlreadyBoughtLabels 同物流商 同渠道为空");
            throw new ProviderServiceException(errorMsg);
        }
        //同数量
        List<String> packageSnList = Lists.newArrayList();
        List<CarriageProductDto> productList = dto.getProductList();
        for (PackageInfoResultListDTO packageInfoResult : packageInfoResultList) {
            List<OrderSendInfoListDTO> orderSendInfoList = packageInfoResult.getOrderSendInfoList();
            if (CollUtil.isEmpty(orderSendInfoList)) {
                continue;
            }
            if (orderSendInfoList.size() != productList.size()) {
                log.info("handleAlreadyBoughtLabels 舍弃的包裹 包裹子单数量不一致 {}", packageInfoResult.getPackageSn());
                continue;
            }
            Map<String, Integer> orderSnKeyNumMap = orderSendInfoList.stream().collect(Collectors.toMap(OrderSendInfoListDTO::getOrderSn, OrderSendInfoListDTO::getQuantity));
            for (CarriageProductDto carriageProductDto : productList) {
                //数量不一致  直接舍掉
                Integer num = orderSnKeyNumMap.get(carriageProductDto.getOutItemId());
                if (num == null || !num.equals(carriageProductDto.getNum())) {
                    log.info("handleAlreadyBoughtLabels 舍弃的包裹 数量不一致 {}", packageInfoResult.getPackageSn());
                    break;
                }
            }
            packageSnList.add(packageInfoResult.getPackageSn());
        }
        if (CollUtil.isEmpty(packageSnList)) {
            log.info("handleAlreadyBoughtLabels 没有同数量的包裹");
            throw new ProviderServiceException(errorMsg);
        }
        log.info("handleAlreadyBoughtLabels packageSnList:{}", JSONObject.toJSONString(packageSnList));
        return packageSnList.get(0);
    }


    @Override
    public CarriageNoRespDto expressSheetLabel(ExpressSheetLabelDto dto, long logisticsId) {
        return this.temuSheetLabel(dto.getOrderId(), dto.getExtraInfo(), dto.getExtendNo());
    }

    public CarriageNoRespDto temuSheetLabel(Long orderId, String extraInfo, String extendNo) {
        JSONObject extraInfoJsonObject = JSONObject.parseObject(extraInfo);
        log.info("temu online logistics, expressSheetLabel extraInfo = {}", JSONObject.toJSONString(extraInfo));
        String packageSn = extraInfoJsonObject.getString(PACKAGE_SN);
        CarriageNoRespDto respDto = new CarriageNoRespDto();
        TemuShipmentRecord existRecord = this.temuShipmentRecordService.getTemuShipmentRecordByPackageSn(packageSn);
        if (Objects.isNull(existRecord)) {
            throw new ProviderServiceException("temu包裹记录已被删除，包裹号 = " + packageSn);
        }
        if (ORIGIN_ALREADY_BOUGHT_LABEL.equals(existRecord.getOrigin())) {
            respDto.setOrderProgressType(ORDER_PROGRESS_TYPE_TUME_ALREADY_BOUGHT_LABEL);
        }
        if (StrUtil.isNotBlank(existRecord.getShippingLabelUrl())) {
            // 成功过，直接返回
            respDto.successLabel(existRecord.getShippingLabelUrl(), existRecord.getShippingLabelUrl());
            return respDto;
        }

        Long merchantStoreId = extraInfoJsonObject.getLong(MERCHANT_STORE_ID);
        if (!NumberUtils.greaterZero(merchantStoreId)) {
            throw new ProviderServiceException("店铺不存在");
        }
        List<MerchantStoreRespDto> storeList =
                this.merchantStoreFeign.findByIds(new IdsSearchHelper(Collections.singletonList(merchantStoreId), "sellerId"));
        if (CollUtil.isEmpty(storeList)) {
            throw new ProviderServiceException("店铺不存在");
        }
        MerchantStoreRespDto merchantStoreRespDto = storeList.get(0);

        OrderDTO orderDTO = this.orderFeign.findById(orderId, "outOrderNo");
        if (orderDTO == null) {
            throw new ProviderServiceException("订单不存在");
        }
        String mallRegion = this.platformOrderExtendFeign.getOnlineOrderRegion(orderDTO.getOutOrderNo(), orderId);
        log.info("temu online logistics, shipmentDocumentGet seller = {},mallRegion = {} packageSn = {}", merchantStoreRespDto.getSellerId(), mallRegion, packageSn);

        BaseResp<ShipmentDocumentResp> shipmentDocumentRespBaseResp = this.temuShipmentFeign.shipmentDocumentGet(merchantStoreRespDto.getSellerId(), mallRegion, packageSn);
        log.info("temu online logistics, shipmentDocumentGet resp = {}", JSONObject.toJSON(shipmentDocumentRespBaseResp));
        Boolean success = shipmentDocumentRespBaseResp.getSuccess();

        if (!Boolean.TRUE.equals(success)) {
            throw new ProviderServiceException(shipmentDocumentRespBaseResp.getErrorMsg());
        }

        String originUrl;
        try {
            ShipmentDocumentResp.ShippingLabelUrl shippingLabelUrl = shipmentDocumentRespBaseResp.getResult().getShippingLabelUrlList().get(0);
            originUrl = shippingLabelUrl.getUrl();
        } catch (Exception e) {
            throw new ProviderServiceException(JSONObject.toJSONString(shipmentDocumentRespBaseResp));
        }

        String labelUrl;
        try {
            UploadTemuPdfReq req = new UploadTemuPdfReq();
            req.setSellerId(merchantStoreRespDto.getSellerId());
            req.setExtendNo(extendNo);
            req.setOriginUrl(originUrl);
            req.setMallRegion(mallRegion);
            log.info("temu uploadTemuPdf req = {}", JSONObject.toJSONString(req));
            UploadTemuPdfResp resp = this.temuPdfFeign.uploadTemuPdf(req);
            log.info("temu uploadTemuPdf resp = {}", JSONObject.toJSONString(resp));
            labelUrl = resp.getUploadUrl();
            this.temuShipmentRecordService.updateLabelUrl(existRecord.getId(), labelUrl);

        } catch (Exception e) {
            throw new ProviderServiceException(e);
        }

        respDto.successLabel(originUrl, labelUrl);
        return respDto;
    }

    @Override
    public TrackDto queryExpressTrackInfo(ExpressTrackInfoDto dto) {
        return null;
    }

    @Override
    public Object getConfig(String configStr) {
        return null;
    }

    public static String LABEL_RETRY_KEY="labelstatus:retry:%s";
    @Override
    public CarriageNoRespDto syncCarriageNo(SyncCarriageNoBO dto, TenantLogisticsRespDto logistics, String eventNo) {
        return this.syncTemuCarriageNo(dto.getOrderId(), dto.getExtraInfo());
    }

    public CarriageNoRespDto syncTemuCarriageNo(Long orderId, String extraInfo) {
        JSONObject extraInfoJsonObject = JSONObject.parseObject(extraInfo);
        Long merchantStoreId = extraInfoJsonObject.getLong(MERCHANT_STORE_ID);
        String packageSn = extraInfoJsonObject.getString(PACKAGE_SN);
        Boolean needScanForm = extraInfoJsonObject.getBoolean(NEED_SCAN_FORM);
        if (!NumberUtils.greaterZero(merchantStoreId)) {
            throw new ProviderServiceException("店铺不存在");
        }
        List<MerchantStoreRespDto> storeList =
                this.merchantStoreFeign.findByIds(new IdsSearchHelper(Collections.singletonList(merchantStoreId), "sellerId"));

        if (CollUtil.isEmpty(storeList)) {
            throw new ProviderServiceException("店铺不存在");
        }


        OrderDTO orderDTO = this.orderFeign.findById(orderId, "outOrderNo");
        if (orderDTO == null) {
            throw new ProviderServiceException("订单不存在");
        }
        String mallRegion = this.platformOrderExtendFeign.getOnlineOrderRegion(orderDTO.getOutOrderNo(), orderId);

        MerchantStoreRespDto merchantStoreRespDto = storeList.get(0);
        ShipmentResultGetReq shipmentResultReq = new ShipmentResultGetReq();
        shipmentResultReq.setPackageSnList(Collections.singletonList(packageSn));
        BaseResp<ShipmentResultGetResp> resultBaseResp = this.temuShipmentFeign.shipmentResult(merchantStoreRespDto.getSellerId(), mallRegion, shipmentResultReq);
        ShipmentResultGetResp shipmentResultGetResp = resultBaseResp.getResult();
        if (Objects.isNull(shipmentResultGetResp)) {
            throw new ProviderServiceException("temu包裹下单结果为空");
        }
        List<PackageInfoResultListDTO> packageInfoResultList = shipmentResultGetResp.getPackageInfoResultList();
        if (CollUtil.isEmpty(packageInfoResultList)) {
            throw new ProviderServiceException("temu包裹下单结果为空");
        }
        PackageInfoResultListDTO packageInfoResultListDTO = packageInfoResultList.get(0);
        if(orderId==793653666756136960L){
            packageInfoResultListDTO.setShippingLabelStatus(3);
        }
        if (3 != packageInfoResultListDTO.getShippingLabelStatus()) {
            String labelRetryKey = getLabelRetryKey(packageSn);
            redisUtil.del(labelRetryKey);
        }

        if (2 == packageInfoResultListDTO.getShippingLabelStatus()) {
            String errorMsg = "";
            if (StrUtil.isNotEmpty(packageInfoResultListDTO.getFailReasonText())) {
                errorMsg += "错误原因: " + packageInfoResultListDTO.getFailReasonText();
            }
            if (StrUtil.isNotEmpty(packageInfoResultListDTO.getSolutionText())) {
                errorMsg += "解决方式: " + packageInfoResultListDTO.getSolutionText();
            }
            TemuShipmentRecord existRecord = this.temuShipmentRecordService.getTemuShipmentRecordByPackageSn(packageSn);
            if (Objects.isNull(existRecord)) {
                throw new ProviderServiceException("temu包裹记录已被删除，包裹号 = " + packageSn);
            }
            this.temuShipmentRecordService.fail(existRecord.getId());
            throw new ProviderServiceException(errorMsg, BasePoConstant.YES);
        }
        CarriageNoRespDto respDto = new CarriageNoRespDto();
        respDto.setScanFormStatus(ScanFormStatusEnum.NONE.getCode());
        if (needScanForm != null && needScanForm) {
            respDto.setScanFormStatus(ScanFormStatusEnum.PENDING.getCode());
        }
        if (1 == packageInfoResultListDTO.getShippingLabelStatus() && StrUtil.isNotBlank(packageInfoResultListDTO.getTrackingNumber())) {
            respDto.setStatus(STATUS_SUCCESS);
            respDto.setCarriageNo(packageInfoResultListDTO.getTrackingNumber());
            TemuShipmentRecord existRecord = this.temuShipmentRecordService.getTemuShipmentRecordByPackageSn(packageSn);
            if (Objects.isNull(existRecord)) {
                throw new ProviderServiceException("temu包裹记录已被删除，包裹号 = " + packageSn);
            }
            this.temuShipmentRecordService.updateCarriageNo(existRecord.getId(), packageInfoResultListDTO.getTrackingNumber());
            respDto.setStatus(STATUS_SUCCESS);
            respDto.setCarriageNo(packageInfoResultListDTO.getTrackingNumber());
            if (ORIGIN_ALREADY_BOUGHT_LABEL.equals(existRecord.getOrigin())) {
                respDto.setOrderProgressType(ORDER_PROGRESS_TYPE_TUME_ALREADY_BOUGHT_LABEL);
            }
        } else if (3 == packageInfoResultListDTO.getShippingLabelStatus()){
            String labelRetryKey = getLabelRetryKey(packageSn);
            Long count = redisUtil.increment(labelRetryKey, Duration.ofDays(1).getSeconds());
            log.info("packageSn={}-labelRetryKey={}-count={}",packageSn,labelRetryKey,count);
            if(count >= 3L){
                redisUtil.del(labelRetryKey);
                throw new ProviderServiceException("temu物流商返回下单失败（状态3），请到店铺后台确认包裹状态", BasePoConstant.YES);
            }else{
                respDto.setStatus(STATUS_SYNCING);
            }
        } else {
            respDto.setStatus(STATUS_SYNCING);
        }
        return respDto;
    }
    private static String getLabelRetryKey(String packageSn) {
        return String.format(LABEL_RETRY_KEY, packageSn);
    }

    /**
     * po单有下单记录时，如果有成功或者失败 直接取之前下单记录的结果
     */
    private CarriageNoRespDto getResultFromExistRecord(CarriageNoRepDto dto, Long orderId, Long merchantStoreId, TemuShipmentRecord existRecord, Boolean needScanForm) {

        this.temuShipmentRecordOrderRelManage.saveIfNotExist(dto.getOutOrderNo(), orderId);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(MERCHANT_STORE_ID, merchantStoreId);
        jsonObject.put(PACKAGE_SN, existRecord.getPackageSn());
        jsonObject.put(NEED_SCAN_FORM, needScanForm);
        String extraInfo = jsonObject.toJSONString();

        //有物流下单成功，但是之后我们定时获取运单/面单，token失效的情况
        //这下面的操作是为了在上面说的token失效这种情况下，重新生成运单能继续正常走temu在线下单的流程

        //创建包裹成功(temuShipmentRecord有记录）不能下新单，只能继续用旧的temu在线物流单获取运单和面单
        //成功不能下新单
        //下单失败失败（创建包裹成功，获取下单结果是失败）才能更新在线物流单
        if (existRecord.getShippingLabelStatus().equals(1)) {
            CarriageNoRespDto respDto = new CarriageNoRespDto();
            respDto.success(existRecord.getTrackingNumber(), existRecord.getShippingLabelUrl(), existRecord.getShippingLabelUrl());
            respDto.setExtraInfo(extraInfo);
            if (needScanForm) {
                respDto.setScanFormStatus(ScanFormStatusEnum.SUCCESS.getCode());
            }
            if (ORIGIN_ALREADY_BOUGHT_LABEL.equals(existRecord.getOrigin())) {
                respDto.setOrderProgressType(ORDER_PROGRESS_TYPE_TUME_ALREADY_BOUGHT_LABEL);
            }
            return respDto;
        } else if (existRecord.getShippingLabelStatus().equals(0)) {
            CarriageNoRespDto respDto = new CarriageNoRespDto();
            if (needScanForm) {
                respDto.setScanFormStatus(ScanFormStatusEnum.PENDING.getCode());
            }
            if (StrUtil.isNotEmpty(existRecord.getTrackingNumber())) {
                respDto.setStatus(STATUS_SUCCESS);
                respDto.setCarriageNo(existRecord.getTrackingNumber());
                if (ORIGIN_ALREADY_BOUGHT_LABEL.equals(existRecord.getOrigin())) {
                    respDto.setOrderProgressType(ORDER_PROGRESS_TYPE_TUME_ALREADY_BOUGHT_LABEL);
                }
            } else {
                respDto.setStatus(CarriageNoRecodeConstant.STATUS_SYNCING);
            }
            respDto.setExtraInfo(extraInfo);
            return respDto;
        }
        return null;
    }

    private String getWarehouseId(CarriageNoRepDto dto, Long onlineLogisticsIssuingBayId, Long merchantStoreId) {
        TemuUsableWarehouseDto usableWarehouse;
        if (NumberUtils.greaterZero(onlineLogisticsIssuingBayId)) {
            usableWarehouse = this.onlineLogisticsWarehouseFeign.usableWarehouseByOnlineIssuingBayId(merchantStoreId, onlineLogisticsIssuingBayId);
        } else {
            Long issuingBayId = dto.getIssuingBayId();
            log.info("temu online logistics, getMerchantStoreId = {}, getIssuingBayId = {}", merchantStoreId, issuingBayId);
            usableWarehouse = this.onlineLogisticsWarehouseFeign.findUsableWarehouse(merchantStoreId, issuingBayId, TEMU_SEMI.getCode());
        }
        List<OnlineLogisticsWarehouseDto> warehouseDtoList = usableWarehouse.getWarehouseDtoList();
        log.info("temu online logistics, usableWarehouse = {}", JSONObject.toJSONString(usableWarehouse));

        if (CollUtil.isEmpty(warehouseDtoList)) {
            String issuingBayName = StrUtil.isEmpty(usableWarehouse.getOnlineIssuingBayName())
                    ? "" : "(" + usableWarehouse.getOnlineIssuingBayName() + ")";
            throw new BusinessException("当前订单发货仓"
                    + issuingBayName
                    + "未映射temu仓库，请去【店铺管理】 - 【半托管/本本发货设置】进行映射");
        }
        return warehouseDtoList.get(0).getWarehouseId();
    }

    @Override
    protected String requestProvider(Object jsonBody, Object config, String eventNo, Integer eventType) {
        return null;
    }

    private List<OrderSendInfoListDTO> genOrderSendInfoListByTemuOrderShipmentInfoDto(TemuOrderShipmentInfoDto dto,
                                                                                      List<CarriageProductDto> productList) {

        List<TemuOrderShipmentItemDto> itemList = dto.getItemList();
        if (CollUtil.isNotEmpty(itemList)) {
            itemList = itemList.stream().filter(a -> TemuOrderStatusEnum.UN_SHIPPING.getStatusValue().equals(a.getOrderStatus())).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(itemList)) {
            return Lists.newArrayList();
        }
        Map<String, Integer> outItemIdKeyNumMap = productList.stream()
                .collect(Collectors.toMap(CarriageProductDto::getOutItemId, CarriageProductDto::getNum, (k1, k2) -> k1));

        List<OrderSendInfoListDTO> orderSendInfoList = new ArrayList<>();
        for (TemuOrderShipmentItemDto itemDto : itemList) {
            int canSendQuantity = itemDto.getQuantity();
            Integer orderItemNum = outItemIdKeyNumMap.get(itemDto.getOrderSn());
            if (!NumberUtils.greaterZero(orderItemNum)) {
                continue;
            }
            if (NumberUtils.greaterZero(itemDto.getShipmentQuantity())) {
                canSendQuantity = canSendQuantity - itemDto.getShipmentQuantity();
            }
            canSendQuantity = Math.min(canSendQuantity, orderItemNum);
            if (!NumberUtils.greaterZero(canSendQuantity)) {
                continue;
            }
            OrderSendInfoListDTO infoListDTO = new OrderSendInfoListDTO();
            infoListDTO.setQuantity(canSendQuantity);
            infoListDTO.setOrderSn(itemDto.getOrderSn());
            infoListDTO.setParentOrderSn(dto.getParentOrderSn());
            orderSendInfoList.add(infoListDTO);
        }

        if (CollUtil.isEmpty(orderSendInfoList)) {
            return Lists.newArrayList();
        }

        return orderSendInfoList;
    }


    private static MerchantStoreMailingDateTypeEnum calculateLogisticsMailingDateOffsetType(LogisticsChannelRespDto logisticsChannelRespDto, String code) {
        boolean needSetMailingDateOffset = isNeedScanForm(logisticsChannelRespDto.getStandardName());
        if (!needSetMailingDateOffset) {
            return null;
        }
        return MerchantStoreMailingDateTypeEnum.findByCode(code);
    }

    private ShipmentUpdateReq genUpdateShipmentReq(CarriageNoRepDto dto,
                                                   MerchantStoreRespDto store,
                                                   String temuWarehouseId,
                                                   TemuOrderShipmentInfoDto canShipQuantityInfo,
                                                   LogisticsChannelRespDto logisticsChannel,
                                                   String mallRegion,
                                                   TemuShipmentRecord existRecord,
                                                   List<OrderSendInfoListDTO> orderSendInfoList) {
        MerchantStoreMailingDateTypeEnum mailingDateType = calculateLogisticsMailingDateOffsetType(logisticsChannel, store.getMailingDateType());

        ShipmentUpdateReq req = new ShipmentUpdateReq();
        RetrySendPackageRequestListDTO requestListDTO = new RetrySendPackageRequestListDTO();
        if (mailingDateType != null) {
            requestListDTO.setUspsMailingDateOffset(mailingDateType.getMailingDateOffset());
        }

        requestListDTO.setPackageSn(existRecord.getPackageSn());
        Long shipCompanyId = TemuShipCompanyEnum.getShipCompanyIdByChannelStandrdName(logisticsChannel.getStandardName());
        if (!NumberUtils.greaterZero(shipCompanyId)) {
            String shipCompanyIdStr = ecommerceLogisticsService.getServiceProviderId(MerchantStorePlatformEnum.TEMU.getCode(), logisticsChannel.getStandardName());
            if (StrUtil.isNotEmpty(shipCompanyIdStr) && StrUtil.isNumeric(shipCompanyIdStr)) {
                shipCompanyId = Long.valueOf(shipCompanyIdStr);
            }
        }
        existRecord.setShipCompanyId(shipCompanyId);
        requestListDTO.setShipCompanyId(shipCompanyId);
        requestListDTO.setWarehouseId(temuWarehouseId);
        double length;
        double width;
        double height;
        double weight;

        if (TemuUtils.isUs(canShipQuantityInfo.getSiteId(), canShipQuantityInfo.getAddressJson())) {
            length = DoubleUtils.divide(dto.getMaxBoxLength(), this.cmToInchRate);
            width = DoubleUtils.divide(dto.getMaxBoxWidth(), this.cmToInchRate);
            height = DoubleUtils.divide(dto.getMaxBoxHeight(), this.cmToInchRate);
            weight = DoubleUtils.divide(dto.getWeight(), this.gToPoundRate);

            requestListDTO.setDimensionUnit("in");
            requestListDTO.setWeightUnit("lb");
        } else {
            length = dto.getMaxBoxLength();
            width = dto.getMaxBoxWidth();
            height = dto.getMaxBoxHeight();
            weight = DoubleUtils.divide(dto.getWeight(), WEIGHT_RATE);

            requestListDTO.setDimensionUnit("cm");
            requestListDTO.setWeightUnit("kg");
        }
        if (YAMATO.name().equalsIgnoreCase(logisticsChannel.getStandardName()) || SAGAWA.name().equalsIgnoreCase(logisticsChannel.getStandardName())) {
            LocalDateTime pickupStartTime = DateUtil.addDaysAndSkipWeekend(LocalDateTime.now(), 2, 10, 0);
            LocalDateTime pickupEndTime = DateUtil.addDaysAndSkipWeekend(LocalDateTime.now(), 2, 15, 0);
            requestListDTO.setPickupStartTime(pickupStartTime.toEpochSecond(ZoneOffset.of("+08:00")));
            requestListDTO.setPickupEndTime(pickupEndTime.toEpochSecond(ZoneOffset.of("+08:00")));
        }

        requestListDTO.setWeight(String.valueOf(weight));
        requestListDTO.setLength(String.valueOf(length));
        requestListDTO.setWidth(String.valueOf(width));
        requestListDTO.setHeight(String.valueOf(height));
//            requestListDTO.setChannelId(channelId);
        requestListDTO.setShipLogisticsType(logisticsChannel.getMethodName());
        if (MerchantStoreTypeEnum.TEMU_LOCAL.getCode().equals(store.getType()) &&
                TemuUtils.isUs(canShipQuantityInfo.getSiteId(), canShipQuantityInfo.getAddressJson())) {
            requestListDTO.setWeight(String.valueOf((int) Math.ceil(weight)));
        }

        requestListDTO.setOrderSendInfoList(orderSendInfoList);
        req.setMallRegion(mallRegion);
        req.setRetrySendPackageRequestList(Collections.singletonList(requestListDTO));
        return req;
    }
    /**
     * 构造请求参数
     */
    private ShipmentCreateReq genCreateShipmentReq(CarriageNoRepDto dto,
                                                   Long orderId,
                                                   MerchantStoreRespDto store,
                                                   String temuWarehouseId,
                                                   TemuOrderShipmentInfoDto canShipQuantityInfo,
                                                   LogisticsChannelRespDto logisticsChannel,
                                                   Long shipCompanyId,
                                                   String mallRegion,
                                                   List<OrderSendInfoListDTO> orderSendInfoList) {
        MerchantStoreMailingDateTypeEnum mailingDateType = calculateLogisticsMailingDateOffsetType(logisticsChannel, store.getMailingDateType());
        boolean isY2 = platformOrderExtendFeign.isMatchByOrderId(orderId, PlatformOrderExtendValueEnum.TEMU_Y2_ADVANCE_SALE);
        ShipmentCreateReq req = new ShipmentCreateReq();
        req.setShipLater(true);
        //如果是Y2，自动履约截止时间传216小时
        if (isY2) {
            req.setShipLaterLimitTime(216L);
        } else {
            req.setShipLaterLimitTime(store.getOnlineLogisticsLimitTime() / 3600);
        }
        req.setSendType(0);
        req.setMallRegion(mallRegion);
        ShipmentCreateSendRequestListDTO requestListDTO = new ShipmentCreateSendRequestListDTO();
        requestListDTO.setWarehouseId(temuWarehouseId);
        double length;
        double width;
        double height;
        Double weight;
        if (mailingDateType != null) {
            requestListDTO.setUspsMailingDateOffset(mailingDateType.getMailingDateOffset());
        }

        //物流商是YAMATO SAGAWA 则需要揽收时间
        if (YAMATO.name().equalsIgnoreCase(logisticsChannel.getStandardName()) || SAGAWA.name().equalsIgnoreCase(logisticsChannel.getStandardName())) {
            LocalDateTime pickupStartTime = DateUtil.addDaysAndSkipWeekend(LocalDateTime.now(), 2, 10, 0);
            LocalDateTime pickupEndTime = DateUtil.addDaysAndSkipWeekend(LocalDateTime.now(), 2, 15, 0);
            requestListDTO.setPickupStartTime(pickupStartTime.toEpochSecond(ZoneOffset.of("+08:00")));
            requestListDTO.setPickupEndTime(pickupEndTime.toEpochSecond(ZoneOffset.of("+08:00")));
        }

        if (TemuUtils.isUs(canShipQuantityInfo.getSiteId(), canShipQuantityInfo.getAddressJson())) {
            length = DoubleUtils.divide(dto.getMaxBoxLength(), this.cmToInchRate);
            width = DoubleUtils.divide(dto.getMaxBoxWidth(), this.cmToInchRate);
            height = DoubleUtils.divide(dto.getMaxBoxHeight(), this.cmToInchRate);
            weight = DoubleUtils.divide(dto.getWeight(), this.gToPoundRate);

            requestListDTO.setDimensionUnit("in");
            requestListDTO.setWeightUnit("lb");
        } else {
            length = dto.getMaxBoxLength();
            width = dto.getMaxBoxWidth();
            height = dto.getMaxBoxHeight();
            weight = DoubleUtils.divide(dto.getWeight(), WEIGHT_RATE);

            requestListDTO.setDimensionUnit("cm");
            requestListDTO.setWeightUnit("kg");
        }
        requestListDTO.setLength(String.valueOf(length));
        requestListDTO.setWidth(String.valueOf(width));
        requestListDTO.setHeight(String.valueOf(height));
        requestListDTO.setWeight(String.valueOf(weight));

        if (MerchantStoreTypeEnum.TEMU_LOCAL.getCode().equals(store.getType()) &&
                TemuUtils.isUs(canShipQuantityInfo.getSiteId(), canShipQuantityInfo.getAddressJson())) {
//                requestListDTO.setWeight(String.valueOf((int) Math.ceil(weight)));
//                requestListDTO.setWeightUnit("lb");
//                String extendWeight = String.valueOf((int) Math.ceil(DoubleUtils.divide(dto.getWeight(), gToOzRate)));
//                requestListDTO.setExtendWeight(extendWeight);
//                requestListDTO.setExtendWeightUnit("oz");
            double integerPart = Math.floor(weight);
            double decimalPart = weight - integerPart;
            requestListDTO.setWeight(String.valueOf(weight.intValue()));
            requestListDTO.setExtendWeight(String.valueOf((int) (decimalPart * 16)));
            requestListDTO.setWeightUnit("lb");
            requestListDTO.setExtendWeightUnit("oz");
        }

        requestListDTO.setShipCompanyId(shipCompanyId);
        requestListDTO.setShipLogisticsType(logisticsChannel.getMethodName());
        requestListDTO.setOrderSendInfoList(orderSendInfoList);
        req.setSendRequestList(Collections.singletonList(requestListDTO));
        return req;
    }

    @Override
    public CarriageScanFormDto scanForm(ScanFormBo dto) {
        CarriageNoRecode recode = dto.getRecode();
        OrderDTO orderDTO = this.orderFeign.findById(recode.getOrderId(), "outOrderNo,merchantStoreId");

        String extraInfoJson = recode.getExtraInfo();
        JSONObject extraInfo = JSON.parseObject(recode.getExtraInfo());
        String tailExtraInfo = extraInfo.getString(TAIL_EXTRA_INFO);
        if (StringUtils.isNotBlank(tailExtraInfo)) {
            extraInfoJson = tailExtraInfo;
        }
        JSONObject extraInfoJsonObject = JSONObject.parseObject(extraInfoJson);
        Boolean needScanForm = extraInfoJsonObject.getBoolean(NEED_SCAN_FORM);
        CarriageScanFormDto result = new CarriageScanFormDto();
        if (needScanForm == null || !needScanForm) {
            result.setScanFormStatus(ScanFormStatusEnum.NONE.getCode());
            return result;
        }
        result.setScanFormStatus(ScanFormStatusEnum.FAILED.getCode());
        if (orderDTO == null) {
            result.setScanFormErrorMsg("订单不存在");
            return result;
        }
        MerchantStoreRespDto store = this.merchantStoreFeign.findById(orderDTO.getMerchantStoreId(), "sellerId,onlineLogisticsLimitTime,type,mailingDateType");
        if (store == null) {
            result.setScanFormErrorMsg("店铺不存在");
            return result;
        }
        TemuShipmentRecord existRecord = this.temuShipmentRecordService.getTemuShipmentRecordByOutOrderNo(orderDTO.getOutOrderNo());
        if (existRecord == null) {
            result.setScanFormErrorMsg("temu第三方单号未创建过订单");
            return result;
        }
        if (ScanFormStatusEnum.SUCCESS.getCode().equals(existRecord.getScanFormStatus())) {
            result.setScanFormSn(existRecord.getPackageSn());
            result.setPackageSn(existRecord.getPackageSn());
            result.setScanFormStatus(ScanFormStatusEnum.SUCCESS.getCode());
        }
        if (!ScanFormStatusEnum.SUCCESS.getCode().equals(result.getScanFormStatus())) {
            getScanFormDtoByTemu(result, recode, orderDTO, store, existRecord);
        }
        if (ScanFormStatusEnum.SUCCESS.getCode().equals(result.getScanFormStatus())) {
            TemuShipmentRecord updateTemuShipment = new TemuShipmentRecord();
            updateTemuShipment.setId(existRecord.getId());
            updateTemuShipment.setScanFormStatus(result.getScanFormStatus());
            updateTemuShipment.setScanFormErrorMsg(result.getScanFormErrorMsg());
            this.temuShipmentRecordService.updateScanForm(updateTemuShipment);
            carriageNoRecodeService.updateScanFormSuccess(recode.getId());
        }
        return result;
    }

    @Nullable
    private void getScanFormDtoByTemu(CarriageScanFormDto result, CarriageNoRecode recode, OrderDTO orderDTO, MerchantStoreRespDto store, TemuShipmentRecord existRecord) {
        String mallRegion = this.platformOrderExtendFeign.getOnlineOrderRegion(orderDTO.getOutOrderNo(), recode.getOrderId());
        LogisticsScanformCreateReq req = new LogisticsScanformCreateReq();

        req.setWarehouseId(existRecord.getWarehouseId());
        req.setShipCompanyId(String.valueOf(existRecord.getShipCompanyId()));
        req.setPackageSnList(Lists.newArrayList(existRecord.getPackageSn()));
        BaseResp<LogisticsScanformCreateResp> resp;
        try {
            LogisticsScanformCreateParam param = new LogisticsScanformCreateParam();
            param.setMallId(store.getSellerId());
            param.setMallRegion(mallRegion);
            param.setReq(req);
            log.info("logisticsScanFormCreate recodeid={}, param {}", recode.getId(), JSONObject.toJSONString(param));
            resp = temuShipmentFeign.logisticsScanformCreateNew(param);
            log.info("logisticsScanFormCreate resp {}", JSONObject.toJSONString(resp));
        } catch (Exception e) {
            result.setScanFormErrorMsg(e.getMessage());
            return;
        }
        if (!resp.getSuccess()) {
            result.setScanFormErrorMsg(resp.getErrorMsg());
            if (StringUtils.isNotBlank(resp.getErrorMsg()) &&
                    resp.getErrorMsg().contains("have been associated with other scan forms.")) {
                result.setPackageSn(existRecord.getPackageSn());
                result.setScanFormStatus(ScanFormStatusEnum.SUCCESS.getCode());
            }
            return;
        }
        LogisticsScanformCreateResp createResp = resp.getResult();
        if (createResp == null) {
            result.setScanFormErrorMsg("scan返回值为空");
            return;
        }
        List<LogisticsScanformCreateResp.ScanFormInfoListDTO> scanFormInfoList = createResp.getScanFormInfoList();
        result.setScanFormErrorMsg("scanForm包裹中不包括此次请求");
        for (LogisticsScanformCreateResp.ScanFormInfoListDTO formInfoListDTO : scanFormInfoList) {
            if (formInfoListDTO.getPackageSnList().contains(existRecord.getPackageSn())) {
                result.setScanFormSn(formInfoListDTO.getScanFormSn());
                result.setPackageSn(existRecord.getPackageSn());
                result.setScanFormStatus(ScanFormStatusEnum.SUCCESS.getCode());
                result.setScanFormErrorMsg("");
                break;
            }
        }
    }

    public static Boolean isNeedScanForm(String standardName) {
        return USPS.name().equals(standardName);
    }


    public static void main(String[] args) {
        LocalDateTime localDateTime = DateUtil.addDaysAndSkipWeekend(LocalDateTime.now(), 2, 9, 0);
        System.out.println(localDateTime);
        LocalDateTime localDateTime2 = DateUtil.addDaysAndSkipWeekend(LocalDateTime.now(), 2, 16, 0);
        System.out.println(localDateTime.getSecond());
        System.out.println(localDateTime.toEpochSecond(ZoneOffset.of("+08:00")));
        System.out.println(localDateTime2.toEpochSecond(ZoneOffset.of("+08:00")));

    }
}
