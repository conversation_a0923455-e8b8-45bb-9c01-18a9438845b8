package com.sdsdiy.logisticsimpl.service.support.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.DoubleUtils;
import com.sdsdiy.logisticsapi.dto.*;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum;
import com.sdsdiy.logisticsimpl.exception.ProviderServiceException;
import com.sdsdiy.logisticsimpl.service.support.LogisticsSupportAbstract;
import com.sdsdiy.logisticsimpl.service.support.dto.CancelLabelDto;
import com.sdsdiy.logisticsimpl.service.support.dto.ConfirmCarriageDto;
import com.sdsdiy.logisticsimpl.service.support.dto.SyncCarriageNoBO;
import com.sdsdiy.logisticsimpl.service.support.dto.verykShip.*;
import lombok.Data;
import okhttp3.HttpUrl;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

import static com.sdsdiy.logisticsapi.constant.CarriageNoRecodeConstant.*;
import static com.sdsdiy.logisticsapi.constant.CarriageRequestHistoryConstant.*;
import static com.sdsdiy.logisticsimpl.config.request.LogClientHttpRequest.EVENT_NO_FLAG;
import static com.sdsdiy.logisticsimpl.config.request.LogClientHttpRequest.EVENT_TYPE_FLAG;


@Service
public class VerykShipServiceImpl extends LogisticsSupportAbstract<VerykShipServiceImpl.Config> {


    public static final String TIMEZONE = "Asia/Shanghai";
    public static final String LB = "lb";
    public static final String KG = "kg";
    public static final String IN = "in";
    public static final String CM = "cm";


    private static String signature(Map<String, String> parameters, String secret) {
        // Convert keys to lowercase and remove 'sign' if present
        Map<String, String> lowerCaseParams = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
        lowerCaseParams.putAll(parameters);
        lowerCaseParams.remove("sign");

        // Sort parameters by key
        Map<String, String> sortedParams = new TreeMap<>(lowerCaseParams);

        // Build query string
        List<String> queryParts = new ArrayList<>();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            try {
                String encodedValue = URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.toString())
                        .replace("%7E", "~");
                queryParts.add(entry.getKey() + "=" + encodedValue);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException("Failed to encode parameter", e);
            }
        }

        String queryString = String.join("&", queryParts);

        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256_HMAC.init(secret_key);

            byte[] hashBytes = sha256_HMAC.doFinal(queryString.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashBytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Failed to generate signature", e);
        }
    }

    private static void checkRespone(VerykShipResponseDto quoteResponse) {
        if (quoteResponse.getStatus() != BasePoConstant.YES) {
            throw new ProviderServiceException(quoteResponse.getMessage());
        }
    }

    private static Map<String, String> getRequestParam(Config config, String timestamp) {
        Map<String, String> params = new HashMap<>();
        params.put("id", config.getAppId());
        params.put("timestamp", timestamp);
        params.put("format", "json");
        return params;
    }

    private static HttpHeaders getHeaders(String eventNo, Integer eventType, String timestamp) {
        HttpHeaders headers = new HttpHeaders();
        headers.set(EVENT_NO_FLAG, eventNo);
        headers.set(EVENT_TYPE_FLAG, Objects.toString(eventType, ""));
        headers.set("timestamp", timestamp);
        headers.set("Accept-Language", "zh-CN");
        headers.set("User-Agent", "Mozilla/4.0 VERYK INC. API");
        headers.set("Accept-Encoding", "gzip");
//        headers.setCacheControl(CacheControl.noCache());
        return headers;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {

        HttpHeaders headers = new HttpHeaders();
        String s = String.valueOf(System.currentTimeMillis() / 1000);
        getHeaders("123", 1, s);
        headers.setCacheControl(CacheControl.noCache());
        Map<String, String> params = new HashMap<>();
        params.put("id", "614");
        params.put("timestamp", s);
        params.put("format", "json");
        params.put("action", Action.quote);
        String sign = signature(params, "3230b0a6194e44290a152a45b0364a55");
        params.put("sign", sign);
        HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse("https://www.verykship.com/api")).newBuilder();
        params.forEach(urlBuilder::addQueryParameter);
        String fullUrl = urlBuilder.build().toString();
        System.out.println(fullUrl);
    }

    @Override
    public Config getConfig(String configStr) {
        if (StringUtils.isEmpty(configStr)) {
            throw new BusinessException("config param empty.");
        }
        Config config = JSONUtil.toBean(configStr, Config.class);
        if (StringUtils.isEmpty(config.getPackageType())) {
            config.setPackageType(VerykShipOrderParam.PackageType.PARCEL);
        }
        if (StringUtils.isEmpty(config.getOrderState())) {
            config.setOrderState(VerykShipOrderParam.OrderState.ORDER);
        }
        return config;
    }


    @Override
    public Long getServiceProviderId() {
        return LogisticsServiceProviderEnum.SERVICE_PROVIDER_VERYK_SHIP.getNumber();
    }

    private static Destination getDestination(CarriageNoAddressDto address) {
        return Destination.builder()
                .name(address.getConsignee())
                .city(address.getCity())
                .province(address.getProvince())
                .regionId(address.getCountryCode())
                .address(address.getAddress1() + address.getAddress2())
                .address2(address.getAddress3())
                .postalcode(address.getPostcode())
                .mobilePhone(address.getMobilePhone())
                .build();
    }

    private static Initiation getInitiation(LogisticsSupportAbstract.LogisticsConfigProcessed processed) {
        return Initiation.builder()
                .name(processed.getShipperAddress().getName())
                .city(processed.getShipperAddress().getCity())
                .province(processed.getShipperAddress().getProvince())
                .company(processed.getShipperAddress().getCompany())
                .postalcode(processed.getShipperAddress().getPostCode())
                .address(processed.getShipperAddress().getAddress1() + processed.getShipperAddress().getAddress2())
                .address2(processed.getShipperAddress().getAddress3())
                .mobilePhone(processed.getShipperAddress().getMobile())
                .regionId(processed.getShipperAddress().getCountry())
                .build();
    }



    @Override
    public TrackDto queryExpressTrackInfo(ExpressTrackInfoDto dto) {
        LogisticsConfigProcessed processed = getLogisticsConfig(dto.getLogisticsId(), dto.getIssuingBayId());
        Config config = processed.getConfig();
        String extraInfo = dto.getExtraInfo();
        VerykShipExtraInfoDto extraInfoDto = JSON.parseObject(extraInfo, VerykShipExtraInfoDto.class);
        String timezone = extraInfoDto.getTimezone() == null ? TIMEZONE : extraInfoDto.getTimezone();
        VerykShipTrackParam param = VerykShipTrackParam.builder()
                .keyword(extraInfoDto.getOrderId())
                .timezone(timezone)
                .build();
        String respStr = this.requestProvider(JSON.toJSONString(param), config, dto.getEventNo(), REQUEST_TYPE_TRACK);
        VerykShipResponseDto<List<VerykShipTrackResponse>> respDto = JSON.parseObject(respStr, new TypeReference<VerykShipResponseDto<List<VerykShipTrackResponse>>>() {
        });
        checkRespone(respDto);

        List<VerykShipTrackResponse> response = respDto.getResponse();
        if (CollUtil.isEmpty(response)) {
            return null;
        }
        List<VerykShipTrackResponse.Status> list = response.get(0).getList();
        TrackDto trackDto = new TrackDto();
        trackDto.setTrackInfoList(list.stream().map(status -> {
            TrackInfo trackInfo = new TrackInfo();
            trackInfo.setInfo(status.getContext());
            trackInfo.setTime(status.getDatetime().get(timezone));
            trackInfo.setSortTime(new Date(status.getTimestamp()));
            return trackInfo;
        }).collect(Collectors.toList()));

        return trackDto;
    }

    @Override
    public CarriageNoRespDto expressSheetLabel(ExpressSheetLabelDto dto, long logisticsId) {
        LogisticsConfigProcessed processed = getLogisticsConfig(logisticsId, dto.getIssuingBayId());
        Config config = processed.getConfig();
        String extraInfo = dto.getExtraInfo();
        VerykShipExtraInfoDto extraInfoDto = JSON.parseObject(extraInfo, VerykShipExtraInfoDto.class);
        VerykShipLabelParam param = VerykShipLabelParam.builder()
                .id(extraInfoDto.getOrderId())
                .option(BasePoConstant.YES)
                .build();
        String respStr = this.requestProvider(JSON.toJSONString(param), config, dto.getEventNo(), REQUEST_TYPE_LABEL);
        VerykShipResponseDto<VerykShipLabelResponse> resp = JSON.parseObject(
                respStr,
                new TypeReference<VerykShipResponseDto<VerykShipLabelResponse>>() {
                });
        checkRespone(resp);
        String labelBase64 = resp.getResponse().getLabel();
        return this.getSheetLabelByBase64Str(resp.getResponse().getWaybillNumber(), labelBase64);
    }

    @Override
    public CarriageNoRespDto syncCarriageNo(SyncCarriageNoBO dto, TenantLogisticsRespDto logistics, String eventNo) {
        LogisticsSupportAbstract<Config>.LogisticsConfigProcessed processed = getLogisticsConfig(dto.getLogisticsId(), dto.getIssuingBayId());
        Config config = processed.getConfig();
        HashMap<Object, Object> map = new HashMap<>();
        String extraInfo = dto.getExtraInfo();
        VerykShipExtraInfoDto extraInfoDto = JSON.parseObject(extraInfo, VerykShipExtraInfoDto.class);
        map.put("id",extraInfoDto.getOrderId());
        String resStr = this.requestProvider(JSON.toJSONString(map), config, eventNo, REQUEST_TYPE_SYNC);
        VerykShipResponseDto<VerykShipOrderDetailResponse> resp = JSON.parseObject(resStr, new TypeReference<VerykShipResponseDto<VerykShipOrderDetailResponse>>() {
        });
        checkRespone(resp);
        String waybillNumber = resp.getResponse().getWaybillNumber();
        CarriageNoRespDto respDto = new CarriageNoRespDto();
        if (StrUtil.isBlank(waybillNumber)) {
            respDto.setStatus(STATUS_SYNCING);
        } else {
            respDto.setCarriageNo(waybillNumber);
            respDto.setStatus(STATUS_SUCCESS);
        }
        return respDto;
    }

    @Override
    public void confirmCarriage(ConfirmCarriageDto dto) {

//        LogisticsConfigProcessed processed = getLogisticsConfig(dto.getLogisticsId(), dto.getIssuingBayId());
//        Conifg config = processed.getConfig();
//        VerykShipOrderParam orderParam = getConfirmOrderParam(dto, processed);
//        String respStr = this.requestProvider(JSON.toJSONString(orderParam), config, dto.getEventNo(), REQUEST_CONFIRM_CARRIAGE);
//        VerykShipResponseDto<VerykShipLabelResponse> shipResponseDto = JSON.parseObject(respStr, new TypeReference<VerykShipResponseDto<VerykShipLabelResponse>>() {
//        });
//        checkRespone(shipResponseDto);

    }

    private VerykShipOrderParam getOrderParam(CarriageNoRepDto dto, LogisticsConfigProcessed processed, VerykShipQuoteResponse.Service service, VerykShipAccountResponse account) {
        CarriageNoAddressDto address = dto.getAddress();
        Config config = processed.getConfig();

        VerykShipOrderParam orderParam = VerykShipOrderParam.builder()
                .serviceId(service.getId())
                .paymentMethod(VerykShipOrderParam.PaymentMethod.ACCOUNT)
                .state(config.getOrderState())
                .initiation(getInitiation(processed))
                .destination(getDestination(address))
                .packageInfo(PackageInfo.builder()
                        .type(config.getPackageType())
                        .packages(getPackages(dto, account))
                        .build())
                .product(dto.getProductList().stream()
                        .map(p -> VerykShipOrderParam.Product.builder()
                                .name(p.getName())
                                .qty(p.getNum())
                                .build())
                        .collect(Collectors.toList())
                )
                .option(Option.builder()
                        .referenceNumber(dto.getExtendNo())
                        .memo("")
                        .build())
                .build();
        return orderParam;
    }

    private @NotNull List<PackageInfo.PackageItem> getPackages(CarriageNoRepDto dto, VerykShipAccountResponse account) {
        return Collections.singletonList(
                PackageInfo.PackageItem.builder()
                        .weight(LB.equals(account.getWeightUnit()) ? DoubleUtils.divide(dto.getWeight(), this.gToPoundRate)
                                : DoubleUtils.divide(dto.getWeight(), 1000D)
                        )
                        .dimension(PackageInfo.Dimension.builder()
                                .height(IN.equals(account.getLengthUnit()) ? DoubleUtils.divide(dto.getMaxBoxHeight(), cmToInchRate)
                                        : dto.getMaxBoxHeight())
                                .width(IN.equals(account.getLengthUnit()) ? DoubleUtils.divide(dto.getMaxBoxWidth(), cmToInchRate)
                                        : dto.getMaxBoxWidth())
                                .length(IN.equals(account.getLengthUnit()) ? DoubleUtils.divide(dto.getMaxBoxLength(), cmToInchRate)
                                        : dto.getMaxBoxWidth())
                                .build())
                        .build()
        );
    }

    private VerykShipOrderParam getConfirmOrderParam(ConfirmCarriageDto dto, LogisticsConfigProcessed processed) {
        CarriageNoAddressDto address = dto.getAddress();
        Config config = processed.getConfig();
        VerykShipExtraInfoDto extraInfoDto = JSON.parseObject(dto.getExtraInfo(), VerykShipExtraInfoDto.class);
        VerykShipOrderParam orderParam = VerykShipOrderParam.builder()
                .token(extraInfoDto.getServiceToken())
                .paymentMethod(VerykShipOrderParam.PaymentMethod.ACCOUNT)
                .state(config.getOrderState())
                .initiation(getInitiation(processed))
                .destination(getDestination(address))
                .packageInfo(PackageInfo.builder()
                        .type(config.getPackageType())
                        .packages(Collections.singletonList(
                                PackageInfo.PackageItem.builder()
                                        .weight(DoubleUtils.divide(dto.getWeight(), this.gToPoundRate))
                                        .dimension(PackageInfo.Dimension.builder()
                                                .height(DoubleUtils.divide(dto.getMaxBoxHeight(), cmToInchRate))
                                                .width(DoubleUtils.divide(dto.getMaxBoxWidth(), cmToInchRate))
                                                .length(DoubleUtils.divide(dto.getMaxBoxLength(), cmToInchRate))
                                                .build())
                                        .build()
                        ))
                        .build())
                .product(dto.getProducts().stream()
                        .map(p -> VerykShipOrderParam.Product.builder()
                                .name(p.getName())
                                .qty(p.getNum())
                                .build())
                        .collect(Collectors.toList())
                )
                .option(Option.builder()
                        .referenceNumber(dto.getExtendNo())
                        .memo("")
                        .build())
                .build();
        return orderParam;
    }

    @Override
    public Boolean cancelLabel(CancelLabelDto dto) {
        LogisticsConfigProcessed processed = getLogisticsConfig(dto.getLogisticsId(), dto.getIssuingBayId());
        Config config = processed.getConfig();
        String extraInfo = dto.getExtraInfo();
        VerykShipExtraInfoDto extraInfoDto = JSON.parseObject(extraInfo, VerykShipExtraInfoDto.class);
        VerykShipCancelOrderParam param = new VerykShipCancelOrderParam();
        param.setId(extraInfoDto.getOrderId());
        param.setReason("");
        String respStr = this.requestProvider(JSON.toJSONString(param), config, dto.getEventNo(), REQUEST_CANCEL_LABEL);
        VerykShipResponseDto responseDto = JSON.parseObject(respStr, VerykShipResponseDto.class);
        checkRespone(responseDto);
        return Boolean.TRUE;
    }




    @Override
    public CarriageNoRespDto applyCarriageNo(CarriageNoRepDto dto, TenantLogisticsRespDto logistics, String eventNo) {
        LogisticsConfigProcessed processed = getLogisticsConfig(logistics.getId(), dto.getIssuingBayId());
        Config config = processed.getConfig();
        VerykShipAccountResponse account = getAccount(config, eventNo);
        VerykShipQuoteResponse.Service service = getService(dto, logistics, eventNo, processed, config, account);
        Assert.validateNull(service, "该地址或邮编不支持配送");
        VerykShipOrderParam orderParam = getOrderParam(dto, processed, service, account);
        String orderRespStr = this.requestProvider(JSON.toJSONString(orderParam), config, eventNo, REQUEST_TYPE_NO);
        VerykShipResponseDto<VerykShipOrderResponse> orderResponse = JSON.parseObject(orderRespStr, new TypeReference<VerykShipResponseDto<VerykShipOrderResponse>>() {
        });
        checkRespone(orderResponse);
        String waybillNumber = orderResponse.getResponse().getWaybillNumber();

        VerykShipExtraInfoDto verykShipExtraInfoDto = new VerykShipExtraInfoDto();
        verykShipExtraInfoDto.setOrderId(orderResponse.getResponse().getId());
        verykShipExtraInfoDto.setServiceId(service.getId());
        verykShipExtraInfoDto.setServiceToken(service.getToken());
        verykShipExtraInfoDto.setLengthUnit(account.getLengthUnit());
        verykShipExtraInfoDto.setWeightUnit(account.getWeightUnit());
        verykShipExtraInfoDto.setTimezone(account.getTimezone());
        CarriageNoRespDto respDto = new CarriageNoRespDto();
        if (StrUtil.isBlank(waybillNumber)) {
            respDto.setStatus(STATUS_SYNCING);
            respDto.setSheetLabelStatus(SHEET_LABEL_STATUS_ING);
        } else {
            respDto.successAndLabelIng(waybillNumber);
        }
        respDto.setExtraInfo(JSON.toJSONString(verykShipExtraInfoDto));
        return respDto;
    }

    private VerykShipAccountResponse getAccount(Config config, String eventNo) {
        String accountRespStr = this.requestProvider(null, config, eventNo, REQUEST_ACCOUNT);
        VerykShipResponseDto<VerykShipAccountResponse> accountResponse = JSON.parseObject(accountRespStr, new TypeReference<VerykShipResponseDto<VerykShipAccountResponse>>() {
        });
        checkRespone(accountResponse);
        return accountResponse.getResponse();
    }

    private VerykShipQuoteResponse.Service getService(CarriageNoRepDto dto, TenantLogisticsRespDto logistics, String eventNo, LogisticsConfigProcessed processed, Config config, VerykShipAccountResponse account) {
        VerykShipQuoteParam quoteParam = VerykShipQuoteParam.builder()
                .initiation(getInitiation(processed))
                .destination(getDestination(dto.getAddress()))
                .packageInfo(PackageInfo.builder()
                        .type(config.getPackageType())
                        .packages(getPackages(dto, account))
                        .build())
                .build();

        String quoteRespStr = this.requestProvider(JSON.toJSONString(quoteParam), config, eventNo, REQUEST_TYPE_OTHER);
        VerykShipResponseDto<List<VerykShipQuoteResponse>> quoteResponse = JSON.parseObject(
                quoteRespStr,
                new TypeReference<VerykShipResponseDto<List<VerykShipQuoteResponse>>>() {
                });
        checkRespone(quoteResponse);
        List<VerykShipQuoteResponse> response = quoteResponse.getResponse();
        VerykShipQuoteResponse.Service service = filterService(logistics, response);
        return service;
    }

    private VerykShipQuoteResponse.Service filterService(TenantLogisticsRespDto logistics, List<VerykShipQuoteResponse> response) {
        if (logistics.getLogisticsChannel() == null) {
            return null;
        }

        String standardName = logistics.getLogisticsChannel().getStandardName();
        return response.stream()
                .filter(quote -> quote.getName().equalsIgnoreCase(standardName))
                .flatMap(quote -> quote.getServices().stream())
                .min(Comparator.comparing(VerykShipQuoteResponse.Service::getCharge))
                .orElse(null);
    }


    @Override
    public String requestProvider(Object jsonBody, Config config, String eventNo, Integer eventType) {
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        HttpHeaders headers = getHeaders(eventNo, eventType, timestamp);
        // Prepare parameters
        Map<String, String> params = getRequestParam(config, timestamp);

        switch (eventType) {
            case REQUEST_TYPE_NO:
                params.put("action", Action.applyCarriageNo);
                break;
            case REQUEST_TYPE_OTHER:
                params.put("action", Action.quote);
                break;
            case REQUEST_TYPE_LABEL:
                params.put("action", Action.label);
                break;
            case REQUEST_CONFIRM_CARRIAGE:
                params.put("action", Action.confirmCarriage);
                break;
            case REQUEST_TYPE_TRACK:
                params.put("action", Action.track);
                break;
            case REQUEST_CANCEL_LABEL:
                params.put("action", Action.cancelOrder);
                break;
            case REQUEST_ACCOUNT:
                params.put("action", Action.account);
                break;
            case REQUEST_TYPE_SYNC:
                params.put("action", Action.orderDetail);
                break;
            default:
                throw new RuntimeException("不支持的请求类型");

        }
        String signature = signature(params, config.getSecret());
        params.put("sign", URLEncoder.encode(signature));

        return this.requestService.postWithQueryParam(config.getHost(), headers, (String) jsonBody,params);

    }



    @Data
    public static class Config {
        private String host;
        private String secret;
        private String appId;
        private String packageType;
        private String orderState;
    }


    public interface Action {
        String applyCarriageNo = "shipment/create";
        String quote = "shipment/quote";
        String cancelOrder = "shipment/void";
        String confirmCarriage = "shipment/submit";
        String track = "shipment/tracking";
        String label = "shipment/label";
        String account = "account";
        String orderDetail = "shipment/detail";
    }
}
