
package com.sdsdiy.logisticsimpl.service.support.dto.gls.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>adeParcelShop_GetByCountryResponse complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType name="adeParcelShop_GetByCountryResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="return" type="{https://adeplus.gls-poland.com/adeplus/pm1/ade_webapi2.php?wsdl}cParcelShopArray"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "adeParcelShop_GetByCountryResponse", propOrder = {
    "_return"
})
public class AdeParcelShopGetByCountryResponse {

    @XmlElement(name = "return", required = true)
    protected CParcelShopArray _return;

    /**
     * ��ȡreturn���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link CParcelShopArray }
     *     
     */
    public CParcelShopArray getReturn() {
        return _return;
    }

    /**
     * ����return���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link CParcelShopArray }
     *     
     */
    public void setReturn(CParcelShopArray value) {
        this._return = value;
    }

}
