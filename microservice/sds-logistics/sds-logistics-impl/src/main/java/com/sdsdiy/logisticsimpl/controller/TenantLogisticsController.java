package com.sdsdiy.logisticsimpl.controller;

import cn.hutool.core.bean.BeanUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.core.base.util.BeanTool;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.logisticsapi.api.TenantLogisticsApi;
import com.sdsdiy.logisticsapi.constant.LogisticsConstant;
import com.sdsdiy.logisticsapi.dto.DeclarationTaxResp;
import com.sdsdiy.logisticsapi.dto.LogisticsListParam;
import com.sdsdiy.logisticsapi.dto.LogisticsSimpleResp;
import com.sdsdiy.logisticsapi.dto.base.LogisticsChannelRespDto;
import com.sdsdiy.logisticsapi.dto.base.ServiceProviderRespDto;
import com.sdsdiy.logisticsapi.dto.base.TenantAddressRespDto;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsapi.dto.logistics.CopyLogisticsParam;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsEditParam;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsFBASaveParam;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsFbmBatchSaveParam;
import com.sdsdiy.logisticsapi.dto.tenant.logistics.TenantLogisticsUpdateParam;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.LogisticsAndLogisticsSourceResp;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.TenantLogisticsAsConditionParam;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.TenantLogisticsSaasPageResp;
import com.sdsdiy.logisticsapi.enums.LogisticsCodeIdEnum;
import com.sdsdiy.logisticsdata.dto.base.LogisticsRespDto;
import com.sdsdiy.logisticsdata.vo.logistics.LogisticsGroupToSelectResp;
import com.sdsdiy.logisticsimpl.entity.po.LogisticsChannel;
import com.sdsdiy.logisticsimpl.entity.po.ServiceProvider;
import com.sdsdiy.logisticsimpl.entity.po.TenantLogistics;
import com.sdsdiy.logisticsimpl.manager.TenantLogisticsMapperManager;
import com.sdsdiy.logisticsimpl.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.common.base.constant.tenant.TenantCommonConstant.SDSDIY_TENANT_ID;

/**
 * 物流渠道表(TenantLogistics)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-01 10:37:08
 */
@RestController
@RequiredArgsConstructor
public class TenantLogisticsController implements TenantLogisticsApi {
    private final ServiceProviderService serviceProviderService;
    /**
     * 服务对象
     */
    @Resource
    private TenantLogisticsService tenantLogisticsService;
    @Resource
    private TenantLogisticsMapperManager tenantLogisticsMapperManager;
    @Resource
    private CountryExpressInfoNewService countryExpressInfoNewService;
    @Resource
    private TenantServiceProviderAccountService tenantServiceProviderAccountService;

    @Override
    public List<LogisticsSimpleResp> getSpanTenantAreaLogisticsAndNoAllProduct(Long tenantId) {
        List<TenantLogistics> logisticsList = tenantLogisticsService.getSpanTenantAreaLogisticsAndNoAllProduct(tenantId);
        return BeanTool.copyBeanList(logisticsList, LogisticsSimpleResp.class);
    }

    @Override
    public Map<Long, Long> spanAreaLogisticsNumMap(BaseListReqDto reqDto) {
        return tenantLogisticsService.getSpanAreaLogisticsNumMap(reqDto.getIdList());
    }

    @Override
    public List<LogisticsRespDto> getSpanTenantAreaLogistics(Long tenantId, Long issuingBayAreaId) {
        return tenantLogisticsService.getSpanTenantAreaLogistics(tenantId, issuingBayAreaId);
    }

    @Override
    public List<TenantLogisticsRespDto> getSpanTenantAreaLogistics() {
        List<TenantLogistics> spanTenantAreaLogistics = tenantLogisticsService.getSpanTenantAreaLogistics();
        return RelationsBinder.convertAndBind(spanTenantAreaLogistics, TenantLogisticsRespDto.class);
    }

    @Override
    public PageListResultRespDto<TenantLogisticsRespDto> page(Long tenantId, Long issuingBayAreaId, String keyword, String deliveryMethod, QueryParamHelper queryParamHelper) {
        return tenantLogisticsService.page(tenantId, issuingBayAreaId, keyword, deliveryMethod, queryParamHelper);
    }

    @Override
    public PageListResultRespDto<TenantLogisticsSaasPageResp> saasPage(Long tenantId, Long issuingBayAreaId, String keyword, String deliveryMethod, QueryParamHelper queryParamHelper) {
        return tenantLogisticsService.saasPage(tenantId, issuingBayAreaId, keyword, deliveryMethod, queryParamHelper);
    }

    @Override
    public void fbmBatch(Long tenantId, Long tenantServiceProviderId, TenantLogisticsFbmBatchSaveParam param) {
        tenantLogisticsService.fbmBatch(tenantId, tenantServiceProviderId, param);
    }

    @Override
    public void saveFbaOne(Long tenantId, @Valid TenantLogisticsFBASaveParam param) {
        param.setName(param.getName().trim());
        if (BasePoConstant.YES.equals(param.getSelfPickup())) {
            // 自提
            tenantLogisticsService.addFbaZtOne(tenantId, param);
        } else {
            tenantLogisticsService.addFbaOne(tenantId, param);
        }
    }

    @Override
    public void saveFbaZtOne(Long tenantId, @Valid TenantLogisticsFBASaveParam param) {
        param.setName(param.getName().trim());
        tenantLogisticsService.addFbaZtOne(tenantId, param);
    }

    @Override
    public void saveFbmZtOne(Long tenantId, @Valid TenantLogisticsFBASaveParam param) {
        param.setName(param.getName().trim());
        tenantLogisticsService.addFbmZtOne(tenantId, param);
    }

    @Override
    public void edit(Long tenantId, Long id, @Valid TenantLogisticsEditParam param) {
        TenantLogistics tenantLogistics = BeanTool.copyBean(param, TenantLogistics.class);
        TenantLogistics one = tenantLogisticsService.getOne(tenantId, id);
        if (param.getName() != null && param.getPackingCharges() == null) {
            param.setName(param.getName().trim());
            tenantLogisticsService.checkName(tenantId, tenantLogistics.getName(), id, one.getIssuingBayAreaId());
        }
        tenantLogisticsService.update(tenantId, id, tenantLogistics);
    }

    @Override
    public void updateStatus(Long tenantId, Long id, Integer status) {
        TenantLogistics tenantLogistics = new TenantLogistics();
        tenantLogistics.setStatus(status);
        TenantLogistics one = tenantLogisticsService.getOneById(id);
        Assert.validateNull(one, "物流不存在！");
        if (YES.equals(status) && !LogisticsCodeIdEnum.isZt(one.getCodeId())) {
            Integer count = countryExpressInfoNewService.countByLogisticsId(id);
            Assert.validateTrue(count == 0, "请先配置定价规则");
            //自提、寄付、线上物流无需校验
            if (!LogisticsConstant.noCheckSpecialProviderIdList().contains(one.getServiceProviderId()) && !LogisticsCodeIdEnum.isZtOrConsignment(one.getCodeId()) && !LogisticsCodeIdEnum.FBA.getCodeId().equals(one.getCodeId())) {
                tenantServiceProviderAccountService.checkStatusByLogisticsId(one.getId(), one.getIssuingBayAreaId());
            }
        }
        tenantLogisticsService.update(tenantId, id, tenantLogistics);
    }

    @Override
    public TenantLogisticsRespDto getOneDetail(Long tenantId, Long id) {
        return tenantLogisticsService.getOneDetail(tenantId, id);
    }

    @Override
    public List<TenantLogisticsRespDto> all(Long tenantId) {
        List<TenantLogistics> all = tenantLogisticsService.getAll(tenantId);
        return RelationsBinder.convertAndBind(all, TenantLogisticsRespDto.class, "issuingBayAreaId");
    }

    @Override
    public List<TenantLogisticsRespDto> findByIds(BaseListReqDto reqDto) {
        List<TenantLogistics> list = tenantLogisticsService.findByIds(reqDto.getIdList());
        List<TenantLogisticsRespDto> dtoList = RelationsBinder.convertAndBind(list, TenantLogisticsRespDto.class, reqDto.getFields());
        if (reqDto.getFields().contains("serviceProviderRespDto")) {
            Set<Long> providerIds = ListUtil.toValueSet(TenantLogistics::getServiceProviderId, list);
            List<ServiceProvider> providerList = serviceProviderService.listByIds(providerIds);
            Map<Long, ServiceProvider> providerMap = ListUtil.toMap(ServiceProvider::getId, providerList);
            dtoList.forEach(i -> i.setServiceProviderRespDto(
                    BeanUtil.copyProperties(providerMap.get(i.getServiceProviderId()), ServiceProviderRespDto.class)));
        }
        return dtoList;
    }

    @Override
    public TenantLogisticsRespDto getDtoById(Long id) {
        return tenantLogisticsService.getDtoById(id);
    }

    @Override
    public TenantLogisticsRespDto findDtoById(Long id, String fields) {
        return tenantLogisticsService.findDtoById(id, fields);
    }

    @Override
    public TenantLogisticsRespDto getWithServiceProviderById(Long id) {
        return tenantLogisticsService.getLogisticsAndProvider(id);
    }

    @Override
    public List<Long> getLogisticsIdsByIssuingBayAreaIds(Long tenantI, BaseListReqDto reqDto) {
        return tenantLogisticsService.getLogisticsIdsByIssuingBayAreaIds(tenantI, reqDto.getIdList());
    }

    @Override
    public List<TenantLogisticsRespDto> getLogisticsByIssuingBayAreaIds(BaseListReqDto reqDto) {
        List<TenantLogistics> tenantLogistics = tenantLogisticsService.getLogisticsByIssuingBayAreaIds(reqDto.getIdList());
        return RelationsBinder.convertAndBind(tenantLogistics, TenantLogisticsRespDto.class);
    }

    @Override
    public TenantLogisticsRespDto getOne(Long tenantId, Long id) {
        TenantLogistics one = tenantLogisticsService.getOne(tenantId, id);
        return RelationsBinder.convertAndBind(one, TenantLogisticsRespDto.class);
    }

    @Override
    public LogisticsRespDto getOneLogisticsResp(Long id) {
        return tenantLogisticsService.getLogisticsRespById(id);
    }

    @Override
    public List<Long> getHaveLogisticsAccountLogisticIdsMainIssuingBay(Long tenantId, Long issuingBayAreaId) {
        return tenantLogisticsService.getHaveLogisticsAccountLogisticIdsMainIssuingBay(tenantId, issuingBayAreaId);
    }

    @Override
    public List<LogisticsRespDto> list(BaseListReqDto reqDto) {
        return tenantLogisticsService.getLogisticsRespList(reqDto);
    }

    @Override
    public List<LogisticsRespDto> all() {
        return tenantLogisticsService.all();
    }

    @Override
    public List<LogisticsRespDto> listByIssuingBayIdAndIsTrackInfo(Long issuingBayAreaId, Integer isTrackInfo) {
        return tenantLogisticsService.listByIssuingBayIdAndIsTrackInfo(issuingBayAreaId, isTrackInfo);
    }

    @Override
    public List<LogisticsSimpleResp> list(LogisticsListParam param) {
        return null;
    }

    @Override
    public List<LogisticsRespDto> getLogisticsListByIssuingBayAreaIdAndType(Long issuingBayAreaId, String type, Long merchantId) {
        return tenantLogisticsService.getLogisticsListByIssuingBayAreaIdAndType(issuingBayAreaId, type, merchantId);
    }

    @Override
    public List<TenantLogisticsRespDto> getByIssuingBayAreaIdAndType(Long issuingBayAreaId, String type) {
        return tenantLogisticsService.getByIssuingBayAreaIdAndType(issuingBayAreaId, type);
    }

    @Override
    public List<TenantLogisticsRespDto> getAsCondition(Long tenantId, TenantLogisticsAsConditionParam param) {
        List<TenantLogisticsRespDto> list = tenantLogisticsService.getAsCondition(tenantId, param);
        if (!SDSDIY_TENANT_ID.equals(tenantId) && YES.equals(param.getIsContainSdsLogistics())) {
            list.addAll(tenantLogisticsService.getAsCondition(SDSDIY_TENANT_ID, param));
        }
        return list;
    }

    @Override
    public List<LogisticsGroupToSelectResp> logisticsGroupToSelect(Collection<Long> ids) {
        return tenantLogisticsService.logisticsGroupToSelect(ids);
    }

    @Override
    public List<LogisticsChannelRespDto> getLogisticsChannelContainSDS(Long tenantId, Long serviceProviderId, Long nonServiceProviderId) {
        List<LogisticsChannel> logisticsChannels = tenantLogisticsService.getLogisticsChannelContainSDS(tenantId, serviceProviderId, nonServiceProviderId);
        return RelationsBinder.convertAndBind(logisticsChannels, LogisticsChannelRespDto.class);
    }

    @Override
    public void initTenantLogistics() {
        sevenInit.initLogistics();
    }

    @Override
    public void initAccount() {
        sevenInit.tenantServiceProvider();
        sevenInit.fbmOfflineHandle();
        sevenInit.fbmOtherHandle();
        sevenInit.fbmAliHandle();
        sevenInit.bayAccountsConfig();
    }

    @Resource
    SevenInit sevenInit;


    @Override
    public void test() {
        sevenInit.initLogistics();
    }

    @Override
    public void initManualAccount() {
        sevenInit.initManualAccount();
    }

    @Override
    public void tenantServiceProvider() {
        sevenInit.tenantServiceProvider();
    }

    @Override
    public void fbmOfflineHandle() {
        sevenInit.fbmOfflineHandle();
        sevenInit.fbmOtherHandle();
    }

    @Override
    public void fbmAliHandle() {
        sevenInit.fbmAliHandle();
    }

    @Override
    public void bayAccountsConfig() {
        sevenInit.bayAccountsConfig();
    }

    @Override
    public void initDhlFaraway() {

    }

    @Override
    public void copyLogistics(CopyLogisticsParam copyLogisticsParam) {
        sevenInit.copyLogistics(copyLogisticsParam);
    }

    @Override
    public void copyAccount(Long tenantId, List<Long> accountIds) {
        sevenInit.copyAccount(tenantId, accountIds);
    }

    @Override
    public void bindAccount(Long tenantId, Long issuingAreaId, List<Long> issuingBayIds) {
        sevenInit.bindAccount(tenantId, issuingAreaId, issuingBayIds);
    }

    @Override
    public Map<Long, LogisticsAndLogisticsSourceResp> getLogisticsAndLogisticsSource(Long tenantId, BaseListReqDto reqDto) {
        return tenantLogisticsService.getLogisticsAndLogisticsSource(tenantId, reqDto.getIdList());
    }

    @Override
    public Map<Long, LogisticsAndLogisticsSourceResp> getLogisticsAndLogisticsSource(Long tenantId, Long issuingAreaId, BaseListReqDto reqDto) {
        return tenantLogisticsService.getLogisticsAndLogisticsSource(tenantId, issuingAreaId, reqDto.getIdList());
    }

    @Override
    public List<BaseIdAndNameDTO> findNameByIds(Collection<Long> ids) {
        return tenantLogisticsMapperManager.findNameByIds(ids);
    }

    @Override
    public String findNameById(Long id) {
        List<BaseIdAndNameDTO> list = tenantLogisticsMapperManager.findNameByIds(Collections.singleton(id));
        return list.isEmpty() ? null : list.get(0).getName();
    }

    @Override
    public List<TenantLogisticsRespDto> getCNSelfSend(Long tenantId, Long issuingAreaId, Long issuingBayId, Integer isTemuFullySelfSend) {
        return tenantLogisticsService.getCNSelfSend(tenantId, issuingAreaId, issuingBayId, isTemuFullySelfSend);
    }

    @Override
    public DeclarationTaxResp getTaxInfos(Long logisticsId, String countryCode) {
        return tenantLogisticsService.getTaxInfos(logisticsId, countryCode);
    }

    @Override
    public void update(Long id, TenantLogisticsUpdateParam param) {
        TenantLogistics tenantLogistics = BeanTool.copyBean(param, TenantLogistics.class);
        tenantLogisticsService.update(id, tenantLogistics);
    }

    @Override
    public TenantAddressRespDto getTenantAddress(Long logisticsId, Long issuingBayId) {
        return tenantLogisticsService.getTenantAddress(logisticsId, issuingBayId);
    }
}