package com.sdsdiy.productimpl.service;

import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class ProductQueryLimitServiceTest {

    @Resource
    private ProductQueryLimitService productQueryLimitService;

    @Test
    void getLimitCategoryIds() {
        System.out.println(productQueryLimitService.getLimitCategoryIds(257L));
    }

    @Test
    void getLimitProductIds() {
        System.out.println(productQueryLimitService.getLimitProductIds("", 257L));
    }
}