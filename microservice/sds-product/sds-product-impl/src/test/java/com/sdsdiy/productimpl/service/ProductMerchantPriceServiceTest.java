package com.sdsdiy.productimpl.service;

import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.bo.AuthAccumulateSetBo;
import com.sdsdiy.productimpl.entity.po.ProductMerchantPrice;
import com.sdsdiy.productimpl.task.ProductMerchantPriceTask;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @author: bin_lin
 * @date: 2020/10/13 14:07
 * @desc:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class ProductMerchantPriceServiceTest {
    @Resource
    ProductMerchantPriceService productMerchantPriceService;
    @Resource
    ProductMerchantPriceTask productMerchantPriceTask;

    @Test
    public void updateMerchantAccumulateLevel() {
        productMerchantPriceTask.updateMerchantAccumulateLevel(null);
    }

    @Test
    public void setAuthPrice() {
        ProductMerchantPrice productMerchantPrice = new ProductMerchantPrice();
        productMerchantPrice.setMerchantId(666L);
        productMerchantPrice.setParentProductId(5L);
        productMerchantPrice.setProductId(22l);
        productMerchantPrice.setAuthOnePrice(new BigDecimal(2));
        productMerchantPrice.setAuthBatchMinNum(2);
        productMerchantPriceService.setAuthPrice(Lists.newArrayList(productMerchantPrice));
    }

    @Test
    public void getCurrentAccumulate() {
        System.out.println(productMerchantPriceService.getCurrentAccumulate(1L, null));
    }

    @Test
    public void setAuthPrice2() {
        AuthAccumulateSetBo authAccumulateSetBo = new AuthAccumulateSetBo();
        authAccumulateSetBo.setMerchantId(11233L);
        authAccumulateSetBo.setLevel(3);
        authAccumulateSetBo.setParentProductId(21125L);
        productMerchantPriceService.setAuthAccumulateNum(Lists.list(authAccumulateSetBo));
    }

}