package com.sdsdiy.productimpl.service.authPrice;

import com.sdsdiy.productapi.dto.authPrice.AccumulatePriceProductDto;
import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class AccumulatePriceServiceTest {

    @Resource
    private AccumulatePriceService accumulatePriceService;

    @Test
    void getAuthorizedProduct() {
    }

    @Test
    void getCanAuthorizedList() {
//        List<AccumulatePriceProductDto> dtoList = accumulatePriceService.getCanAuthorizedList(2651L);
//        System.out.println(dtoList);
//        List<Long> ids = new ArrayList<>();
//        for (AccumulatePriceProductDto dto : dtoList) {
//            System.out.println(dto.getProductId());
//            ids.add(dto.getProductId());
//            if (dto.getProductId() == 2571L) {
//                System.out.println("111");
//            }
//        }
//        System.out.println("ahashshsh");
//        System.out.println(ids.contains(5271L));
    }
}