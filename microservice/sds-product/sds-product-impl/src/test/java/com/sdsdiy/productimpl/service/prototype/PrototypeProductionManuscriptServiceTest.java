package com.sdsdiy.productimpl.service.prototype;

import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.Assert.*;

/**
 * @description:
 * @Author: zmy
 * @Date: 2024/1/17 11:08
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class PrototypeProductionManuscriptServiceTest {
    @Resource
    PrototypeProductionManuscriptService prototypeProductionManuscriptService;
    @Resource
    SvgNameFilterService svgNameFilterService;

    @Test
    public void getSvgNames() throws Exception {
        String url="https://static-photo-center-prov.oss-cn-hangzhou.aliyuncs.com/svgs/91rr3AHARTasVhdqqVyNm4TGH9ub5wHb8VhZiE45/1b85b3eeccb0ddb2dc4e4bf53b1d0746.svg";
        List<String> filterSvgNames = svgNameFilterService.getFilterSvgNames();
        List<String> svgNames = prototypeProductionManuscriptService.getSvgNames(url, filterSvgNames);
        System.out.println(svgNames);
    }
}