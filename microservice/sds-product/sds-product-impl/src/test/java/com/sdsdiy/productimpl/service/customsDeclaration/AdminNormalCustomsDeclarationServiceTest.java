package com.sdsdiy.productimpl.service.customsDeclaration;

import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.service.product.ProductReadService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class AdminNormalCustomsDeclarationServiceTest {

    @Autowired
    private AdminNormalCustomsDeclarationService adminNormalCustomsDeclarationService;
    @Autowired
    private ProductReadService productReadService;

    @Test
    void create() {
//        adminNormalCustomsDeclarationService.createOrUpdate(199L);
    }

    @Test
    void updateVariantDeclaration() {
        adminNormalCustomsDeclarationService.updateVariantDeclaration(199L);
    }

}