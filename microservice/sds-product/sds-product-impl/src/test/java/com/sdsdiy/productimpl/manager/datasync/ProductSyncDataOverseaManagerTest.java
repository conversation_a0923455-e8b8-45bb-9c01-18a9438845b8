package com.sdsdiy.productimpl.manager.datasync;

import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class ProductSyncDataOverseaManagerTest {

    @Autowired
    private ProductSyncDataOverseaManager productSyncDataOverseaManager;

    @Test
    void getSyncProductInfo() {

        BaseListDto<Long> param = new BaseListDto<>();
        param.setList(Arrays.asList(102360L));
        productSyncDataOverseaManager.getSyncProductInfo(param);
    }
}
