package com.sdsdiy.productimpl.service;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.productapi.dto.ProductIssuingBayReqDto;
import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

/**
 * @description:
 * @Author: zmy
 * @Date: 2024/2/20 10:38
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class ProductIssuingBayServiceTest {
    @Resource
    ProductIssuingBayService productIssuingBayService;

    @Test
    public void testBay() {
        productIssuingBayService.testBay();
    }
    @Test
    public void getProductIssuingBayInfoNew() {
        ProductIssuingBayReqDto dto= JSON.parseObject("{\"cityCode\":\"\",\"countryCode\":\"US\",\"isDistributionOrder\":0,\"itemMergeNumDtos\":[{\"mergeNum\":2,\"productId\":101811},{\"mergeNum\":1,\"productId\":101814}],\"logisticsId\":616,\"merchantId\":15321,\"orderItemNum\":2,\"orderOriginType\":\"COPY_ORDER\",\"productIdFactoryIdMap\":{},\"provinceCode\":\"\",\"supplyChainTypeReqDtos\":[{\"productIds\":[101811,101814],\"supplyChainType\":\"ONE_PIECE\"}]}",ProductIssuingBayReqDto.class);
        productIssuingBayService.getProductIssuingBayInfoNew(dto);
    }
}