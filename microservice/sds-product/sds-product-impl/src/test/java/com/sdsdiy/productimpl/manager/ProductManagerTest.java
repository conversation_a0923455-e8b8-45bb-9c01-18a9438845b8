package com.sdsdiy.productimpl.manager;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.productapi.dto.product.MerchantEnableProductIdListDto;
import com.sdsdiy.productdata.dto.PrototypeGroupSyncBigolloDTO;
import com.sdsdiy.productdata.dto.product.datasync.ProductSyncResultDTO;
import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.entity.po.datasync.PrototypeBigolloRel;
import com.sdsdiy.productimpl.manager.datasync.ProductSyncDataOverseaManager;
import com.sdsdiy.productimpl.manager.datasync.PrototypeSyncOverseasManager;
import com.sdsdiy.productimpl.service.datasync.PrototypeBigolloRelService;
import com.sdsdiy.productimpl.service.product.ProductSummaryInfoService;
import com.sdsdiy.productimpl.service.prototype.ThemePrototypeService;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class ProductManagerTest {
    @Autowired
    private ThemePrototypeService themePrototypeService;
    @Resource
    private ProductManager productManager;
    @Resource
    private ProductSyncDataOverseaManager productSyncDataOverseaManager;
    @Resource
    private PrototypeSyncOverseasManager prototypeSyncOverseasManager;
    @Resource
    private PrototypeBigolloRelService prototypeBigolloRelService;
    @Resource
    private ProductSummaryInfoService productSummaryInfoService;

    @Test
    void test(){
        productSummaryInfoService.initSummaryInfo(102664l);
    }
    @Test
    void syncTheme() {
        List<Long> ids = Arrays.asList(
                344682371320590336L,
                344694302848790528L,
                344703266680418304L,
                344934058576785408L,
                344935470861856768L,
                344936681619009536L,
                344938542124183552L,
                344939054554886144L,
                344941225694408704L,
                344941767413936128L,
                344942863842750464L,
                344944420520931328L,
                344945123154931712L,
                344947667801419776L,
                344948265095475200L,
                344948745372643328L,
                419322595887296512L,
                419339777862995968L,
                419349243026223104L,
                419374608322932736L,
                419586444825735168L,
                443895828208947200L,
                443988314329198592L,
                443988934700314624L,
                444621311264174080L,
                444621452570275840L,
                486647664690200576L
        );
        for (Long id : ids) {
            try {
                themePrototypeService.syncBigolloOne(id);
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }

    }

    @Test
    void dataSyncOldDataDeal() {
        this.productSyncDataOverseaManager.oldDataDeal();
    }

    @Test
    void prototypeSyncUpdate() {
        this.prototypeSyncOverseasManager.syncUpdate(77L);
    }

    @Test
    void queryPrototypeMapping() {
        PrototypeBigolloRel bigolloRel = this.prototypeBigolloRelService.findById(64L);
        this.prototypeSyncOverseasManager.queryPrototypeMapping(bigolloRel);
    }

    @Test
    void productionManuscriptNameDeal() {
        PrototypeBigolloRel bigolloRel = this.prototypeBigolloRelService.findById(66L);
        this.prototypeSyncOverseasManager.productionManuscriptNameDeal(bigolloRel);
    }

    @Test
    void testGetIdsThatSuppliedAndMerchantAuthorizedAndMemberLvValidNotIncludeParentId() {
        MerchantEnableProductIdListDto bo = this.productManager.getMerchantEnableIds(2566L, true);
        Assert.assertFalse(bo.getEnableProductIds().contains(5896L));
    }

    @Test
    void aa() {
        String syncProductResultJson = "{\"innerOutProductIdMap\":{54095:3598,54096:3599,54097:3600,54098:3601,53406:3589,53407:3590,53408:3591,53409:3592,53410:3593,53411:3594,53412:3595,53413:3596,53414:3597,54252:3602,54253:3603,54254:3604,54255:3605,54256:3610,54257:3611,54258:3612,54259:3613,53427:3606,53428:3607,53429:3608,53430:3609},\"innerOutProductSkuMap\":{53406:\"********\"},\"innerParentIds\":[53406],\"outParentIds\":[3589]}";
        ProductSyncResultDTO productSyncResultDTO = JSON.parseObject(syncProductResultJson, ProductSyncResultDTO.class);
        PrototypeGroupSyncBigolloDTO prototypeGroupSyncBigolloDTO = new PrototypeGroupSyncBigolloDTO();
        prototypeGroupSyncBigolloDTO.setProductIdsMap(productSyncResultDTO.getInnerOutProductIdMap());
        Long groupId = 9723L;
        prototypeGroupSyncBigolloDTO.setPrototypeGroupId(groupId);
        this.prototypeSyncOverseasManager.syncAdd(prototypeGroupSyncBigolloDTO);
    }
}