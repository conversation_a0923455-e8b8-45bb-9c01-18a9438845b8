package com.sdsdiy.productimpl.service.authPrice;

import com.sdsdiy.productdata.dto.auth.AuthOnePriceParam;
import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.service.product.ProductPriceV2Service;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class AuthOnePriceServiceTest {

    @Resource
    AuthOnePriceService authOnePriceService;
    @Autowired
    private ProductPriceV2Service productPriceV2Service;

    @Test
    void set() {
        AuthOnePriceParam param = new AuthOnePriceParam();
        param.setProductId(6789L);
        param.setVariantId(6789L);
        param.setOnePrice(new BigDecimal(1));
        param.setBatchPrice(new BigDecimal(1));
        param.setBatchMinNum(1);
        authOnePriceService.set(999999L, Arrays.asList(param));
    }

    @Test
    void getValidAuthorizedProductCount() {
        System.out.println(authOnePriceService.getValidAuthorizedProductCount(2614L));
    }
    @Test
    void currentParentTenantPurchasePrice() {
        productPriceV2Service.currentParentTenantPurchasePrice(25L, Collections.singletonList(39404L));
    }

}