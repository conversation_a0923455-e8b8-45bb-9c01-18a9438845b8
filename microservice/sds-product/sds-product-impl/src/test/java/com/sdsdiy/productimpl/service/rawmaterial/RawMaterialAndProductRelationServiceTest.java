package com.sdsdiy.productimpl.service.rawmaterial;

import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.productdata.vo.rawmaterial.ProductRawMaterialItemRelPageVO;
import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.entity.po.factory.RawMaterialCategory;
import com.sdsdiy.productimpl.service.factory.RawMaterialCategoryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class RawMaterialAndProductRelationServiceTest {

    @Resource
    private RawMaterialAndProductRelationService rawMaterialAndProductRelationService;
    @Resource
    private RawMaterialCategoryService rawMaterialCategoryService;

    @Test
    public void testPage() {
        PageResultDto<ProductRawMaterialItemRelPageVO> page =
                rawMaterialAndProductRelationService.page("ljptest07031", null, null, 498L, 1L, 10L);
        System.out.println(JSONObject.toJSON(page));
    }

    @Test
    public void testGenRawMaterialCategoryName() {
        List<RawMaterialCategory> allCategories = rawMaterialCategoryService.listAllCategoryByLeafCategoryIds(Arrays.asList(626L, 627L), null);
        Map<Long, RawMaterialCategory> map = allCategories.stream().collect(Collectors.toMap(RawMaterialCategory::getId, Function.identity()));
        String s = rawMaterialAndProductRelationService.genRawMaterialCategoryName(627L, map);
        System.out.println(s);
    }
}