package com.sdsdiy.productimpl.service.authPrice;

import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class AllAccumulatePriceLevelsServiceTest {

    @Resource
    AllAccumulatePriceLevelsService allAccumulatePriceLevelsService;

    @Test
    void setLevel() {
//        allAccumulatePriceLevelsService.setAllAccumulatePriceLevel(999999999L, 1, 999999999L);
//        allAccumulatePriceLevelsService.setAllAccumulatePriceLevel(999999999L, 0, 999999999L);
    }
}