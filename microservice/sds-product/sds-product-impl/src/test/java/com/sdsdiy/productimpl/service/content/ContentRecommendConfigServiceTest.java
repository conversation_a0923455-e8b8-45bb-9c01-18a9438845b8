package com.sdsdiy.productimpl.service.content;

import com.google.common.collect.Lists;
import com.sdsdiy.productapi.constant.content.ContentTypeEnum;
import com.sdsdiy.productimpl.ProductServiceApplication;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @description:
 * @Author: zmy
 * @Date: 2022/9/2 16:30
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class ContentRecommendConfigServiceTest {
    @Resource
    ContentRecommendConfigService contentRecommendConfigService;

    @Test
    public void testDeleteConfigs() {
        contentRecommendConfigService.deleteConfigs(ContentTypeEnum.BLOG.getType(), Lists.newArrayList(1L,2L));
    }
}