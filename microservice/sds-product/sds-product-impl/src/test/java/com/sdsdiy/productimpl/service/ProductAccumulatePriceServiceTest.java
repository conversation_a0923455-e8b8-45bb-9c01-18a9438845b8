package com.sdsdiy.productimpl.service;

import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class ProductAccumulatePriceServiceTest {

    @Resource
    ProductAccumulatePriceService productAccumulatePriceService;
    @Resource
    private ProductPriceService productPriceService;

    @Test
    void getParentAccumulateGroup() {
        System.out.println(productAccumulatePriceService.getParentAccumulateGroup(new ArrayList<>()).keySet());
    }

    @Test
    void getHasAccumulatePriceParentProductIdList() {
        System.out.println(productPriceService.getHasAccumulatePriceParentProductIdList());
    }

    @Test
    void testGetParentAccumulateGroup() {
        System.out.println(productAccumulatePriceService.getParentAccumulateGroup(Arrays.asList(199L)));
    }
}