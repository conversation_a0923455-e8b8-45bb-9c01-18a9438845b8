package com.sdsdiy.productimpl.service;

import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class CycleWarningServiceTest {

	@Resource
	CycleWarningService cycleWarningService;
	@Resource
	ProductHolidayDayLogService productHolidayDayLogService;

	@Test
	void init() {

		cycleWarningService.init();
	}

	@Test
	void productHolidayDayLogService() {
		productHolidayDayLogService.holidayLogRecord(101804L, System.currentTimeMillis());
	}
}