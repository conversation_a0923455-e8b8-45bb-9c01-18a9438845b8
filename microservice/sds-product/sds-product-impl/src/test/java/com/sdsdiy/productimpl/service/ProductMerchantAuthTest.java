package com.sdsdiy.productimpl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.sdsdiy.common.base.constant.status.ProductPublicStatus;
import com.sdsdiy.productdata.dto.auth.AuthAccumulatePriceLevelDTO;
import com.sdsdiy.productdata.dto.auth.AuthAllAccumulatePriceLevelDTO;
import com.sdsdiy.productdata.dto.auth.AuthMerchantReqDTO;
import com.sdsdiy.productdata.dto.product.ProductIdReqDTO;
import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.feign.MerchantFeign;
import com.sdsdiy.productimpl.service.auth.MerchantAuthProductParentService;
import com.sdsdiy.productimpl.service.authPrice.AccumulatePriceService;
import com.sdsdiy.productimpl.service.authPrice.AllAccumulatePriceLevelsService;
import com.sdsdiy.productimpl.service.distribution.ProductDistributionService;
import com.sdsdiy.productimpl.service.product.ProductSummaryInfoService;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @author: bin_lin
 * @date: 2020/10/13 14:07
 * @desc:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class ProductMerchantAuthTest {
    @Resource
    MerchantAuthProductParentService merchantAuthProductParentService;
    @Autowired
    private AllAccumulatePriceLevelsService allAccumulatePriceLevelsService;
    @Autowired
    private AccumulatePriceService accumulatePriceService;
    @Autowired
    private MerchantFeign merchantFeign;
    @Autowired
    private ProductDistributionService productDistributionService;

    @Test
    public void authPublicPlatformProductToMerchant() {
        ProductIdReqDTO reqDTO = new ProductIdReqDTO();
        reqDTO.setIds(Collections.singletonList(19390L));
        reqDTO.setTenantId(10L);
        reqDTO.setUid(0L);
        merchantAuthProductParentService.authSelfProductByProductOnline(reqDTO);
    }

    @Test
    public void authAllPublicPlatformProduct() {
        AuthMerchantReqDTO authMerchantReqDTO = new AuthMerchantReqDTO();
        authMerchantReqDTO.setTenantId(1L);
        authMerchantReqDTO.setUid(0L);
        authMerchantReqDTO.setProductTenantId(1L);
        authMerchantReqDTO.setMerchantId(3959L);
        List<Long> addAuthParentIds = merchantAuthProductParentService.authSelfProductByAllPublic(authMerchantReqDTO);
        System.out.println(JSON.toJSONString(addAuthParentIds));
    }

    @Test
    public void onMessage() {
        AuthAllAccumulatePriceLevelDTO levelDTO = new AuthAllAccumulatePriceLevelDTO();
        levelDTO.setTenantId(273L);
        levelDTO.setMerchantId(12335L);
        levelDTO.setUid(0L);
        levelDTO.setProductTenantId(1L);
        levelDTO.setLevel(2);
        boolean selfProduct = levelDTO.getTenantId().equals(levelDTO.getProductTenantId());
        StopWatch stopWatch = DateUtil.createStopWatch();
        if (levelDTO.getMerchantTenantId() == null) {
            MerchantRespDto merchant = merchantFeign.getMerchantById(levelDTO.getMerchantId());
            levelDTO.setMerchantTenantId(merchant.getTenantId());
        }
        stopWatch.start("记录全部授权");
        // 记录全部授权
        allAccumulatePriceLevelsService.setAllAccumulatePriceLevel(levelDTO);
        stopWatch.stop();
        stopWatch.start("生成授权关系、产品库");
        // 生成授权关系、产品库
        AuthMerchantReqDTO authMerchantReqDTO = BeanUtil.copyProperties(levelDTO, AuthMerchantReqDTO.class);
        List<Long> addAuthParentIds = merchantAuthProductParentService.authSelfProductByAllPublic(authMerchantReqDTO);
        stopWatch.stop();
        if (selfProduct) {
            stopWatch.start("设置档位");
            // 设置档位,仅对新增授权生效，旧的授权关系，不改档位
            AuthAccumulatePriceLevelDTO priceLevelDTO = BeanUtil.copyProperties(levelDTO, AuthAccumulatePriceLevelDTO.class);
            priceLevelDTO.setParentIds(addAuthParentIds);
            priceLevelDTO.setPublicStatus(ProductPublicStatus.OPEN);
            accumulatePriceService.setAuthAccumulatePriceLevel(priceLevelDTO);
            stopWatch.stop();
        }
    }
    @Autowired
    private ProductSummaryInfoService productSummaryInfoService;
    @Test
    public void updateProductAccumulatePrice(){
        productSummaryInfoService.updateProductAccumulatePrice(20431L);
    }
}