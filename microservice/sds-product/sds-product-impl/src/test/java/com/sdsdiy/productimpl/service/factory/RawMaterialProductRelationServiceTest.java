package com.sdsdiy.productimpl.service.factory;

import com.alibaba.fastjson.JSONObject;
import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.bo.rawmaterial.RawMaterialAndProductBO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class RawMaterialProductRelationServiceTest {

    @Resource
    private RawMaterialProductRelationService rawMaterialProductRelationService;


    @Test
    public void testCountByFactoryProductSupply() {
        Long count = rawMaterialProductRelationService.countRawMaterialAndProductBoList(498L, "test专用", null);
        System.out.println(count);
    }
    @Test
    public void testListRawMaterialAndProductBoListByFactoryIdAndParentIds() {
        List<RawMaterialAndProductBO> rawMaterialAndProductBOS =
                rawMaterialProductRelationService
                        .listRawMaterialAndProductBoListByFactoryIdAndParentIds(104L, "白色", Arrays.asList(209L, 3067L),
                                1L, 10L);
        System.out.println(JSONObject.toJSON(rawMaterialAndProductBOS));
    }
}