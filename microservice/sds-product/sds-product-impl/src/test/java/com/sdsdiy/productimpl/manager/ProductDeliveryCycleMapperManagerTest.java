package com.sdsdiy.productimpl.manager;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.entity.po.ProductDeliveryCycle;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class ProductDeliveryCycleMapperManagerTest {

    @Autowired
    private ProductDeliveryCycleMapperManager productDeliveryCycleMapperManager;

    @Test
    void findAllBYProductParentId() {
        List<ProductDeliveryCycle> deliveryCycleList = productDeliveryCycleMapperManager.findAllBYProductParentId(Arrays.asList(102289L));
        System.out.println(JSON.toJSONString(deliveryCycleList));
    }
}
