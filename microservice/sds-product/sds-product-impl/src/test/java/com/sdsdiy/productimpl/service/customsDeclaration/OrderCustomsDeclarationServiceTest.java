package com.sdsdiy.productimpl.service.customsDeclaration;

import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class OrderCustomsDeclarationServiceTest {

    @Autowired
    private OrderCustomsDeclarationService orderCustomsDeclarationService;

    @Test
    void get() {
        List<Long> variantIds = Arrays.asList(200L, 201L);
    }
}