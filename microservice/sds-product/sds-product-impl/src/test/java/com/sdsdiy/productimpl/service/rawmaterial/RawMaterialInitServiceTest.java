package com.sdsdiy.productimpl.service.rawmaterial;

import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class RawMaterialInitServiceTest {

    @Resource
    private RawMaterialInitSupplyService rawMaterialInitSupplyService;

    @Test
    public void testInitRawMaterialWithLock() {
        rawMaterialInitSupplyService.initRawMaterialWithLock(Arrays.asList(139465L, 139464L, 139463L));
    }
}