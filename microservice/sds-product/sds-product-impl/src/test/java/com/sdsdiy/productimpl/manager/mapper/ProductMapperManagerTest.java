package com.sdsdiy.productimpl.manager.mapper;

import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class ProductMapperManagerTest {

    @Autowired
    private ProductMapperManager productMapperManager;

    @Test
    void findBlankPageForTenant() {

    }
}