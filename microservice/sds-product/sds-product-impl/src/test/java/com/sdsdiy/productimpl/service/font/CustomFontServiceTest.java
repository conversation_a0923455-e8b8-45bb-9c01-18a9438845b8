package com.sdsdiy.productimpl.service.font;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sdsdiy.productapi.dto.font.CustomFontDetailRespDto;
import com.sdsdiy.productapi.dto.font.MerchantFontRespDto;
import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @description:
 * @Author: zmy
 * @Date: 2024/2/16 16:47
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class CustomFontServiceTest {
    @Resource
    CustomFontService customFontService;

    @Test
    public void toUnconfirmedTask() {
        customFontService.toUnconfirmedTask();
    }

    @Test
    public void detail() {
        CustomFontDetailRespDto detail = customFontService.detail(5L);
        System.out.println(detail);
    }
    @Test
    public void userFontCssBuild() {
        customFontService.userFontCssBuild(2577L);
    }
    @Test
    public void syncCustomFontDataToOutSite() {
        customFontService.syncCustomFontDataToOutSite(Lists.newArrayList(106L));
    }
    @Test
    public void systemFontCssBuildAndSync() {
        customFontService.systemFontCssBuildAndSync();
    }
    @Test
    public void allFontCssBuildAndSync() {
        customFontService.allFontCssBuildAndSync();
    }
    @Test
    public void syncFontToOut() {
        customFontService.syncFontFileToOutSite("d80804dd6ac744e39c8e8573d1f897f5.otf");
    }
    @Test
    public void merchantFonts() {
        MerchantFontRespDto respDto = customFontService.fonts(2577L);
        System.out.println(JSON.toJSONString(respDto));
    }
}