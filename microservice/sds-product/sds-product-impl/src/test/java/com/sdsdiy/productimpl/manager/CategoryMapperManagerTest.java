package com.sdsdiy.productimpl.manager;

import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.entity.po.Category;
import com.sdsdiy.productimpl.manager.mapper.CategoryMapperManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class CategoryMapperManagerTest {
    @Resource
    CategoryMapperManager categoryMapperManager;

    @Test
    public void getParentListByIdIn() {
        Map<Long, List<Category>> map = categoryMapperManager.getParentListByIdIn(Arrays.asList(271L));
        for (Long id : map.keySet()) {
            System.out.println(id);
            System.out.println(map.get(id));
        }
    }
}