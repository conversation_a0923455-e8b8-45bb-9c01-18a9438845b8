package com.sdsdiy.productimpl.service.product;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.entity.po.ProductSupply;
import com.sdsdiy.userdata.param.AnnouncementQueryProductParam;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class ProductQueryServiceTest {

    @Autowired
    private ProductQueryService productQueryService;

    @Test
    void getSupplyForAnnouncement() {
        AnnouncementQueryProductParam param = new AnnouncementQueryProductParam();
        param.setQueryOfflineSupply(true);

        List<ProductSupply> supplyForAnnouncement = productQueryService.getSupplyForAnnouncement(param);
        System.out.println(JSON.toJSONString(supplyForAnnouncement));
    }
}