package com.sdsdiy.productimpl.service.font;

import com.sdsdiy.productdata.dto.msg.FontImageTaskMsg;
import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.listener.font.FontImageRenderingListener;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @description:
 * @Author: zmy
 * @Date: 2024/2/20 18:13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class FontImageTaskServiceTest {
    @Resource
    FontImageTaskService fontImageTaskService;
    @Resource
    FontImageRenderingListener fontImageRenderingListener;

    @Test
    public void getRenderingImage() {
        String fileCode="0ac58cf6dc5636491be2500209594790.otf";
        String renderingImage = fontImageTaskService.getRenderingImage(fileCode);
        System.out.println(renderingImage);
    }

    @Test
    public void ondd() {
        FontImageTaskMsg msg=new FontImageTaskMsg();
        msg.setId(126L);
        fontImageRenderingListener.onMessage(msg);
    }
}