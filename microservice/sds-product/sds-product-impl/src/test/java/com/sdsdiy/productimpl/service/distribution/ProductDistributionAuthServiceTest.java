package com.sdsdiy.productimpl.service.distribution;

import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class ProductDistributionAuthServiceTest {

    @Resource
    private ProductDistributionAuthService productDistributionAuthService;

    @Test
    void factoryProductHaveAuthDistributorList() {
        List<Long> mustShowTenantIds = new ArrayList<>();
        mustShowTenantIds.add(1L);
        productDistributionAuthService.factoryProductHaveAuthDistributorList(23L, 500L, mustShowTenantIds);
    }
}