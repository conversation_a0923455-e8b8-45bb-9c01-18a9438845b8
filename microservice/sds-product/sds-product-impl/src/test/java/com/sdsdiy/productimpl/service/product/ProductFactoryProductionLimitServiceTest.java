package com.sdsdiy.productimpl.service.product;

import com.google.common.collect.Lists;
import com.sdsdiy.productapi.dto.product.OrderItemProductFactoryLimitDto;
import com.sdsdiy.productapi.dto.product.OrderProductFactoryLimitDto;
import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @Author: zmy
 * @Date: 2024/12/12 16:33
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class ProductFactoryProductionLimitServiceTest {
    @Resource
    private ProductFactoryProductionLimitService productFactoryProductionLimitService;

    @Test
    void updateUsedCount() throws Exception {
        OrderProductFactoryLimitDto dto=new OrderProductFactoryLimitDto();
        dto.setUserId(0L);
        List<OrderItemProductFactoryLimitDto> items= Lists.newArrayList();
        dto.setItems(items);
        OrderItemProductFactoryLimitDto item=new OrderItemProductFactoryLimitDto();
        item.setNum(3);
        item.setProductId(102503L);
        item.setFactoryId(102L);
        item.setSupplyChainType("ONE_PIECE");
        items.add(item);

        productFactoryProductionLimitService.updateUsedCount(dto);
    }
    @Test
    void reset() {
        productFactoryProductionLimitService.limitReset();
    }
}