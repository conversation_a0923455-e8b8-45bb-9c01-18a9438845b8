package com.sdsdiy.productimpl.service;

import com.sdsdiy.productimpl.ProductServiceApplication;
import org.apache.commons.compress.utils.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/11 21:40)
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class TenantCategoryRelServiceTest {

    @Resource
    TenantCategoryRelService tenantCategoryRelService;
    @Resource
    CategoryService categoryService;

    @Test
    public void test() {
        categoryService.getAllParentIdsByChildIds(Sets.newHashSet(1179L));
    }

    @Test
    public void batchSaveTenantCategories() {
    }

    @Test
    public void restTenantCategoryByTenantId() {
    }

    @Test
    public void saveTenantCategories() {
    }

    @Test
    public void delCategories() {
    }

    @Test
    public void findCategoryIdsByTenantId() {
    }

    @Test
    public void initTenantCategory() {
        tenantCategoryRelService.initTenantCategory();
    }
}