package com.sdsdiy.productimpl.service.product;

import com.google.common.collect.Lists;
import com.sdsdiy.core.mq.SqlSyncSqs;
import com.sdsdiy.productdata.constants.ProductConstant;
import com.sdsdiy.productimpl.ProductServiceApplication;
import com.sdsdiy.productimpl.sqs.ProductSqsSender;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @Author: zmy
 * @Date: 2024/5/23 18:19
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
public class ProductWriteServiceTest {
    @Resource
    private ProductWriteService productWriteService;
    @Resource
    private ProductSqsSender productSqsSender;

    @Test
    public void syncProductEsAndSqs() {
        // 改host和env
        // String sqsUrl = String.format(SQL_SQS_URL, "production");
        // List<SendMessageBatchRequestEntry> entries = Lists.newArrayList();
        // for (Long id : ids) {
        //     SqlMsg sqlMsg = new SqlMsg();
        //     sqlMsg.setId(id);
        //     sqlMsg.setTable(table);
        //     sqlMsg.setApiHost("http://saas-api.sdspod.com");

        List<Long> ids = Lists.newArrayList(83933L);
        productSqsSender.sendToSqs(ProductConstant.TABLE_PRODUCT, SqlSyncSqs.TYPE_UPDATE,
            new ArrayList<>(ids), ProductSqsSender.MAIN_DATABASE);

    }
}