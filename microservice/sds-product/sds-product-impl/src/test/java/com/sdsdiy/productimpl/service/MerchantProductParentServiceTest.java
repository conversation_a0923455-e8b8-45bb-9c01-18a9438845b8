package com.sdsdiy.productimpl.service;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.productapi.dto.MerchantProductParentDTO;
import com.sdsdiy.productimpl.ProductServiceApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashSet;
import java.util.List;
import java.util.Set;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {ProductServiceApplication.class})
class MerchantProductParentServiceTest {

    @Autowired
    private MerchantProductParentService merchantProductParentService;

    @Test
    void findAllByMerchantIdGroupByProductId() {
        List<MerchantProductParentDTO> allByMerchantIdGroupByProductId = merchantProductParentService.findAllByMerchantIdGroupByProductId(1L);
        System.out.println(JSON.toJSONString(allByMerchantIdGroupByProductId));
        Set<Long> productIds = new HashSet<>();
        for (MerchantProductParentDTO merchantProductParentDTO : allByMerchantIdGroupByProductId) {
            productIds.add(merchantProductParentDTO.getProductId());
        }
        System.out.println(productIds.size());
        System.out.println(allByMerchantIdGroupByProductId.size());

    }
}