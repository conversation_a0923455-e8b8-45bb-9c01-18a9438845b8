package com.sdsdiy.productimpl.controller;

import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.helper.PageListResultRespDto;
import com.sdsdiy.common.base.helper.QueryParamHelper;
import com.sdsdiy.productapi.api.MerchantProductParentApi;
import com.sdsdiy.productapi.dto.IdsParam;
import com.sdsdiy.productapi.dto.MerchantProductParentDTO;
import com.sdsdiy.productapi.dto.MerchantProductParentReqDto;
import com.sdsdiy.productimpl.service.MerchantProductParentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商户产品库(merchant_product_parent)表控制层
 *
 * <AUTHOR>
 * @since 2020-05-22 15:40:13
 */

@RestController
public class MerchantProductParentController implements MerchantProductParentApi {
    @Autowired
    private MerchantProductParentService merchantProductParentService;

    @Override
    public MerchantProductParentDTO findById(Long mid, Long pid, Long currUserId) {
        return this.merchantProductParentService.findByMerchantId(mid, pid, currUserId);
    }

    @Override
    public List<MerchantProductParentDTO> getByAuthorizeUserIds(Long merchantId, String authorizeUserIds) {
        return this.merchantProductParentService.getByAuthorizeUserIds(merchantId, authorizeUserIds);
    }

    @Override
    public List<MerchantProductParentDTO> findByMerchantIdUserId(Long merchantId, Long authorizeUserId) {
        return this.merchantProductParentService.findByMerchantIdUserId(merchantId, authorizeUserId);
    }

    @Override
    public List<MerchantProductParentDTO> findByMerchantId(Long merchantId) {
        return this.merchantProductParentService.findAllByMerchantIdGroupByProductId(merchantId);
    }

    @Override
    public List<MerchantProductParentDTO> getByProductIdAuthorizeUserIds(Long merchantId, String productIds, String authorizeUserIds) {
        return this.merchantProductParentService.getByProductIdsAuthorizeUserIds(merchantId, productIds, authorizeUserIds);
    }

    @Override
    public List<MerchantProductParentDTO> getByType(Long merchantId, String productIds, Integer type) {
        return this.merchantProductParentService.getByType(merchantId, productIds, type);
    }

    @Override
    public PageListResultRespDto<MerchantProductParentDTO> productListByAuthorizeUserId(Long merchantId, String productLibraryType,
                                                                                        Long authorizeUserId, String keyword, Long categoryId,
                                                                                        boolean filterValid, QueryParamHelper queryParamHelper) {
        return this.merchantProductParentService.productListByAuthorizeUserId(merchantId, productLibraryType, authorizeUserId, keyword, categoryId, filterValid, queryParamHelper);
    }

    @Override
    public void deleteByAuthorizationUserIds(IdsParam param) {
        this.merchantProductParentService.deleteByAuthorizeUserIds(param.getMerchantId(), param.getIds());
    }

    @Override
    public void create(List<MerchantProductParentReqDto> reqDtos) {
        this.merchantProductParentService.insertBatch(reqDtos);
    }

    @Override
    public void updateType(Integer type, String merchantIds, Long parentId) {
        this.merchantProductParentService.updateTypeByMerchantIdsParentId(type, merchantIds, parentId);
    }

    @Override
    public void removeByParentIds(IdsParam param, Integer type) {
        this.merchantProductParentService.removeByParentIds(param.getMerchantId(), param.getIds(), type);
    }

    @Override
    public void removeUserAuthByParentIds(Long userId, IdsParam param) {
        this.merchantProductParentService.removeUserAuthByParentIds(param.getMerchantId(), userId, param.getIds());
    }

    @Override
    public List<MerchantProductParentDTO> findByProductIds(Long merchantId, Long authorizeUserId, BaseListDto<Long> dto) {
        return this.merchantProductParentService.findByProductIds(merchantId, authorizeUserId, dto.getList());
    }
}