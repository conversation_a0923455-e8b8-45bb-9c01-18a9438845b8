package com.sdsdiy.productimpl.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.sdsdiy.common.base.helper.McContentHelper;
import com.sdsdiy.productdata.dto.CategorySortBaseDTO;
import com.sdsdiy.productimpl.entity.po.CategorySort;
import com.sdsdiy.productimpl.mapper.CategorySortMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/18
 */
@Slf4j
@Service
@DS("common")
public class CategorySortService extends ServiceImpl<CategorySortMapper, CategorySort> {
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void add(CategorySort sort) {
        this.save(sort);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteByCategoryId(Long categoryId) {
        LambdaQueryWrapper<CategorySort> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategorySort::getCategoryId, categoryId);
        baseMapper.delete(queryWrapper);
    }

    public CategorySort findByTenantIdAndCategoryId(Long tenantId, Long categoryId) {
        LambdaQueryWrapper<CategorySort> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategorySort::getCategoryId, categoryId).eq(CategorySort::getTenantId, tenantId);
        return baseMapper.selectOne(queryWrapper);
    }

    public List<CategorySort> findByTenantIdAndCategoryIds(List<Long> tenantIds, List<Long> categoryIds) {
        LambdaQueryWrapper<CategorySort> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CategorySort::getCategoryId, categoryIds).in(CategorySort::getTenantId, tenantIds);
        return baseMapper.selectList(queryWrapper);
    }

    public List<CategorySort> findByCategoryIds(List<Long> categoryIds) {
        LambdaQueryWrapper<CategorySort> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CategorySort::getCategoryId, categoryIds);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 获取排序下一个
     *
     * @param tenantId
     * @param categoryParentId
     * @param sort
     * @return
     */
    public CategorySort findNextByTenantIdAndCategoryId(Long tenantId, Long categoryParentId, Long sort) {
        LambdaQueryWrapper<CategorySort> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategorySort::getCategoryParentId, categoryParentId)
                .eq(CategorySort::getTenantId, tenantId)
                .gt(CategorySort::getSort, sort)
                .orderByAsc(CategorySort::getSort)
                .last("limit 1")
        ;
        return baseMapper.selectOne(queryWrapper);
    }

    /**
     * 获取排序上一个
     * @param tenantId
     * @param categoryParentId
     * @param sort
     * @return
     */
    public CategorySort findLastByTenantIdAndCategoryId(Long tenantId, Long categoryParentId,Long sort) {
        LambdaQueryWrapper<CategorySort> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategorySort::getCategoryParentId, categoryParentId)
                .eq(CategorySort::getTenantId, tenantId)
                .lt(CategorySort::getSort,sort)
                .orderByDesc(CategorySort::getSort)
                .last("limit 1")
        ;
        return baseMapper.selectOne(queryWrapper);
    }

    /**
     * 交换排序
     * @param categorySort1
     * @param categorySort2
     */
    public void exchangeSort(CategorySort categorySort1, CategorySort categorySort2) {
        this.updateSort(categorySort1.getId(),categorySort2.getSort());
        this.updateSort(categorySort2.getId(),categorySort1.getSort());
    }

    /**
     * 修改排序
     * @param id
     * @param sort
     */
    public void updateSort(Long id,Long sort) {
        CategorySort categorySort = new CategorySort();
        categorySort.setId(id);
        categorySort.setSort(sort);
        baseMapper.updateById(categorySort);
    }


    public void sortCategory(Long tenantId, List<? extends CategorySortBaseDTO> categorySortBaseDTOList) {
        if (tenantId == null) {
            log.warn("sortCategory tenantId is null,use default tenantId 0L");
            Long currentTenantId = McContentHelper.getCurrentTenantId();
            tenantId = currentTenantId == null ? 0 : currentTenantId;
        }
        if (CollUtil.isEmpty(categorySortBaseDTOList)) {
            return;
        }
        List<Long> categoryIds = categorySortBaseDTOList.stream().map(CategorySortBaseDTO::getId).collect(Collectors.toList());
        Map<Long, Long> idKeyTenantIdMap = categorySortBaseDTOList.stream().collect(Collectors.toMap(CategorySortBaseDTO::getId, CategorySortBaseDTO::getTenantId));
        List<Long> tenantIds = Lists.newArrayList();
        tenantIds.add(tenantId);
        tenantIds.addAll(categorySortBaseDTOList.stream().map(CategorySortBaseDTO::getTenantId).collect(Collectors.toList()));
//        List<Long> tenantIds = TenantCommonConstant.tenantIds(tenantId);

        List<CategorySort> categorySorts = this.findByTenantIdAndCategoryIds(tenantIds, categoryIds);
        //sds设置的排序信息
        Map<Long, CategorySort> sdsSortMap = Maps.newHashMap();
        //租户本身设置的排序信息
        Map<Long, CategorySort> tenantSortMap = Maps.newHashMap();

        for (CategorySort categorySort : categorySorts) {
            if (!tenantId.equals(categorySort.getTenantId())) {
                Long categoryTenantId = idKeyTenantIdMap.getOrDefault(categorySort.getCategoryId(), 0L);
                if (categoryTenantId.equals(categorySort.getTenantId())) {
                    sdsSortMap.put(categorySort.getCategoryId(), categorySort);
                }
            } else {
                tenantSortMap.put(categorySort.getCategoryId(), categorySort);
            }
        }

        for (CategorySortBaseDTO categorySortBaseDTO : categorySortBaseDTOList) {
            Long sort = null;
            //排序的租户的id
            Long sortTenantId = null;
            CategorySort categorySort = null;
            if(tenantSortMap.get(categorySortBaseDTO.getId()) != null){
                categorySort = tenantSortMap.get(categorySortBaseDTO.getId());
            }else if(sdsSortMap.get(categorySortBaseDTO.getId()) != null){
                categorySort = sdsSortMap.get(categorySortBaseDTO.getId());
            }

            //正常 categorySort 不应该有null的情况
            sort = categorySort == null ? categorySortBaseDTO.getId() : categorySort.getSort();
            sortTenantId = categorySort == null ? tenantId : categorySort.getTenantId();
            categorySortBaseDTO.setSortId(categorySort == null ? null : categorySort.getId());
            categorySortBaseDTO.setSort(sort);
            categorySortBaseDTO.setSortTenantId(sortTenantId);
        }
        Collections.sort(categorySortBaseDTOList);

    }

}
