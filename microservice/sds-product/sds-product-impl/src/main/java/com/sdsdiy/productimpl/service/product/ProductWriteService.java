package com.sdsdiy.productimpl.service.product;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.beust.jcommander.internal.Maps;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.CategoryStatus;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.common.base.constant.ProductStatus;
import com.sdsdiy.common.base.constant.status.ProductPublicStatus;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.BasePO;
import com.sdsdiy.common.base.enums.product.ProductInquiryNotificationMessageEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.MapListUtil;
import com.sdsdiy.core.base.service.BaseServiceImpl;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.product.ProductTagConst;
import com.sdsdiy.core.transaction.TransactionUtil;
import com.sdsdiy.orderapi.enumeration.EnumProductCompensationStatus;
import com.sdsdiy.productapi.constant.MerchantProductParentConstant;
import com.sdsdiy.productapi.dto.color.ColorDto;
import com.sdsdiy.productapi.event.PrototypeChangeMsg;
import com.sdsdiy.productapi.myenum.EnumProductSupplyStatus;
import com.sdsdiy.productdata.constants.ProductConstant;
import com.sdsdiy.productdata.constants.ProductEnumConstant;
import com.sdsdiy.productdata.dto.distribution.DistributionAuthDTO;
import com.sdsdiy.productdata.dto.distribution.ProductDistributionStatusDTO;
import com.sdsdiy.productdata.dto.msg.ProductOfflineMsg;
import com.sdsdiy.productdata.dto.price.LadderPriceDTO;
import com.sdsdiy.productdata.dto.product.*;
import com.sdsdiy.productdata.dto.product.edit.*;
import com.sdsdiy.productdata.dto.prototype.PrototypeRequireDto;
import com.sdsdiy.productdata.dto.prototype.add.PrototypeGroupByProductParam;
import com.sdsdiy.productdata.enums.supply.SupplyChainTypeEnum;
import com.sdsdiy.productdata.util.ProductUtil;
import com.sdsdiy.productimpl.bo.ProductAddVariantCheckResultBO;
import com.sdsdiy.productimpl.entity.po.*;
import com.sdsdiy.productimpl.entity.po.product.VariantProperty;
import com.sdsdiy.productimpl.feign.LogisticsProductFeign;
import com.sdsdiy.productimpl.feign.MerchantProductDayStaticFeign;
import com.sdsdiy.productimpl.feign.OfficialDesignProductFeign;
import com.sdsdiy.productimpl.feign.ProductStatFeign;
import com.sdsdiy.productimpl.feign.order.FactoryOrderCreateTaskFeign;
import com.sdsdiy.productimpl.manager.ProductDeliveryCycleMapperManager;
import com.sdsdiy.productimpl.manager.ProductionDeliveryCycleMapperManager;
import com.sdsdiy.productimpl.manager.product.VariantPropertyMapperManager;
import com.sdsdiy.productimpl.mapper.ProductMapper;
import com.sdsdiy.productimpl.service.*;
import com.sdsdiy.productimpl.service.auth.MerchantAuthProductParentService;
import com.sdsdiy.productimpl.service.color.ColorService;
import com.sdsdiy.productimpl.service.customsDeclaration.AdminNormalCustomsDeclarationService;
import com.sdsdiy.productimpl.service.distribution.ProductDistributionAuthService;
import com.sdsdiy.productimpl.service.log.ProductUpdateLogService;
import com.sdsdiy.productimpl.service.product.copy.ProductCopyRecordService;
import com.sdsdiy.productimpl.service.prototype.PrototypeGroupService;
import com.sdsdiy.productimpl.service.prototype.PrototypeRequireService;
import com.sdsdiy.productimpl.service.size.SizeService;
import com.sdsdiy.productimpl.service.supply.ProductSupplyLadderPriceService;
import com.sdsdiy.productimpl.service.supply.ProductSupplySaleLadderPriceService;
import com.sdsdiy.productimpl.sqs.ProductSqsSender;
import com.sdsdiy.productimpl.util.ProductPriceUtil;
import com.sdsdiy.statapi.dto.HotProductStatDTO;
import com.sdsdiy.statdata.dto.query.ProductStaticDto;
import com.sdsdiy.userdata.enums.MemberLevelEnum;
import io.seata.spring.annotation.GlobalTransactional;
import io.seata.tm.api.transaction.TransactionHookAdapter;
import io.seata.tm.api.transaction.TransactionHookManager;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.common.Strings;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.CommonStatus.ONLINE;
import static com.sdsdiy.productdata.constants.PrototypeConstant.PrototypeType.IMAGE_CUTOM;
import static com.sdsdiy.productdata.enums.supply.SupplyChainTypeEnum.ONE_PIECE;
import static com.sdsdiy.productdata.enums.supply.SupplyChainTypeEnum.SMALL_ORDER;

/**
 * 产品 写
 *
 * <AUTHOR>
 * @date 2021/11/6
 */
@Service
@Log4j2
public class ProductWriteService extends BaseServiceImpl<ProductMapper, Product> {

    @Autowired
    private VariantPropertyMapperManager variantPropertyMapperManager;

    @Autowired
    @Lazy
    private ProductReadService productReadService;
    @Resource
    private ProductStatFeign productStatFeign;
    @Resource
    private CategoryService categoryService;
    private static final Long NEW_STATUS_TIME = TimeUnit.DAYS.toMillis(30);
    @Resource
    private ProductSupplyService productSupplyService;
    @Resource
    private LogisticsProductFeign logisticsProductFeign;
    @Autowired
    private ProductUpdateLogService productUpdateLogService;
    @Autowired
    private ProductSummaryInfoService productSummaryInfoService;
    @Autowired
    private ProductSqsSender productSqsSender;
    @Autowired
    private OfficialDesignProductFeign officialDesignProductFeign;
    @Autowired
    private AdminNormalCustomsDeclarationService adminNormalCustomsDeclarationService;
    @Autowired
    private ProductDetailsService productDetailsService;
    @Autowired
    private ColorService colorService;
    @Autowired
    private SizeService sizeService;
    @Autowired
    @Lazy
    private PrototypeRequireService prototypeRequireService;
    @Autowired
    @Lazy
    private PrototypeGroupService prototypeGroupService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    @Lazy
    private MerchantAuthProductParentService merchantAuthProductParentService;
    @Autowired
    @Lazy
    private ProductDistributionAuthService productDistributionAuthService;
    @Resource
    private ProductAvailableCartonService productAvailableCartonService;
    @Resource
    private ProductInquiryService productInquiryService;
    @Resource
    private MerchantProductDayStaticFeign merchantProductDayStaticFeign;
    @Resource
    private ProductSupplySaleLadderPriceService productSupplySaleLadderPriceService;
    @Resource
    private ProductSupplyLadderPriceService productSupplyLadderPriceService;
    @Resource
    private MerchantProductParentService merchantProductParentService;
    @Resource
    private ProductLabelService productLabelService;
    @Resource
    private FactoryOrderCreateTaskFeign factoryOrderCreateTaskFeign;
    @Resource
    private ProductDeliveryCycleMapperManager productDeliveryCycleMapperManager;
    @Resource
    private ProductionDeliveryCycleMapperManager productionDeliveryCycleMapperManager;
    @Resource
    private ProductAttributeRelService productAttributeRelService;
    @Resource
    private ProductCopyRecordService productCopyRecordService;

    /**
     * 逻辑删除产品
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteProduct(ProductDeleteDTO deleteDTO) {
        Product one = lambdaQuery().eq(Product::getId, deleteDTO.getId()).one();
        productCopyRecordService.checkCopyStatusAndError(one.getParentId());
        Assert.validateNull(one, "产品不存在");
        this.checkFactoryOrderCreateWait(one);
        if (!one.getTenantId().equals(deleteDTO.getTenantId())) {
            // 跨租户删除产品，走移除授权关系的方法
            Assert.validateTrue(ProductEnumConstant.SupplyChainStatusEnum.online(one.getOnePieceSupplyChainStatus()), "产品未下架不能移除");
            DistributionAuthDTO authDTO = BeanUtil.copyProperties(deleteDTO, DistributionAuthDTO.class);
            authDTO.setParentId(one.getId())
                    .setMerchantTenantId(deleteDTO.getTenantId());
            productDistributionAuthService.removeDistributionAuth(authDTO);
            return;
        }
        Assert.validateBool(one.getStatus().equals(ProductConstant.STATUS_OFFLINE), "产品未下架，无法删除");
        if (one.isParent()) {
            // 上架的变体数
            Integer count = lambdaQuery().eq(Product::getParentId, deleteDTO.getId())
                    .eq(Product::getStatus, ProductConstant.STATUS_ONLINE).count();
            Assert.validateBool(count == null || count == 0, "删除产品需先下架所有变体");
            //校验是否有模板组需求
            prototypeRequireService.prodcutParentHasRequire(one.getId());
        } else {
            //校验是否有模板组需求
            prototypeRequireService.productHasRequire(one.getParentId(), one.getId());
        }
        if (one.isParent()) {
            lambdaUpdate().eq(Product::getParentId, deleteDTO.getId())
                    .or().eq(Product::getId, deleteDTO.getId())
                    .set(Product::getStatus, ProductConstant.STATUS_DELETE).update();
            prototypeGroupService.deleteParentProductPrototypeGroup(one.getId());
            productSummaryInfoService.deleteProduct(one.getId());
            productSupplyService.removeProductSupply(one.getId(), null, null);
            logisticsProductFeign.deleteByParentId(one.getId(), deleteDTO.getUid());
        } else {
            lambdaUpdate().eq(Product::getId, deleteDTO.getId()).set(Product::getStatus, ProductConstant.STATUS_DELETE).update();
            prototypeGroupService.deleteProductPrototype(one.getParentId(), one.getId());
            productSummaryInfoService.updateVariantNum(one.getParentId());
            productSupplyService.removeProductSupply(one.getId());
            logisticsProductFeign.deleteByVariantId(one.getId());
        }
        productUpdateLogService.log(new ProductLogDTO(deleteDTO)
                .setLogContent("删除产品").setProductId(one.getId()));
        productSqsSender.sendProductSyncMsg(Collections.singletonList(one.getId()));
    }

    /**
     * 校验工厂单情况
     */
    private void checkFactoryOrderCreateWait(Product one) {
        Map<Long, Integer> productIdKeyWaitFactoryNum = Maps.newHashMap();
        if (one.isParent()) {
            // 上架的变体数
            List<Long> productIds = lambdaQuery().eq(Product::getParentId, one.getId()).select(Product::getId).list()
                    .stream().map(Product::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(productIds)) {
                productIdKeyWaitFactoryNum = factoryOrderCreateTaskFeign.waitTaskCountByProductIds(productIds);
            }
        } else {
            productIdKeyWaitFactoryNum = factoryOrderCreateTaskFeign.waitTaskCountByProductIds(Lists.newArrayList(one.getId()));
        }
        if (CollectionUtil.isNotEmpty(productIdKeyWaitFactoryNum)) {
            List<Integer> haveWaitFactoryTask = productIdKeyWaitFactoryNum.values().stream().filter(num -> num > 0).collect(Collectors.toList());
            Assert.validateNotEmpty(haveWaitFactoryTask, "当前产品存在未分配工厂订单，无法删除，请稍后重试！");
        }
    }

    /**
     * 赔付开关
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateCompensationStatus(ProductCompensationStatusDTO statusDTO) {
        if (CollUtil.isEmpty(statusDTO.getProductIds())) {
            return;
        }
        productCopyRecordService.checkCopyListStatusAndError(statusDTO.getProductIds());
        EnumProductCompensationStatus status = EnumProductCompensationStatus.checkByStatus(statusDTO.getCompensationStatus());
        List<Product> children = lambdaQuery().select(Product::getId)
                .in(Product::getId, statusDTO.getProductIds()).eq(Product::getTenantId, statusDTO.getTenantId())
                .ne(Product::getParentId, 0).list();
        if (CollUtil.isNotEmpty(children)) {
            throw new BusinessException("只能修改母体");
        }
        lambdaUpdate().and(a -> a.in(Product::getId, statusDTO.getProductIds()).or().in(Product::getParentId, statusDTO.getProductIds()))
                .eq(Product::getTenantId, statusDTO.getTenantId())
                .set(Product::getCompensationStatus, status.status)
                .update();
        productSummaryInfoService.updateCompensationStatus(statusDTO.getProductIds(), status);
        productUpdateLogService.log(new ProductLogDTO(statusDTO)
                .setLogContent("赔付" + status.desc + ":" + JSON.toJSONString(statusDTO.getProductIds())));
    }


    /**
     * 关闭租户下所有产品的赔付状态
     *
     * @param tenantId 租户id
     */
    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 600000)
    public void closeCompensation(Long tenantId) {
        // 赔付作用于母体
        List<Product> products = lambdaQuery().select(Product::getId).eq(Product::getParentId, 0L).eq(Product::getTenantId, tenantId).eq(Product::getCompensationStatus, EnumProductCompensationStatus.OPEN.status).list();
        List<Long> productIds = products.stream().map(Product::getId).collect(Collectors.toList());
        lambdaUpdate().set(Product::getCompensationStatus, EnumProductCompensationStatus.CLOSE.status)
                .in(Product::getId, productIds)
                .update();
        productSummaryInfoService.updateCompensationStatus(productIds, EnumProductCompensationStatus.CLOSE);
    }

    /**
     * 分销开关
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateIsDistribution(ProductDistributionStatusDTO statusDTO) {
        if (CollUtil.isEmpty(statusDTO.getIds())) {
            return;
        }
        lambdaUpdate().and(a -> a.in(Product::getId, statusDTO.getIds()).or().in(Product::getParentId, statusDTO.getIds()))
                .eq(Product::getTenantId, statusDTO.getTenantId())
                .set(Product::getIsDistribution, statusDTO.getIsDistribution())
                .update();
        productSummaryInfoService.updateIsDistribution(statusDTO.getIds(), statusDTO.getIsDistribution());
        productUpdateLogService.log(new ProductLogDTO(statusDTO)
                .setLogContent("分销" + (BasePoConstant.yes(statusDTO.getIsDistribution()) ? "开启" : "关闭")
                        + ":" + JSON.toJSONString(statusDTO.getIds())));
    }

    /***
     *
     *颜色表更新修改对应产品信息
     * <AUTHOR>
     * @date 2020/6/29 9:41
     */
    public void updateColor(Long id, ColorDto colorDto) {
        LambdaUpdateWrapper<Product> productLambdaUpdateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(StrUtil.isNotBlank(colorDto.getColorCode()), Product::getColorCode, colorDto.getColorCode())
                .set(StrUtil.isNotBlank(colorDto.getColorName()), Product::getColorName, colorDto.getColorName())
                .set(Product::getColorOpacity, colorDto.getColorOpacity())
                .set(null != colorDto.getColorSort(), Product::getColorSort, colorDto.getColorSort())
                .set(null != colorDto.getChineseNotes(), Product::getColocChineseNotes, colorDto.getChineseNotes())
                .eq(Product::getColorId, id)
                .eq(Product::getTenantId, colorDto.getTenantId())
                .ne(Product::getStatus, CategoryStatus.DELETE.getStatus());
        baseMapper.update(null, productLambdaUpdateWrapper);
    }

    /***
     *
     *颜色表更新颜色排序
     * <AUTHOR>
     * @date 2020/6/29 9:41
     */
    public void updateColorSort(Integer colorSort, Integer newColorSort, Long tenantId) {
        LambdaUpdateWrapper<Product> productLambdaUpdateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getColorSort, newColorSort)
                .eq(Product::getColorSort, colorSort)
                .eq(Product::getTenantId, tenantId)
                .ne(Product::getStatus, CategoryStatus.DELETE.getStatus());
        baseMapper.update(null, productLambdaUpdateWrapper);
    }

    /***
     *
     *颜色表更新颜色排序
     * <AUTHOR>
     * @date 2020/6/29 9:41
     */
    public void updateColorSortByColorId(Integer colorSort, Long colorId) {
        LambdaUpdateWrapper<Product> productLambdaUpdateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getColorSort, colorSort)
                .eq(Product::getColorId, colorId)
                .ne(Product::getStatus, CategoryStatus.DELETE.getStatus());
        baseMapper.update(null, productLambdaUpdateWrapper);
    }

    /***
     *更新尺寸
     * <AUTHOR>
     * @date 2020/6/29 10:02
     */
    public void updateSize(Size size) {
        LambdaUpdateWrapper<Product> productLambdaUpdateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getSize, size.getSizeName())
                .set(Product::getSizeRemark, size.getSizeRemark())
                .set(Product::getSizeSort, size.getSizeSort())
                .eq(Product::getSizeId, size.getId());
        baseMapper.update(null, productLambdaUpdateWrapper);
    }

    public Boolean initBestStatus() {
        List<HotProductStatDTO> hotProductStatDTOS = productStatFeign.getHotProduct(44);
        this.resetBestStatus();
        for (HotProductStatDTO hotProductStatDTO : hotProductStatDTOS) {
            this.setBestStatus(hotProductStatDTO.getProductId(), hotProductStatDTO.getHotOrderNum().intValue());
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean initOnSaleStatus() {
        Set<Long> parentIds = new HashSet<>();
        Set<Long> allIds = new HashSet<>();
        productReadService.getOnSaleProducts().forEach(p -> {
            parentIds.add(p.getParentId());
            allIds.add(p.getParentId());
            allIds.add(p.getId());
        });
        this.resetProductOnSaleStatus();
        this.updateProductOnSaleStatus(allIds, ProductConstant.ON_SALE_STATUS_OPEN);
        productSummaryInfoService.initAllOnSalePrice(parentIds);
        return true;
    }

    public Boolean initOnSaleStatusByParentId(Long parentId) {
        List<Product> onSaleProducts = productReadService.getOnSaleProductsByParentId(parentId);
        List<Long> parentIds = onSaleProducts.stream().map(Product::getParentId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(parentIds)) {
            //有parentId说明存在促销的变体
            this.updateProductOnSaleStatus(parentIds, ONLINE.getStatus());
        } else {
            //没有parentId说明没有促销的变体
            this.updateProductOnSaleStatus(Lists.newArrayList(parentId), CommonStatus.OFFLINE.getStatus());
        }
        return true;
    }

    /**
     * 获取全部促销中的产品
     *
     * @return
     */
    public void updateProductOnSaleStatus(Collection<Long> ids, Integer status) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        LambdaUpdateWrapper<Product> productLambdaUpdateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getOnSaleStatus, status)
                .in(Product::getId, ids);
        this.update(productLambdaUpdateWrapper);
    }


    /**
     * 获取全部促销中的产品
     *
     * @return
     */
    public void resetProductOnSaleStatus() {
        LambdaUpdateWrapper<Product> productLambdaUpdateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getOnSaleStatus, ProductConstant.ON_SALE_STATUS_CLOSE)
                .eq(Product::getOnSaleStatus, ProductConstant.ON_SALE_STATUS_OPEN);
        this.update(productLambdaUpdateWrapper);
    }

    /**
     * 超过一个月的设置为非新品
     *
     * @return
     */
    public Boolean resetNewStatus() {
        LambdaUpdateWrapper<Product> productLambdaUpdateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getNewStatus, CommonStatus.OFFLINE.getStatus())
                .le(Product::getSwitchTime, new Date(System.currentTimeMillis() - NEW_STATUS_TIME))
                .eq(Product::getParentId, 0L)
                .ne(Product::getStatus, CategoryStatus.DELETE.getStatus());
        this.update(productLambdaUpdateWrapper);
        return true;
    }

    /**
     * 所有都设为非热卖
     */
    public void resetBestStatus() {
        LambdaUpdateWrapper<Product> productLambdaUpdateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getHotSellStatus, CommonStatus.OFFLINE.getStatus())
                .set(Product::getHotSellNum, 0)
                .ne(Product::getStatus, CategoryStatus.DELETE.getStatus());
        this.update(productLambdaUpdateWrapper);
    }

    /**
     * 根据id设为热卖
     */
    public void setBestStatus(Long id, Integer hotSellNum) {
        Product product = new Product();
        product.setId(id);
        product.setHotSellStatus(ONLINE.getStatus());
        product.setHotSellNum(hotSellNum);
        this.updateById(product);
    }

    /**
     * 根据id设为精品
     */
    public void updateBestStatus(List<Long> productIds, Integer bestStatus) {
        lambdaUpdate()
                .set(Product::getBestStatus, bestStatus)
                .in(Product::getId, productIds)
                .update();
    }

    public void updateBindLogistics(List<Long> ids, Integer isBindLogistics) {
        LambdaUpdateWrapper<Product> updateWrapper = Wrappers.lambdaUpdate(Product.class)
                .in(Product::getId, ids)
                .set(Product::getIsBindLogistics, isBindLogistics);
        this.update(updateWrapper);
    }


    /**
     * 更新产品英文名称
     *
     * @param tenantId    租户id
     * @param productId   产品id
     * @param englishName 产品英文名
     */
    public void updateEnglishName(Long tenantId, Long productId, String englishName) {
        lambdaUpdate()
                .set(Product::getEnglishName, englishName)
                .eq(Product::getTenantId, tenantId)
                .and(a -> a.eq(Product::getId, productId).or().eq(Product::getParentId, productId))
                .update();
    }


    public void updateBlankDesignImageByIds(String url, List<Long> ids) {
        LambdaUpdateWrapper<Product> lambdaUpdateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getBlankDesignUrl, url)
                .in(Product::getId, ids);
        baseMapper.update(null, lambdaUpdateWrapper);
    }

    public void updateAccumulateLevel(Long parentId, int accumulateLevel) {
        LambdaUpdateWrapper<Product> updateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getAccumulateLevel, accumulateLevel);
        updateWrapper.and(w -> w.eq(Product::getId, parentId).or().eq(Product::getParentId, parentId));
        this.update(updateWrapper);
    }

    public void updateSwitchTime(List<Long> idList) {
        LambdaUpdateWrapper<Product> lambdaUpdateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getSwitchTime, new Date())
                .in(Product::getId, idList);
        baseMapper.update(null, lambdaUpdateWrapper);
    }

    /**
     * 设置 预下架
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public void setOfflineForeshow(ProductVariantShelfStatusReqDTO reqDTO) {
        Assert.validateNull(reqDTO.getOfflineForeshowDay(), "预告时间不能为空");
        lambdaUpdate().eq(reqDTO.getParentId() != null, Product::getParentId, reqDTO.getParentId())
                .in(Product::getId, reqDTO.getChildIds())
                .set(StrUtil.isNotBlank(reqDTO.getRemark()), Product::getRemark, reqDTO.getRemark())
                .set(StrUtil.isNotBlank(reqDTO.getSmallOrderRemark()), Product::getSmallOrderRemark, reqDTO.getSmallOrderRemark())
                .set(SupplyChainTypeEnum.isOnePiece(reqDTO.getSupplyChainType()), Product::getOfflineForeshowDay, reqDTO.getOfflineForeshowDay())
                .set(SupplyChainTypeEnum.isOnePiece(reqDTO.getSupplyChainType()), Product::getOfflineTime, reqDTO.getOfflineTimeLong())
                .set(SupplyChainTypeEnum.isSmallOrder(reqDTO.getSupplyChainType()), Product::getSmallOrderOfflineForeshowDay, reqDTO.getOfflineForeshowDay())
                .set(SupplyChainTypeEnum.isSmallOrder(reqDTO.getSupplyChainType()), Product::getSmallOrderOfflineTime, reqDTO.getOfflineTimeLong())
                .update();
        reqDTO.getParentIds().add(reqDTO.getParentId());
        productSummaryInfoService.updateOfflineTime(reqDTO.getParentIds(), reqDTO.getOfflineTimeLong(), reqDTO.getSupplyChainType());
        String logContent = SupplyChainTypeEnum.isOnePiece(reqDTO.getSupplyChainType()) ? "一件产能线预下架" : "小单产能线预下架";
        productUpdateLogService.log(new ProductLogDTO(reqDTO)
                .setLogContent(logContent).setProductId(reqDTO.getParentId()));
        TransactionHookManager.registerHook(new TransactionHookAdapter() {
            @Override
            public void afterCommit() {
                offlineMessage(reqDTO.getParentIds(), reqDTO.getSupplyChainType());
            }
        });
    }

    /**
     * 设置了预下架的产品，定时器自动下架
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public void autoOfflineProduct() {
        //一件产能线自动下架
        autoOfflineProductSupplyChain(ONE_PIECE.name());
        //小单产能线自动下架
        autoOfflineProductSupplyChain(SupplyChainTypeEnum.SMALL_ORDER.name());
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    public void autoOfflineProductSupplyChain(String supplyChainType) {
        boolean isOnePieceSupplyChain = SupplyChainTypeEnum.isOnePiece(supplyChainType);
        boolean isSmallOrderSupplyChain = SupplyChainTypeEnum.isSmallOrder(supplyChainType);

        long nowTime = System.currentTimeMillis();
        List<Product> variantList = Lists.newArrayList();
        //根据产能线查询对应产能线可以下架的变体
        if (isOnePieceSupplyChain) {
            variantList = lambdaQuery().select(Product::getId, Product::getParentId)
                    .eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                    .gt(Product::getOfflineTime, 0L)
                    .le(Product::getOfflineTime, nowTime)
                    .ne(Product::getParentId, 0L).list();
        }
        if (isSmallOrderSupplyChain) {
            variantList = lambdaQuery().select(Product::getId, Product::getParentId)
                    .eq(Product::getSmallOrderSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                    .gt(Product::getSmallOrderOfflineTime, 0L)
                    .le(Product::getSmallOrderOfflineTime, nowTime)
                    .ne(Product::getParentId, 0L).list();
        }

        if (CollUtil.isEmpty(variantList)) {
            return;
        }
        List<Long> variantIds = new ArrayList<>();
        Set<Long> parentIds = new HashSet<>();
        List<Long> offlineProductIds = Lists.newArrayList();
        List<Long> updateProductId = Lists.newArrayList();
        //母体产品id-变体产品id列表Map
        MapListUtil<Long, Long> mapListUtil = MapListUtil.instance();
        variantList.forEach(v -> {
            if (v.getParentId() > 0) {
                variantIds.add(v.getId());
                parentIds.add(v.getParentId());
                mapListUtil.addDistinctValue(v.getParentId(), v.getId());
            }
        });
        if (CollUtil.isEmpty(variantIds)) {
            return;
        }
        //根据产能线更新对应的产能线状态
        //一件产能线
        if (isOnePieceSupplyChain) {
            lambdaUpdate().in(Product::getId, variantIds).ne(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name())
                    .set(Product::getOfflineTime, 0L).set(Product::getOfflineForeshowDay, 0)
                    .set(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name()).update();
        }
        //小单产能线
        if (isSmallOrderSupplyChain) {
            lambdaUpdate().in(Product::getId, variantIds).ne(Product::getSmallOrderSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name())
                    .set(Product::getSmallOrderOfflineTime, 0L).set(Product::getSmallOrderOfflineForeshowDay, 0)
                    .set(Product::getSmallOrderSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name()).update();
        }

        this.resetOffline(parentIds, supplyChainType);
        //更新母体最小重量
        this.updateParentFieldAboutChild(parentIds);
        //同时下架供应关系
        productSupplyService.autoOfflineSupply(variantIds, supplyChainType);
        // 变体、母体状态下架、母体产能线状态下架
        List<Long> updateParentIds = offineShelfStatusByParentIds(parentIds, variantIds);

        //有更新的产品发送变更sqs消息
        updateProductId.addAll(variantIds);
        updateProductId.addAll(updateParentIds);
        productSqsSender.sendProductSyncMsg(offlineProductIds);

        //记录更新日志
        String logContent = isOnePieceSupplyChain ? "产品一件产能线设置预下架时间到，自动下架:" : "产品小单产能线设置预下架时间到，自动下架:";
        productUpdateLogService.log(new ProductLogDTO().setLogContent(logContent + JSON.toJSONString(offlineProductIds)));

        if (isOnePieceSupplyChain) {
            //发送产品下架，特惠价失效通知
            mapListUtil.getMaps().forEach((parentProductId, productIds) -> {
                productInquiryService.sendProductStatusChangeNotification(parentProductId, productIds, ProductInquiryNotificationMessageEnum.PRODUCT_OFFLINE);
            });
        }

    }

    public List<Long> offineShelfStatusByParentIds(Collection<Long> parentIdList, Collection<Long> variantIdList) {
        if (CollUtil.isEmpty(parentIdList) || CollUtil.isEmpty(variantIdList)) {
            return Lists.newArrayList();
        }
        Map<Long, Product> parentMap = productReadService.findByIds(parentIdList).stream().collect(Collectors.toMap(Product::getId, Function.identity()));
        Set<Long> updateParentIdList = Sets.newHashSet();
        //更新变体上下架状态
        lambdaUpdate().in(Product::getId, variantIdList)
                .eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name())
                .eq(Product::getSmallOrderSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name())
                .eq(Product::getStatus, ProductConstant.STATUS_ONLINE)
                .set(Product::getStatus, ProductConstant.STATUS_OFFLINE).update();

        //更新母体产能线状态
        Set<Long> onePieceSupplyChainOfflineParentIds = new HashSet<>();
        Set<Long> smallOrderSupplyChainOfflineParentIds = new HashSet<>();
        Map<Long, String> parentOnePieceSupplyChainStatusMap = Maps.newHashMap();
        Map<Long, String> parentSmallOrderSupplyChainStatusMap = Maps.newHashMap();
        //查询母体不同产能线的上架变体数
        Map<Long, Integer> onePieceSupplyChainOnlineVariantNumMap = productReadService.mapCountSupplyChainOnlineVariantNum(parentIdList,
                ONE_PIECE.name());
        Map<Long, Integer> smallOrderSupplyChainOnlineVariantNumMap = productReadService.mapCountSupplyChainOnlineVariantNum(parentIdList,
                SupplyChainTypeEnum.SMALL_ORDER.name());
        //取出要变更产能线状态的母体
        parentIdList.forEach(p -> {
            Product parent = parentMap.getOrDefault(p, new Product());
            parentOnePieceSupplyChainStatusMap.put(p, parent.getOnePieceSupplyChainStatus());
            parentSmallOrderSupplyChainStatusMap.put(p, parent.getSmallOrderSupplyChainStatus());
            if (onePieceSupplyChainOnlineVariantNumMap.getOrDefault(p, 0) <= 0
                    && ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name().equals(parent.getOnePieceSupplyChainStatus())) {
                onePieceSupplyChainOfflineParentIds.add(p);
                parentOnePieceSupplyChainStatusMap.put(p, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name());
            }
            if (smallOrderSupplyChainOnlineVariantNumMap.getOrDefault(p, 0) <= 0
                    && ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name().equals(parentMap.getOrDefault(p, new Product()).getSmallOrderSupplyChainStatus())) {
                smallOrderSupplyChainOfflineParentIds.add(p);
                parentSmallOrderSupplyChainStatusMap.put(p, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name());
            }
        });
        //变更母体产能线状态
        if (CollUtil.isNotEmpty(onePieceSupplyChainOfflineParentIds)) {
            offlineParentSupplyChain(onePieceSupplyChainOfflineParentIds, ONE_PIECE.name());
            updateParentIdList.addAll(onePieceSupplyChainOfflineParentIds);
        }
        if (CollUtil.isNotEmpty(smallOrderSupplyChainOfflineParentIds)) {
            offlineParentSupplyChain(smallOrderSupplyChainOfflineParentIds, SupplyChainTypeEnum.SMALL_ORDER.name());
            updateParentIdList.addAll(smallOrderSupplyChainOfflineParentIds);
        }

        //更新母体状态
        Set<Long> offlineParentIds = new HashSet<>();
        parentIdList.forEach(productId -> {
            Product parent = parentMap.getOrDefault(productId, new Product());
            boolean parentOnline = ProductConstant.STATUS_ONLINE.equals(parent.getStatus());
            //母体的所有产能线都下架，母体也下架
            if (ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name().equals(parentOnePieceSupplyChainStatusMap.get(productId))
                    && ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name().equals(parentSmallOrderSupplyChainStatusMap.get(productId)) && parentOnline) {
                offlineParentIds.add(productId);
            }
        });
        if (CollUtil.isNotEmpty(offlineParentIds)) {
            //母体下架,同时更新汇总表状态
            offlineParent(offlineParentIds);
            offlineParentIds.forEach(pid -> {
                //母体下架，则官方成品上架的需要下架处理
                // todo 最好 写个批量的方法，或者走消息
                officialDesignProductFeign.batchReleaseByProductParentId(pid, 0L);
                //母体下架，根据产品名称删除标签
                Product parent = parentMap.getOrDefault(pid, new Product());
                productLabelService.deleteByName(parent.getName());
            });
            updateParentIdList.addAll(offlineParentIds);
        }
        return Lists.newArrayList(updateParentIdList);
    }

    private void updateParentFieldAboutChild(Collection<Long> parentIds) {
        List<Product> updateParentProductList = Lists.newArrayList();
        List<Product> parentProductList = productReadService.findByIds(parentIds);
        if (CollUtil.isEmpty(parentProductList)) {
            return;
        }
        //查询母体下上架状态的变体
        List<Product> childProductList = productReadService.findVariantByParentIdsAndStatus(parentIds, ProductStatus.ON_SHELF);
        if (CollUtil.isEmpty(childProductList)) {
            return;
        }
        Map<Long, List<Product>> childProductMap = childProductList.stream().collect(Collectors.groupingBy(Product::getParentId));
        parentProductList.forEach(parent -> {
            Double minWeight = 0D;
            List<Product> productList = childProductMap.get(parent.getId());
            if (CollUtil.isEmpty(productList)) {
                return;
            }
            for (Product child : productList) {
                //变体最小重量
                if (minWeight == 0D || child.getWeight() < minWeight) {
                    minWeight = child.getWeight();
                }
            }
            if (!minWeight.equals(parent.getMinWeight())) {
                //更新最小重量
                Product updateParentProduct = new Product();
                updateParentProduct.setId(parent.getId());
                updateParentProduct.setMinWeight(minWeight);
                updateParentProductList.add(updateParentProduct);
            }
        });

        if (CollUtil.isNotEmpty(updateParentProductList)) {
            updateBatchById(updateParentProductList);
        }

    }


    /**
     * 重置汇总表的预下架时间
     *
     * @param parentIds
     */
    public void resetOffline(Collection<Long> parentIds, String supplyChainType) {
        // 有变体还是预休假状态
        List<Product> productList = lambdaQuery().in(Product::getParentId, parentIds)
                //根据产能线取对应的下架时间大于0的
                .gt(SupplyChainTypeEnum.isOnePiece(supplyChainType), Product::getOfflineTime, 0L)
                .gt(SupplyChainTypeEnum.isSmallOrder(supplyChainType), Product::getSmallOrderOfflineTime, 0L).list();
        List<Long> list = productList.stream().map(Product::getParentId).distinct().collect(Collectors.toList());
        List<Long> offlineIds = Lists.newArrayList();
        offlineIds.addAll(parentIds);
        offlineIds.removeAll(list);
        productSummaryInfoService.updateOfflineTime(offlineIds, 0L, supplyChainType);
    }

    /**
     * 母体下架
     */
    private void offlineParent(Collection<Long> parentIds) {
        lambdaUpdate().in(Product::getId, parentIds)
                .set(Product::getStatus, ProductConstant.STATUS_OFFLINE).update();
        productSummaryInfoService.updateShelfStatus(parentIds, ProductConstant.STATUS_OFFLINE);
    }

    /**
     * 母体上架
     */
    private void onlineParent(Collection<Long> parentIds) {
        lambdaUpdate().in(Product::getId, parentIds).set(Product::getStatus, ProductConstant.STATUS_ONLINE).set(Product::getSwitchTime, new Date()).update();
        productSummaryInfoService.updateShelfStatus(parentIds, ProductConstant.STATUS_ONLINE);
    }

    private void offlineParentSupplyChain(Collection<Long> parentIds, String supplyChainType) {
        //下架母体产能线状态
        lambdaUpdate().in(Product::getId, parentIds)
                .set(SupplyChainTypeEnum.isOnePiece(supplyChainType), Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name())
                .set(SupplyChainTypeEnum.isSmallOrder(supplyChainType), Product::getSmallOrderSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name())
                .update();
        //更新汇总表的产能线状态
        productSummaryInfoService.updateSupplyChainStatus(parentIds, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name(), supplyChainType);
        // 下架发送消息，删除推荐产品
        TransactionUtil.afterCommit(() -> {
            parentIds.forEach(parentId -> {
                ProductOfflineMsg productOfflineMsg = new ProductOfflineMsg();
                productOfflineMsg.setProductId(parentId);
                productOfflineMsg.setSupplyChainType(supplyChainType);
                rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PRODUCT, ProductTagConst.PRODUCT_OFFLINE, productOfflineMsg);
            });
        });
    }

    private void onlineParentSupplyChain(Collection<Long> parentIds, String supplyChainType) {
        //上架母体产能线状态
        lambdaUpdate().in(Product::getId, parentIds)
                .set(SupplyChainTypeEnum.isOnePiece(supplyChainType), Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                .set(SupplyChainTypeEnum.isSmallOrder(supplyChainType), Product::getSmallOrderSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                .update();
        //更新汇总表的产能线状态
        productSummaryInfoService.updateSupplyChainStatus(parentIds, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name(), supplyChainType);
    }

    public void offlineMessage(Collection<Long> parentIds, String supplyChainType) {
        for (Long parentId : parentIds) {
            ProductOfflineMsg productOfflineMsg = new ProductOfflineMsg();
            productOfflineMsg.setProductId(parentId);
            productOfflineMsg.setSupplyChainType(supplyChainType);
            //mqTemplate.sendMessage(EvenConstant.PRODUCT_WILL_OFFLINE, productOfflineMsg);
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PRODUCT, ProductTagConst.PRODUCT_WILL_OFFLINE, productOfflineMsg);
        }
    }

    /**
     * 取消产能线预下架
     * 母体下的指定变体或所有变体都取消
     * 判断汇总表母体的状态
     */
    public void cancelOfflineForeshow(ProductVariantShelfStatusReqDTO reqDTO) {
        LambdaUpdateChainWrapper<Product> updateWrapper = lambdaUpdate().eq(Product::getParentId, reqDTO.getParentId())
                .in(CollUtil.isNotEmpty(reqDTO.getChildIds()), Product::getId, reqDTO.getChildIds());
        //一件产能线取消预下架
        if (SupplyChainTypeEnum.isOnePiece(reqDTO.getSupplyChainType())) {
            updateWrapper
                    // 下架时间大于当前时间
                    .gt(Product::getOfflineTime, System.currentTimeMillis())
                    .set(Product::getOfflineForeshowDay, 0)
                    .set(Product::getOfflineTime, 0L)
                    .update();
        }
        //小单产能线取消预下架
        if (SupplyChainTypeEnum.isSmallOrder(reqDTO.getSupplyChainType())) {
            updateWrapper
                    // 小单下架时间大于当前时间
                    .gt(Product::getSmallOrderOfflineTime, System.currentTimeMillis())
                    .set(Product::getSmallOrderOfflineForeshowDay, 0)
                    .set(Product::getSmallOrderOfflineTime, 0L)
                    .update();
        }
        //重置产品汇总表预下架时间
        this.resetOffline(Lists.newArrayList(reqDTO.getParentId()), reqDTO.getSupplyChainType());
        String logContent = SupplyChainTypeEnum.isOnePiece(reqDTO.getSupplyChainType()) ? "一件产能线取消预下架" : "小单产能线取消预下架";
        productUpdateLogService.log(new ProductLogDTO(reqDTO)
                .setLogContent(logContent).setProductId(reqDTO.getParentId()));
    }

    /**
     * 产能线上下架状态变更
     * 必须指定母体，针对某个母体下的批量操作
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateSupplyChainStatus(ProductVariantShelfStatusReqDTO reqDTO) {
        //校验状态
        if (!ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name().equals(reqDTO.getSupplyChainStatus())
                && !ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name().equals(reqDTO.getSupplyChainStatus())) {
            throw new BusinessException("状态错误");
        }
        //校验产品
        Product parent = productReadService.findById(reqDTO.getParentId());
        if (parent == null || parent.notParent()) {
            throw new BusinessException("产品母体id错误");
        }
        Assert.validateTrue(ProductConstant.STATUS_DELETE.equals(parent.getStatus()), "产品已被删除");


        boolean isOnePieceSupplyChain = SupplyChainTypeEnum.isOnePiece(reqDTO.getSupplyChainType());
        boolean isSmallOrderSupplyChain = SupplyChainTypeEnum.isSmallOrder(reqDTO.getSupplyChainType());
        List<Product> variantList = lambdaQuery().eq(Product::getParentId, reqDTO.getParentId())
                .in(CollUtil.isNotEmpty(reqDTO.getChildIds()), Product::getId, reqDTO.getChildIds())
                //根据要变更的产能线类型来选择是要判断哪个产能线状态
                .ne(isOnePieceSupplyChain, Product::getOnePieceSupplyChainStatus, reqDTO.getSupplyChainStatus())
                .ne(isSmallOrderSupplyChain, Product::getSmallOrderSupplyChainStatus, reqDTO.getSupplyChainStatus())
                .ne(Product::getStatus, ProductConstant.STATUS_DELETE).list();
        if (CollUtil.isEmpty(variantList)) {
            return;
        }
        List<Long> variantIds = variantList.stream().map(Product::getId).collect(Collectors.toList());
        // 是否上架
        boolean online = ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name().equals(reqDTO.getSupplyChainStatus());
        if (online) {
            // 需要所有公开模板都绑定了变体才能上架
            Assert.validateBool(prototypeGroupService.isBindAllPrototypeGroup(variantList), "模板未配置不能上架");
            //根据产能线校验对应产能线的供应关系是否已配置
            List<ProductSupply> supplyList = productSupplyService.findByProductIds(variantIds, EnumProductSupplyStatus.ONLINE.status, reqDTO.getSupplyChainType());
            Map<Long, List<ProductSupply>> supplyMap = supplyList.stream().collect(Collectors.groupingBy(ProductSupply::getProductId));
            variantList.forEach(variant -> {
                Assert.validateTrue(variant.getWeight() <= 0, "重量未设置不能上架");
//                Assert.validateTrue(variant.getProductionCycleMin() <= 0, "未设置生产周期不能上架");
                Assert.validateEmpty(supplyMap.get(variant.getId()), "供应关系未配置不能上架");
            });

            //一件产能线
            if (isOnePieceSupplyChain) {
                Boolean haveCycles = productDeliveryCycleMapperManager.isHaveCycles(reqDTO.getParentId(), ONE_PIECE.name());
                Assert.validateBool(haveCycles, "发货周期为0不能上架");
                Boolean haveProductionCycles = productionDeliveryCycleMapperManager.isHaveCycles(reqDTO.getParentId(), ONE_PIECE.name());
                Assert.validateBool(haveProductionCycles, "未设置生产周期不能上架");
                variantList.forEach(variant -> {
//                    Assert.validateTrue(variant.getProductionCycle() <= 0, "发货周期为0不能上架");
                    Assert.validateTrue(StrUtil.isBlank(variant.getPrice()), "平台价未设置不能上架");
                });
            }
            //小单产能线
            if (isSmallOrderSupplyChain) {
                Boolean haveCycles = productDeliveryCycleMapperManager.isHaveCycles(reqDTO.getParentId(), SMALL_ORDER.name());
                Assert.validateBool(haveCycles, "发货周期为0不能上架");
                Boolean haveProductionCycles = productionDeliveryCycleMapperManager.isHaveCycles(reqDTO.getParentId(), SMALL_ORDER.name());
                Assert.validateBool(haveProductionCycles, "未设置生产周期不能上架");
                variantList.forEach(variant -> {
//                    Assert.validateTrue(variant.getSmallOrderProductionCycle() <= 0, "发货周期为0不能上架");
                    Assert.validateTrue(StrUtil.isBlank(variant.getSmallOrderPrice()), "平台价未设置不能上架");
                });
            }
        }
        //一件产能线
        if (isOnePieceSupplyChain) {
            //更新一件产能线状态
            lambdaUpdate().in(Product::getId, variantIds).ne(Product::getOnePieceSupplyChainStatus, reqDTO.getSupplyChainStatus())
                    .set(Product::getOnePieceSupplyChainStatus, reqDTO.getSupplyChainStatus()).set(online, Product::getOfflineTime, 0L)
                    .set(!online && StrUtil.isNotBlank(reqDTO.getRemark()), Product::getRemark, reqDTO.getRemark()).update();
        }
        //小单产能线
        if (isSmallOrderSupplyChain) {
            //更新小单产能线状态
            lambdaUpdate().in(Product::getId, variantIds).ne(Product::getSmallOrderSupplyChainStatus, reqDTO.getSupplyChainStatus())
                    .set(Product::getSmallOrderSupplyChainStatus, reqDTO.getSupplyChainStatus()).set(online, Product::getSmallOrderOfflineTime, 0L)
                    .set(!online && StrUtil.isNotBlank(reqDTO.getSmallOrderRemark()), Product::getSmallOrderRemark, reqDTO.getSmallOrderRemark()).update();
        }
        //根据产能线重置product汇总表的预下架时间
        this.resetOffline(Lists.newArrayList(reqDTO.getParentId()), reqDTO.getSupplyChainType());
        //刷新产品跟母体的状态，产能线状态
        reqDTO.setChildIds(variantIds);
        //联动影响变体的上下架状态，母体的产能线状态与上下架状态
        updateShelfStatus(reqDTO);

        // 上架时
        if (online) {
            // 新上架设置为新品
            lambdaUpdate().eq(Product::getId, parent.getId()).set(Product::getNewStatus, ProductConstant.STATUS_ONLINE).update();
            // 更新通用报关信息申报价格
            adminNormalCustomsDeclarationService.updateVariantPrice(variantIds, parent.getId());
        }
        if (!online) {
            updateParentFieldAboutChild(Lists.newArrayList(parent.getId()));
        }
        String supplyChain = isOnePieceSupplyChain ? "一件产能线" : "小单产能线";
        productUpdateLogService.log(new ProductLogDTO(reqDTO)
                .setLogContent("产品" + supplyChain + (online ? "上架" : "下架") + ":" + variantIds)
                .setProductId(reqDTO.getParentId()));
        List<Long> updateProductIdList = Lists.newArrayList(variantIds);
        updateProductIdList.add(parent.getId());
        productSqsSender.sendProductSyncMsg(updateProductIdList);
        // 消息
        if (online && isOnePieceSupplyChain) {
            //产品一件产能线上架 产品询价单（限时特惠价）生效通知
            productInquiryService.sendProductStatusChangeNotification(parent.getId(), variantIds, ProductInquiryNotificationMessageEnum.PRODUCT_ONLINE);
        }
        if (!online && isOnePieceSupplyChain) {
            //产品一件产能线下架 产品询价单（限时特惠价）失效通知
            productInquiryService.sendProductStatusChangeNotification(parent.getId(), variantIds, ProductInquiryNotificationMessageEnum.PRODUCT_OFFLINE);
        }
        if (online) {
            // 上架消息
            ProductIdReqDTO mqReqDTO = new ProductIdReqDTO(Collections.singletonList(parent.getId()));
            mqReqDTO.setUid(reqDTO.getUid());
            mqReqDTO.setTenantId(parent.getTenantId());
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PRODUCT, ProductTagConst.TAG_PRODUCT_ONLINE, mqReqDTO);
        }
        //更新产品图
        updateProductMsg(parent.getId());
    }

    public void updateProductMsg(Long id) {
        PrototypeChangeMsg msg = new PrototypeChangeMsg();
        msg.setProductParentId(id);
        // mqTemplate.sendMessage(ProductEven.PROTOTYPE_CHANGE_TOPIC, ProductEven.PROTOTYPE_BIND, msg);
        rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PRODUCT, ProductTagConst.PROTOTYPE_BIND, msg);
    }

    /**
     * 变体母体上下架状态、母体产能线状态变更
     * 必须指定母体，针对某个母体下的批量操作
     */
    public void updateShelfStatus(ProductVariantShelfStatusReqDTO reqDTO) {
        Product parent = productReadService.findById(reqDTO.getParentId());
        boolean online = ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name().equals(reqDTO.getSupplyChainStatus());
        //更新变体上下架状态
        if (online) {
            //已有产能线上架的变体，状态要变为上架
            lambdaUpdate().in(Product::getId, reqDTO.getChildIds())
                    .and(updateWrapper ->
                            updateWrapper.eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                                    .or()
                                    .eq(Product::getSmallOrderSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                    ).eq(Product::getStatus, ProductConstant.STATUS_OFFLINE)
                    .set(Product::getStatus, ProductConstant.STATUS_ONLINE).update();
        }
        if (!online) {
            //所有产能线都下架的变体，状态要变为下架
            lambdaUpdate().eq(Product::getParentId, parent.getId())
                    .eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name())
                    .eq(Product::getSmallOrderSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name())
                    .eq(Product::getStatus, ProductConstant.STATUS_ONLINE)
                    .set(Product::getStatus, ProductConstant.STATUS_OFFLINE).update();
        }

        //更新母体产能线状态
        String parentOnePieceSupplyChainStatus = parent.getOnePieceSupplyChainStatus();
        String parentSmallOrderSupplyChainStatus = parent.getSmallOrderSupplyChainStatus();
        //一件产能线
        if (SupplyChainTypeEnum.isOnePiece(reqDTO.getSupplyChainType())) {
            //更新母体一件产能线状态
            Integer onePieceSupplyChainOnlineCount = lambdaQuery().eq(Product::getParentId, parent.getId())
                    .eq(Product::getOnePieceSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                    .ne(Product::getStatus, ProductConstant.STATUS_DELETE).count();
            boolean parentOnePieceSupplyChainOnline = ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name().equals(parent.getOnePieceSupplyChainStatus());
            if (onePieceSupplyChainOnlineCount <= 0 && parentOnePieceSupplyChainOnline) {
                // 母体一件产能线下架，并发送产能线下架通知
                offlineParentSupplyChain(Collections.singleton(parent.getId()), ONE_PIECE.name());
                parentOnePieceSupplyChainStatus = ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name();
            } else if (onePieceSupplyChainOnlineCount > 0 && !parentOnePieceSupplyChainOnline) {
                // 母体一件产能线上架
                onlineParentSupplyChain(Collections.singleton(parent.getId()), ONE_PIECE.name());
                parentOnePieceSupplyChainStatus = ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name();
            }
        }
        //小单产能线
        if (SupplyChainTypeEnum.isSmallOrder(reqDTO.getSupplyChainType())) {
            //更新母体小单产能线状态
            Integer smallOrderSupplyChainOnlineCount = lambdaQuery().eq(Product::getParentId, parent.getId())
                    .eq(Product::getSmallOrderSupplyChainStatus, ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                    .ne(Product::getStatus, ProductConstant.STATUS_DELETE).count();
            boolean parentSmallOrderChainPieceOnline = ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name().equals(parent.getSmallOrderSupplyChainStatus());
            if (smallOrderSupplyChainOnlineCount <= 0 && parentSmallOrderChainPieceOnline) {
                // 母体小单产能线下架
                offlineParentSupplyChain(Collections.singleton(parent.getId()), SupplyChainTypeEnum.SMALL_ORDER.name());
                parentSmallOrderSupplyChainStatus = ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name();
            } else if (smallOrderSupplyChainOnlineCount > 0 && !parentSmallOrderChainPieceOnline) {
                // 母体小单产能线上架
                onlineParentSupplyChain(Collections.singleton(parent.getId()), SupplyChainTypeEnum.SMALL_ORDER.name());
                parentSmallOrderSupplyChainStatus = ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name();
            }
        }

        //更新母体状态
        // 母体是否上架
        boolean parentOnline = ProductConstant.STATUS_ONLINE.equals(parent.getStatus());
        if (ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name().equals(parentOnePieceSupplyChainStatus)
                && ProductEnumConstant.SupplyChainStatusEnum.OFFLINE.name().equals(parentSmallOrderSupplyChainStatus) && parentOnline) {
            //所有变体的所有产能线都下架，母体下架,同时更新汇总表状态
            offlineParent(Collections.singleton(parent.getId()));
            //母体下架，则官方成品上架的需要下架处理
            officialDesignProductFeign.batchReleaseByProductParentId(parent.getId(), reqDTO.getUid());
            //母体下架，根据产品名称删除标签
            productLabelService.deleteByName(parent.getName());
        } else if ((ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name().equals(parentOnePieceSupplyChainStatus)
                || ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name().equals(parentSmallOrderSupplyChainStatus)) && !parentOnline) {
            //有一个变体的一个产能线上架，母体上架，同时更新汇总表状态
            onlineParent(Collections.singleton(parent.getId()));
        }
    }


    /**
     * 新增母体
     */
    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 90000)
    public BaseListDto<Long> addParent(ProductAddParentDTO addParentDTO) {
        long nowTime = System.currentTimeMillis();
        Product parent = BeanUtil.copyProperties(addParentDTO.getParent(), Product.class);
        parent.setUserId(addParentDTO.getUid())
                .setTenantId(addParentDTO.getTenantId());
        List<Category> categories = categoryService.generateParentList(parent.getCategoryId());
        String parentSku = categoryService.generateProductParentSku(categories);
        parent.setSku(parentSku);
        // 由于变体一定没有模板，所以默认下架
        parent.setStatus(ProductConstant.STATUS_OFFLINE);
        parent.setUpdateTime(nowTime);
        parent.setNewStatus(CommonStatus.OFFLINE.getStatus());
        parent.setMinPrice(ProductConstant.PRODUCT_MAX_PRICE);
        parent.setMinWeight(ProductConstant.PRODUCT_MAX_WEIGHT);
        parent.setCreatedTime(nowTime);
        if (StrUtil.isEmpty(parent.getDeclarationName())) {
            parent.setDeclarationName(parent.getName());
        }
        if (StrUtil.isEmpty(parent.getDeclarationEnglishName())) {
            parent.setDeclarationEnglishName(parent.getEnglishName());
        }
        if (parent.getMemberLevel() == null || parent.getMemberLevel().compareTo(BigDecimal.ZERO) <= 0) {
            parent.setMemberLevel(MemberLevelEnum.V0.value);
        }
        PrototypeGroupByProductParam prototypeGroup = addParentDTO.getPrototypeGroup();
        if (prototypeGroup != null && IMAGE_CUTOM.name().equals(prototypeGroup.getPrototypeType())) {
            parent.setPrototypeType(prototypeGroup.getPrototypeType());
        }
        // 保存母体
        save(parent);
        addParentDTO.setParentId(parent.getId());
        addParentDTO.getParent().getProductDetails().setProductId(parent.getId());
        productAttributeRelService.saveOrUpdate(parent.getId(), addParentDTO.getParent().getProductDetails());
        productDetailsService.saveProductDetail(addParentDTO.getParent().getProductDetails());
        productUpdateLogService.log(new ProductLogDTO(addParentDTO)
                .setProductId(parent.getId()).setLogContent("新增母体:" + JSON.toJSONString(parent)));
        //新增可用纸箱
        if (CollUtil.isNotEmpty(addParentDTO.getParent().getProductAvailableCartons())) {
            productAvailableCartonService.addAvailableCarton(parent.getId(), addParentDTO.getParent().getProductAvailableCartons());
        }
        if (prototypeGroup != null && IMAGE_CUTOM.name().equals(prototypeGroup.getPrototypeType())) {
            prototypeGroup.setTenantId(parent.getTenantId());
            prototypeGroup.setOperateUserId(parent.getUserId());
            prototypeGroup.setProductId(parent.getId());
            prototypeGroupService.addByParent(prototypeGroup);
        }
        // 新增变体
        return ((ProductWriteService) AopContext.currentProxy()).addVariant(addParentDTO, parent, true);
    }

    public ProductAddVariantCheckResultBO checkAddVariant(ProductAddVariantDTO variantDTO, boolean newParent) {
        Set<Long> colorIds = new HashSet<>();
        Set<Long> sizeIds = new HashSet<>();
        variantDTO.getChildren().forEach(c -> {
            colorIds.add(c.getColorId());
            sizeIds.add(c.getSizeId());
        });
        List<Color> colors = colorService.findByIds(colorIds, variantDTO.getTenantId());
        Assert.validateBool(colorIds.size() == colors.size(), "颜色不存在");
        List<Size> sizes = sizeService.findByIds(sizeIds, variantDTO.getTenantId());
        Assert.validateBool(sizeIds.size() == sizes.size(), "尺寸不存在");

        Map<Long, Color> colorMap = colors.stream().collect(Collectors.toMap(BasePO::getId, c -> c));
        Map<Long, Size> sizeMap = sizes.stream().collect(Collectors.toMap(BasePO::getId, s -> s));

        List<Product> oldVariantList = newParent ? Collections.emptyList()
                : lambdaQuery().eq(Product::getParentId, variantDTO.getParentId()).list();
        // 改成获取最大sku的最后三位更合理
        int childrenNum = oldVariantList.size();
        if (!newParent && childrenNum > 0) {
            List<String> newChildKey = variantDTO.getChildren().stream().map(ProductEditChildrenDTO::getChildKey)
                    .collect(Collectors.toList());
            oldVariantList.stream().filter(v -> !ProductConstant.STATUS_DELETE.equals(v.getStatus()))
                    .forEach(old -> {
                        if (newChildKey.contains(old.getColorId() + "&&" + old.getSizeId())) {
                            Assert.wrong(StrUtil.format("颜色[{}]、尺寸[{}] 组合已存在重复的变体"
                                    , colorMap.get(old.getColorId()).getColorName()
                                    , sizeMap.get(old.getSizeId()).getSizeName()));
                        }
                    });
        }
        ProductAddVariantCheckResultBO resultBO = new ProductAddVariantCheckResultBO();
        resultBO.setChildrenNum(childrenNum);
        resultBO.setColorMap(colorMap);
        resultBO.setSizeMap(sizeMap);
        return resultBO;
    }

    /**
     * 新增变体
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public BaseListDto<Long> addVariant(ProductAddVariantDTO variantDTO, Product parent, boolean newParent) {
        ProductAddVariantCheckResultBO resultBO = checkAddVariant(variantDTO, newParent);
        //校验变体小单产能线数量配置是否正确
        checkVarinatSmallOrderNumWithColorAndSize(variantDTO.getSmallOrderSupply(), variantDTO.getSmallOrderPrice(), resultBO.getColorMap(),
                resultBO.getSizeMap());
        //校验一个母体下所有产能线的供应关系是否都属于同一个区域
        if (CollUtil.isNotEmpty(variantDTO.getSupply()) || CollUtil.isNotEmpty(variantDTO.getSmallOrderSupply())) {
            ProductUpdateSupplyReqDTO productUpdateSupplyReqDTO = new ProductUpdateSupplyReqDTO();
            productUpdateSupplyReqDTO.setSupply(variantDTO.getSupply());
            productUpdateSupplyReqDTO.setSmallOrderSupply(variantDTO.getSmallOrderSupply());
            productUpdateSupplyReqDTO.setParentId(parent.getId());
            productSupplyService.validIssuingArea(productUpdateSupplyReqDTO, true);
        }
        Integer childrenNum = resultBO.getChildrenNum();
        long nowTime = System.currentTimeMillis();

        BigDecimal minPlatformPrice = parent.getMinPrice();
        Double minWeight = parent.getMinWeight() == null ? 0.00D : parent.getMinWeight();
        List<Product> children = new ArrayList<>();
        Map<String, ProductEditChildrenDTO> childrenDtoMap = new HashMap<>(variantDTO.getChildren().size());
        // 平台价 可以跳过
        boolean hasPrice = CollUtil.isNotEmpty(variantDTO.getPrice());
        boolean hasSmallOrderPrice = CollUtil.isNotEmpty(variantDTO.getSmallOrderPrice());
        Map<String, List<LadderPriceDTO>> priceDtoMap = Collections.emptyMap();
        Map<String, List<LadderPriceDTO>> smallOrderPriceDtoMap = Collections.emptyMap();
        if (hasPrice) {
            priceDtoMap = variantDTO.getPrice().stream()
                    .collect(Collectors.toMap(ProductEditPriceDTO::getChildKey, ProductEditPriceDTO::getPlatformPrice));
        }
        if (hasSmallOrderPrice) {
            smallOrderPriceDtoMap = variantDTO.getSmallOrderPrice().stream()
                    .collect(Collectors.toMap(ProductEditPriceDTO::getChildKey, ProductEditPriceDTO::getPlatformPrice));
        }

        Map<String, VariantProperty> variantPropertyMap = new HashMap<>();
        for (ProductEditChildrenDTO cDto : variantDTO.getChildren()) {
            String childKey = cDto.getChildKey();
            VariantProperty variantProperty = new VariantProperty();
            variantProperty.setExportHeight(cDto.getExportHeight());
            variantProperty.setExportWidth(cDto.getExportWidth());
            variantProperty.setExportLength(cDto.getExportLength());
            variantProperty.setChikdKey(childKey);
            variantPropertyMap.put(childKey, variantProperty);

            Product child = BeanUtil.copyProperties(cDto, Product.class);
            BigDecimal childMinPrice = ProductConstant.PRODUCT_MAX_PRICE;
            Color color = resultBO.getColorMap().get(cDto.getColorId());
            child.setColorName(color.getColorName()).setColorCode(color.getColorCode())
                    .setColorOpacity(color.getColorOpacity()).setColorSort(color.getColorSort());

            Size size = resultBO.getSizeMap().get(cDto.getSizeId());
            child.setSize(size.getSizeName()).setSizeSort(size.getSizeSort());
            childrenNum++;
            child.setParentId(parent.getId()).setParentSku(parent.getSku());
            child.setName(parent.getName()).setEnglishName(parent.getEnglishName());
            child.setCategoryId(parent.getCategoryId()).setTextureId(parent.getTextureId());
            String childSku = parent.getSku() + String.format("%03d", childrenNum);
            child.setSku(childSku);
            // 由于模板需要申请，所以默认下架
            child.setStatus(ProductConstant.STATUS_OFFLINE).setPublicStatus(parent.getPublicStatus());
            child.setUserId(variantDTO.getUid()).setCreatedTime(nowTime).setUpdateTime(nowTime);
            child.setMemberLevel(parent.getMemberLevel());
            // 同步分销
            child.setIsDistribution(parent.getIsDistribution())
                    .setDistributionAccumulatePriceLevel(parent.getDistributionAccumulatePriceLevel());
            child.setProductionCycle(parent.getProductionCycle());
            child.setSmallOrderProductionCycle(parent.getSmallOrderProductionCycle());
            child.setPrototypeType(parent.getPrototypeType());
            child.setCompressLength(cDto.getCompressLength());
            child.setCompressWidth(cDto.getCompressWidth());
            child.setCompressHeight(cDto.getCompressHeight());
            // 平台价格
            if (hasPrice) {
                List<LadderPriceDTO> platformPriceList = priceDtoMap.get(cDto.getChildKey());
                BigDecimal childOnePieceMinPrice = platformPriceList.stream()
                        .min(Comparator.comparing(LadderPriceDTO::getPrice)).get().getPrice();
                if (childOnePieceMinPrice.compareTo(minPlatformPrice) < 0) {
                    minPlatformPrice = childOnePieceMinPrice;
                }
                if (childOnePieceMinPrice.compareTo(childMinPrice) < 0) {
                    childMinPrice = childOnePieceMinPrice;
                }
                child.setPrice(JSON.toJSONString(platformPriceList));
            }
            //小单平台价
            if (hasSmallOrderPrice) {
                List<LadderPriceDTO> platformPriceList = smallOrderPriceDtoMap.get(cDto.getChildKey());
                BigDecimal childSmallOrderMinPrice = platformPriceList.stream()
                        .min(Comparator.comparing(LadderPriceDTO::getPrice)).get().getPrice();
                if (childSmallOrderMinPrice.compareTo(minPlatformPrice) < 0) {
                    minPlatformPrice = childSmallOrderMinPrice;
                }
                if (childSmallOrderMinPrice.compareTo(childMinPrice) < 0) {
                    childMinPrice = childSmallOrderMinPrice;
                }
                child.setSmallOrderPrice(JSON.toJSONString(platformPriceList));
            }
            child.setTenantId(parent.getTenantId());
            child.setMinPrice(childMinPrice.compareTo(ProductConstant.PRODUCT_MAX_PRICE) < 0 ? childMinPrice : BigDecimal.ZERO);
            if (!TenantCommonConstant.isSdsdiy(parent.getTenantId())) {
                child.setRealWeight(null);
                child.setPackRealWeight(null);
            }
            //变体最小重量
            if (minWeight > child.getWeight()) {
                minWeight = child.getWeight();
            }
            child.setPrototypeType(parent.getPrototypeType());
            children.add(child);
            childrenDtoMap.put(cDto.getChildKey(), cDto);


        }
        saveBatch(children);

        List<VariantProperty> savedVariantProperties = new ArrayList<>();
        for (Product child : children) {
            String childKey = child.getChildKey();
            VariantProperty variantProperty = variantPropertyMap.get(childKey);
            if (variantProperty != null) {
                variantProperty.setVariantId(child.getId());
                variantProperty.setProductId(child.getParentId());
                variantProperty.setCreateUid(child.getUserId());
                savedVariantProperties.add(variantProperty);
            }
        }
        variantPropertyMapperManager.saveBatch(savedVariantProperties);

        if (((hasPrice || hasSmallOrderPrice) && minPlatformPrice.compareTo(parent.getMinPrice()) < 0) || minWeight < parent.getMinWeight()) {
            log.info("变更母体价格");
            // 设置最低平台价、最小重量
            lambdaUpdate().eq(Product::getId, parent.getId())
                    .set(Product::getMinPrice, minPlatformPrice)
                    .set(Product::getMinWeight, minWeight).update();
        }
        // 海关申报
        adminNormalCustomsDeclarationService.createOrUpdate(parent.getId());
        Map<String, Long> childrenIdMap = new HashMap<>(children.size());
        // 变体id回填dto
        children.forEach(c -> {
            String key = c.getColorId() + "&&" + c.getSizeId();
            childrenIdMap.put(key, c.getId());
            childrenDtoMap.get(key).setId(c.getId());
        });
        BaseListDto<Long> supplyIds = new BaseListDto<>(Lists.newArrayList());
        if (CollUtil.isNotEmpty(variantDTO.getSupply())) {
            // 供应关系
            variantDTO.getSupply().forEach(s -> {
                s.setId(childrenIdMap.get(s.getChildKey()));
                s.getFactory().forEach(productEditSupplyFactoryDTO -> {
                    productEditSupplyFactoryDTO.setSupplyChainType(ONE_PIECE.name());
                });
            });
            supplyIds = productSupplyService.addProductSupply(variantDTO.getSupply(), children, variantDTO.getUid());
        }
        if (CollUtil.isNotEmpty(variantDTO.getSmallOrderSupply())) {
            // 小单供应关系
            variantDTO.getSmallOrderSupply().forEach(s -> {
                s.setId(childrenIdMap.get(s.getChildKey()));
                s.getFactory().forEach(productEditSupplyFactoryDTO -> {
                    productEditSupplyFactoryDTO.setSupplyChainType(SupplyChainTypeEnum.SMALL_ORDER.name());
                });
            });
            BaseListDto<Long> smallOrderSupplyIds = productSupplyService.addProductSupply(variantDTO.getSmallOrderSupply(), children, variantDTO.getUid());
            supplyIds.getList().addAll(smallOrderSupplyIds.getList());
        }
        if (variantDTO.getPrototype() != null) {
            // 模板
            PrototypeRequireDto requireDto = BeanUtil.copyProperties(variantDTO.getPrototype(), PrototypeRequireDto.class);
            requireDto.setProductId(parent.getId());
            requireDto.setPrototypeType(variantDTO.getPrototype().getPrototypeType());
            requireDto.setProductIds(StrUtil.join(StrUtil.COMMA, childrenIdMap.values()));
            ArrayList<PrototypeRequireDto> requireDtos = new ArrayList<>();
            requireDtos.add(requireDto);
            prototypeRequireService.addBatch(requireDtos);
        }
        // 初始化汇总表
        productSummaryInfoService.initSummaryInfo(parent.getId());
        List<Long> productIds = new ArrayList<>(childrenIdMap.values());
        productIds.add(parent.getId());
        productSqsSender.sendProductSyncMsg(productIds);
        return supplyIds;
    }


    /**
     * 编辑母体
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateParent(ProductEditParentDTO parentDTO) {
        Product parent = BeanUtil.copyProperties(parentDTO, Product.class);
        if (Strings.isEmpty(parent.getDeclarationName())) {
            parent.setDeclarationName(parent.getName());
        }
        if (Strings.isEmpty(parent.getDeclarationEnglishName())) {
            parent.setDeclarationEnglishName(parent.getEnglishName());
        }
        if (parent.getMemberLevel() == null || parent.getMemberLevel().compareTo(BigDecimal.ZERO) <= 0) {
            parent.setMemberLevel(MemberLevelEnum.V0.value);
        }
        boolean switchPublic = parent.getPublicStatus() != null && ProductPublicStatus.OPEN == parent.getPublicStatus();
        boolean switchPrivate = parent.getPublicStatus() != null && ProductPublicStatus.CLOSE == parent.getPublicStatus();
        if (switchPublic) {
            // 每次切公开，更新这个时间，影响商户端排序
            parent.setSwitchTime(new Date());
        }
        this.updateById(parent);
        lambdaUpdate().eq(Product::getParentId, parent.getId())
                .set(Product::getName, parent.getName())
                .set(Product::getEnglishName, parent.getEnglishName())
                .set(Product::getCategoryId, parent.getCategoryId())
                .set(Product::getTextureId, parent.getTextureId())
                .set(Product::getMemberLevel, parent.getMemberLevel())
//                .set(Product::getProductionCycle, parent.getProductionCycle())
//                .set(parent.getSmallOrderProductionCycle() != null, Product::getSmallOrderProductionCycle, parent.getSmallOrderProductionCycle())
                .set(parent.getPublicStatus() != null, Product::getPublicStatus, parent.getPublicStatus())
                .set(parent.getSwitchTime() != null, Product::getSwitchTime, parent.getSwitchTime())
                .update();
        productSummaryInfoService.initSummaryInfo(parent.getId());
        productUpdateLogService.log(new ProductLogDTO(parentDTO)
                .setProductId(parent.getId()).setLogContent("修改母体信息:" + JSON.toJSONString(parent)));
        List<Long> productIds = lambdaQuery().select(Product::getId).eq(Product::getParentId, parentDTO.getId()).list()
                .stream().map(Product::getId).collect(Collectors.toList());
        productIds.add(parentDTO.getId());
        //更新纸箱信息
        productAvailableCartonService.updateAvailableCarton(parent.getParentId(), parentDTO.getProductAvailableCartons());
        //公开切私有，要把商户自己加入产品库的记录删除掉
        if (switchPrivate) {
            removeMerchantSelfAuth(parent.getId(), parentDTO.getTenantId());
        }
        productSqsSender.sendProductSyncMsg(productIds);
        if (switchPublic) {
            // 切公开，也算上架
            ProductIdReqDTO mqReqDTO = new ProductIdReqDTO(Collections.singletonList(parent.getId()));
            mqReqDTO.setUid(parentDTO.getUid());
            mqReqDTO.setTenantId(parent.getTenantId());
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PRODUCT, ProductTagConst.TAG_PRODUCT_ONLINE, mqReqDTO);
        }
    }

    private void removeMerchantSelfAuth(Long parentId, Long currentTenantId) {
        Set<Long> merchantIdSet = merchantProductParentService.getMerchantByType(parentId, 0L, MerchantProductParentConstant.TYPE_MERCHANT);
        if (CollUtil.isEmpty(merchantIdSet)) {
            return;
        }
        merchantAuthProductParentService.removeMerchantSelfAuth(parentId, Lists.newArrayList(merchantIdSet), currentTenantId);
    }

    /**
     * 更新变体信息
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateVariant(ProductUpdateVariantReqDTO reqDTO, Product parent) {
        long nowTime = System.currentTimeMillis();
        Double minWeight = 0D;
        List<Product> children = new ArrayList<>();
        for (ProductEditChildrenDTO cDto : reqDTO.getChildren()) {
            Product child = BeanUtil.copyProperties(cDto, Product.class);

            child.setUserId(reqDTO.getUid());
            child.setUpdateTime(nowTime);
            if (!TenantCommonConstant.isSdsdiy(reqDTO.getTenantId())) {
                child.setRealWeight(null);
                child.setPackRealWeight(null);
            }
            //最小重量
            if (minWeight == 0D || child.getWeight() < minWeight) {
                minWeight = child.getWeight();
            }
            children.add(child);
        }
        //更新最小重量
        lambdaUpdate().eq(Product::getId, parent.getId()).set(Product::getMinWeight, minWeight).update();

        this.updateBatchById(children);
        productSummaryInfoService.initSummaryInfo(reqDTO.getParentId());
        productUpdateLogService.log(new ProductLogDTO(reqDTO)
                .setProductId(reqDTO.getParentId()).setLogContent("修改变体信息"));

        List<VariantProperty> variantProperties = VariantPropertyService.generateVariantPropertyEntityList(reqDTO.getParentId(), reqDTO.getChildren(), reqDTO.getUid());
        variantPropertyMapperManager.edit(variantProperties);

        List<Long> productIds = children.stream().map(Product::getId).collect(Collectors.toList());
        productIds.add(reqDTO.getParentId());
        productSqsSender.sendProductSyncMsg(productIds);
    }

    public void updateDistributionAccumulatePriceLevel(Map<Integer, List<Long>> updateMap) {
        if (CollUtil.isEmpty(updateMap)) {
            return;
        }
        updateMap.forEach((level, parentIds) ->
                lambdaUpdate().ne(Product::getStatus, ProductConstant.STATUS_DELETE)
                        .and(a -> a.in(Product::getId, parentIds).or().in(Product::getParentId, parentIds))
                        .set(Product::getDistributionAccumulatePriceLevel, level)
                        .update()
        );
    }

    public void updatePrototypeType(Long productId, String prototypeType) {
        this.lambdaUpdate()
                .eq(Product::getId, productId)
                .or()
                .eq(Product::getParentId, productId)
                .set(Product::getPrototypeType, prototypeType)
                .update();
    }

    public void updatePrototypeScope(Long parentProductId, String prototypeScope) {
        this.lambdaUpdate()
                .eq(Product::getId, parentProductId)
                .set(Product::getPrototypScope, prototypeScope)
                .update();
    }

    /**
     * 设置询价条件
     *
     * @param parentProductId
     * @param productUpdateInquiryReqDto
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateInquiry(Long parentProductId, ProductUpdateInquiryReqDto productUpdateInquiryReqDto) {
        this.lambdaUpdate()
                .eq(Product::getId, parentProductId)
                .set(StrUtil.isNotBlank(productUpdateInquiryReqDto.getIsInquiry()), Product::getIsInquiry, productUpdateInquiryReqDto.getIsInquiry())
                .set(productUpdateInquiryReqDto.getInquiryThreshold() != null, Product::getInquiryThreshold, productUpdateInquiryReqDto.getInquiryThreshold())
                .update();
    }

    public void productDataStat() {
        //查询所有的母体产品列表
        List<Product> allParentList = productReadService.findAllParentIdAndSwitchTime();
        if (CollUtil.isEmpty(allParentList)) {
            return;
        }
        long now = DateUtil.beginOfDay(new Date()).getTime();
        Long before7Day = DateUtil.offsetDay(new Date(now), -7).getTime();
        Long before14Day = DateUtil.offsetDay(new Date(before7Day), -7).getTime();
        //查询时段内的母体产品数据统计信息
        Map<Long, ProductStaticDto> before7to14DayStatMap = merchantProductDayStaticFeign.mapStatAllProductByTime(before14Day, before7Day);
        Map<Long, ProductStaticDto> last7DayStatMap = merchantProductDayStaticFeign.mapStatAllProductByTime(before7Day, now);
        // 0对象防止空值
        ProductStaticDto zeroDTO = new ProductStaticDto();
        zeroDTO.setMerchantNum(0).setGoodsNum(0);

        List<Product> updateList = Lists.newArrayList();
        allParentList.forEach(i -> {
            Product update = new Product();
            update.setId(i.getId());
            //设置母体的销售商品增长数（前7天跟再前7天的商品销售数量相比的相对增长数）
            ProductStaticDto before7to14DayStatDto = before7to14DayStatMap.getOrDefault(i.getId(), zeroDTO);
            ProductStaticDto last7DayStatDto = last7DayStatMap.getOrDefault(i.getId(), zeroDTO);
            Integer hotValue = ProductUtil.hotValueCal(before7to14DayStatDto.getGoodsNum(), last7DayStatDto.getGoodsNum()
                    , last7DayStatDto.getMerchantNum(), i.getSwitchTime());
            update.setRelativeGrowthNum(hotValue);
            update.setMerchantNum(last7DayStatDto.getMerchantNum());
            updateList.add(update);
        });
        //更新全部母体产品
        this.updateBatchById(updateList);
    }

    @GlobalTransactional()
    public boolean onSaleAutoContinue() {
        Set<Long> parentIds = new HashSet<>();
        Set<Long> variantProductIds = new HashSet<>();
        productReadService.getOnSaleAutoContinueProducts().forEach(p -> {
            parentIds.add(p.getParentId());
            variantProductIds.add(p.getId());
        });
        //重置促销时间
        this.resetOnSaleTime(variantProductIds);
        return true;
    }

    private void resetOnSaleTime(Collection<Long> variantProductIds) {
        if (CollUtil.isEmpty(variantProductIds)) {
            return;
        }

        Date onSaleStartDate = DateUtil.beginOfDay(new Date());
        Date onSaleEndDate = DateUtil.offsetDay(onSaleStartDate, 30).offset(DateField.SECOND, -1);
        //更新产品的促销时间
        LambdaUpdateWrapper updateWrapper = Wrappers.<Product>lambdaUpdate()
                .set(Product::getOnSaleBeginTime, onSaleStartDate.getTime())
                .set(Product::getOnSaleEndTime, onSaleEndDate.getTime())
                .in(Product::getId, variantProductIds);
        this.update(updateWrapper);

        //更新产品供应价促销时间
        List<Long> productSupplyIdList = Lists.newArrayList();
        //查询要变更促销时间的产品供应价信息
        List<ProductSupply> productSupplyList = productSupplyService.findByProductIds(Lists.newArrayList(variantProductIds), null, ONE_PIECE.name());
        if (CollUtil.isNotEmpty(productSupplyList)) {
            productSupplyIdList = productSupplyList.stream().map(ProductSupply::getId).collect(Collectors.toList());
        }
        //根据产品供应价id列表更新供应价的促销时间
        productSupplySaleLadderPriceService.updateOnSaleTimeByProductSupplyIds(productSupplyIdList, onSaleStartDate, onSaleEndDate);
    }

    public void checkMultiParentVarinatSmallOrderNum(List<Long> variantIdList, Collection<Long> exSupplyIds) {
        if (CollUtil.isEmpty(variantIdList)) {
            return;
        }

        List<Product> productList = productReadService.findByIds(variantIdList);
        if (CollUtil.isEmpty(productList)) {
            return;
        }
        Map<Long, Product> productMap = productList.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
        //查询最新的平台价信息
        List<ProductEditPriceDTO> smallOrderPrice = generateProductEditPriceDTO(productList);

        //查询最新的小单供应关系(并去掉要过滤的供应关系)
        List<ProductEditSupplyDTO> smallOrderSupply = generateProductEditSupplyDTO(productList, exSupplyIds, SupplyChainTypeEnum.SMALL_ORDER.name());

        //根据变体id进行平台价与供应关系的匹配与校验
        List<Long> smallOrderNumErrorVariantIdList = checkVarinatSmallOrderNumWithVariantId(smallOrderSupply, smallOrderPrice);
        //报错
        if (CollUtil.isNotEmpty(smallOrderNumErrorVariantIdList)) {
            Set<String> productNameList = Sets.newHashSet();
            for (Long variantId : smallOrderNumErrorVariantIdList) {
                Product product = productMap.get(variantId);
                productNameList.add(product.getName());
            }
            throw new BusinessException("该工厂下架将导致产品下单异常，不可下架的产品：" + Joiner.on(",").join(productNameList));
        }
    }

    /**
     * 校验小单产能线平台价与供应关系的第一档数量是否满足(仅限单个母体下的多个变体)
     *
     * @param smallOrderSupply
     * @param smallOrderPrice
     * @param tenantId
     */
    public void checkOneParentVarinatSmallOrderNum(List<ProductEditSupplyDTO> smallOrderSupply, List<ProductEditPriceDTO> smallOrderPrice, Long tenantId) {
        if (CollUtil.isEmpty(smallOrderSupply) && CollUtil.isEmpty(smallOrderPrice)) {
            return;
        }
        //可能校验时仅有小单供应关系或者小单平台价传进来，这时要根据变体id把缺失的供应关系或平台价查询出来，进行数量比较
        List<Long> variantIdList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(smallOrderPrice)) {
            variantIdList = smallOrderPrice.stream().map(ProductEditPriceDTO::getId).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(smallOrderSupply) && CollUtil.isEmpty(variantIdList)) {
            variantIdList = smallOrderSupply.stream().map(ProductEditSupplyDTO::getId).collect(Collectors.toList());
        }
        List<Product> productList = productReadService.findByIds(variantIdList);
        if (CollUtil.isEmpty(productList)) {
            return;
        }
        Map<Long, Product> productMap = productList.stream().collect(Collectors.toMap(Product::getId, Function.identity()));

        //根据变体id查询出他们的颜色与尺码信息,后面报错信息里要用
        Set<Long> colorIds = new HashSet<>();
        Set<Long> sizeIds = new HashSet<>();
        productList.forEach(product -> {
            colorIds.add(product.getColorId());
            sizeIds.add(product.getSizeId());
        });
        List<Color> colors = colorService.findByIds(colorIds, tenantId);
        Assert.validateBool(colorIds.size() == colors.size(), "颜色不存在");
        List<Size> sizes = sizeService.findByIds(sizeIds, tenantId);
        Assert.validateBool(sizeIds.size() == sizes.size(), "尺寸不存在");
        Map<Long, Color> colorMap = colors.stream().collect(Collectors.toMap(BasePO::getId, c -> c));
        Map<Long, Size> sizeMap = sizes.stream().collect(Collectors.toMap(BasePO::getId, s -> s));

        //补全要校验的小单平台价或小单供应价信息
        if (CollUtil.isEmpty(smallOrderPrice)) {
            //小单平台价信息为空则查询最新的平台价信息
            smallOrderPrice = generateProductEditPriceDTO(productList);
        } else {
            //小单平台价信息不为空则补全产品的颜色尺码信息
            smallOrderPrice.forEach(price -> {
                Product product = productMap.get(price.getId());
                price.setColorId(product.getColorId());
                price.setSizeId(product.getSizeId());
            });
        }
        if (CollUtil.isEmpty(smallOrderSupply)) {
            //小单供应关系为空则查询最新的小单供应关系
            smallOrderSupply = generateProductEditSupplyDTO(productList, Lists.newArrayList(), SupplyChainTypeEnum.SMALL_ORDER.name());
        } else {
            //小单供应关系不为空则补全产品的颜色尺码信息
            smallOrderSupply.forEach(supply -> {
                Product product = productMap.get(supply.getId());
                supply.setColorId(product.getColorId());
                supply.setSizeId(product.getSizeId());
            });
        }

        //根据颜色、尺码进行平台价与供应关系的匹配，并报错
        checkVarinatSmallOrderNumWithColorAndSize(smallOrderSupply, smallOrderPrice, colorMap, sizeMap);
    }

    private List<ProductEditPriceDTO> generateProductEditPriceDTO(List<Product> productList) {
        List<ProductEditPriceDTO> productEditPriceDTOList = Lists.newArrayList();
        productList.forEach(product -> {
            if (StrUtil.isBlank(product.getSmallOrderPrice())) {
                return;
            }
            ProductEditPriceDTO productEditPriceDTO = new ProductEditPriceDTO();
            productEditPriceDTO.setId(product.getId());
            productEditPriceDTO.setColorId(product.getColorId());
            productEditPriceDTO.setSizeId(product.getSizeId());
            productEditPriceDTO.setPlatformPrice(JSONObject.parseArray(product.getSmallOrderPrice(), LadderPriceDTO.class));
            productEditPriceDTOList.add(productEditPriceDTO);
        });
        return productEditPriceDTOList;
    }

    private List<ProductEditSupplyDTO> generateProductEditSupplyDTO(List<Product> productList, Collection<Long> exSupplyIds, String supplyChainType) {
        List<ProductEditSupplyDTO> productEditSupplyDTOList = Lists.newArrayList();
        List<Long> productIdList = productList.stream().map(Product::getId).collect(Collectors.toList());
        List<ProductSupply> productSupplyList = productSupplyService.findByProductIds(productIdList, EnumProductSupplyStatus.ONLINE.status, supplyChainType);
        if (CollUtil.isEmpty(productSupplyList)) {
            return productEditSupplyDTOList;
        }
        //过滤掉要除外的供应关系id
        productSupplyList = productSupplyList.stream().filter(productSupply -> !exSupplyIds.contains(productSupply.getId())).collect(Collectors.toList());

        Map<Long, List<ProductSupply>> productSupplyMap = productSupplyList.stream().collect(Collectors.groupingBy(ProductSupply::getProductId));
        //查询供应价
        Map<Long, List<LadderPriceDTO>> productSupplyPriceMap = productSupplyLadderPriceService.mapByProductSupplyIds(
                productSupplyList.stream().map(ProductSupply::getId).collect(Collectors.toList()));

        productList.forEach(product -> {
            List<ProductSupply> variantProductSupplyList = productSupplyMap.get(product.getId());
            if (CollUtil.isEmpty(variantProductSupplyList)) {
                return;
            }
            //给供应关系塞入供应价
            List<ProductEditSupplyDTO.ProductEditSupplyFactoryDTO> productEditSupplyFactoryDTOList = BeanUtil.copyToList(variantProductSupplyList,
                    ProductEditSupplyDTO.ProductEditSupplyFactoryDTO.class);
            productEditSupplyFactoryDTOList.forEach(productEditSupplyFactoryDTO -> {
                productEditSupplyFactoryDTO.setLadderPrice(productSupplyPriceMap.get(productEditSupplyFactoryDTO.getId()));
            });
            ProductEditSupplyDTO productEditSupplyDTO = new ProductEditSupplyDTO();
            productEditSupplyDTO.setId(product.getId());
            productEditSupplyDTO.setColorId(product.getColorId());
            productEditSupplyDTO.setSizeId(product.getSizeId());
            productEditSupplyDTO.setFactory(productEditSupplyFactoryDTOList);
            productEditSupplyDTOList.add(productEditSupplyDTO);
        });
        return productEditSupplyDTOList;
    }

    /**
     * 校验小单产能线平台价与供应关系的第一档数量是否满足
     * 因为是通过颜色与尺码进行匹配，所以仅限单个母体下的校验
     *
     * @param smallOrderSupply
     * @param smallOrderPrice
     * @param colorMap
     * @param sizeMap
     */
    private void checkVarinatSmallOrderNumWithColorAndSize(List<ProductEditSupplyDTO> smallOrderSupply, List<ProductEditPriceDTO> smallOrderPrice,
                                                           Map<Long, Color> colorMap, Map<Long, Size> sizeMap) {
        ProductEditPriceDTO productEditPriceDTO = this.checkVarinatSmallOrderNumWithColorAndSize(smallOrderSupply, smallOrderPrice);
        if (productEditPriceDTO != null) {
            Color color = colorMap.get(productEditPriceDTO.getColorId());
            Size size = sizeMap.get(productEditPriceDTO.getSizeId());
            throw new BusinessException(String.format("变体：%s %s 平台价第一档数量低于工厂的第一档数量", color.getColorName(), size.getSizeName()));
        }
    }

    private ProductEditPriceDTO checkVarinatSmallOrderNumWithColorAndSize(List<ProductEditSupplyDTO> productEditSupplyDTOList,
                                                                          List<ProductEditPriceDTO> productEditPriceDTOList) {
        if (CollUtil.isEmpty(productEditSupplyDTOList) || CollUtil.isEmpty(productEditPriceDTOList)) {
            // 小单供应关系或者小单平台价格有一个为空就不用校验数量
            return null;
        }
        //变体colorId&&sizeId-供应关系第一挡最小数量Map,因为新增的变体这时可能还没有id
        Map<String, Integer> variantSupplyMinNumMap = Maps.newHashMap();

        for (ProductEditSupplyDTO variantSupplyDTO : productEditSupplyDTOList) {
            List<ProductEditSupplyDTO.ProductEditSupplyFactoryDTO> factorySupplyList = variantSupplyDTO.getFactory();
            if (CollUtil.isEmpty(factorySupplyList)) {
                continue;
            }
            factorySupplyList.forEach(factorySupply -> {
                //产能为0或下架状态的供应关系不算
                if (factorySupply.getDistributionPercent() <= 0 || !EnumProductSupplyStatus.ONLINE.status.equals(factorySupply.getStatus())) {
                    return;
                }
                List<LadderPriceDTO> ladderPriceDtoList = factorySupply.getLadderPrice();
                String key = variantSupplyDTO.getColorId() + "&&" + variantSupplyDTO.getSizeId();
                //取出当前变体与工厂的供应关系中第一档的个数
                Integer minNum = ProductPriceUtil.getAccumulateNumByLevel(ladderPriceDtoList, ProductPriceUtil.NUM_MIN_0);
                //与当前变体与其他工厂的供应关系中第一档个数最小做对比
                if (variantSupplyMinNumMap.containsKey(key)) {
                    Integer oldMinNum = variantSupplyMinNumMap.get(key);
                    if (minNum < oldMinNum) {
                        variantSupplyMinNumMap.replace(key, minNum);
                    }
                } else {
                    variantSupplyMinNumMap.put(key, minNum);
                }
            });
        }
        //校验平台价第一档数量是否大于变体的供应关系中第一档个数最小
        for (ProductEditPriceDTO price : productEditPriceDTOList) {
            Integer supplyMinNum = variantSupplyMinNumMap.get(price.getColorId() + "&&" + price.getSizeId());
            //可能此变体的供应关系还未配置或没有上架状态的供应关系，不进行校验
            if (supplyMinNum == null) {
                continue;
            }
            Integer priceMinNum = ProductPriceUtil.getAccumulateNumByLevel(price.getPlatformPrice(), ProductPriceUtil.NUM_MIN_0);
            if (priceMinNum < supplyMinNum) {
                return price;
            }
        }

        return null;
    }

    private List<Long> checkVarinatSmallOrderNumWithVariantId(List<ProductEditSupplyDTO> productEditSupplyDTOList,
                                                              List<ProductEditPriceDTO> productEditPriceDTOList) {
        if (CollUtil.isEmpty(productEditSupplyDTOList) || CollUtil.isEmpty(productEditPriceDTOList)) {
            // 小单供应关系或者小单平台价格有一个为空就不用校验数量
            return Lists.newArrayList();
        }
        //变体id-供应关系第一挡最小数量Map
        Map<Long, Integer> variantSupplyMinNumMap = Maps.newHashMap();
        List<Long> smallOrderNumErrorVariantIdList = Lists.newArrayList();

        for (ProductEditSupplyDTO variantSupplyDTO : productEditSupplyDTOList) {
            List<ProductEditSupplyDTO.ProductEditSupplyFactoryDTO> factorySupplyList = variantSupplyDTO.getFactory();
            if (CollUtil.isEmpty(factorySupplyList)) {
                continue;
            }
            factorySupplyList.forEach(factorySupply -> {
                //产能为0或下架状态的供应关系不算
                if (factorySupply.getDistributionPercent() <= 0 || !EnumProductSupplyStatus.ONLINE.status.equals(factorySupply.getStatus())) {
                    return;
                }
                List<LadderPriceDTO> ladderPriceDtoList = factorySupply.getLadderPrice();
                Long key = variantSupplyDTO.getId();
                //取出当前变体与工厂的供应关系中第一档的个数
                Integer minNum = ProductPriceUtil.getAccumulateNumByLevel(ladderPriceDtoList, ProductPriceUtil.NUM_MIN_0);
                //与当前变体与其他工厂的供应关系中第一档个数最小做对比
                if (variantSupplyMinNumMap.containsKey(key)) {
                    Integer oldMinNum = variantSupplyMinNumMap.get(key);
                    if (minNum < oldMinNum) {
                        variantSupplyMinNumMap.replace(key, minNum);
                    }
                } else {
                    variantSupplyMinNumMap.put(key, minNum);
                }
            });
        }
        //校验平台价第一档数量是否大于变体的供应关系中第一档个数最小
        for (ProductEditPriceDTO price : productEditPriceDTOList) {
            Integer supplyMinNum = variantSupplyMinNumMap.get(price.getId());
            //可能此变体的供应关系还未配置或没有上架状态的供应关系，不进行校验
            if (supplyMinNum == null) {
                continue;
            }
            Integer priceMinNum = ProductPriceUtil.getAccumulateNumByLevel(price.getPlatformPrice(), ProductPriceUtil.NUM_MIN_0);
            if (priceMinNum < supplyMinNum) {
                smallOrderNumErrorVariantIdList.add(price.getId());
            }
        }

        return smallOrderNumErrorVariantIdList;
    }

    public void updateAmazonCustom(List<Long> ids, Integer amazonCustom) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        lambdaUpdate().set(Product::getAmazonCustom, amazonCustom)
                .in(Product::getId, ids)
                .update();
    }
}
