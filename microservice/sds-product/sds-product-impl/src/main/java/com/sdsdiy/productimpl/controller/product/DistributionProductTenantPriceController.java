package com.sdsdiy.productimpl.controller.product;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.status.ProductPublicStatus;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.enums.ApplicationCodeEnum;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.productapi.api.product.DistributionProductTenantPriceApi;
import com.sdsdiy.productdata.constants.ProductConstant;
import com.sdsdiy.productdata.dto.price.*;
import com.sdsdiy.productdata.dto.product.ProductIdCheckDTO;
import com.sdsdiy.productdata.dto.product.edit.ProductUpdateOnSalePriceReqDTO;
import com.sdsdiy.productdata.dto.product.edit.ProductUpdateOnSalePriceRespDTO;
import com.sdsdiy.productimpl.entity.po.Product;
import com.sdsdiy.productimpl.entity.po.distribution.DistributionProductTenantPrice;
import com.sdsdiy.productimpl.feign.ApplicationFeign;
import com.sdsdiy.productimpl.feign.TenantFeign;
import com.sdsdiy.productimpl.feign.TenantWalletFeign;
import com.sdsdiy.productimpl.service.distribution.DistributionProductTenantPriceService;
import com.sdsdiy.productimpl.service.distribution.ProductDistributionService;
import com.sdsdiy.productimpl.service.product.ProductPriceV2Service;
import com.sdsdiy.productimpl.service.product.ProductReadService;
import com.sdsdiy.productimpl.util.ProductPriceUtil;
import com.sdsdiy.userapi.dto.tenant.TenantRespDto;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 分销产品租户设置的价格 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021/11/18
 */
@RestController
@RequiredArgsConstructor
public class DistributionProductTenantPriceController implements DistributionProductTenantPriceApi {
    private final TenantFeign tenantFeign;
    @Autowired
    private DistributionProductTenantPriceService distributionProductTenantPriceService;
    @Autowired
    private ProductReadService productReadService;
    @Autowired
    private TenantWalletFeign tenantWalletFeign;
    @Autowired
    private ProductPriceV2Service productPriceV2Service;
    @Autowired
    private ApplicationFeign applicationFeign;
    @Autowired
    private ProductDistributionService productDistributionService;

    @Override
    public BaseListDto<ProductVariantPlatformPriceDTO> variantTenantPlatformPrice(ProductPlatformPriceReqDTO reqDto) {
        List<Product> variantList = productReadService.findOnePieceOnlineVariantByParentId(reqDto.getId(), null);
        if (CollUtil.isEmpty(variantList)) {
            return BaseListDto.of(Collections.emptyList());
        }
        List<Long> variantIds = variantList.stream().map(Product::getId).collect(Collectors.toList());
        // 租户平台价
        Map<Long, List<LadderPriceDTO>> platformPriceMap = distributionProductTenantPriceService.mapPlatformPriceByParentId(reqDto.getId(), reqDto.getTenantId());
        Boolean openDistributionApp = applicationFeign.checkApplicationIsOpenByCode(reqDto.getTenantId(), ApplicationCodeEnum.DISTRIBUTE_EXTERNAL_PRODUCT.getCode());
        // 分销平台价
        Map<Long, List<LadderPriceDTO>> distPriceMap = openDistributionApp ?
                productDistributionService.mapDistributionPlatformPrice(variantIds) : Collections.emptyMap();
        List<ProductVariantPlatformPriceDTO> dtoList = variantList.stream().map(variant -> {
            ProductVariantPlatformPriceDTO priceDTO = BeanUtil.copyProperties(variant, ProductVariantPlatformPriceDTO.class);
            priceDTO.setPlatformPrice(platformPriceMap.getOrDefault(variant.getId(), Collections.emptyList()));
            // 开了分销应用是分销平台价，没开是sds平台价
            priceDTO.setSupplyPrice(distPriceMap.getOrDefault(variant.getId(), ProductPriceUtil.platformPriceStr2List(variant.getPrice())));
            return priceDTO;
        }).collect(Collectors.toList());
        if (BasePoConstant.YES.equals(reqDto.getWithSupplyFactory())) {

            Long productTenantId = variantList.get(0).getTenantId();
            String productTenantName = tenantFeign.getNameMapByIds(Collections.singletonList(productTenantId)).get(productTenantId);

            Map<Long, TenantPurchasePriceRespDTO> purchasePriceMap = productPriceV2Service.currentVariantTenantPurchasePrice(reqDto.getTenantId(), variantIds)
                    .stream().collect(Collectors.toMap(TenantPurchasePriceRespDTO::getProductId, i -> i));
            dtoList.forEach(variant -> {
                ProductFactorySupplyPriceDTO supplyPriceList = new ProductFactorySupplyPriceDTO();
                supplyPriceList.setName(productTenantName);
                variant.setSupplyFactory(Collections.singletonList(supplyPriceList));
                variant.setMaxOneLevelSupplyPrice(BigDecimal.ZERO);
                TenantPurchasePriceRespDTO purchasePrice = purchasePriceMap.get(variant.getId());
                if (purchasePrice != null) {
                    supplyPriceList.setMinPrice(purchasePrice.getMin());
                    supplyPriceList.setMaxPrice(purchasePrice.getMax());
                    variant.setMaxOneLevelSupplyPrice(purchasePrice.getMin());
                }
            });
        }
        return new BaseListDto<>(dtoList);
    }

    @Override
    public void updateVariantTenantPlatformPrice(ProductPlatformPriceEditDTO editDTO) {
        Assert.validateBool(tenantWalletFeign.isOnlineOpen(editDTO.getTenantId()),
                "未开启线上收款，无法设置分销产品的平台价");
        Product product = productReadService.findByIdUnDelete(editDTO.getParentId(), null);
        Assert.validateNull(product, "产品已被删除");
        Assert.validateBool(ProductConstant.STATUS_ONLINE.equals(product.getStatus()), "产品已下架");
        editDTO.setProductTenantId(product.getTenantId());
        distributionProductTenantPriceService.updateTenantPlatformPrice(editDTO);
    }

    @Override
    public ProductUpdateOnSalePriceRespDTO variantTenantOnSalePrice(ProductIdCheckDTO reqDTO) {
        // 变体
        List<Product> variantList = productReadService.findOnePieceOnlineVariantByParentId(reqDTO.getId(), null);
        Map<Long, DistributionProductTenantPrice> tenantPriceMap = distributionProductTenantPriceService.mapTenantPriceByParentId(reqDTO.getId(), reqDTO.getTenantId());
        List<Long> variantIds = new ArrayList<>();
        variantList.forEach(v -> {
            DistributionProductTenantPrice tenantPrice = tenantPriceMap.get(v.getId());
            if (reqDTO.getIsCheck()) {
                Assert.validateTrue(tenantPrice == null || StrUtil.isBlank(tenantPrice.getPlatformPrice()), "请先设置平台价");
            }
            variantIds.add(v.getId());
        });
        ProductUpdateOnSalePriceRespDTO respDTO = new ProductUpdateOnSalePriceRespDTO();
        if (CollUtil.isEmpty(variantList)) {
            return respDTO;
        }
        Long productTenantId = variantList.get(0).getTenantId();
        String productTenantName = tenantFeign.getNameMapByIds(Collections.singletonList(productTenantId)).get(productTenantId);
        // 供应价
        Map<Long, TenantPurchasePriceRespDTO> purchasePriceMap = productPriceV2Service.currentVariantTenantPurchasePrice(reqDTO.getTenantId(), variantIds)
                .stream().collect(Collectors.toMap(TenantPurchasePriceRespDTO::getProductId, i -> i));
        List<ProductUpdateOnSalePriceRespDTO.VariantOnSalePriceDTO> list = new ArrayList<>();
        variantList.forEach(variant -> {
            ProductUpdateOnSalePriceRespDTO.VariantOnSalePriceDTO dto = BeanUtil.copyProperties(variant, ProductUpdateOnSalePriceRespDTO.VariantOnSalePriceDTO.class);
            ProductUpdateOnSalePriceRespDTO.VariantOnSalePriceDTO.SupplyFactoryDTO supplyFactory = new ProductUpdateOnSalePriceRespDTO.VariantOnSalePriceDTO.SupplyFactoryDTO();
            // 供货价
            TenantPurchasePriceRespDTO purchasePrice = purchasePriceMap.get(variant.getId());
            if (purchasePrice != null) {
                supplyFactory.setMinPrice(purchasePrice.getMin());
                supplyFactory.setMaxPrice(purchasePrice.getMax());
                dto.setMaxSupplyPrice(purchasePrice.getMax());
            } else {
                dto.setMaxSupplyPrice(BigDecimal.ZERO);
            }
            supplyFactory.setName(productTenantName);
            dto.setSupplyFactory(Collections.singletonList(supplyFactory));
            // 促销价
            DistributionProductTenantPrice tenantPrice = tenantPriceMap.get(variant.getId());
            if (tenantPrice != null) {
                dto.setOnSalePrice(tenantPrice.getOnSalePrice())
                        .setLadderPrices(ProductPriceUtil.ladderPriceStr2List(tenantPrice.getPromotionLadderPrice()))
                        .setOnSaleStatus(tenantPrice.getOnSaleStatus())
                        .setOnSaleBeginTime(tenantPrice.getOnSaleBeginTime())
                        .setOnSaleEndTime(tenantPrice.getOnSaleEndTime());
            } else {
                dto.setOnSalePrice(BigDecimal.ZERO)
                        .setLadderPrices(Lists.newArrayList())
                        .setOnSaleStatus(ProductConstant.ON_SALE_STATUS_CLOSE)
                        .setOnSaleBeginDate("").setOnSaleEndDate("");
            }
            list.add(dto);
        });
        respDTO.setList(list);
        return respDTO;
    }

    @Override
    public void updateVariantTenantOnSalePrice(ProductUpdateOnSalePriceReqDTO reqDto) {
        Assert.validateBool(tenantWalletFeign.isOnlineOpen(reqDto.getTenantId()),
                "未开启线上收款，无法设置分销产品的促销价");
        Product product = productReadService.findByIdUnDelete(reqDto.getParentId(), null);
        Assert.validateNull(product, "产品已被删除");
        Assert.validateBool(ProductConstant.STATUS_ONLINE.equals(product.getStatus()), "产品已下架");
        reqDto.setProductTenantId(product.getTenantId());
        distributionProductTenantPriceService.updateTenantOnSalePrice(reqDto);
    }
}

