package com.sdsdiy.productimpl.mapper.auth;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sdsdiy.common.base.entity.dto.SqlLongCount;
import com.sdsdiy.core.base.util.SimpleSelectInLangDriver;
import com.sdsdiy.productdata.dto.auth.*;
import com.sdsdiy.productimpl.entity.po.auth.MerchantAuthProductParent;
import org.apache.ibatis.annotations.Lang;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商户被授权产品母体表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021/11/02
 */
public interface MerchantAuthProductParentMapper extends BaseMapper<MerchantAuthProductParent> {
    /**
     * 已授权产品列表
     *
     * @param page   分页
     * @param reqDTO 条件
     * @return 列表
     */
    IPage<MerchantAuthProductParentRespDTO> pageMerchantAuthProductParent(Page<?> page, @Param("reqDTO") MerchantAuthPageReqDTO reqDTO);

    /**
     * 已授权母体id
     * 区分公开、私有产品
     */
    List<Long> listAuthParentIdByPublicStatus(@Param("reqDTO") MerchantAuthPageReqDTO reqDTO);

    /**
     * 未授权母体id
     */
    List<Long> listUnAuthParentIdByPublicStatus(@Param("reqDTO") MerchantAuthPageReqDTO reqDTO);

    /**
     * 已授权产品数、授权指定工厂生产数
     */
    List<AuthProductDataCountDTO> countAuthProductNumAndAuthFactoryNum(@Param("reqDTO") AuthProductDataReqDTO reqDTO);

    /**
     * 授权累计价档位数
     */
    List<AuthProductDataCountDTO> countAccumulateLevelNum(@Param("reqDTO") AuthProductDataReqDTO reqDTO);

    /**
     * 授权专属价格数
     */
    List<AuthProductDataCountDTO> countAuthPriceNum(@Param("reqDTO") AuthProductDataReqDTO reqDTO);

    /**
     * 母体授权数量统计
     */
    @Select("SELECT product_parent_id, count( id ) num FROM sds_mc_product.merchant_auth_product_parent " +
            " WHERE is_delete = 0 AND product_parent_id in(#{parentIds}) " +
            " AND merchant_tenant_id = #{merchantTenantId} GROUP BY product_parent_id ")
    @Lang(SimpleSelectInLangDriver.class)
    List<AuthProductCountDTO> countParentAuthMerchantNum(@Param("parentIds") Collection<Long> parentIds, @Param("merchantTenantId") Long merchantTenantId);

    @Select("SELECT factory_id as `key`, count( DISTINCT merchant_id ) as qty FROM sds_mc_product.merchant_auth_product_parent " +
            " WHERE is_delete = 0 AND factory_id in (#{factoryIds}) " +
            " GROUP BY factory_id ")
    @Lang(SimpleSelectInLangDriver.class)
    List<SqlLongCount> getFactoryMerchantAuthParenNums(@Param("factoryIds") List<Long> factoryIds);

    Long checkPrivateProductAuth(@Param("merchantId") Long merchantId);
    Long countPrivateProductAuth(@Param("merchantId") Long merchantId);
}
