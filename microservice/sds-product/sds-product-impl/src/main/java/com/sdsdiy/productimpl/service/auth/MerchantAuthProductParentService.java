package com.sdsdiy.productimpl.service.auth;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.status.ProductPublicStatus;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.*;
import com.sdsdiy.common.base.enums.SdsPlatformEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.core.base.service.BaseServiceImpl;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.product.ProductTagConst;
import com.sdsdiy.core.redis.lock.LockUtil;
import com.sdsdiy.core.redis.util.RedisUtil;
import com.sdsdiy.productapi.constant.MerchantProductParentConstant;
import com.sdsdiy.productapi.dto.MerchantProductParentReqDto;
import com.sdsdiy.productapi.param.MerchantProductLibraryDeclarationStatusParam;
import com.sdsdiy.productdata.constants.ProductConstant;
import com.sdsdiy.productdata.constants.ProductEnumConstant;
import com.sdsdiy.productdata.dto.auth.*;
import com.sdsdiy.productdata.dto.merchantproductparent.MerchantProductParentBatchAddResp;
import com.sdsdiy.productdata.dto.price.LadderPriceDTO;
import com.sdsdiy.productdata.dto.product.ProductIdReqDTO;
import com.sdsdiy.productdata.dto.product.SmallOrderPriceDto;
import com.sdsdiy.productdata.enums.MerchantAuthType;
import com.sdsdiy.productdata.enums.supply.SupplyChainTypeEnum;
import com.sdsdiy.productimpl.entity.po.AuthAllAccumulatePriceRecord;
import com.sdsdiy.productimpl.entity.po.Product;
import com.sdsdiy.productimpl.entity.po.ProductSupply;
import com.sdsdiy.productimpl.entity.po.auth.MerchantAuthProductParent;
import com.sdsdiy.productimpl.entity.po.auth.TenantAuthProductParent;
import com.sdsdiy.productimpl.feign.MerchantFeign;
import com.sdsdiy.productimpl.feign.TenantFeign;
import com.sdsdiy.productimpl.feign.user.MerchantSysUserProductPermissionFeign;
import com.sdsdiy.productimpl.manager.mapper.AuthAccumulatePriceLevelMapperManager;
import com.sdsdiy.productimpl.manager.mapper.AuthAllAccumulatePriceRecordMapperManager;
import com.sdsdiy.productimpl.manager.mapper.AuthOnePriceMapperManager;
import com.sdsdiy.productimpl.mapper.auth.MerchantAuthProductParentMapper;
import com.sdsdiy.productimpl.service.CategoryService;
import com.sdsdiy.productimpl.service.MerchantProductParentService;
import com.sdsdiy.productimpl.service.ProductMerchantPriceService;
import com.sdsdiy.productimpl.service.ProductSupplyService;
import com.sdsdiy.productimpl.service.authPrice.AccumulatePriceService;
import com.sdsdiy.productimpl.service.customsDeclaration.MerchantProductLibraryDeclarationStatusService;
import com.sdsdiy.productimpl.service.product.ProductReadService;
import com.sdsdiy.productimpl.service.product.ProductSummaryInfoService;
import com.sdsdiy.productimpl.service.product.copy.ProductCopyRecordService;
import com.sdsdiy.productimpl.service.supply.ProductSupplyLadderPriceService;
import com.sdsdiy.userapi.constant.ProductLibraryTypeEnum;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.MerchantSimpleDto;
import com.sdsdiy.userapi.dto.MerchantSysUserProductPermissionRespDto;
import com.sdsdiy.userapi.param.ProductPermissionIdsParam;
import com.sdsdiy.userdata.dto.merchant.MerchantPageRespDTO;
import com.sdsdiy.userdata.enums.MemberLevelEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.productdata.enums.MerchantAuthType.CROSS_TENANT;

/**
 * <p>
 * 商户被授权产品母体表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021/11/02
 */
@Service
@Log4j2
@DS("common")
@RequiredArgsConstructor
public class MerchantAuthProductParentService extends BaseServiceImpl<MerchantAuthProductParentMapper, MerchantAuthProductParent> {
    private final RocketMQTemplate rocketMQTemplate;
    private final LockUtil lockUtil;
    @Autowired
    private CategoryService categoryService;
    @Autowired
    private TenantAuthProductParentService tenantAuthProductParentService;
    @Autowired
    private ProductReadService productReadService;
    @Autowired
    private MerchantFeign merchantFeign;
    @Autowired
    private AuthOnePriceMapperManager authOnePriceMapperManager;
    @Autowired
    private AuthSupplyLadderPriceService authSupplyLadderPriceService;
    @Autowired
    private AuthAllAccumulatePriceRecordMapperManager authAllAccumulatePriceRecordMapperManager;
    @Autowired
    private AuthAccumulatePriceLevelMapperManager authAccumulatePriceLevelMapperManager;
    @Autowired
    @Lazy
    private MerchantProductParentService merchantProductParentService;
    @Autowired
    private ProductMerchantPriceService productMerchantPriceService;
    @Autowired
    private ProductSummaryInfoService productSummaryInfoService;
    @Autowired
    @Lazy
    private MerchantProductLibraryDeclarationStatusService merchantProductLibraryDeclarationStatusService;
    @Autowired
    private AccumulatePriceService accumulatePriceService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private MerchantSysUserProductPermissionFeign merchantSysUserProductPermissionFeign;
    @Resource
    private ProductSupplyService productSupplyService;
    @Resource
    private ProductSupplyLadderPriceService productSupplyLadderPriceService;
    @Resource
    private ProductCopyRecordService productCopyRecordService;

    private Set<String> authedKeySet(Collection<Long> merchantIds, Collection<Long> parentIds) {
        if (CollUtil.isEmpty(merchantIds) || CollUtil.isEmpty(parentIds)) {
            return Collections.emptySet();
        }
        // 授权关系
        List<MerchantAuthProductParent> authList = this.lambdaQuery()
                .select(MerchantAuthProductParent::getMerchantId, MerchantAuthProductParent::getProductParentId)
                .in(MerchantAuthProductParent::getMerchantId, merchantIds)
                .in(MerchantAuthProductParent::getProductParentId, parentIds).list();
        return authList.stream().map(a -> (a.getMerchantId() + "&&" + a.getProductParentId()))
                .collect(Collectors.toSet());
    }


    @Transactional(rollbackFor = Exception.class)
    public void authCrossTenantProduct(Long currentTenantId, List<Long> merchantIds, List<Product> parentList) {
        if (CollUtil.isEmpty(merchantIds) || CollUtil.isEmpty(parentList)) {
            return;
        }
        // 只授权其他租户的产品
        parentList = ListUtil.filter(parentList, i -> !i.getTenantId().equals(currentTenantId));
        if (CollUtil.isEmpty(parentList)) {
            return;
        }
        List<MerchantSimpleDto> merchantList = this.merchantFeign.findSimpleByIds(BaseListDto.of(merchantIds));
        // 只授权租户自己的商户
        merchantList = ListUtil.filter(merchantList, i -> i.getTenantId().equals(currentTenantId));
        if (CollUtil.isEmpty(merchantList)) {
            return;
        }
        merchantIds = ListUtil.toValueList(merchantList, MerchantSimpleDto::getId);
        List<Long> parentIds = ListUtil.toValueList(parentList, Product::getId);
        // 已存在的授权关系
        Set<String> authedKeySet = this.authedKeySet(merchantIds, parentIds);

        List<MerchantAuthProductParent> saveList = new ArrayList<>();
        Long userId = McContentHelper.getCurrentUserId();
        for (MerchantSimpleDto merchant : merchantList) {
            List<Long> saveParentIds = new ArrayList<>();
            for (Product parent : parentList) {
                if (authedKeySet.contains(merchant.getId() + "&&" + parent.getId())) {
                    // 授权过了
                    continue;
                }
                MerchantAuthProductParent auth = new MerchantAuthProductParent();
                auth.setAuthType(MerchantAuthType.SELF_TENANT.type)
                        .setFactoryId(0L).setIsExclusiveSupplyPrice(BasePoConstant.NO)
                        .setMerchantId(merchant.getId()).setMerchantTenantId(merchant.getTenantId())
                        .setProductParentId(parent.getId())
                        .setProductTenantId(parent.getTenantId())
                        .setCreateTenantId(currentTenantId)
                        .setChainedU(userId);
                saveList.add(auth);
                saveParentIds.add(parent.getId());
            }
            if (CollUtil.isEmpty(saveParentIds)) {
                continue;
            }
            ProductIdReqDTO idReqDTO = new ProductIdReqDTO();
            idReqDTO.setMerchantId(merchant.getId());
            idReqDTO.setIds(saveParentIds);
            idReqDTO.setUid(userId);
            this.syncProductLibrary(idReqDTO);
        }
        this.saveBatch(saveList);
    }

    /**
     * 同步产品库、报关信息
     */
    private void syncProductLibrary(ProductIdReqDTO reqDTO) {
        if (CollUtil.isEmpty(reqDTO.getIds())) {
            return;
        }
        // 同步新增产品库表
        // 已经在产品库的产品
        List<Long> hasParentIds = this.merchantProductParentService.findParentIdsByParentIds(reqDTO.getMerchantId()
                , reqDTO.getIds(), 0L);
        long now = System.currentTimeMillis();
        List<MerchantProductParentReqDto> reqDtoList = new ArrayList<>();
        reqDTO.getIds().forEach(parentId -> {
            if (hasParentIds.contains(parentId)) {
                return;
            }
            MerchantProductParentReqDto dto = new MerchantProductParentReqDto();
            dto.setMerchantId(reqDTO.getMerchantId());
            dto.setProductId(parentId);
            dto.setType(MerchantProductParentConstant.TYPE_ADMIN);
            dto.setCreatedUserId(reqDTO.getUid());
            dto.setCreatedTime(now);
            dto.setProductLibraryType(ProductLibraryTypeEnum.SHARED.getCode());
            reqDtoList.add(dto);
        });
        this.merchantProductParentService.insertBatch(reqDtoList);
        MerchantProductLibraryDeclarationStatusParam param = new MerchantProductLibraryDeclarationStatusParam();
        param.setMerchantId(reqDTO.getMerchantId());
        param.setProductIds(reqDTO.getIds());
        // 报关信息
        this.merchantProductLibraryDeclarationStatusService.addProductToLibrary(param);
    }

    public void authChange(Collection<Long> parentIds, Long productTenantId, Long createTenantId) {
        if (!createTenantId.equals(productTenantId)) {
            // 操作人租户id不等于产品所属租户id时不计算
            return;
        }
        this.productSummaryInfoService.updateAuthMerchantNum(parentIds, productTenantId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelAllAuth(Long merchantId, Long tenantId) {
        // 取消全部授权（切回自定义）不再清除已有授权
        this.authAllAccumulatePriceRecordMapperManager.deleteByMerchantId(merchantId, tenantId);
    }

    /**
     * 自有产品授权-公开产品开启全部授权
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Long> authSelfProductByAllPublic(AuthMerchantReqDTO req) {
        boolean selfProduct = req.getTenantId().equals(req.getProductTenantId());
        if (!selfProduct) {
            return Collections.emptyList();
        }
        MerchantPageRespDTO merchant = this.merchantFeign.getOneWithSetMeal(req.getMerchantId());
        Assert.validateNull(merchant, "商户不存在");
        // 获取全部未授权产品
        MerchantAuthPageReqDTO reqDTO = new MerchantAuthPageReqDTO();
        reqDTO.setPublicStatus(ProductPublicStatus.OPEN).setProductTenantId(req.getProductTenantId())
                //非sds商户只能授权一件产能线上架的产品
                .setOnePieceSupplyChainStatus(TenantCommonConstant.isSdsdiy(merchant.getTenantId()) ?
                        null : ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name())
                .setMerchantId(req.getMerchantId());
        List<Long> parentIds = this.listUnAuthParentIdByPublicStatus(reqDTO);
        List<MerchantAuthProductParent> saveList = new ArrayList<>();
        for (Long parentId : parentIds) {
            MerchantAuthProductParent auth = new MerchantAuthProductParent();
            auth.setAuthType(MerchantAuthType.SELF_TENANT.type)
                    .setFactoryId(0L).setProductParentId(parentId)
                    .setMerchantId(req.getMerchantId()).setMerchantTenantId(merchant.getTenantId())
                    .setProductTenantId(req.getProductTenantId())
                    // 如果是主商户，则算产品所属租户授权的
                    .setCreateTenantId(req.getTenantId())
                    .setIsExclusiveSupplyPrice(BasePoConstant.NO);
            auth.setChainedU(req.getUid(), req.getUid());
            saveList.add(auth);
        }
        this.saveBatch(saveList, 500);
        ProductIdReqDTO idReqDTO = BeanUtil.copyProperties(req, ProductIdReqDTO.class);
        idReqDTO.setIds(parentIds);
        this.syncProductLibrary(idReqDTO);
        return parentIds;
    }

    /**
     * 自有产品授权-产品上架
     */
    @Transactional(rollbackFor = Exception.class)
    public void authSelfProductByProductOnline(ProductIdReqDTO reqDTO) {
        // 获取产品
        List<Product> parentList = this.productReadService.findOnlineParent(reqDTO.getIds(), ProductPublicStatus.OPEN);
        if (CollUtil.isEmpty(parentList)) {
            return;
        }
        // 只自动授权自己商户 sprint7.0.0
        List<AuthAllAccumulatePriceRecord> authRecordList = this.authAllAccumulatePriceRecordMapperManager.findSelfTenantAuthList(reqDTO.getTenantId());
        if (CollUtil.isEmpty(authRecordList)) {
            return;
        }
        Map<Long, Integer> levelMap = ListUtil.toMap(AuthAllAccumulatePriceRecord::getMerchantId, AuthAllAccumulatePriceRecord::getAuthorizedLevel, authRecordList);
        List<MerchantPageRespDTO> merchantList = this.merchantFeign.listWithSetMeal(BaseListDto.of(levelMap.keySet()));
        if (CollUtil.isEmpty(merchantList)) {
            return;
        }
        Set<Long> mIds = merchantList.stream().map(MerchantPageRespDTO::getId).collect(Collectors.toSet());
        Set<Long> pIds = parentList.stream().map(Product::getId).collect(Collectors.toSet());
        // 已授权关系
        Set<String> authedKeySet = this.authedKeySet(mIds, pIds);
        MapListUtil<Long, Long> merchantProductMapList = MapListUtil.instance();
        List<MerchantAuthProductParent> saveList = new ArrayList<>();
        for (Product parent : parentList) {
            merchantList.forEach(merchant -> {
                if (authedKeySet.contains(merchant.getId() + "&&" + parent.getId())) {
                    // 授权过了
                    return;
                }
                // 自己租户的产品
                boolean selfProduct = parent.getTenantId().equals(merchant.getTenantId());
                if (!selfProduct) {
                    // 跨租户不处理
                    return;
                }
                if (BasePoConstant.CLOSE.equals(parent.getPublicStatus())) {
                    // 私有的不授权
                    return;
                }
                MerchantAuthProductParent auth = new MerchantAuthProductParent();
                auth.setAuthType(MerchantAuthType.SELF_TENANT.type)
                        .setProductParentId(parent.getId()).setProductTenantId(parent.getTenantId())
                        .setMerchantId(merchant.getId()).setMerchantTenantId(merchant.getTenantId())
                        .setCreateTenantId(reqDTO.getTenantId())
                        .setFactoryId(0L).setIsExclusiveSupplyPrice(BasePoConstant.NO);
                auth.setChainedU(reqDTO.getUid(), reqDTO.getUid());
                merchantProductMapList.addDistinctValue(merchant.getId(), parent.getId());
                saveList.add(auth);
            });
        }
        if (CollUtil.isEmpty(saveList)) {
            return;
        }
        this.saveBatch(saveList);
        this.authChange(reqDTO.getIds(), reqDTO.getTenantId(), reqDTO.getTenantId());
        merchantProductMapList.getMaps().forEach((merchantId, parentIds) -> {
            ProductIdReqDTO syncProductLibraryReqDTO = new ProductIdReqDTO(parentIds);
            syncProductLibraryReqDTO.setMerchantId(merchantId);
            this.syncProductLibrary(syncProductLibraryReqDTO);
            // 授权累计档位
            AuthAccumulatePriceLevelDTO priceLevelDTO = new AuthAccumulatePriceLevelDTO();
            priceLevelDTO.setParentIds(parentIds);
            priceLevelDTO.setLevel(levelMap.get(merchantId));
            priceLevelDTO.setPublicStatus(ProductPublicStatus.OPEN);
            priceLevelDTO.setUid(BasePoConstant.SYSTEM_UID);
            priceLevelDTO.setMerchantId(merchantId);
            priceLevelDTO.setTenantId(reqDTO.getTenantId());
            this.accumulatePriceService.setAuthAccumulatePriceLevel(priceLevelDTO);
        });
    }


    @Transactional(rollbackFor = Exception.class)
    public void delAuthByMessage(RemoveProductAuthTenantMessageDTO messageDTO) {
        if (CollUtil.isEmpty(messageDTO.getParentIds())) {
            return;
        }
        List<MerchantAuthProductParent> authList = this.lambdaQuery()
                .eq(MerchantAuthProductParent::getMerchantTenantId, messageDTO.getMerchantTenantId())
                .eq(messageDTO.getProductTenantId() != null, MerchantAuthProductParent::getProductTenantId, messageDTO.getProductTenantId())
                .in(MerchantAuthProductParent::getProductParentId, messageDTO.getParentIds()).list();
        if (CollUtil.isEmpty(authList)) {
            return;
        }
        this.realDel(authList, messageDTO.getCurrentTenantId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void realDel(List<MerchantAuthProductParent> authList, Long currentTenantId) {
        if (CollUtil.isEmpty(authList)) {
            return;
        }
        Set<Long> authIds = new HashSet<>();
        Set<Long> tenantParentIds = new HashSet<>();
        authList.forEach(a -> {
            authIds.add(a.getId());
            if (a.getProductTenantId().equals(currentTenantId)) {
                tenantParentIds.add(a.getProductParentId());
            }
        });
        this.lambdaUpdate().in(BaseFieldPO::getId, authIds).remove();
        this.authSupplyLadderPriceService.deleteByAuthIds(authIds, null);
        // 只刷新租户自己产品的授权数量
        this.authChange(tenantParentIds, currentTenantId, currentTenantId);

        ListUtil.toMapValueList(MerchantAuthProductParent::getMerchantId
                , MerchantAuthProductParent::getProductParentId, authList).forEach((mId, pIds) -> {
            // 异步删除其他数据
            ProductIdReqDTO idReqDTO = new ProductIdReqDTO();
            idReqDTO.setIds(new ArrayList<>(pIds));
            idReqDTO.setMerchantId(mId);
            idReqDTO.setTenantId(currentTenantId);
            this.rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_PRODUCT
                    , ProductTagConst.PRODUCT_AUTH_MERCHANT_DEL_AFTER_TAG, idReqDTO);
        });
    }

    /**
     * 移除授权其他相关表
     */
    @Transactional(rollbackFor = Exception.class)
    public void delMerchantAuthAfterHandler(Long merchantId, Collection<Long> parentIds) {
        this.authOnePriceMapperManager.deleteAuthOnePrice(merchantId, parentIds);
        this.authAccumulatePriceLevelMapperManager.deleteAuthAccumulatePriceLevel(merchantId, parentIds);
        this.merchantProductParentService.removeByParentIds(merchantId, parentIds, null);
        this.productMerchantPriceService.cancelAuth(merchantId, parentIds);
        // 失效/删除自定义报关信息
        MerchantProductLibraryDeclarationStatusParam param = new MerchantProductLibraryDeclarationStatusParam();
        param.setMerchantId(merchantId);
        param.setProductIds(new ArrayList<>(parentIds));
        this.merchantProductLibraryDeclarationStatusService.removeProductFromLibrary(param);
    }

    /**
     * 商户自己加入产品库
     */
    @Transactional(rollbackFor = Exception.class, timeout = 120)
    public MerchantProductParentBatchAddResp addFromProductLibrary(ProductIdReqDTO reqDTO) {
        MerchantProductParentBatchAddResp resp = new MerchantProductParentBatchAddResp();
        resp.setSuccessNum(0);
        MerchantRespDto merchant = this.merchantFeign.findDtoById(reqDTO.getMerchantId(), "");
        Assert.validateNull(merchant, "商户不存在");
        reqDTO.setTenantId(merchant.getTenantId());
        List<MerchantAuthProductParent> authProductParents = this.listMerchantAuthParent(reqDTO.getIds(), reqDTO.getMerchantId());
        if (reqDTO.getIds().size() == authProductParents.size()) {
            return resp;
        }
        if (authProductParents.size() > 0) {
            // 过滤已授权过的母体
            List<Long> authedParentIds = authProductParents.stream().map(MerchantAuthProductParent::getProductParentId).collect(Collectors.toList());
            reqDTO.getIds().removeAll(authedParentIds);
        }
        if (CollectionUtil.isEmpty(reqDTO.getIds())) {
            resp.setSuccessNum(0);
            return resp;
        }
        List<Product> products = this.productReadService.findByIdsAndStatus(reqDTO.getIds(), ProductConstant.STATUS_ONLINE);
        Assert.validateEmpty(products, "产品已下架或被删除");
        List<MerchantAuthProductParent> saveAuthList = new ArrayList<>();
        List<Long> sdsPublicProductIds = new ArrayList<>();
        for (Product parent : products) {
            // 公开产品 且 自己平台
            boolean canAdd = parent.getPublicStatus() == ProductPublicStatus.OPEN
                    && parent.getTenantId().equals(reqDTO.getTenantId());
            Assert.validateBool(canAdd, "您没有该产品的权限，请联系客服为您授权");
            MerchantAuthProductParent auth = new MerchantAuthProductParent();
            auth.setAuthType(MerchantAuthType.SELF_ADD.type)
                    .setFactoryId(0L).setProductParentId(parent.getId())
                    .setMerchantId(reqDTO.getMerchantId()).setMerchantTenantId(reqDTO.getTenantId())
                    .setProductTenantId(parent.getTenantId())
                    .setCreateTenantId(merchant.getMainMerchantId() == 0 ? parent.getTenantId() : reqDTO.getTenantId())
                    .setIsExclusiveSupplyPrice(BasePoConstant.NO);
            auth.setChainedU(reqDTO.getUid(), reqDTO.getUid());
            saveAuthList.add(auth);
            if (TenantCommonConstant.isSdsdiy(parent.getTenantId()) && parent.getPublicStatus() == ProductPublicStatus.OPEN) {
                // sds的公开产品
                sdsPublicProductIds.add(parent.getId());
            }
        }
        this.saveBatch(saveAuthList);
        resp.setSuccessNum(saveAuthList.size());
        MemberLevelEnum memberLevel = MemberLevelEnum.getMemberLevel(merchant.getMemberLevel());
        if (CollUtil.isNotEmpty(sdsPublicProductIds)
                // sds子商户
                && TenantCommonConstant.isSdsdiy(merchant.getTenantId())
                // 授权档位0以上
                && memberLevel.authAccumulatePriceLevel > 0) {
            // v7.5.0.1 yrs 自动授权档累计价
            AuthAccumulatePriceLevelDTO priceLevelDTO = new AuthAccumulatePriceLevelDTO();
            priceLevelDTO.setParentIds(sdsPublicProductIds);
            priceLevelDTO.setLevel(memberLevel.authAccumulatePriceLevel);
            priceLevelDTO.setPublicStatus(ProductPublicStatus.OPEN);
            priceLevelDTO.setUid(BasePoConstant.SYSTEM_UID);
            priceLevelDTO.setMerchantId(reqDTO.getMerchantId());
            priceLevelDTO.setTenantId(TenantCommonConstant.SDSDIY_TENANT_ID);
            this.accumulatePriceService.setAuthAccumulatePriceLevel(priceLevelDTO);
        }
        this.authChange(reqDTO.getIds(), reqDTO.getTenantId(), reqDTO.getTenantId());
        return resp;
    }

    /**
     * 自有产品新增授权-pod
     * 授权 子商户
     * ！！自己加入产品库的不要走这个方法！！
     * 产品库走这个{@see this#addFromProductLibrary}
     */
    @Transactional(rollbackFor = Exception.class)
    public void addAuthSelfProduct(ProductIdReqDTO reqDTO) {
        Assert.validateEmpty(reqDTO.getIds(), "必须选择授权产品");
        MerchantPageRespDTO merchant = this.merchantFeign.getOneWithSetMeal(reqDTO.getMerchantId());
        Assert.validateNull(merchant, "商户不存在");
        boolean crossTenant = !merchant.getTenantId().equals(reqDTO.getTenantId());
        Assert.validateTrue(crossTenant, "不允许跨租户授权商户");

        List<MerchantAuthProductParent> authProductParents = this.listMerchantAuthParent(reqDTO.getIds(), reqDTO.getMerchantId());
        Assert.validateTrue(reqDTO.getIds().size() == authProductParents.size(), "产品已授权过");
        if (authProductParents.size() > 0) {
            // 过滤已授权过的母体
            List<Long> authedParentIds = authProductParents.stream().map(MerchantAuthProductParent::getProductParentId).collect(Collectors.toList());
            reqDTO.getIds().removeAll(authedParentIds);
        }
        List<Product> products = this.productReadService.findByIdsAndStatus(reqDTO.getIds(), ProductConstant.STATUS_ONLINE);
        Assert.validateEmpty(products, "产品已下架或被删除");
        Long productTenantId = null;
        List<MerchantAuthProductParent> saveList = new ArrayList<>();
        List<Long> saveParentIds = new ArrayList<>();
        MerchantAuthType authType = MerchantAuthType.SELF_TENANT;
        for (Product parent : products) {
            productTenantId = parent.getTenantId();
            MerchantAuthProductParent auth = new MerchantAuthProductParent();
            auth.setAuthType(authType.type)
                    .setFactoryId(0L).setProductParentId(parent.getId())
                    .setMerchantId(reqDTO.getMerchantId()).setMerchantTenantId(merchant.getTenantId())
                    .setProductTenantId(parent.getTenantId())
                    .setCreateTenantId(reqDTO.getTenantId())
                    .setIsExclusiveSupplyPrice(BasePoConstant.NO)
                    .setChainedU(reqDTO.getUid());
            saveList.add(auth);
            saveParentIds.add(parent.getId());
        }
        reqDTO.setIds(saveParentIds);
        this.saveBatch(saveList);
        this.syncProductLibrary(reqDTO);
        this.authChange(reqDTO.getIds(), productTenantId, reqDTO.getTenantId());
    }

    /**
     * 移除【其他租户已下架/删除产品】的授权关系
     */
    @Deprecated
    public void removeCrossTenantDeletedProduct(ProductIdReqDTO reqDTO) {
        if (CollUtil.isEmpty(reqDTO.getIds())) {
            return;
        }
        List<MerchantAuthProductParent> authList = this.lambdaQuery()
                // 只移除非当前租户产品
                .ne(MerchantAuthProductParent::getProductTenantId, reqDTO.getTenantId())
                .eq(MerchantAuthProductParent::getMerchantTenantId, reqDTO.getTenantId())
                .in(MerchantAuthProductParent::getProductParentId, reqDTO.getIds())
                .list();
        Assert.validateEmpty(authList, "产品已经移除");
        this.realDel(authList, reqDTO.getTenantId());
    }

    /**
     * 移除授权，可以同时移除多个租户的产品<br>
     * 已下架产品，可以直接移除<br>
     * 自有公开产品：校验商户全部授权<br>
     * 跨租户产品：校验产品全部授权<br>
     * 非自有公开产品：只有主商户或者pod操作才能移除授权
     */
    public void deleteAuthCheck(ProductIdReqDTO reqDTO) {
        if (CollUtil.isEmpty(reqDTO.getIds())) {
            return;
        }
        Long currentTenantId = reqDTO.getTenantId();
        MerchantRespDto merchant = this.merchantFeign.getMerchantById(reqDTO.getMerchantId());
        Assert.validateNull(merchant, "商户不存在");
        Assert.validateEqual(merchant.getTenantId(), currentTenantId, "不允许移除其他租户的商户授权");
        // 产品
        List<Product> parentList = this.productReadService.findByIdsUnDelete(reqDTO.getIds(), null);
        // 商户端子商户不能主动移除授权产品的授权关系：非主商户，且不是pod操作
        if (!BasePoConstant.LONG_ZERO.equals(merchant.getMainMerchantId())
                && !SdsPlatformEnum.POD.equalsCode(McContentHelper.getRequestPlatform())) {
            // 私有或跨租户
            boolean privateOrCrossTenant = parentList.stream().anyMatch(p ->
                    ProductConstant.STATUS_ONLINE.equals(p.getStatus())
                            && (p.getPublicStatus() == ProductPublicStatus.CLOSE
                            || !p.getTenantId().equals(currentTenantId)));
            Assert.validateTrue(privateOrCrossTenant, "授权产品,无法移除");
        }
        // 租户自己的公开的产品
        boolean selfPublicProduct = parentList.stream().anyMatch(p ->
                ProductConstant.STATUS_ONLINE.equals(p.getStatus())
                        && p.getPublicStatus() == ProductPublicStatus.OPEN
                        && p.getTenantId().equals(currentTenantId));
        if (selfPublicProduct) {
            AuthAllAccumulatePriceRecord record = this.authAllAccumulatePriceRecordMapperManager
                    .findByMerchantId(reqDTO.getMerchantId(), currentTenantId);
            if (record != null) {
                throw new BusinessException("商户已经设置全部授权,无法移除");
            }
        }
        // 跨租户产品
        Set<Long> crossTenantParentIds = ListUtil.toValueSet(parentList, Product::getId, i ->
                ProductConstant.STATUS_ONLINE.equals(i.getStatus()) && !i.getTenantId().equals(currentTenantId));
        if (CollUtil.isNotEmpty(crossTenantParentIds)) {
            List<TenantAuthProductParent> crossTenantProductAuthList = this.tenantAuthProductParentService.findByTargetTenantAndParentIds(currentTenantId, crossTenantParentIds);
            List<Long> allAuthParentIds = ListUtil.toValueList(crossTenantProductAuthList, TenantAuthProductParent::getProductParentId, i -> BasePoConstant.yes(i.getAllMerchant()));
            parentList = ListUtil.filter(parentList, i -> !allAuthParentIds.contains(i.getId()));
            Assert.validateEmpty(parentList, "产品已设置全部授权,无法移除");
        }
        // 已下架的产品不用校验都可以移除
        List<Long> parentIds = ListUtil.toValueList(parentList, Product::getId);

        List<MerchantAuthProductParent> authList = this.lambdaQuery()
                .in(MerchantAuthProductParent::getProductParentId, parentIds)
                .eq(MerchantAuthProductParent::getMerchantId, reqDTO.getMerchantId())
                .list();
        Assert.validateEmpty(authList, "产品已经移除");
        this.realDel(authList, currentTenantId);
    }

    /**
     * 根据id获取
     *
     * @return dto
     */
    public MerchantAuthProductParentDTO getOneDto(Long id) {
        MerchantAuthProductParent one = this.getById(id);
        if (one == null) {
            return null;
        }
        return BeanUtil.copyProperties(one, MerchantAuthProductParentDTO.class);
    }

    public MerchantAuthProductParentDTO getOneDtoByMerchantIdAndParentId(Long merchantId, Long parentId) {
        MerchantAuthProductParent one = this.lambdaQuery().eq(MerchantAuthProductParent::getMerchantId, merchantId)
                .eq(MerchantAuthProductParent::getProductParentId, parentId).orderByDesc(BaseFieldPO::getId)
                .last("limit 1").one();
        if (one == null) {
            return null;
        }
        return BeanUtil.copyProperties(one, MerchantAuthProductParentDTO.class);
    }

    public void updateAuth(MerchantAuthProductParent update) {
        this.updateById(update);
    }

    /**
     * 产品授权商户id
     *
     * @param parentIds        母体id
     * @param merchantTenantId 商户租户id
     * @return 商户id
     */
    public List<Long> findMerchantIdByProductAndMerchantTenant(Collection<Long> parentIds, Long merchantTenantId) {
        if (CollUtil.isEmpty(parentIds)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().select(MerchantAuthProductParent::getMerchantId)
                .eq(MerchantAuthProductParent::getMerchantTenantId, merchantTenantId)
                .in(MerchantAuthProductParent::getProductParentId, parentIds)
                .groupBy(MerchantAuthProductParent::getMerchantId).list()
                .stream().map(MerchantAuthProductParent::getMerchantId).collect(Collectors.toList());
    }

    /**
     * 跨租户产品授权关系
     */
    public List<MerchantAuthProductParent> findCrossTenantProductAuth(Long parentId, Long merchantTenantId
            , Collection<Long> merchantIds) {
        return this.lambdaQuery()
                .eq(MerchantAuthProductParent::getMerchantTenantId, merchantTenantId)
                .eq(MerchantAuthProductParent::getProductParentId, parentId)
                .in(CollUtil.isNotEmpty(merchantIds), MerchantAuthProductParent::getMerchantId, merchantIds)
                .list();
    }

    /**
     * ids查询
     *
     * @param merchantId 商户id 可选
     */
    public List<MerchantAuthProductParent> listAuth(Collection<Long> ids, Long merchantId) {
        if (CollUtil.isEmpty(ids) && merchantId == null) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().in(CollUtil.isNotEmpty(ids), MerchantAuthProductParent::getId, ids)
                .eq(merchantId != null, MerchantAuthProductParent::getMerchantId, merchantId)
                .orderByDesc(BaseFieldPO::getId).list();
    }

    public List<MerchantAuthProductParent> listMerchantAuthParent(Collection<Long> parentIds, Long merchantId) {
        if (CollUtil.isEmpty(parentIds)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().in(MerchantAuthProductParent::getProductParentId, parentIds)
                .eq(MerchantAuthProductParent::getMerchantId, merchantId)
                .orderByDesc(BaseFieldPO::getId).list();
    }

    public List<MerchantAuthProductParent> listMerchantAuthParentByProductParentId(Long ProductParentId) {
        return this.lambdaQuery()
                .eq(MerchantAuthProductParent::getProductParentId, ProductParentId)
                .list();
    }

    public PageListResultDTO<MerchantAuthProductParentRespDTO> pageMerchantProductParent(MerchantAuthPageReqDTO select) {
        if (QueryParamHelper.checkQueryId(select.getCategoryId())) {
            select.setCategoryIds(this.categoryService.findChildrenIds(Collections.singletonList(select.getCategoryId())));
        }
        IPage<MerchantAuthProductParentRespDTO> page = this.baseMapper.pageMerchantAuthProductParent(new Page<>(select.getPage(), select.getSize()), select);
        return new PageListResultDTO<>(page.getTotal(), page.getRecords());
    }

    public List<Long> listAuthParentIdByPublicStatus(MerchantAuthPageReqDTO reqDTO) {
        return this.baseMapper.listAuthParentIdByPublicStatus(reqDTO);
    }

    public List<Long> listUnAuthParentIdByPublicStatus(MerchantAuthPageReqDTO reqDTO) {
        return this.baseMapper.listUnAuthParentIdByPublicStatus(reqDTO);
    }

    /**
     * 已授权产品数、授权指定工厂生产数
     */
    public Map<Long, AuthProductDataCountDTO> countAuthProductNumAndAuthFactoryNum(AuthProductDataReqDTO reqDTO) {
        return this.baseMapper.countAuthProductNumAndAuthFactoryNum(reqDTO).stream()
                .collect(Collectors.toMap(AuthProductDataCountDTO::getMerchantId, d -> d));
    }

    /**
     * 授权累计价档位数
     */
    public Map<Long, Integer> countAccumulateLevelNum(AuthProductDataReqDTO reqDTO) {
        List<AuthProductDataCountDTO> list = this.baseMapper.countAccumulateLevelNum(reqDTO);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(AuthProductDataCountDTO::getMerchantId
                , AuthProductDataCountDTO::getAuthAccumulateLevelNum));
    }

    /**
     * 授权专属价格数
     */
    public Map<Long, Integer> countAuthPriceNum(AuthProductDataReqDTO reqDTO) {
        return this.baseMapper.countAuthPriceNum(reqDTO).stream()
                .collect(Collectors.toMap(AuthProductDataCountDTO::getMerchantId
                        , AuthProductDataCountDTO::getAuthPriceNum));
    }

    /**
     * 授权商户数
     */
    public Integer countAuthMerchantNum(Long parentId, Long merchantTenantId) {
        return this.lambdaQuery()
                .eq(MerchantAuthProductParent::getProductParentId, parentId)
                .eq(MerchantAuthProductParent::getMerchantTenantId, merchantTenantId)
                .count();
    }

    public List<MerchantAuthProductParent> findAllByMerchantId(Long merchantId) {
        return this.lambdaQuery()
                .eq(MerchantAuthProductParent::getMerchantId, merchantId)
                .list();
    }

    /**
     * 母体授权商户数
     */
    public Map<Long, Integer> mapCountParentAuthMerchantNum(Collection<Long> parentIds, Long merchantTenantId) {
        if (CollUtil.isEmpty(parentIds)) {
            return Collections.emptyMap();
        }
        return this.baseMapper.countParentAuthMerchantNum(parentIds, merchantTenantId).stream()
                .collect(Collectors.toMap(AuthProductCountDTO::getProductParentId, AuthProductCountDTO::getNum));
    }

    /**
     * 某个产品的授权商户列表
     */
    public Page<MerchantAuthProductParent> pageOfProductAuthMerchant(AuthMerchantPageReqDTO reqDTO) {
        return this.lambdaQuery()
                .eq(MerchantAuthProductParent::getProductParentId, reqDTO.getParentId())
                .eq(MerchantAuthProductParent::getMerchantTenantId, reqDTO.getTenantId())
                .in(CollUtil.isNotEmpty(reqDTO.getMerchantIds()), MerchantAuthProductParent::getMerchantId, reqDTO.getMerchantIds())
                .orderByDesc(BaseFieldPO::getId)
                .page(new Page<>(reqDTO.getPage(), reqDTO.getSize()));
    }

    public Map<Long, Integer> getFactoryMerchantAuthParenNums(List<Long> factoryIds) {
        List<SqlLongCount> factoryMerchantAuthParenNums = this.baseMapper.getFactoryMerchantAuthParenNums(factoryIds);
        return factoryMerchantAuthParenNums.stream().collect(Collectors.toMap(SqlLongCount::getKey, SqlLongCount::getQty));
    }


    public Page<MerchantAuthProductParent> pageOfFactoryAuthMerchant(Long factoryId, BasePageSelect pageSelect) {
        return this.lambdaQuery()
                .eq(MerchantAuthProductParent::getFactoryId, factoryId)
                .groupBy(MerchantAuthProductParent::getMerchantId)
                .page(new Page<>(pageSelect.getPage(), pageSelect.getSize()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeMerchantTenantId(Long merchantId, Long newTenantId, Long oldTenantId) {
        Assert.validateNull(merchantId, "商户id不能为空");
        this.lambdaUpdate().eq(MerchantAuthProductParent::getMerchantId, merchantId)
                .set(MerchantAuthProductParent::getMerchantTenantId, newTenantId).update();
        // 旧租户的产品变成跨租户授权，且授权人为旧租户
        this.lambdaUpdate().eq(MerchantAuthProductParent::getMerchantId, merchantId)
                .eq(MerchantAuthProductParent::getProductTenantId, oldTenantId)
                .set(MerchantAuthProductParent::getAuthType, CROSS_TENANT.type)
                .set(MerchantAuthProductParent::getCreateTenantId, oldTenantId).update();
        // 新租户的产品变成自己租户授权，且授权人变为新租户
        this.lambdaUpdate().eq(MerchantAuthProductParent::getMerchantId, merchantId)
                .eq(MerchantAuthProductParent::getProductTenantId, newTenantId)
                .set(MerchantAuthProductParent::getAuthType, MerchantAuthType.SELF_TENANT.type)
                .set(MerchantAuthProductParent::getCreateTenantId, newTenantId).update();
    }

    public MerchantProductParentBatchAddResp batchAddForMerchant(ProductIdReqDTO reqDTO) {
        //判断用户是否是授权类型
        this.checkProductLibraryType(reqDTO.getMerchantId(), reqDTO.getUid());
        //校验商户和产品是否可被加入产品库中，并过滤掉不可添加到产品库的产品
        this.vaildMerchantAndProduct(reqDTO);
        //批量添加授权信息（商户自己添加），包括产品库信息跟商户被授权产品母体信息
        MerchantProductParentBatchAddResp resp = this.batchAddAuthForMerchant(reqDTO);

        return resp;
    }

    private MerchantProductParentBatchAddResp batchAddAuthForMerchant(ProductIdReqDTO reqDTO) {
        boolean lock = this.redisUtil.tryLock(ProductConstant.LOCK_PRODUCT_AUTH_MERCHANT + reqDTO.getMerchantId(), 180);
        Assert.validateBool(lock, "正在授权操作中，请稍后再试");

        MerchantProductParentBatchAddResp resp;
        try {
            resp = this.addProductLibraryForMerchant(reqDTO);
        } finally {
            this.redisUtil.unlock(ProductConstant.LOCK_PRODUCT_AUTH_MERCHANT + reqDTO.getMerchantId());
        }
        return resp;
    }

    private MerchantProductParentBatchAddResp addProductLibraryForMerchant(ProductIdReqDTO reqDTO) {
        // 新增商户产品库表
        // 已经在产品库的产品
        List<Long> hasParentIds = this.merchantProductParentService.findParentIdsByParentIds(reqDTO.getMerchantId()
                , reqDTO.getIds(), 0L);
        long now = System.currentTimeMillis();
        List<MerchantProductParentReqDto> reqDtoList = new ArrayList<>();
        List<Long> parentProductIds = Lists.newArrayList();
        reqDTO.getIds().forEach(parentId -> {
            if (hasParentIds.contains(parentId)) {
                return;
            }
            MerchantProductParentReqDto dto = new MerchantProductParentReqDto();
            dto.setMerchantId(reqDTO.getMerchantId());
            dto.setProductId(parentId);
            dto.setType(MerchantProductParentConstant.TYPE_MERCHANT);
            dto.setCreatedUserId(reqDTO.getUid());
            dto.setCreatedTime(now);
            dto.setProductLibraryType(ProductLibraryTypeEnum.SHARED.getCode());
            reqDtoList.add(dto);
            parentProductIds.add(parentId);
        });
        this.merchantProductParentService.insertBatch(reqDtoList);
        MerchantProductLibraryDeclarationStatusParam param = new MerchantProductLibraryDeclarationStatusParam();
        param.setMerchantId(reqDTO.getMerchantId());
        param.setProductIds(reqDTO.getIds());
        // 报关信息
        this.merchantProductLibraryDeclarationStatusService.addProductToLibrary(param);

        reqDTO.setIds(parentProductIds);
        //添加商户被授权产品母体
        MerchantProductParentBatchAddResp resp = this.addFromProductLibrary(reqDTO);

        return resp;
    }

    /**
     * 校验商户和产品是否可被加入产品库中，并过滤掉不可被商户自己添加到产品库的产品
     *
     * @param reqDTO 产品请求参数
     */
    private void vaildMerchantAndProduct(ProductIdReqDTO reqDTO) {
        Assert.validateEmpty(reqDTO.getIds(), "必须选择授权产品");
        List<Long> productIdList = Lists.newArrayList();
        List<Product> products = this.productReadService.findByIdsAndStatus(reqDTO.getIds(), ProductConstant.STATUS_ONLINE);
        MerchantPageRespDTO merchant = this.merchantFeign.getOneWithSetMeal(reqDTO.getMerchantId());
        Assert.validateNull(merchant, "商户不存在");
        products.forEach(product -> {
            //只能添加母体产品
            if (product.notParent()) {
                return;
            }
            //只能添加公开的产品
            if (ProductPublicStatus.OPEN != product.getPublicStatus()) {
                return;
            }
            //商户只能添加不高于当前套餐会员等级的产品
            if (merchant.getCurrentSetMeal().getLevel().compareTo(product.getMemberLevel()) < 0) {
                return;
            }
            productIdList.add(product.getId());
        });
        reqDTO.setIds(productIdList);
    }

    public void checkProductLibraryType(Long merchantId, Long userId) {
        boolean isSharedProductPermission = true;
        if (null != userId) {
            ProductPermissionIdsParam param = new ProductPermissionIdsParam();
            param.setMerchantId(merchantId);
            param.setSysUserIds(Lists.newArrayList(userId));
            List<MerchantSysUserProductPermissionRespDto> permissionRespDtos = this.merchantSysUserProductPermissionFeign.getByUserId(param);
            if (CollUtil.isNotEmpty(permissionRespDtos)) {
                isSharedProductPermission = permissionRespDtos.get(0).getProductLibraryType().equals(ProductLibraryTypeEnum.SHARED.getCode());
            }
        }
        if (null != userId && !isSharedProductPermission) {
            throw new BusinessException("您无权限操作，请让管理员为您授权产品");
        }
    }

    public List<Long> listParentIdByMerchant(Collection<Long> parentIds, Long merchantId) {
        return this.lambdaQuery().select(MerchantAuthProductParent::getProductParentId)
                .in(CollUtil.isNotEmpty(parentIds), MerchantAuthProductParent::getProductParentId, parentIds)
                .eq(MerchantAuthProductParent::getMerchantId, merchantId)
                .list().stream().map(MerchantAuthProductParent::getProductParentId).collect(Collectors.toList());
    }

    //sds产品授权给租户的母体id
    public List<Long> listParentIdBySdsProductAuthTenant(Collection<Long> parentIds, Long tenantId) {
        return this.lambdaQuery().select(MerchantAuthProductParent::getProductParentId)
                .in(CollUtil.isNotEmpty(parentIds), MerchantAuthProductParent::getProductParentId, parentIds)
                .eq(MerchantAuthProductParent::getMerchantTenantId, tenantId)
                .eq(MerchantAuthProductParent::getProductTenantId, TenantCommonConstant.SDSDIY_TENANT_ID)
                .list().stream().map(MerchantAuthProductParent::getProductParentId).collect(Collectors.toList());
    }

    public Map<Long, Integer> finalSmallOrderMinNumMap(Long merchantId, List<Product> variants) {
        Map<Long, Integer> smallOrderMinNumMap = Maps.newHashMap();

        Map<Long, List<SmallOrderPriceDto>> finalSmallOrderPricesMap = this.finalSmallOrderPricesMap(merchantId, variants);
        for (Map.Entry<Long, List<SmallOrderPriceDto>> entry : finalSmallOrderPricesMap.entrySet()) {
            smallOrderMinNumMap.put(entry.getKey(), entry.getValue().get(0).getNum());
        }
        return smallOrderMinNumMap;
    }

    public Map<Long, List<SmallOrderPriceDto>> finalSmallOrderPricesMap(Long merchantId, List<Product> variants) {
        Map<Long, List<SmallOrderPriceDto>> smallOrderPricesMap = Maps.newHashMap();
        Map<Long, Integer> smallOrderAuthLadderMinNumMap = this.smallOrderAuthLadderMinNumMap(merchantId, variants);
        for (Product variant : variants) {
            List<SmallOrderPriceDto> smallOrderPlatformPrices = JSONObject.parseArray(variant.getSmallOrderPrice(), SmallOrderPriceDto.class);
            if (CollectionUtil.isEmpty(smallOrderPlatformPrices)) {
                continue;
            }
            List<SmallOrderPriceDto> newSmallOrderPriceDtos = this.getNewSmallOrderPriceDtos(variant.getId(), smallOrderPlatformPrices, smallOrderAuthLadderMinNumMap);
            smallOrderPricesMap.put(variant.getId(), newSmallOrderPriceDtos);
        }
        return smallOrderPricesMap;
    }

    public List<SmallOrderPriceDto> getNewSmallOrderPriceDtos(Long productId, List<SmallOrderPriceDto> smallOrderPlatformPrices,
                                                              Map<Long, Integer> variantIdKeyLadderNumsMap) {
        if (variantIdKeyLadderNumsMap.get(productId) == null) {
            return smallOrderPlatformPrices;
        }
        Integer minBathNum = variantIdKeyLadderNumsMap.get(productId);
        List<SmallOrderPriceDto> newSmallOrderPlatformPrices = Lists.newArrayList();
        for (int i = smallOrderPlatformPrices.size() - 1; i >= 0; i--) {
            SmallOrderPriceDto smallOrderPriceDto = smallOrderPlatformPrices.get(i);
            if (smallOrderPriceDto.getNum() > minBathNum) {
                newSmallOrderPlatformPrices.add(smallOrderPriceDto);
            } else {
                smallOrderPriceDto.setNum(minBathNum);
                newSmallOrderPlatformPrices.add(smallOrderPriceDto);
                break;
            }
        }
        newSmallOrderPlatformPrices.sort(Comparator.comparing(SmallOrderPriceDto::getNum));
        return newSmallOrderPlatformPrices;
    }

    public Map<Long, Integer> smallOrderAuthLadderMinNumMap(Long merchantId, List<Product> variants) {
        Map<Long, Integer> variantIdKeyLadderNumsMap = Maps.newHashMap();
        if (CollectionUtil.isEmpty(variants)) {
            return variantIdKeyLadderNumsMap;
        }
        Set<Long> parentIds = variants.stream().map(Product::getParentId).collect(Collectors.toSet());
        Set<Long> variantIds = variants.stream().map(Product::getId).collect(Collectors.toSet());
        List<MerchantAuthProductParent> merchantAuthProductParents = this.listMerchantAuthParent(parentIds, merchantId);
        Map<Long, MerchantAuthProductParent> parentIdKeyFactoryIdMap = merchantAuthProductParents.stream().filter(p -> p.getSmallOrderFactoryId() != 0).collect(Collectors.toMap(MerchantAuthProductParent::getProductParentId, Function.identity(), (a, b) -> a));
        if (CollectionUtil.isEmpty(parentIdKeyFactoryIdMap.values())) {
            return variantIdKeyLadderNumsMap;
        }
        Set<Long> factoryIds = parentIdKeyFactoryIdMap.values().stream().map(MerchantAuthProductParent::getSmallOrderFactoryId).collect(Collectors.toSet());
        List<ProductSupply> productSupplies = this.productSupplyService.findByProductIdsAndFactoryIds(variantIds, factoryIds, SupplyChainTypeEnum.SMALL_ORDER.name());
        Map<Long, List<AuthSupplyLadderPriceDTO>> variantIdGroup = this.authSupplyLadderPriceService.getMerchantAuthSupplyPrice(merchantId, variantIds, SupplyChainTypeEnum.SMALL_ORDER.name()).stream().collect(Collectors.groupingBy(AuthSupplyLadderPriceDTO::getProductId));

        Map<Long, List<ProductSupply>> variantSupplyGroup = productSupplies.stream().collect(Collectors.groupingBy(ProductSupply::getProductId));
        Map<Long, Long> variantIdKeySupplyIdsMap = Maps.newHashMap();
        for (Product variant : variants) {
            MerchantAuthProductParent merchantAuthProductParent = parentIdKeyFactoryIdMap.get(variant.getParentId());
            List<ProductSupply> supplyList = variantSupplyGroup.get(variant.getId());
            if (merchantAuthProductParent == null || CollectionUtil.isEmpty(supplyList)) {
                continue;
            }
            if (CollectionUtil.isNotEmpty(variantIdGroup.get(variant.getId()))) {
                Integer ladderNum = variantIdGroup.get(variant.getId()).stream().map(AuthSupplyLadderPriceDTO::getNum).min(Comparator.comparing(i -> i)).orElse(1);
                variantIdKeyLadderNumsMap.put(variant.getId(), ladderNum);
                continue;
            }
            Long supplyId = supplyList.stream().filter(s -> s.getFactoryId().equals(merchantAuthProductParent.getSmallOrderFactoryId())).map(ProductSupply::getId).findFirst().orElse(0L);
            variantIdKeySupplyIdsMap.put(variant.getId(), supplyId);
        }
        Map<Long, List<LadderPriceDTO>> supplyIdKeyPriceMap = this.productSupplyLadderPriceService.mapByProductSupplyIds(variantIdKeySupplyIdsMap.values());
        variantIdKeySupplyIdsMap.forEach((variantId, supplyId) -> {
            List<LadderPriceDTO> ladderPriceDTOS = supplyIdKeyPriceMap.get(supplyId);
            if (CollectionUtil.isNotEmpty(ladderPriceDTOS)) {
                Integer ladderNum = ladderPriceDTOS.stream().map(LadderPriceDTO::getNum).min(Comparator.comparing(i -> i)).orElse(1);
                variantIdKeyLadderNumsMap.put(variantId, ladderNum);
            }
        });
        return variantIdKeyLadderNumsMap;
    }

    public Map<Long, Integer> smallOrderMinNumMap(Long merchantId, List<Product> variants) {
        Map<Long, Integer> variantIdKeyLadderNumsMap = Maps.newHashMap();
        if (CollectionUtil.isEmpty(variants)) {
            return variantIdKeyLadderNumsMap;
        }
        Set<Long> parentIds = variants.stream().map(Product::getParentId).collect(Collectors.toSet());
        Set<Long> variantIds = variants.stream().map(Product::getId).collect(Collectors.toSet());
        List<MerchantAuthProductParent> merchantAuthProductParents = this.listMerchantAuthParent(parentIds, merchantId);
        Map<Long, MerchantAuthProductParent> parentIdKeyFactoryIdMap = merchantAuthProductParents.stream().filter(p -> p.getSmallOrderFactoryId() != 0).collect(Collectors.toMap(MerchantAuthProductParent::getProductParentId, Function.identity(), (a, b) -> a));
        if (CollectionUtil.isEmpty(parentIdKeyFactoryIdMap.values())) {
            return variantIdKeyLadderNumsMap;
        }
        Set<Long> factoryIds = parentIdKeyFactoryIdMap.values().stream().map(MerchantAuthProductParent::getSmallOrderFactoryId).collect(Collectors.toSet());
        List<ProductSupply> productSupplies = this.productSupplyService.findByProductIdsAndFactoryIds(variantIds, factoryIds, SupplyChainTypeEnum.SMALL_ORDER.name());
        Map<Long, List<AuthSupplyLadderPriceDTO>> variantIdGroup = this.authSupplyLadderPriceService.getMerchantAuthSupplyPrice(merchantId, variantIds, SupplyChainTypeEnum.SMALL_ORDER.name()).stream().collect(Collectors.groupingBy(AuthSupplyLadderPriceDTO::getProductId));

        Map<Long, List<ProductSupply>> variantSupplyGroup = productSupplies.stream().collect(Collectors.groupingBy(ProductSupply::getProductId));
        Map<Long, Long> variantIdKeySupplyIdsMap = Maps.newHashMap();
        for (Product variant : variants) {
            MerchantAuthProductParent merchantAuthProductParent = parentIdKeyFactoryIdMap.get(variant.getParentId());
            List<ProductSupply> supplyList = variantSupplyGroup.get(variant.getId());
            if (merchantAuthProductParent == null || CollectionUtil.isEmpty(supplyList)) {
                continue;
            }
            if (CollectionUtil.isNotEmpty(variantIdGroup.get(variant.getId()))) {
                Integer ladderNum = variantIdGroup.get(variant.getId()).stream().map(AuthSupplyLadderPriceDTO::getNum).min(Comparator.comparing(i -> i)).orElse(1);
                variantIdKeyLadderNumsMap.put(variant.getId(), ladderNum);
                continue;
            }
            Long supplyId = supplyList.stream().filter(s -> s.getFactoryId().equals(merchantAuthProductParent.getSmallOrderFactoryId())).map(ProductSupply::getId).findFirst().orElse(0L);
            variantIdKeySupplyIdsMap.put(variant.getId(), supplyId);
        }
        Map<Long, List<LadderPriceDTO>> supplyIdKeyPriceMap = this.productSupplyLadderPriceService.mapByProductSupplyIds(variantIdKeySupplyIdsMap.values());
        variantIdKeySupplyIdsMap.forEach((variantId, supplyId) -> {
            List<LadderPriceDTO> ladderPriceDTOS = supplyIdKeyPriceMap.get(supplyId);
            if (CollectionUtil.isNotEmpty(ladderPriceDTOS)) {
                Integer ladderNum = ladderPriceDTOS.stream().map(LadderPriceDTO::getNum).min(Comparator.comparing(i -> i)).orElse(1);
                variantIdKeyLadderNumsMap.put(variantId, ladderNum);
            }
        });
        return variantIdKeyLadderNumsMap;
    }

    public void deleteMerchantAuth(MerchantAuthProductReqDTO reqDTO) {
        this.productCopyRecordService.checkCopyStatusAndError(reqDTO.getProductId());
        //校验删除权限，并获取待删除的授权信息列表
        List<MerchantAuthProductParent> authList = this.deleteMerchantAuthCheck(reqDTO);
        if (CollUtil.isEmpty(authList)) {
            return;
        }
        //按商户分组后删除授权
        this.realDel(authList, reqDTO.getTenantId());
    }

    private List<MerchantAuthProductParent> deleteMerchantAuthCheck(MerchantAuthProductReqDTO reqDTO) {
        if (CollUtil.isEmpty(reqDTO.getMerchantIdList())) {
            return Collections.emptyList();
        }
        List<MerchantSimpleDto> merchantList = this.merchantFeign.findSimpleByIds(BaseListDto.of(reqDTO.getMerchantIdList()));
        Assert.validateEmpty(merchantList, "商户不存在");
        Long currentTenantId = reqDTO.getTenantId();
        boolean crossTenantMerchant = merchantList.stream()
                //商户租户不是当前租户且本身不是主商户的商户列表
                .anyMatch(merchant -> !merchant.getTenantId().equals(currentTenantId));
        Assert.validateTrue(crossTenantMerchant, "不允许移除其他租户商户授权");
        Long parentId = reqDTO.getProductId();
        Product product = this.productReadService.findById(parentId);
        Assert.validateNull(product, "产品不存在");
        boolean selfProduct = product.getTenantId().equals(currentTenantId);
        boolean isOpenProduct = product.getPublicStatus() == ProductPublicStatus.OPEN;
        // 上架且公开的产品要校验全部授权
        if (ProductConstant.STATUS_ONLINE.equals(product.getStatus()) && isOpenProduct && selfProduct) {
            List<AuthAllAccumulatePriceRecord> authAllAccumulatePriceRecordList = this.authAllAccumulatePriceRecordMapperManager.findByMerchantIds(
                    reqDTO.getMerchantIdList(), product.getTenantId());
            if (CollUtil.isNotEmpty(authAllAccumulatePriceRecordList)) {
                throw new BusinessException("部分商户已经设置全部授权,无法移除");
            }
        }
        if (!selfProduct) {
            // 跨租户产品
            TenantAuthProductParent tenantAuth = this.tenantAuthProductParentService.findByTargetTenantAndParentId(currentTenantId, parentId);
            if (tenantAuth != null && BasePoConstant.yes(tenantAuth.getAllMerchant())) {
                Assert.wrong("产品已经设置全部授权,无法移除");
            }
        }
        //查询要移除的授权记录
        List<MerchantAuthProductParent> authList = this.lambdaQuery()
                .select(BaseFieldPO::getId, MerchantAuthProductParent::getMerchantId
                        , MerchantAuthProductParent::getProductParentId, MerchantAuthProductParent::getProductTenantId)
                .eq(MerchantAuthProductParent::getProductParentId, parentId)
                .in(MerchantAuthProductParent::getMerchantId, reqDTO.getMerchantIdList()).list();
        Assert.validateEmpty(authList, "产品已经移除");
        return authList;
    }

    public void removeMerchantSelfAuth(Long parentId, List<Long> merchantIdList, Long currentTenantId) {
        List<MerchantAuthProductParent> authList = this.lambdaQuery().select(BaseFieldPO::getId, MerchantAuthProductParent::getMerchantId
                        , MerchantAuthProductParent::getProductParentId, MerchantAuthProductParent::getProductTenantId)
                .eq(MerchantAuthProductParent::getProductTenantId, currentTenantId)
                .eq(MerchantAuthProductParent::getProductParentId, parentId)
                .in(MerchantAuthProductParent::getMerchantId, merchantIdList)
                .list();
        if (CollUtil.isEmpty(authList)) {
            return;
        }
        this.realDel(authList, currentTenantId);
    }

    public boolean checkPrivateProductAuth(Long merchantId) {
        if (!NumberUtils.greaterZero(merchantId)) {
            return false;
        }
        Long id = this.baseMapper.checkPrivateProductAuth(merchantId);
        return NumberUtils.greaterZero(id);
    }

    public Long countPrivateProductAuth(Long merchantId) {
        if (!NumberUtils.greaterZero(merchantId)) {
            return 0L;
        }
        return this.baseMapper.countPrivateProductAuth(merchantId);
    }
}
