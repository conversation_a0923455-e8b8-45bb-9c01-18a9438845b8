package com.sdsdiy.productimpl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.common.base.constant.status.ProductSupplyDelFlag;
import com.sdsdiy.common.base.constant.status.ProductSupplyStatus;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.entity.dto.SqlKeyValueBo;
import com.sdsdiy.common.base.enums.product.ProductInquiryNotificationMessageEnum;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.core.base.service.BaseServiceImpl;
import com.sdsdiy.core.dtoconvert.RelationsBinder;
import com.sdsdiy.core.redis.util.RedisUtil;
import com.sdsdiy.issuingbayapi.dto.areafactory.IssuingAreaFactoryResp;
import com.sdsdiy.productapi.dto.*;
import com.sdsdiy.productapi.dto.product.ProductInquiryItemRespDto;
import com.sdsdiy.productapi.myenum.EnumProductStatus;
import com.sdsdiy.productapi.myenum.EnumProductSupplyBackupType;
import com.sdsdiy.productapi.myenum.EnumProductSupplyStatus;
import com.sdsdiy.productdata.constants.ProductConstant;
import com.sdsdiy.productdata.constants.ProductEnumConstant;
import com.sdsdiy.productdata.dto.auth.AuthSupplyLadderPriceDTO;
import com.sdsdiy.productdata.dto.price.LadderPriceDTO;
import com.sdsdiy.productdata.dto.price.ProductFactorySupplyPriceDTO;
import com.sdsdiy.productdata.dto.price.ProductSupplySaleLadderPriceDto;
import com.sdsdiy.productdata.dto.product.edit.ProductEditSupplyDTO;
import com.sdsdiy.productdata.dto.product.edit.ProductUpdateSupplyReqDTO;
import com.sdsdiy.productdata.dto.supply.*;
import com.sdsdiy.productdata.enums.FactoryStatus;
import com.sdsdiy.productdata.enums.ProductSupplyHolidayStatus;
import com.sdsdiy.productdata.enums.supply.SupplyChainTypeEnum;
import com.sdsdiy.productimpl.entity.po.Product;
import com.sdsdiy.productimpl.entity.po.ProductSupply;
import com.sdsdiy.productimpl.entity.po.ProductSupplyLadderPrice;
import com.sdsdiy.productimpl.feign.IssuingAreaFactoryFeign;
import com.sdsdiy.productimpl.feign.IssuingBayAreaFeign;
import com.sdsdiy.productimpl.feign.IssuingBayFeign;
import com.sdsdiy.productimpl.mapper.ProductSupplyMapper;
import com.sdsdiy.productimpl.service.auth.AuthSupplyLadderPriceService;
import com.sdsdiy.productimpl.service.product.ProductInquiryItemService;
import com.sdsdiy.productimpl.service.product.ProductInquiryService;
import com.sdsdiy.productimpl.service.product.ProductReadService;
import com.sdsdiy.productimpl.service.supply.ProductSupplyLadderPriceService;
import com.sdsdiy.productimpl.util.ProductPriceUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
@DS("master")
public class ProductSupplyService extends BaseServiceImpl<ProductSupplyMapper, ProductSupply> {
    @Autowired
    private FactoryService factoryService;
    @Autowired
    private IssuingBayAreaFeign issuingBayAreaFeign;
    @Autowired
    private IssuingBayFeign issuingBayFeign;
    @Autowired
    private ProductSupplyLadderPriceService productSupplyLadderPriceService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private AuthSupplyLadderPriceService authSupplyLadderPriceService;
    @Resource
    private ProductInquiryItemService productInquiryItemService;
    @Resource
    private ProductInquiryService productInquiryService;
    @Resource
    private IssuingAreaFactoryFeign issuingAreaFactoryFeign;
    @Resource
    @Lazy
    private ProductReadService productReadService;

    public ProductSupply findOneByFactoryIdAndProductId(Long factoryId, long productId, String supplyChainType) {
        return this.lambdaQuery()
                .eq(ProductSupply::getFactoryId, factoryId)
                .eq(ProductSupply::getProductId, productId)
                .eq(ProductSupply::getSupplyChainType, supplyChainType)
                .one();
    }


    public List<Long> findAvailableFactoryIdByParentId(Long parentId, Integer status, String supplyChainType) {
        return this.lambdaQuery().select(ProductSupply::getFactoryId)
                .eq(ProductSupply::getProductParentId, parentId)
                .eq(status != null, ProductSupply::getStatus, status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .gt(ProductSupply::getDistributionPercent, 0)
                .groupBy(ProductSupply::getFactoryId).list()
                .stream().map(ProductSupply::getFactoryId).collect(Collectors.toList());
    }

    public List<Long> findAvailableFactoryIdByVariantIds(Collection<Long> variantIds, Integer status, String supplyChainType) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().select(ProductSupply::getFactoryId)
                .in(ProductSupply::getProductId, variantIds)
                .eq(status != null, ProductSupply::getStatus, status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .gt(ProductSupply::getDistributionPercent, 0)
                .groupBy(ProductSupply::getFactoryId).list()
                .stream().map(ProductSupply::getFactoryId).collect(Collectors.toList());
    }


    public List<ProductSupply> findByParentId(Long parentId, Integer status, String supplyChainType) {
        return this.lambdaQuery().eq(ProductSupply::getProductParentId, parentId)
                .eq(status != null, ProductSupply::getStatus, status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .orderByDesc(ProductSupply::getIssuingBayId)
                .orderByDesc(ProductSupply::getFactoryId).list();
    }

    public List<ProductSupply> findByProductId(Long productId, Integer status, String supplyChainType) {
        return this.lambdaQuery().eq(ProductSupply::getProductId, productId)
                .eq(status != null, ProductSupply::getStatus, status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .orderByDesc(ProductSupply::getIssuingBayId)
                .orderByDesc(ProductSupply::getFactoryId).list();
    }

    public List<ProductSupplyDTO> findAvailableByProductId(Long productId, String fields, String supplyChainType) {
        List<ProductSupply> productSupplyList = this.lambdaQuery().eq(ProductSupply::getProductId, productId)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .gt(ProductSupply::getDistributionPercent, 0)
                .eq(ProductSupply::getDelFlag, 1)
                .orderByDesc(ProductSupply::getIssuingBayId)
                .orderByDesc(ProductSupply::getFactoryId).list();

        return RelationsBinder.convertAndBind(productSupplyList, ProductSupplyDTO.class, fields);
    }

    public ProductSupplyDTO findAvailableByProductIdAndFactoryId(Long productId, String fields, String supplyChainType, Long factoryId) {
        ProductSupply productSupplyList = this.lambdaQuery().eq(ProductSupply::getProductId, productId)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .gt(ProductSupply::getDistributionPercent, 0)
                .eq(ProductSupply::getDelFlag, 1)
                .eq(ProductSupply::getFactoryId, factoryId)
                .orderByDesc(ProductSupply::getIssuingBayId)
                .orderByDesc(ProductSupply::getFactoryId).one();

        return RelationsBinder.convertAndBind(productSupplyList, ProductSupplyDTO.class, fields);
    }

    public Map<Long, List<ProductSupply>> mapOnlineByProductIds(List<Long> productIds, String supplyChainType) {
        List<ProductSupply> productSupplyList = this.findByProductIds(productIds, EnumProductSupplyStatus.ONLINE.status, supplyChainType);
        if (CollUtil.isEmpty(productSupplyList)) {
            return Maps.newHashMap();
        }
        return productSupplyList.stream().collect(Collectors.groupingBy(ProductSupply::getProductId));
    }

    public List<ProductSupply> findByProductIds(List<Long> productIds, Integer status, String supplyChainType) {
        if (CollUtil.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery().in(ProductSupply::getProductId, productIds)
                .eq(status != null, ProductSupply::getStatus, status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .orderByDesc(ProductSupply::getIssuingBayId)
                .orderByDesc(ProductSupply::getFactoryId).list();
    }

    public List<ProductSupply> publicListByProductIds(List<Long> productId, String supplyChainType) {
        return this.getBaseMapper().publicListByProductIds(productId, supplyChainType);
    }

    public List<ProductSupply> publicListByProductIdsNoSupplyChain(List<Long> productId) {
        return this.getBaseMapper().publicListByProductIdsNoSupplyChain(productId);
    }

    public List<ProductSupply> findUsableByProductIdAreaIdFactoryId(Long productId, Long areaId, Long factoryId) {
        LambdaQueryWrapper<ProductSupply> lambdaQueryWrapper = Wrappers.<ProductSupply>lambdaQuery()
                .eq(ProductSupply::getProductId, productId)
                .eq(ProductSupply::getIssuingAreaId, areaId)
                .eq(ProductSupply::getFactoryId, factoryId)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status);
        return this.getBaseMapper().selectList(lambdaQueryWrapper);
    }

    public ProductSupplyDTO findBackupFactoryProductIdAreaId(Long productId, Long areaId, String supplyChainType) {
        ProductSupply supply = this.lambdaQuery("factoryId", ProductSupplyDTO.class)
                .eq(ProductSupply::getProductId, productId)
                .eq(ProductSupply::getIssuingAreaId, areaId)
                .eq(ProductSupply::getBackupType, EnumProductSupplyBackupType.BACKUP.getStatus())
                .eq(ProductSupply::getSupplyChainType, supplyChainType)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status).one();
        return RelationsBinder.convertAndBind(supply, ProductSupplyDTO.class, "factoryId");
    }


    public List<ProductSupplyDTO> findBackupFactoryProductIdsAreaId(List<Long> productIds, Long areaId) {
        List<ProductSupply> list = this.lambdaQuery("factoryId,productId", ProductSupplyDTO.class)
                .in(ProductSupply::getProductId, productIds)
                .eq(ProductSupply::getIssuingAreaId, areaId)
                .eq(ProductSupply::getBackupType, EnumProductSupplyBackupType.BACKUP.getStatus())
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status).list();
        return RelationsBinder.convertAndBind(list, ProductSupplyDTO.class, "factoryId,productId");
    }

    public List<ProductSupplyDTO> findOnlineSupplyByVariantIds(Collection<Long> variantIds, Long areaId, String fields) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyList();
        }
        List<ProductSupply> productSupplies = this.lambdaQuery(fields, ProductSupplyDTO.class)
                .in(ProductSupply::getProductId, variantIds)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .eq(NumberUtils.greaterZero(areaId), ProductSupply::getIssuingAreaId, areaId).list();
        return RelationsBinder.convertAndBind(productSupplies, ProductSupplyDTO.class, fields);
    }

    public List<ProductSupplyDTO> findOnlineSupplyByVariantIdsSupplyChainType(Collection<Long> variantIds, Long areaId, String fields, String supplyChainType) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyList();
        }
        List<ProductSupply> productSupplies = this.lambdaQuery(fields, ProductSupplyDTO.class)
                .in(ProductSupply::getProductId, variantIds)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .eq(NumberUtils.greaterZero(areaId), ProductSupply::getIssuingAreaId, areaId).list();
        return RelationsBinder.convertAndBind(productSupplies, ProductSupplyDTO.class, fields);
    }


    /**
     * 根据产品id返回区域中可用的工厂
     *
     * @param productIds
     * @return
     */
    public List<ProductSupplyFactoryRespDTO> findUsableAreaFactoryByProductIds(List<Long> productIds, Long areaBayId) {
        List<ProductSupplyDTO> productSupplyDTOS = this.findOnlineSupplyByVariantIds(productIds, areaBayId, "productId,issuingAreaId,factoryId");
        //所有区域可用的工厂
        MapListUtil<Long, Long> areaFactoryTotal = MapListUtil.instance();
        //实际可用的
        MapListUtil<Long, Long> areaFactoryUsable = MapListUtil.instance();
        //产品 可用的区域 工厂
        Map<String, Boolean> productAreaFactoryMap = Maps.newHashMap();

        for (ProductSupplyDTO productSupplyDTO : productSupplyDTOS) {
            areaFactoryTotal.addDistinctValue(productSupplyDTO.getIssuingAreaId(), productSupplyDTO.getFactoryId());
            String productAreaFactoryKey = productAreaFactoryKey(productSupplyDTO.getProductId(), productSupplyDTO.getIssuingAreaId(), productSupplyDTO.getFactoryId());
            productAreaFactoryMap.put(productAreaFactoryKey, true);
        }

        for (Long areaId : areaFactoryTotal.getKeys()) {
            List<Long> factoryIds = areaFactoryTotal.getList(areaId);
            for (Long factoryId : factoryIds) {
                //是否该区域的所有工厂都有此产品
                boolean isAllHaveFactory = true;
                for (Long productId : productIds) {
                    String key = productAreaFactoryKey(productId, areaId, factoryId);
                    if (productAreaFactoryMap.get(key) == null) {
                        isAllHaveFactory = false;
                    }
                }
                if (isAllHaveFactory) {
                    areaFactoryUsable.addDistinctValue(areaId, factoryId);
                }
            }
        }

        return this.convertAreaFactory(areaFactoryUsable);
    }

    /**
     * 转换工厂与区域
     *
     * @param areaFactoryMapList
     * @return
     */
    public List<ProductSupplyFactoryRespDTO> convertAreaFactory(MapListUtil<Long, Long> areaFactoryMapList) {
        List<Long> areaIds = Lists.newArrayList();
        List<Long> searchFactoryIds = Lists.newArrayList();
        for (Long areaId : areaFactoryMapList.getKeys()) {
            areaIds.add(areaId);
            List<Long> factoryIds = areaFactoryMapList.getList(areaId);
            searchFactoryIds.addAll(factoryIds);
        }
        Map<Long, com.sdsdiy.issuingbayapi.dto.base.IssuingBayAreaRespDto> areaRespDtoHashMap = Maps.newHashMap();

        List<FactoryDto> factoryDtos = this.factoryService.findByIds(searchFactoryIds, "platformIssuingBayId");
        this.factoryService.formatBay(factoryDtos);

        List<ProductSupplyFactoryRespDTO> respDTOS = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(areaIds)) {
            List<com.sdsdiy.issuingbayapi.dto.base.IssuingBayAreaRespDto> areaRespDtos = this.issuingBayAreaFeign.findByIds("", areaIds);
            for (com.sdsdiy.issuingbayapi.dto.base.IssuingBayAreaRespDto areaRespDto : areaRespDtos) {
                areaRespDtoHashMap.put(areaRespDto.getId(), areaRespDto);
            }
        }

        Map<Long, FactoryDto> factoryDtoMap = ListUtil.toMap(FactoryDto::getId, factoryDtos);
        for (Long areaId : areaFactoryMapList.getKeys()) {
            List<Long> factoryIds = areaFactoryMapList.getList(areaId);
            List<FactoryDto> factoryDtoList = Lists.newArrayList();
            for (Long factoryId : factoryIds) {
                factoryDtoList.add(factoryDtoMap.get(factoryId));
            }

            ProductSupplyFactoryRespDTO productSupplyFactoryRespDTO = new ProductSupplyFactoryRespDTO();
            productSupplyFactoryRespDTO.setFactorys(factoryDtoList);
            productSupplyFactoryRespDTO.setArea(areaRespDtoHashMap.get(areaId));
            respDTOS.add(productSupplyFactoryRespDTO);

        }

        return respDTOS;


    }

    public void updateBackupFactory(Long productId, Long areaId, Long factoryId) {

        this.cleanBackupFactory(productId, areaId);
        if (factoryId != null && !factoryId.equals(0L)) {
            List<ProductSupply> productSupplies = this.findUsableByProductIdAreaIdFactoryId(productId, areaId, factoryId);
            if (CollectionUtil.isEmpty(productSupplies)) {
                throw new BusinessException("工厂供应已下架，请刷新重试");
            }
            Long id = productSupplies.get(0).getId();

            ProductSupply productSupply = new ProductSupply();
            productSupply.setId(id);
            productSupply.setBackupType(EnumProductSupplyBackupType.BACKUP.getStatus());
            this.baseMapper.updateById(productSupply);
        }


    }

    @Transactional
    public void batchUpdateBackupFactory(ProductSupplyUpdateBackupFactoryDTO param) {
        for (Long productId : param.getProductIds()) {
            for (ProductSupplyUpdateBackupFactoryConfigDTO config : param.getConfigs()) {
                this.updateBackupFactory(productId, config.getAreaId(), config.getFactoryId());
            }
        }
    }

    public void cleanBackupFactory(Long productId, Long areaId) {
        LambdaUpdateWrapper<ProductSupply> lambdaUpdateWrapper = Wrappers.<ProductSupply>lambdaUpdate()
                .eq(ProductSupply::getProductId, productId)
                .eq(ProductSupply::getIssuingAreaId, areaId);
        ProductSupply productSupply = new ProductSupply();
        productSupply.setBackupType(EnumProductSupplyBackupType.NONE.getStatus());
        this.getBaseMapper().update(productSupply, lambdaUpdateWrapper);
    }

    public static String productAreaFactoryKey(Long productId, Long areaId, Long factoryId) {
        return productId + "&" + areaId + "&" + factoryId;

    }

    public Set<Long> getIssuingBayIdsBytProductIds(BaseListReqDto productIds) {
        LambdaQueryWrapper<ProductSupply> lambdaQueryWrapper = Wrappers.<ProductSupply>lambdaQuery()
                .in(ProductSupply::getProductId, productIds.getIdList());
        List<ProductSupply> supplies = this.list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(supplies)) {
            Map<Long, List<ProductSupply>> collect = supplies.stream().collect(Collectors.groupingBy(ProductSupply::getIssuingBayId));
            return collect.keySet();
        }
        return Sets.newHashSet();
    }

    public List<ProductSupply> findByFactoryIdProductIdsAndOnePiece(Long factoryId, List<Long> productIds) {
        return this.lambdaQuery()
                .eq(ProductSupply::getFactoryId, factoryId)
                .in(ProductSupply::getProductId, productIds)
                .eq(ProductSupply::getSupplyChainType, SupplyChainTypeEnum.ONE_PIECE)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .eq(ProductSupply::getDelFlag, 1)
                .list();
    }

    public List<ProductSupply> findByFactoryIdProductIds(Long factoryId, List<Long> productIds) {
        return this.lambdaQuery()
                .eq(ProductSupply::getFactoryId, factoryId)
                .in(ProductSupply::getProductId, productIds)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .eq(ProductSupply::getDelFlag, 1)
                .list();
    }

    public List<ProductSupply> findByFactoryIdsProductIds(Collection<Long> factoryIds
        , Collection<Long> productIds, Collection<String> supplyChainTypes) {
        return this.lambdaQuery()
            .in(ProductSupply::getFactoryId, factoryIds)
            .in(ProductSupply::getProductId, productIds)
            .in(CollUtil.isNotEmpty(supplyChainTypes), ProductSupply::getSupplyChainType, supplyChainTypes)
            .eq(ProductSupply::getDelFlag, 1)
            .list();
    }


    /**
     * 获取指定工厂、指定母体的供应关系
     *
     * @param status EnumProductSupplyStatus
     */
    public List<ProductSupply> findByFactoryIdProductParentId(Long factoryId, Long productParentId, Integer status, String supplyChainType) {
        return this.lambdaQuery().eq(ProductSupply::getFactoryId, factoryId)
                .eq(status != null, ProductSupply::getStatus, status)
                .eq(ProductSupply::getProductParentId, productParentId)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType).list();
    }

    /**
     * 批量更新
     */
    public void batchUpdate(List<Long> ids, ProductSupplyDTO productSupplyDTO) {
        LambdaUpdateWrapper<ProductSupply> lambdaUpdateWrapper = Wrappers.<ProductSupply>lambdaUpdate()
                .in(ProductSupply::getId, ids);
        ProductSupply productSupply = new ProductSupply();
        BeanUtil.copyProperties(productSupplyDTO, productSupply);
        this.update(productSupply, lambdaUpdateWrapper);
    }

    /**
     * 获取产品供应工厂及发货仓列表
     */
    public List<ProductSupplyFactoryDTO> listSupplyFactoryByProductParentId(Long productParentId, String supplyChainType) {
        return this.baseMapper.listSupplyFactoryByProductParentId(productParentId, supplyChainType);
    }

    public static final int ADD_TYPE_ADMIN = 1;

    /**
     * 新增变体时，新增供应关系
     */
    // @GlobalTransactional(rollbackFor = Exception.class)
    public BaseListDto<Long> addProductSupply(List<ProductEditSupplyDTO> supply, List<Product> variantList, Long uid) {
        // 变体map
        Map<Long, Product> variantMap = variantList.stream().collect(Collectors.toMap(Product::getId, v -> v));
        Assert.validateBool(this.redisUtil.tryLock(ProductConstant.LOCK_PRODUCT_SUPPLY_ADD, 60, variantMap.keySet()), "操作频繁，请稍后重试");
        // 工厂
        Set<Long> factoryIds = new HashSet<>();
        supply.forEach(s -> factoryIds.addAll(s.getFactory().stream()
                .map(ProductEditSupplyDTO.ProductEditSupplyFactoryDTO::getFactoryId).collect(Collectors.toSet())));
        List<FactoryDto> factoryList = this.factoryService.findByIds(factoryIds, "status,tenantId");
        Map<Long, FactoryDto> factoryDtoMap = new HashMap<>(factoryIds.size());
        Long tenantId = variantList.stream().findFirst().get().getTenantId();
        factoryList.stream().filter(f -> FactoryStatus.ONLINE.status.equals(f.getStatus()))
                .forEach(f -> {
                    Assert.validateEqual(tenantId, f.getTenantId(), "无效工厂");
                    factoryDtoMap.put(f.getId(), f);
                });
        Assert.validateEqual(factoryDtoMap.keySet().size(), factoryIds.size(), "工厂已下架或关闭,无法供应");
        // 发货区域与工厂关系
        List<IssuingAreaFactoryResp> areaFactories = this.issuingAreaFactoryFeign.getListByFactoryIds(new BaseListReqDto(Lists.newArrayList(factoryIds)));
        Map<Long, Long> factoryIdIssuingAreaIdMap = areaFactories.stream().collect(Collectors.toMap(IssuingAreaFactoryResp::getFactoryId, IssuingAreaFactoryResp::getIssuingAreaId, (a, b) -> b));

        List<ProductSupply> addSupplyList = new ArrayList<>();
        long nowTime = System.currentTimeMillis();
        Map<String, List<LadderPriceDTO>> supplyPriceMap = new HashMap<>(50);
        supply.forEach(s -> {
            // 循环变体
            Product variant = variantMap.get(s.getId());
            s.getFactory().stream().filter(f -> f.getId() == null && EnumProductSupplyStatus.ONLINE.status.equals(f.getStatus())).forEach(f -> {
                // 循环工厂
                ProductSupply productSupply = BeanUtil.copyProperties(f, ProductSupply.class);
                // 价格校验并设置
                Integer minNum = ProductPriceUtil.NUM_MIN_1;
                //小单供应价的第一档数量可以不为1
                if (SupplyChainTypeEnum.SMALL_ORDER.name().equals(f.getSupplyChainType())) {
                    minNum = ProductPriceUtil.getAccumulateNumByLevel(f.getLadderPrice(), ProductPriceUtil.NUM_MIN_0);
                }
                productSupply.setSupplyPrice(ProductPriceUtil.checkLadderPrice(f.getLadderPrice(), minNum));
                supplyPriceMap.put(s.getId() + "&&" + f.getFactoryId(), f.getLadderPrice());
                // 基本信息
                productSupply.setAuditUserId(uid).setCreateTime(nowTime).setAuditTime(nowTime)
//                      .setType(type)
                        .setAddType(ADD_TYPE_ADMIN).setStatus(EnumProductSupplyStatus.ONLINE.status);
                // 工厂信息
//                Long issuingBayId = factoryDtoMap.get(f.getFactoryId()).getIssuingBayId();
//                Long issuingAreaId = issuingBayMap.getOrDefault(issuingBayId, 0L);
                Long issuingAreaId = factoryIdIssuingAreaIdMap.getOrDefault(f.getFactoryId(), 0L);
                Assert.validateTrue(f.getDistributionPercent() > 0 && (issuingAreaId <= 0),
                        "设置产能的供应工厂必须绑定发货仓、发货区域");
                productSupply.setIssuingAreaId(issuingAreaId);
                // 产品信息
                productSupply.setProductId(variant.getId()).setProductParentId(variant.getParentId())
                        .setSku(variant.getSku()).setCode(variant.getSku())
                        .setCategoryId(variant.getCategoryId())
                        .setPrototypeSku(variant.getPrototypeSku());

                addSupplyList.add(productSupply);
            });
        });
        this.saveBatch(addSupplyList);
        // 供应价
        List<ProductSupplyLadderPrice> supplyPriceList = new ArrayList<>();
        List<Long> supplyIds = new ArrayList<>();
        addSupplyList.forEach(ps -> {
            ProductSupplyLadderPriceService.buildSupplyPriceList(supplyPriceList,
                    supplyPriceMap.get(ps.getProductId() + "&&" + ps.getFactoryId()),
                    ps.getId());
            supplyIds.add(ps.getId());
        });
        this.productSupplyLadderPriceService.saveBatch(supplyPriceList);
        this.redisUtil.unlock(ProductConstant.LOCK_PRODUCT_SUPPLY_ADD, variantMap.keySet());
        // 固定初始化原料rawMaterialService.initRawMaterial  在聚合层调用
        return new BaseListDto<>(supplyIds);
        // 更新产品ES由调用方控制
    }

    // @GlobalTransactional(rollbackFor = Exception.class)
    public List<Long> updateProductSupply(List<ProductEditSupplyDTO.ProductEditSupplyFactoryDTO> supply, Long uid) {
        if (CollUtil.isEmpty(supply)) {
            return Collections.emptyList();
        }
        String supplyChainType = supply.get(0).getSupplyChainType();
        List<Long> supplyIds = supply.stream().map(ProductEditSupplyDTO.ProductEditSupplyFactoryDTO::getId).collect(Collectors.toList());
        Map<Long, ProductSupply> oldProductSupplyMap = this.listByIds(supplyIds).stream().collect(Collectors.toMap(ProductSupply::getId, i -> i));
        List<ProductSupply> updateSupplyList = new ArrayList<>();
        List<Long> newOnlineIds = new ArrayList<>();
        long nowTime = System.currentTimeMillis();
        Map<Long, List<LadderPriceDTO>> supplyPriceMap = new HashMap<>(50);
        supply.forEach(newSupply -> {
            ProductSupply old = oldProductSupplyMap.get(newSupply.getId());
            Assert.validateTrue(newSupply.getDistributionPercent() > 0 && (old.getIssuingAreaId() <= 0), "设置产能的供应工厂必须绑定发货区域");
            if (EnumProductSupplyStatus.ONLINE.status.equals(newSupply.getStatus())
                    && EnumProductSupplyStatus.OFFLINE.status.equals(old.getStatus())) {
                // 原来未上架的变成上架状态
                newOnlineIds.add(newSupply.getId());
            }
            ProductSupply productSupply = BeanUtil.copyProperties(newSupply, ProductSupply.class);
            // 基本信息
            productSupply.setAuditUserId(uid).setAuditTime(nowTime)
                    .setAddType(ADD_TYPE_ADMIN).setFactoryId(null).setIssuingBayId(null);
            if (EnumProductSupplyStatus.ONLINE.status.equals(newSupply.getStatus())) {
                // 上架的才校验
                // 价格校验并设置
                Integer minNum = ProductPriceUtil.NUM_MIN_1;
                //小单供应价的第一档数量可以不为1
                if (SupplyChainTypeEnum.isSmallOrder(supplyChainType)) {
                    minNum = ProductPriceUtil.getAccumulateNumByLevel(newSupply.getLadderPrice(), ProductPriceUtil.NUM_MIN_0);
                }
                productSupply.setSupplyPrice(ProductPriceUtil.checkLadderPrice(newSupply.getLadderPrice(), minNum));
            }
            supplyPriceMap.put(newSupply.getId(), newSupply.getLadderPrice());
            updateSupplyList.add(productSupply);
        });
        this.updateBatchById(updateSupplyList);
        // 供应价
        List<ProductSupplyLadderPrice> supplyPriceList = new ArrayList<>();
        updateSupplyList.forEach(ps -> ProductSupplyLadderPriceService.buildSupplyPriceList(supplyPriceList,
                supplyPriceMap.get(ps.getId()), ps.getId()));
        // 先删除旧的
        this.productSupplyLadderPriceService.deleteBySupplyIds(supplyPriceMap.keySet());
        // 再新增
        this.productSupplyLadderPriceService.saveBatch(supplyPriceList);
        return newOnlineIds;
    }

    public List<ProductSupplyDTO> getAvailableByParentId(Long parentId, Collection<Long> usableSupplyIds, String fields, String supplyChainType) {
        if (parentId == null) {
            return Lists.newArrayList();
        }
        List<ProductSupply> productSupplyList = this.lambdaQuery().eq(ProductSupply::getProductParentId, parentId)
                .notIn(CollUtil.isNotEmpty(usableSupplyIds), ProductSupply::getId, usableSupplyIds)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .gt(ProductSupply::getDistributionPercent, 0)
                .eq(ProductSupply::getDelFlag, 1).list();

        return RelationsBinder.convertAndBind(productSupplyList, ProductSupplyDTO.class, fields);
    }

    public void removeProductSupply(Long parentId, Collection<Long> usableSupplyIds, String supplyChainType) {
        if (parentId == null) {
            return;
        }
        this.lambdaUpdate().eq(ProductSupply::getProductParentId, parentId)
                .notIn(CollUtil.isNotEmpty(usableSupplyIds), ProductSupply::getId, usableSupplyIds)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .set(ProductSupply::getStatus, EnumProductSupplyStatus.OFFLINE.status)
                .set(ProductSupply::getDelFlag, CommonStatus.DELETE.getStatus()).update();
    }

    public void removeProductSupply(Long productId) {
        if (productId == null) {
            return;
        }
        this.lambdaUpdate().eq(ProductSupply::getProductId, productId)
                .set(ProductSupply::getStatus, EnumProductSupplyStatus.OFFLINE.status)
                .set(ProductSupply::getDelFlag, CommonStatus.DELETE.getStatus()).update();
    }

    public FactorySupplyHolidayStatDTO factoryHolidayStat(Long factoryId) {
        List<ProductSupply> list = this.lambdaQuery().select(ProductSupply::getProductParentId, ProductSupply::getSupplyChainType)
                .eq(ProductSupply::getFactoryId, factoryId)
                .in(ProductSupply::getHolidayStatus, ProductSupplyHolidayStatus.HAVE_HOLIDAY)
                .groupBy(ProductSupply::getProductParentId, ProductSupply::getSupplyChainType).list();
        Map<String, List<Long>> typeMap = ListUtil.toMapValueList(ProductSupply::getSupplyChainType, ProductSupply::getProductParentId, list);
        return new FactorySupplyHolidayStatDTO().setFactoryId(factoryId)
                .setOnePieceSupplyHolidayNum(typeMap.getOrDefault(SupplyChainTypeEnum.ONE_PIECE.name(), Collections.emptyList()).size())
                .setSmallOrderSupplyHolidayNum(typeMap.getOrDefault(SupplyChainTypeEnum.SMALL_ORDER.name(), Collections.emptyList()).size());
    }

    /**
     * 设置休假
     */
    public void setHoliday(ProductSupplyHolidayReqDTO reqDTO) {
        if (CollUtil.isEmpty(reqDTO.getProductSupplyIds())) {
            return;
        }
        Long startTime = QueryParamHelper.getTimestamp(QueryParamHelper.getQueryDateStart(reqDTO.getHolidayStartTime()));
        long now = System.currentTimeMillis();
        boolean holiday = startTime != null && startTime < now;
        this.lambdaUpdate().in(ProductSupply::getId, reqDTO.getProductSupplyIds())
                .set(ProductSupply::getHolidayStatus, holiday ? ProductSupplyHolidayStatus.HOLIDAY.status : ProductSupplyHolidayStatus.READY.status)
                .set(ProductSupply::getHolidayStartTime, startTime)
                .set(ProductSupply::getHolidayEndTime, QueryParamHelper.getTimestamp(QueryParamHelper.getQueryDateEnd(reqDTO.getHolidayEndTime())))
                .set(ProductSupply::getHolidayForeshowDay, reqDTO.getHolidayForeshowDay())
                .set(ProductSupply::getAuditUserId, reqDTO.getUid())
                .set(ProductSupply::getUpdateTime, now).update();
    }

    /**
     * 取消休假
     */
    public void cancelHoliday(ProductSupplyHolidayReqDTO reqDTO) {
        Set<Long> haveHolidaySupplyIds = this.listSupplyIdsByFactoryAndParentIds(reqDTO.getFactoryId(),
                Collections.singleton(reqDTO.getParentId()), ProductSupplyHolidayStatus.HAVE_HOLIDAY, reqDTO.getSupplyChainType()).keySet();
        this.cancelHolidayBySupplyIds(haveHolidaySupplyIds, reqDTO.getUid());
    }

    public void cancelHolidayBatch(FactoryCancelHolidayReqDTO reqDTO) {
        Assert.validateEmpty(reqDTO.getSupplyChainTypeList(), "至少选择一种产能线类型");
        List<ProductSupply> list = this.lambdaQuery().select(ProductSupply::getId)
                .eq(ProductSupply::getFactoryId, reqDTO.getFactoryId())
                .in(ProductSupply::getHolidayStatus, ProductSupplyHolidayStatus.HAVE_HOLIDAY)
                .in(ProductSupply::getSupplyChainType, reqDTO.getSupplyChainTypeList()).list();
        List<Long> ids = ListUtil.toValueList(ProductSupply::getId, list);
        this.cancelHolidayBySupplyIds(ids, reqDTO.getUid());
    }

    private void cancelHolidayBySupplyIds(Collection<Long> ids, Long uid) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        this.lambdaUpdate().in(ProductSupply::getId, ids)
                .set(ProductSupply::getHolidayStatus, ProductSupplyHolidayStatus.NONE.status)
                .set(ProductSupply::getHolidayStartTime, 0L)
                .set(ProductSupply::getHolidayEndTime, 0L)
                .set(ProductSupply::getHolidayForeshowDay, 0L)
                .set(ProductSupply::getAuditUserId, uid)
                .set(ProductSupply::getUpdateTime, System.currentTimeMillis()).update();
    }

    @Transactional(rollbackFor = Exception.class)
    public void autoUpdateHolidayStatus() {
        long currentTimeMillis = System.currentTimeMillis();
        // 休假已经结束的
        this.lambdaUpdate().le(ProductSupply::getHolidayEndTime, currentTimeMillis)
                .in(ProductSupply::getHolidayStatus, ProductSupplyHolidayStatus.HAVE_HOLIDAY)
                .set(ProductSupply::getHolidayStatus, ProductSupplyHolidayStatus.NONE.status)
                .set(ProductSupply::getUpdateTime, currentTimeMillis).update();
        // 开始休假
        this.lambdaUpdate().le(ProductSupply::getHolidayStartTime, currentTimeMillis)
                .eq(ProductSupply::getHolidayStatus, ProductSupplyHolidayStatus.READY.status)
                .set(ProductSupply::getHolidayStatus, ProductSupplyHolidayStatus.HOLIDAY.status)
                .set(ProductSupply::getUpdateTime, currentTimeMillis).update();
    }

    /**
     * 获取供应关系ids
     *
     * @param factoryId       工厂ids
     * @param parentIds       母体ids
     * @param holidayStatus   休假状态
     * @param supplyChainType 产能线类型
     * @return supplyId, productSupply
     */
    public Map<Long, ProductSupply> listSupplyIdsByFactoryAndParentIds(Long factoryId
            , Collection<Long> parentIds, List<Integer> holidayStatus, String supplyChainType) {
        return this.lambdaQuery().select(ProductSupply::getId, ProductSupply::getProductId, ProductSupply::getProductParentId)
                .eq(ProductSupply::getFactoryId, factoryId)
                .in(CollUtil.isNotEmpty(parentIds), ProductSupply::getProductParentId, parentIds)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .in(CollUtil.isNotEmpty(holidayStatus), ProductSupply::getHolidayStatus, holidayStatus).list()
                .stream().collect(Collectors.toMap(ProductSupply::getId, s -> s));
    }

    public Map<String, List<Long>> mapSupplyIdsByFactory(Long factoryId
            , List<Integer> holidayStatus, Collection<String> supplyChainType) {
        List<ProductSupply> list = this.lambdaQuery().select(ProductSupply::getId, ProductSupply::getSupplyChainType)
                .eq(ProductSupply::getFactoryId, factoryId)
                .in(CollUtil.isNotEmpty(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .in(CollUtil.isNotEmpty(holidayStatus), ProductSupply::getHolidayStatus, holidayStatus).list();
        return ListUtil.toMapValueList(ProductSupply::getSupplyChainType, ProductSupply::getId, list);
    }

    /**
     * 获取供应关系ids
     *
     * @param factoryId 工厂ids
     * @param parentIds 母体ids
     * @return supplyId, productSupply
     */
    public Map<Long, ProductSupply> findSupplyMapByFactoryIdAndParentIds(Long factoryId, Collection<Long> parentIds, String supplyChainType) {
        return this.lambdaQuery().eq(ProductSupply::getFactoryId, factoryId)
                .in(CollUtil.isNotEmpty(parentIds), ProductSupply::getProductParentId, parentIds)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .list()
                .stream().collect(Collectors.toMap(ProductSupply::getId, s -> s));
    }

    /**
     * 工厂供应产品母体ids
     */
    public List<Long> listFactorySupplyProductParentId(Long factoryId, String supplyChainType) {
        List<ProductSupply> supplyList = this.lambdaQuery().select(ProductSupply::getProductParentId)
                .eq(ProductSupply::getFactoryId, factoryId)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .groupBy(ProductSupply::getProductParentId).list();
        return supplyList.stream().map(ProductSupply::getProductParentId).collect(Collectors.toList());
    }

    /**
     * 休假供应关系
     *
     * @param parentIds 产品母体
     * @return 《母体id,《工厂id,供应列表》》
     */
    public Map<Long, Map<Long, List<ProductSupply>>> mapHolidaySupplyByParentId(List<Long> parentIds, String supplyChainType) {
        if (CollUtil.isEmpty(parentIds)) {
            return Collections.emptyMap();
        }
        List<Long> productIdList = this.productReadService.findVariantByParentIdsAndSupplyChainStatus(parentIds,
                SupplyChainTypeEnum.isOnePiece(supplyChainType) ? ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name() : null,
                SupplyChainTypeEnum.isSmallOrder(supplyChainType) ? ProductEnumConstant.SupplyChainStatusEnum.ONLINE.name() : null,
                EnumProductStatus.ONLINE.getStatus());
        if (CollUtil.isEmpty(productIdList)) {
            return Collections.emptyMap();
        }

        List<ProductSupply> supplyList = this.lambdaQuery().in(ProductSupply::getProductParentId, parentIds)
                .in(ProductSupply::getProductId, productIdList)
                .in(ProductSupply::getHolidayStatus, ProductSupplyHolidayStatus.HAVE_HOLIDAY)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType).list();
        Map<Long, List<ProductSupply>> parentMap = supplyList.stream()
                .collect(Collectors.groupingBy(ProductSupply::getProductParentId));
        return parentMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                e -> e.getValue().stream().collect(Collectors.groupingBy(ProductSupply::getFactoryId))));
    }

    /**
     * 母体供货价，分工厂
     */
    public Map<Long, List<ProductFactorySupplyPriceDTO>> mapProductParentFactorySupplyPrice(List<Long> productParentIds, String supplyChainType) {
        if (CollUtil.isEmpty(productParentIds)) {
            return Collections.emptyMap();
        }
        return this.baseMapper.listProductParentFactorySupplyPrice(productParentIds, supplyChainType).stream()
                .collect(Collectors.groupingBy(ProductFactorySupplyPriceDTO::getProductParentId));
    }

    /**
     * 每个变体每个工厂供货价
     *
     * @return 变体id, 供应工厂list
     */
    public Map<Long, List<ProductFactorySupplyPriceDTO>> mapProductVariantFactorySupplyPrice(Long productParentId, Long factoryId, String supplyChainType) {
        return this.baseMapper.listProductVariantFactorySupplyPrice(productParentId, factoryId, supplyChainType).stream()
                .collect(Collectors.groupingBy(ProductFactorySupplyPriceDTO::getProductId));
    }

    /**
     * 变体供货价
     *
     * @return 变体id, 价格
     */
    public Map<Long, ProductFactorySupplyPriceDTO> mapProductVariantSupplyPrice(Long productParentId, String supplyChainType) {
        return this.baseMapper.listProductVariantSupplyPrice(productParentId, supplyChainType).stream()
                .collect(Collectors.toMap(ProductFactorySupplyPriceDTO::getProductId, i -> i));
    }

    public Map<Long, BigDecimal> maxSupplyPriceByVariantIds(Collection<Long> variantIds, String supplyChainType) {
        if (CollUtil.isEmpty(variantIds)) {
            return Collections.emptyMap();
        }
        List<SqlKeyValueBo<Long, BigDecimal>> sqlKeyValueBos = this.baseMapper.maxSupplyPriceByVariantIds(variantIds, supplyChainType);
        return sqlKeyValueBos.stream().collect(Collectors.toMap(SqlKeyValueBo::getKey, SqlKeyValueBo::getValue));
    }

    /**
     * 批量上下架
     *
     * @param supplyIds ids
     * @param status    EnumProductSupplyStatus
     * @param uid       更新人
     */
    public void updateSupplyStatusBatch(Collection<Long> supplyIds, Integer status, Long uid) {
        if (CollUtil.isEmpty(supplyIds)) {
            return;
        }
        this.lambdaUpdate().in(ProductSupply::getId, supplyIds)
                .ne(ProductSupply::getStatus, status).set(ProductSupply::getStatus, status)
                .set(ProductSupply::getAuditUserId, uid)
                .set(ProductSupply::getUpdateTime, System.currentTimeMillis()).update();
    }
    public void updateSupplyPercentBatch(Collection<Long> supplyIds, Integer distributionPercent, Long uid) {
        if (CollUtil.isEmpty(supplyIds)) {
            return;
        }
        this.lambdaUpdate().in(ProductSupply::getId, supplyIds)
                .set(ProductSupply::getDistributionPercent, distributionPercent)
                .set(ProductSupply::getAuditUserId, uid)
                .set(ProductSupply::getUpdateTime, System.currentTimeMillis()).update();
    }

    /**
     * 更新下架时间
     */
    public void updateSupplyOfflineTime(Collection<Long> supplyIds, Long offlineTime, Long uid) {
        if (CollUtil.isEmpty(supplyIds)) {
            return;
        }
        this.lambdaUpdate().in(ProductSupply::getId, supplyIds)
                .set(ProductSupply::getOfflineTime, offlineTime)
                .set(ProductSupply::getAuditUserId, uid)
                .set(ProductSupply::getUpdateTime, System.currentTimeMillis()).update();
    }


    /**
     * 根据休假状态获取产品母体id
     *
     * @param parentIds 产品母体id
     * @param status    休假状态 1：休假 0：预休假
     * @return 产品母体id
     */
    public List<Long> findByHolidayStatus(List<Long> parentIds, Integer status, String supplyChainType) {
        return this.lambdaQuery().in(ProductSupply::getProductParentId, parentIds)
                .eq(ProductSupply::getHolidayStatus, status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .list()
                .stream()
                .map(ProductSupply::getProductParentId)
                .collect(Collectors.toList());
    }

    /**
     * 自动下架供应关系
     * 【变体需要先下架】
     */
    public void autoOfflineSupply(Collection<Long> variantIds, String supplyChainType) {
        if (CollUtil.isEmpty(variantIds)) {
            return;
        }
        long currentTimeMillis = System.currentTimeMillis();
        this.lambdaUpdate().in(ProductSupply::getProductId, variantIds)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .le(ProductSupply::getOfflineTime, currentTimeMillis)
                .set(ProductSupply::getStatus, EnumProductSupplyStatus.OFFLINE.status)
//                .set(ProductSupply::getOfflineTime, 0)
                .set(ProductSupply::getUpdateTime, currentTimeMillis).update();
    }

    /**
     * 查找无供应关系的上架产品
     *
     * @param parentIds   母体ids
     * @param exSupplyIds 排除的供应id
     * @return 变体ids
     */
    public List<Long> findNoSupplyProductIds(List<Long> parentIds, Collection<Long> exSupplyIds, String supplyChainType) {
        if (CollUtil.isEmpty(parentIds)) {
            return Collections.emptyList();
        }
        return this.baseMapper.findNoSupplyProductIds(parentIds, exSupplyIds, supplyChainType);
    }

    /**
     * 供应产品信息
     * 给工厂用的
     */
    public List<SupplyProductInfoDTO> listSupplyProductInfo(List<Long> supplyIds) {
        if (CollUtil.isEmpty(supplyIds)) {
            return Collections.emptyList();
        }
        return this.baseMapper.listSupplyProductInfo(supplyIds);
    }

    /**
     * 更新工厂发货仓信息
     */
    public void updateFactoryIssuing(Long factoryId, Long issuingBayId, Long issuingAreaId) {
        Assert.validateNull(factoryId, "工厂id不能为空");
        this.lambdaUpdate().eq(ProductSupply::getFactoryId, factoryId)
                .set(ProductSupply::getIssuingBayId, issuingBayId)
                .set(ProductSupply::getIssuingAreaId, issuingAreaId).update();
    }

    /**
     * 判断工厂是否还有上架且产能>0的供应关系
     */
    public boolean checkFactoryProductionCapacity(Long factoryId) {
        Integer count = this.lambdaQuery().eq(ProductSupply::getFactoryId, factoryId)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .gt(ProductSupply::getDistributionPercent, 0).count();
        return count != null && count > 0;
    }

    public List<ProductSupply> findByFactoryId(Long factoryId) {
        Assert.validateNull(factoryId, "工厂id不能为空");

        return this.lambdaQuery().eq(ProductSupply::getFactoryId, factoryId).eq(ProductSupply::getDelFlag, 1).list();
    }


    public List<ProductSupply> findByFactoryIds(Collection<Long> factoryId) {
        if (CollUtil.isEmpty(factoryId)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery().in(ProductSupply::getFactoryId, factoryId)
            // 1-不删除，99-删除
            .eq(ProductSupply::getDelFlag, 1)
            .list();
    }

    /**
     * 根据区域获取上架且产能>0的产品母体id
     */
    public Set<Long> findProductParentIdsByIssuingAreaId(long issuerAreaId) {
        return this.lambdaQuery()
                .eq(ProductSupply::getIssuingAreaId, issuerAreaId)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .gt(ProductSupply::getDistributionPercent, 0)
                .select(ProductSupply::getProductParentId)
                .list()
                .stream()
                .map(ProductSupply::getProductParentId)
                .collect(Collectors.toSet());
    }

    public List<ProductSupply> findByProductIdsAndFactoryIds(List<Long> productIds, List<Long> factoryIds, Integer status, String supplyChainType) {
        if (CollUtil.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery().in(ProductSupply::getProductId, productIds)
                .in(ProductSupply::getFactoryId, factoryIds)
                .eq(status != null, ProductSupply::getStatus, status)
                .eq(StrUtil.isNotBlank(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .list();
    }

    public List<ProductSupply> findByProductIdsAndFactoryIds(Collection<Long> productIds, Collection<Long> factoryIds, String supplyChainType) {
        if (CollUtil.isEmpty(productIds) || CollUtil.isEmpty(factoryIds)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery()
                .in(ProductSupply::getProductId, productIds)
                .in(ProductSupply::getFactoryId, factoryIds)
                .eq(ProductSupply::getStatus, EnumProductSupplyStatus.ONLINE.status)
                .eq(StringUtils.isEmpty(supplyChainType), ProductSupply::getSupplyChainType, supplyChainType)
                .list();
    }

    public List<ProductSupplyFactoryDTO> getProductSupplyByProductId(Long productId, Integer num, Long merchantId, String supplyChainType) {
        List<ProductSupplyFactoryDTO> productSupplyFactoryDTOList = Lists.newArrayList();
        //根据变体产品id查询可用的供应关系
        List<ProductSupplyDTO> productSupplyDTOList = this.findAvailableByProductId(productId,
                "supplyPrice,productionCycleMin,productionCycleMax,ladderPrice,saleLadderPrice", supplyChainType);
        if (CollUtil.isEmpty(productSupplyDTOList)) {
            return Lists.newArrayList();
        }
        //根据供应关系列表查询对应的工厂列表
        List<Long> factoryIdList = productSupplyDTOList.stream().map(ProductSupplyDTO::getFactoryId).collect(Collectors.toList());
        List<FactoryDto> factoryList = this.factoryService.findByIds(factoryIdList, "");
        Map<Long, FactoryDto> factoryDtoMap = factoryList.stream().collect(Collectors.toMap(FactoryDto::getId, value -> value));
        //格式化供应关系的productId、factoryId对应的授权指定供货价格列表
        this.formatAuthSupplyLadderPrice(merchantId, productSupplyDTOList, supplyChainType);


        //生成变体产品供应工厂列表
        productSupplyDTOList.forEach(productSupply -> {
            ProductSupplyFactoryDTO productSupplyFactoryDTO = new ProductSupplyFactoryDTO();

            FactoryDto factoryDto = factoryDtoMap.get(productSupply.getFactoryId());
            if (!FactoryStatus.isActive(factoryDto.getStatus())) {
                return;
            }
            productSupplyFactoryDTO.setId(factoryDto.getId());
            productSupplyFactoryDTO.setName(factoryDto.getName());
            productSupplyFactoryDTO.setCurrentSupplyPrice(getPrice(productSupply, num));
            productSupplyFactoryDTO.setProductionCycleMin(productSupply.getProductionCycleMin());
            productSupplyFactoryDTO.setProductionCycleMax(productSupply.getProductionCycleMax());
            productSupplyFactoryDTOList.add(productSupplyFactoryDTO);
        });
        return productSupplyFactoryDTOList;
    }

    public ProductSupplyFactoryDTO getProductSupplyByProductIdAndFactoryId(Long productId, Integer num, Long merchantId, String supplyChainType, Long factoryId) {
        //根据变体产品id查询可用的供应关系
        ProductSupplyDTO productSupplyDTO = this.findAvailableByProductIdAndFactoryId(productId, "supplyPrice,productionCycleMin,productionCycleMax,ladderPrice,saleLadderPrice", supplyChainType, factoryId);
        if (productSupplyDTO == null) {
            return null;
        }
        //根据供应关系列表查询对应的工厂列表
        //格式化供应关系的productId、factoryId对应的授权指定供货价格列表
        this.formatAuthSupplyLadderPrice(merchantId, Lists.newArrayList(productSupplyDTO), supplyChainType);


        //生成变体产品供应工厂列表
        ProductSupplyFactoryDTO productSupplyFactoryDTO = new ProductSupplyFactoryDTO();
        productSupplyFactoryDTO.setId(factoryId);
        productSupplyFactoryDTO.setCurrentSupplyPrice(getPrice(productSupplyDTO, num));
        productSupplyFactoryDTO.setProductionCycleMin(productSupplyDTO.getProductionCycleMin());
        productSupplyFactoryDTO.setProductionCycleMax(productSupplyDTO.getProductionCycleMax());

        return productSupplyFactoryDTO;
    }


    private void formatAuthSupplyLadderPrice(Long merchantId, List<ProductSupplyDTO> productSupplyDTOList, String supplyChainType) {
        if (CollUtil.isEmpty(productSupplyDTOList)) {
            return;
        }
        List<Long> productIdList = productSupplyDTOList.stream().map(ProductSupplyDTO::getProductId).collect(Collectors.toList());

        Map<String, List<AuthSupplyLadderPriceDTO>> authSupplyLadderPriceDTOMap = Maps.newHashMap();
        //查询授权指定供货价格
        List<AuthSupplyLadderPriceDTO> authSupplyLadderPriceDTOList = this.authSupplyLadderPriceService.getMerchantAuthSupplyPrice(merchantId, productIdList, supplyChainType);
        if (CollUtil.isNotEmpty(authSupplyLadderPriceDTOList)) {
            //将授权指定供货价格列表按 productId&&factoryId 分组
            authSupplyLadderPriceDTOList.forEach(authSupplyLadderPrice -> {
                String key = authSupplyLadderPrice.getProductId() + "&&" + authSupplyLadderPrice.getFactoryId();
                List<AuthSupplyLadderPriceDTO> authSupplyLadderPriceDTOS = authSupplyLadderPriceDTOMap.get(key);
                if (CollUtil.isEmpty(authSupplyLadderPriceDTOS)) {
                    authSupplyLadderPriceDTOS = Lists.newArrayList();
                }
                authSupplyLadderPriceDTOS.add(authSupplyLadderPrice);
                authSupplyLadderPriceDTOMap.put(key, authSupplyLadderPriceDTOS);
            });
        }

        productSupplyDTOList.forEach(productSupplyDTO -> {
            List<AuthSupplyLadderPriceDTO> authSupplyLadderPriceDTOS = authSupplyLadderPriceDTOMap.get(productSupplyDTO.getProductId() + "&&" + productSupplyDTO.getFactoryId());
            productSupplyDTO.setAuthSupplyLadderPrice(authSupplyLadderPriceDTOS);
        });
    }

    public static BigDecimal getPrice(ProductSupplyDTO productSupplyDTO, Integer num) {
        if (productSupplyDTO == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal minPrice = null;
        BigDecimal price = BigDecimal.valueOf(productSupplyDTO.getSupplyPrice());

        if (CollUtil.isEmpty(productSupplyDTO.getLadderPrice())
                && CollUtil.isEmpty(productSupplyDTO.getSaleLadderPrice())
                && CollUtil.isEmpty(productSupplyDTO.getAuthSupplyLadderPrice())
        ) {
            return price;
        }

        Date now = new Date();
        BigDecimal salePrice = null;
        BigDecimal authPrice = null;
        BigDecimal ladderPrice = null;
        //授权一口价与批发一口价
        if (CollectionUtils.isNotEmpty(productSupplyDTO.getAuthSupplyLadderPrice())) {
            for (AuthSupplyLadderPriceDTO authSupplyLadderPriceDTO : productSupplyDTO.getAuthSupplyLadderPrice()) {
                if (authSupplyLadderPriceDTO.getNum() <= num) {
                    authPrice = authSupplyLadderPriceDTO.getPrice();
                } else {
                    break;
                }
            }
        }
        //促销供货价
        if (CollectionUtils.isNotEmpty(productSupplyDTO.getSaleLadderPrice())) {
            for (ProductSupplySaleLadderPriceDto saleLadderPrice : productSupplyDTO.getSaleLadderPrice()) {
                if (saleLadderPrice.getStartTime().before(now) && saleLadderPrice.getEndTime().after(now)) {
                    if (saleLadderPrice.getSaleNum() <= num) {
                        salePrice = saleLadderPrice.getSalePrice();
                    } else {
                        break;
                    }
                } else {
                    break;
                }
            }
        }
        //供货价
        if (CollectionUtils.isNotEmpty(productSupplyDTO.getLadderPrice())) {
            for (ProductSupplyLadderPriceDTO productSupplyLadderPriceDTO : productSupplyDTO.getLadderPrice()) {
                if (productSupplyLadderPriceDTO.getNum() <= num) {
                    ladderPrice = productSupplyLadderPriceDTO.getPrice();
                } else {
                    break;
                }
            }
        }

        //取多个价格当中最低的价格
        minPrice = MathUtils.min(salePrice, authPrice);
        minPrice = MathUtils.min(minPrice, ladderPrice);
        minPrice = MathUtils.min(minPrice, price);

        return minPrice;
    }

    public static boolean isAvailable(ProductSupply productSupply) {
        if (productSupply == null || productSupply.getDistributionPercent() == null) {
            return false;
        }
        if (EnumProductSupplyStatus.ONLINE.status.equals(productSupply.getStatus()) && productSupply.getDistributionPercent() > 0) {
            return true;
        }
        return false;
    }

    public void sendProductInquiryNotification(Long parentProductId, List<ProductSupplyDTO> productSupplyList, Integer productSupplyStatus) {
        if (CollUtil.isEmpty(productSupplyList)) {
            return;
        }
        List<Long> productInquiryItemIdList = Lists.newArrayList();
        ProductInquiryNotificationMessageEnum productInquiryNotificationMessageEnum = null;

        //取出供应关系变化的变体产品id列表
        Set<Long> productIdSet = productSupplyList.stream().map(ProductSupplyDTO::getProductId).collect(Collectors.toSet());
        //根据变体查询有效的产品询价子单列表
        List<ProductInquiryItemRespDto> productInquiryItemRespDtoList = this.productInquiryItemService.getAvailableListByProductIds(
                Lists.newArrayList(productIdSet), false);
        //无有效的产品询价子单列表，则不用发送通知消息
        if (CollUtil.isEmpty(productInquiryItemRespDtoList)) {
            return;
        }
        //将未失效的询价子单列表按 productId&&factoryId 分组（同一组下是不同商户，相同变体产品id与指定工厂id的产品询价子单）
        Map<String, List<ProductInquiryItemRespDto>> productInquiryItemRespDtoMap = Maps.newHashMap();
        productInquiryItemRespDtoList.forEach(itemRespDto -> {
            String key = itemRespDto.getProductId() + "&&" + itemRespDto.getFactoryId();
            List<ProductInquiryItemRespDto> subProductInquiryItemRespDtoList = productInquiryItemRespDtoMap.get(key);
            if (CollUtil.isEmpty(subProductInquiryItemRespDtoList)) {
                subProductInquiryItemRespDtoList = Lists.newArrayList();
                productInquiryItemRespDtoMap.put(key, subProductInquiryItemRespDtoList);
            }
            subProductInquiryItemRespDtoList.add(itemRespDto);
        });

        productSupplyList.forEach(productSupplyDTO -> {
            List<ProductInquiryItemRespDto> subProductInquiryItemRespDtoList = productInquiryItemRespDtoMap.get(
                    productSupplyDTO.getProductId() + "&&" + productSupplyDTO.getFactoryId());
            if (CollUtil.isEmpty(subProductInquiryItemRespDtoList)) {
                return;
            }
            productInquiryItemIdList.addAll(subProductInquiryItemRespDtoList.stream().map(ProductInquiryItemRespDto::getId).collect(Collectors.toList()));
        });

        if (productSupplyStatus == ProductSupplyStatus.ON_SHELF) {
            productInquiryNotificationMessageEnum = ProductInquiryNotificationMessageEnum.PRODUCT_SUPPLY_AVAILABLE;
        }
        if (productSupplyStatus == ProductSupplyStatus.OFF_SHELF) {
            productInquiryNotificationMessageEnum = ProductInquiryNotificationMessageEnum.PRODUCT_SUPPLY_UNAVAILABLE;
        }

        if (CollUtil.isNotEmpty(productInquiryItemIdList)) {
            this.productInquiryService.sendProductSupplyChangeNotification(parentProductId, productInquiryItemIdList, productInquiryNotificationMessageEnum);
        }
    }

    public void updateProductSupplyAreaIdByFactoryId(Long factoryId, Long issuingAreaId) {
        this.lambdaUpdate()
                .eq(ProductSupply::getFactoryId, factoryId)
                .ne(ProductSupply::getIssuingAreaId, issuingAreaId)
                .set(ProductSupply::getIssuingAreaId, issuingAreaId)
                .update();

    }

    /**
     * 校验供应关系的区域是否唯一
     *
     * @param reqDTO      供应关系请求参数
     * @param isAddSupply 是否为新增的供应关系
     */
    public void validIssuingArea(ProductUpdateSupplyReqDTO reqDTO, Boolean isAddSupply) {
        if (reqDTO == null) {
            return;
        }
        //取出一件供应关系与小单供应关系
        List<ProductEditSupplyDTO.ProductEditSupplyFactoryDTO> allSupplyList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(reqDTO.getSupply())) {
            reqDTO.getSupply().forEach(variantProductSupply -> {
                variantProductSupply.check();
                allSupplyList.addAll(variantProductSupply.getFactory());
            });
        }
        if (CollUtil.isNotEmpty(reqDTO.getSmallOrderSupply())) {
            reqDTO.getSmallOrderSupply().forEach(variantProductSupply -> {
                variantProductSupply.check();
                allSupplyList.addAll(variantProductSupply.getFactory());
            });
        }
        if (CollUtil.isEmpty(allSupplyList)) {
            return;
        }

        List<ProductSupply> productSupplyList = BeanUtil.copyToList(allSupplyList, ProductSupply.class);
        //校验母体下的所有供应关系的区域是否都是同一个
        this.validProductSupplyIssuingAreaForParent(productSupplyList, isAddSupply ? reqDTO.getParentId() : null);
    }

    public void validProductSupplyIssuingAreaForParent(List<ProductSupply> productSupplyList, Long parentId) {
        if (CollUtil.isEmpty(productSupplyList)) {
            return;
        }
        Long issuingAreaId = 0L;
        //parentId不为0时，表示母体当前的供应关系不会更新，要校验的供应关系是新增的
        if (parentId != null) {
            issuingAreaId = this.findIssuingAreaByParentId(parentId);
        }

        for (ProductSupply productSupply : productSupplyList) {
            if (issuingAreaId <= 0L) {
                issuingAreaId = productSupply.getIssuingAreaId();
            } else {
                //母体产品的供应关系列表中出现区域id不同的供应关系就抛异常
                Assert.validateFalse(issuingAreaId.equals(productSupply.getIssuingAreaId()), "每个产品只能配置一个发货区域");
            }
        }
    }

    public Long findIssuingAreaByParentId(Long parentId) {
        List<ProductSupply> allProductSupplyList = this.findByParentId(parentId, EnumProductSupplyStatus.ONLINE.status, null);
        if (CollUtil.isNotEmpty(allProductSupplyList)) {
            return allProductSupplyList.get(0).getIssuingAreaId();
        }
        return 0L;
    }

    public List<ProductSupplyVariantDTO> listSupplyVariant(ProductSupplyReqDTO reqDto) {
        List<ProductSupply> supplyList = this.findByFactoryIdProductParentId(
                reqDto.getFactoryId(), reqDto.getProductId(), EnumProductSupplyStatus.ONLINE.status, reqDto.getSupplyChainType());
        if (CollUtil.isEmpty(supplyList)) {
            return Collections.emptyList();
        }
        // 工厂供货价，最低最高价
        Map<Long, List<ProductFactorySupplyPriceDTO>> supplyPriceMap = reqDto.getWidthSupplyPrice() ? this
                .mapProductVariantFactorySupplyPrice(reqDto.getProductId(), reqDto.getFactoryId(), reqDto.getSupplyChainType()) : Collections.emptyMap();

        Map<Long, Long> variantIdKeySupplyIdMap = reqDto.getWidthSupplyPrice() ? supplyList.stream().collect(Collectors
                .toMap(ProductSupply::getProductId, ProductSupply::getId)) : Collections.emptyMap();
        // 工厂供货价
        Map<Long, List<LadderPriceDTO>> productSupplyMap = reqDto.getWidthSupplyPrice() ?
                this.productSupplyLadderPriceService.mapByProductSupplyIds(variantIdKeySupplyIdMap.values()) : Collections.emptyMap();

        Map<Long, ProductSupply> supplyMap = supplyList.stream().collect(Collectors.toMap(ProductSupply::getProductId, s -> s));
        //
        List<Product> variantList = this.productReadService.findByIdsUnDelete(supplyMap.keySet(), reqDto.getTenantId());
        List<ProductSupplyVariantDTO> dtoList = new ArrayList<>();
        variantList.forEach(variant -> {
            ProductSupplyVariantDTO variantDTO = BeanUtil.copyProperties(variant, ProductSupplyVariantDTO.class);
            ProductSupply supply = supplyMap.get(variant.getId());
            variantDTO.setHolidayStatus(supply.getHolidayStatus());
            if (ProductSupplyHolidayStatus.HAVE_HOLIDAY.contains(supply.getHolidayStatus())) {
                variantDTO.setHolidayStartTime(supply.getHolidayStartTime() > 0 ? DateUtil.formatDate(new Date(supply.getHolidayStartTime())) : null);
                variantDTO.setHolidayEndTime(supply.getHolidayEndTime() > 0 ? DateUtil.formatDate(new Date(supply.getHolidayEndTime())) : null);
                variantDTO.setHolidayForeshowDay(supply.getHolidayForeshowDay());
            }
            variantDTO.setProductSupplyId(supply.getId());
            if (reqDto.getWidthSupplyPrice()) {
                variantDTO.setSupplyPrice(supplyPriceMap.get(variant.getId()));
                variantDTO.setFactorySupplyPrice(productSupplyMap.get(variantIdKeySupplyIdMap.get(variant.getId())));
            }
            dtoList.add(variantDTO);
        });
        return dtoList;
    }


    public List<ProductSupply> findProductSupplyListByFactoryIdAndVariantIds(Long factoryId, Collection<Long> variantIds) {
        Assert.validateNull(factoryId, "工厂id不能为空");
        return this.baseMapper.findProductSupplyListByFactoryIdAndVariantIds(factoryId, variantIds);
    }

    /**
     * 获取上架的产品母体id
     */
    public List<Long> getOnlineParentIdsByFactoryIdAndParentIds(Long factoryId, Collection<Long> parentIds) {
        if (factoryId == null || CollectionUtil.isEmpty(parentIds)) {
            return Lists.newArrayList();
        }
        return this.lambdaQuery().select(ProductSupply::getProductParentId)
                .eq(ProductSupply::getFactoryId, factoryId)
                .in(ProductSupply::getProductParentId, parentIds)
                .eq(ProductSupply::getStatus, ProductSupplyStatus.ON_SHELF)
                .groupBy(ProductSupply::getProductParentId)
                .list().stream().map(ProductSupply::getProductParentId)
                .collect(Collectors.toList());
    }

    public Map<Long, List<Integer>> getFactoryIdKeySupplyLadderPricesNumsMap(ProductSupplyReqDTO productSupplyReqDTO) {
        List<ProductSupply> productSupplies = this.lambdaQuery()
                .eq(ProductSupply::getSupplyChainType, productSupplyReqDTO.getSupplyChainType())
                .eq(ProductSupply::getProductParentId, productSupplyReqDTO.getProductId())
                .eq(ProductSupply::getDelFlag, ProductSupplyDelFlag.NOT_DELETE)
                .list();
        if (CollectionUtil.isEmpty(productSupplies)) {
            return Maps.newHashMap();
        }

        Map<Long, Long> factoryIdKeySupplyIdsMap = productSupplies.stream().sorted(Comparator.comparing(ProductSupply::getProductId)).collect(Collectors.toMap(ProductSupply::getFactoryId, ProductSupply::getId, (a, b) -> a));
        Map<Long, List<LadderPriceDTO>> supplyIdKeyMap = this.productSupplyLadderPriceService.mapByProductSupplyIds(factoryIdKeySupplyIdsMap.values());

        Map<Long, List<Integer>> factoryIdKeyLadderPriceMap = Maps.newHashMap();
        factoryIdKeySupplyIdsMap.forEach((factoryId, supplyId) -> {
            List<LadderPriceDTO> ladderPrices = supplyIdKeyMap.get(supplyId);
            if (CollectionUtil.isEmpty(ladderPrices)) {
                return;
            }
            factoryIdKeyLadderPriceMap.put(factoryId, ladderPrices.stream().map(LadderPriceDTO::getNum).collect(Collectors.toList()));
        });
        return factoryIdKeyLadderPriceMap;
    }

    public List<ProductSupplyDTO> findSupplyByProductIds(Collection<Long> productIds) {
        if (productIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<ProductSupply> productSupplies = this.lambdaQuery()
                .in(ProductSupply::getProductId, productIds)
                .list();
        return BeanUtil.copyToList(productSupplies, ProductSupplyDTO.class);
    }
    public List<ProductSupply> findSupplyNotDelByProductIds(Collection<Long> productIds) {
        if (productIds.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .in(ProductSupply::getProductId, productIds)
                .eq(ProductSupply::getDelFlag, ProductSupplyDelFlag.NOT_DELETE)
                .list();
    }



    public List<ProductSupply> getByProductParentId(List<Long> productParentIds, List<Long> factoryIds, String supplyChainType) {
        if(CollUtil.isEmpty(productParentIds)||CollUtil.isEmpty(factoryIds)){
            return Collections.emptyList();
        }
        return lambdaQuery()
            .in(ProductSupply::getProductParentId, productParentIds)
            .in(ProductSupply::getFactoryId, factoryIds)
            .eq(ProductSupply::getSupplyChainType, supplyChainType)
            .list();
    }

    public void updateReachLimitByIds(List<Long> ids, Integer reachLimit){
        if(CollUtil.isEmpty(ids)){
            return;
        }
        lambdaUpdate()
            .set(ProductSupply::getReachLimit,reachLimit)
            .in(ProductSupply::getId,ids)
            .update();
    }

    public void updateReachLimit(Integer reachLimit){
        lambdaUpdate()
            .set(ProductSupply::getReachLimit,reachLimit)
            .ne(ProductSupply::getReachLimit, reachLimit)
            .update();
    }

    /**
     * 从productParentIds中过滤出这个工厂有的产品母体id
     */
    public List<Long> filterExistProductsByFactory(Long factoryId, List<Long> productParentIds, String supplyChainType) {
        if (CollUtil.isEmpty(productParentIds)) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .select(ProductSupply::getProductParentId)
                .eq(ProductSupply::getFactoryId, factoryId)
                .in(ProductSupply::getProductParentId, productParentIds)
                .eq(ProductSupply::getDelFlag, ProductSupplyDelFlag.NOT_DELETE)
                .eq(ProductSupply::getSupplyChainType, supplyChainType)
                .groupBy(ProductSupply::getProductParentId)
                .list()
                .stream()
                .map(ProductSupply::getProductParentId)
                .collect(Collectors.toList());
    }
}

