<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdsdiy.productimpl.mapper.ActivityCalendarMapper">

    <resultMap type="com.sdsdiy.productimpl.entity.po.ActivityCalendar" id="ActivityCalendarMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="calendarTime" column="calendar_time" jdbcType="TIMESTAMP"/>
        <result property="week" column="week" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createUid" column="create_uid" jdbcType="INTEGER"/>
        <result property="updateUid" column="update_uid" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


</mapper>