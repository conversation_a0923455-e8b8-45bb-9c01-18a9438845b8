<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sdsdiy.productimpl.mapper.ProductionRetryDeliveryCycleMapper">

    <resultMap type="com.sdsdiy.productimpl.entity.po.ProductionRetryDeliveryCycle" id="ProductionRetryDeliveryCycleMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="productParentId" column="product_parent_id" jdbcType="INTEGER"/>
        <result property="factoryId" column="factory_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="retryCycleMin" column="retry_cycle_min" jdbcType="INTEGER"/>
        <result property="retryCycleMax" column="retry_cycle_max" jdbcType="INTEGER"/>
        <result property="createUid" column="create_uid" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
    </resultMap>


</mapper>
