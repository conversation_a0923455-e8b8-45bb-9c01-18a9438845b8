xxl:
  job:
    enable: false
rocketmq:
  listenerEnable: false
spring:
  profiles:
    active: local
  datasource:
    dynamic:
      primary: master
      seata: true
      datasource:
        master:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: *****************************************************************************************************************************************************************************
          username: read_user
          password: 5zEOdlpTyj4ARUGM
          initialSize: 5
          minIdle: 10
          maxActive: 1000
          #配置获取连接等待超时的时间
          maxWait: 60000
          #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          timeBetweenEvictionRunsMillis: 60000
          #配置一个连接在池中最小生存的时间，单位是毫秒
          minEvictableIdleTimeMillis: 300000
          #验证连接是否有效。此参数必须设置为非空字符串，下面三项设置成true才能生效
          validationQuery: SELECT 1
          #指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
          testWhileIdle: true
          #指明是否在从池中取出连接前进行检验,如果检验失败,则从池中去除连接并尝试取出另一个
          testOnBorrow: true
          #指明是否在归还到池中前进行检验
          testOnReturn: false
          #打开PSCache，并且指定每个连接上PSCache的大小
          poolPreparedStatements: true
          maxPoolPreparedStatementPerConnectionSize: 20
          #配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
          filters: stat,wall,log4j
          #通过connectProperties属性来打开mergeSql功能；慢SQL记录
          connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=1000;druid.stat.logSlowSql=true
          #合并多个DruidDataSource的监控数据
          useGlobalDataSourceStat: true
        common:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ********************************************************************************************************************************************************************************
          username: read_user
          password: 5zEOdlpTyj4ARUGM
          initialSize: 5
          minIdle: 10
          maxActive: 1000
          #配置获取连接等待超时的时间
          maxWait: 60000
          #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          timeBetweenEvictionRunsMillis: 60000
          #配置一个连接在池中最小生存的时间，单位是毫秒
          minEvictableIdleTimeMillis: 300000
          #验证连接是否有效。此参数必须设置为非空字符串，下面三项设置成true才能生效
          validationQuery: SELECT 1
          #指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
          testWhileIdle: true
          #指明是否在从池中取出连接前进行检验,如果检验失败,则从池中去除连接并尝试取出另一个
          testOnBorrow: true
          #指明是否在归还到池中前进行检验
          testOnReturn: false
          #打开PSCache，并且指定每个连接上PSCache的大小
          poolPreparedStatements: true
          maxPoolPreparedStatementPerConnectionSize: 20
          #配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
          filters: stat,wall,log4j
          #通过connectProperties属性来打开mergeSql功能；慢SQL记录
          connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=1000;druid.stat.logSlowSql=true
          #合并多个DruidDataSource的监控数据
          useGlobalDataSourceStat: true
sds:
  mq-topic-suffix: _dev