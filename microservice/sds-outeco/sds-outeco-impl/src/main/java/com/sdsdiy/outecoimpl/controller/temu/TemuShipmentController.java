package com.sdsdiy.outecoimpl.controller.temu;

import com.alibaba.fastjson.JSON;
import com.sds.platform.sdk.temu.BaseResp;
import com.sds.platform.sdk.temu.TemuServerException;
import com.sds.platform.sdk.temu.logistics.*;
import com.sds.platform.sdk.temu.order.OrderInfoSyncReq;
import com.sds.platform.sdk.temu.order.OrderInfoSyncResp;
import com.sds.platform.sdk.temu.order.OrderShipmentInfoResp;
import com.sds.platform.sdk.temu.order.OrderShipmentInfoV2Resp;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.outecoapi.api.temu.TemuShipmentApi;
import com.sdsdiy.outecodata.dto.OutecoFeignActionEnum;
import com.sdsdiy.outecodata.dto.OutecoPublishResultDto;
import com.sdsdiy.outecodata.param.LogisticsScanformCreateParam;
import com.sdsdiy.outecoimpl.service.temu.TemuShipmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class TemuShipmentController implements TemuShipmentApi {

    private final TemuShipmentService temuShipmentService;

    @Override
    public OutecoPublishResultDto<ShippingServiceGetResp> shippingServiceGet(String mallId, ShippingServiceGetReq req) {
        OutecoPublishResultDto<ShippingServiceGetResp> dto = new OutecoPublishResultDto<>();
        dto.setAction(OutecoFeignActionEnum.TEMU_ORDER_GET_SHIPPING_SERVICE.getDesc());
        try {
            ShippingServiceGetResp getRespBaseResp = temuShipmentService.shippingServiceGet(mallId, req);
            dto.setSuccess(true).setDto(getRespBaseResp);
        } catch (BusinessException businessException) {
            dto.setSuccess(false)
                    .setHttpStatus(0)
                    .setErrorMsg(businessException.getLocalizedMessage());
        } catch (TemuServerException temuServerException) {
            dto.setSuccess(false)
                    .setErrorMsg(temuServerException.getErrorMsg())
                    .setRequestId(temuServerException.getRequestId());
        }
        return dto;
    }


    @Override
    public OrderShipmentInfoResp getOrderShipmentInfo(String mallId, String mallRegion, String parentOrderSn, String orderSn) {
        return temuShipmentService.getOrderShipmentInfo(mallId, mallRegion, parentOrderSn, orderSn);
    }

    @Override
    public OrderShipmentInfoV2Resp getOrderShipmentInfoV2(String mallId, String mallRegion, String parentOrderSn, String orderSn) {
        return temuShipmentService.getOrderShipmentInfoV2(mallId, mallRegion, parentOrderSn, orderSn);
    }

    @Override
    public BaseResp<ShipmentCreateResp> createShipment(String mallId, ShipmentCreateReq req) {
        return temuShipmentService.createShipment(mallId, req);
    }

    @Override
    public BaseResp<Boolean> updateShipment(String mallId, ShipmentUpdateReq req) {
        return temuShipmentService.updateShipment(mallId, req);
    }

    @Override
    public BaseResp<ShipmentResultGetResp> shipmentResult(String mallId, String mallRegion, ShipmentResultGetReq req) {
        return temuShipmentService.shipmentResult(mallId, mallRegion, req);
    }

    @Override
    public BaseResp<Boolean> shippingTypeUpdate(String mallId, String mallRegion, String packageSn, String trackingNumber, Long shipCompanyId) {
        return temuShipmentService.shippingTypeUpdate(mallId, mallRegion, packageSn, trackingNumber, shipCompanyId);
    }

    @Override
    public BaseResp<ShipmentDocumentResp> shipmentDocumentGet(String mallId, String mallRegion, String packageSn) {
        return temuShipmentService.shipmentDocumentGet(mallId, mallRegion, packageSn);
    }

    @Override
    public BaseResp<PackageConfirmResp> packageConfirm(String mallId, PackageConfirmReq req) {
        return temuShipmentService.packageConfirm(mallId, req);
    }

    @Override
    public OutecoPublishResultDto<OrderInfoSyncResp> orderFulfillmentSync(String mallId, String mallRegion, OrderInfoSyncReq req) {
        OutecoPublishResultDto<OrderInfoSyncResp> dto = new OutecoPublishResultDto<>();
        dto.setAction(OutecoFeignActionEnum.TEMU_ORDER_FULFILLMENT_SYNC.getDesc());
        try {
            BaseResp<OrderInfoSyncResp> baseResp = temuShipmentService.orderFulfillmentSync(mallId, mallRegion, req);
            dto.setSuccess(true).setDto(baseResp.getResult());
        } catch (BusinessException businessException) {
            dto.setSuccess(false)
                    .setHttpStatus(0)
                    .setErrorMsg(businessException.getLocalizedMessage());
        } catch (TemuServerException temuServerException) {
            dto.setSuccess(false)
                    .setErrorMsg(temuServerException.getErrorMsg())
                    .setRequestId(temuServerException.getRequestId());
        }
        return dto;
    }

    @Override
    public BaseResp<String> getPickupRules(String mallId, String mallRegion, String packageSn) {
        return temuShipmentService.getPickupRules(mallId, mallRegion, packageSn);
    }

    @Override
    public BaseResp<LogisticsScanformCreateResp> logisticsScanformCreate(String mallId, String mallRegion, LogisticsScanformCreateReq req) {
        log.info("TemuShipmentController.logisticsScanformCreate mallId={}, region={}, req={}", mallId, mallRegion, JSON.toJSONString(req));
        return temuShipmentService.logisticsScanformCreate(mallId, mallRegion, req);
    }

    @Override
    public BaseResp<LogisticsScanformCreateResp> logisticsScanformCreateNew(LogisticsScanformCreateParam param) {
        log.info("TemuShipmentController.logisticsScanformCreateNew mallId={}, region={}, req={}", param.getMallId(), param.getMallRegion(), JSON.toJSONString(param));
        BaseResp<LogisticsScanformCreateResp> logisticsScanformCreateRespBaseResp = temuShipmentService.logisticsScanformCreate(param.getMallId(), param.getMallRegion(), param.getReq());
        log.info("TemuShipmentController.logisticsScanformCreateNew req={}, result={}", JSON.toJSONString(param.getReq()), JSON.toJSONString(logisticsScanformCreateRespBaseResp));
        return logisticsScanformCreateRespBaseResp;
    }
}
