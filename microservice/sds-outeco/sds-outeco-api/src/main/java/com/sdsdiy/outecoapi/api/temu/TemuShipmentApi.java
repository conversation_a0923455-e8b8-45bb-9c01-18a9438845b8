package com.sdsdiy.outecoapi.api.temu;

import com.sds.platform.sdk.temu.BaseResp;
import com.sds.platform.sdk.temu.logistics.*;
import com.sds.platform.sdk.temu.order.OrderInfoSyncReq;
import com.sds.platform.sdk.temu.order.OrderInfoSyncResp;
import com.sds.platform.sdk.temu.order.OrderShipmentInfoResp;
import com.sds.platform.sdk.temu.order.OrderShipmentInfoV2Resp;
import com.sdsdiy.outecodata.dto.OutecoPublishResultDto;
import com.sdsdiy.outecodata.param.LogisticsScanformCreateParam;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/microservice/outeco/temu/shipment")
public interface TemuShipmentApi {

    /**
     * 获取物流服务商
     */
    @PostMapping("/{mallId}/shippingServiceGet")
    OutecoPublishResultDto<ShippingServiceGetResp> shippingServiceGet(@PathVariable("mallId") String mallId,
                                                                      @RequestBody ShippingServiceGetReq req);

    /**
     * 获取子单已发货信息
     */
    @GetMapping("/{mallId}/getOrderShipmentInfo")
    OrderShipmentInfoResp getOrderShipmentInfo(@PathVariable("mallId") String mallId,
                                               @RequestParam(name = "mallRegion", defaultValue = "", required = false) String mallRegion,
                                               @RequestParam("parentOrderSn") String parentOrderSn,
                                               @RequestParam("orderSn") String orderSn);

    @GetMapping("/{mallId}/getOrderShipmentInfoV2")
    OrderShipmentInfoV2Resp getOrderShipmentInfoV2(@PathVariable("mallId") String mallId,
                                                   @RequestParam(name = "mallRegion", defaultValue = "", required = false) String mallRegion,
                                                   @RequestParam("parentOrderSn") String parentOrderSn,
                                                   @RequestParam("orderSn") String orderSn);

    /**
     * 在线下单
     */
    @PostMapping("/{mallId}/createShipment")
    BaseResp<ShipmentCreateResp> createShipment(@PathVariable("mallId") String mallId,
                                                @RequestBody ShipmentCreateReq req);

    /**
     * 更新线上物流单
     */
    @PutMapping("/{mallId}/updateShipment")
    BaseResp<Boolean> updateShipment(@PathVariable("mallId") String mallId,
                                     @RequestBody ShipmentUpdateReq req);

    /**
     * 在线发货下单结果查询
     */
    @PostMapping("/{mallId}/mallRegions/{mallRegion}/shipmentResult")
    BaseResp<ShipmentResultGetResp> shipmentResult(@PathVariable("mallId") String mallId,
                                                   @PathVariable("mallRegion") String mallRegion,
                                                   @RequestBody ShipmentResultGetReq req);

    /**
     * 在线发货修改物流
     */
    @PutMapping("/{mallId}/shippingTypeUpdate")
    BaseResp<Boolean> shippingTypeUpdate(@PathVariable("mallId") String mallId,
                                         @RequestParam("mallRegion") String mallRegion,
                                         @RequestParam("packageSn") String packageSn,
                                         @RequestParam("trackingNumber") String trackingNumber,
                                         @RequestParam("shipCompanyId") Long shipCompanyId);

    /**
     * 在线发货打印面单
     */
    @GetMapping("/{mallId}/shipmentDocumentGet")
    BaseResp<ShipmentDocumentResp> shipmentDocumentGet(@PathVariable("mallId") String mallId,
                                                       @RequestParam("mallRegion") String mallRegion,
                                                       @RequestParam("packageSn") String packageSn);

    /**
     * 确认包裹发货接口
     */
    @PostMapping("/{mallId}/packageConfirm")
    BaseResp<PackageConfirmResp> packageConfirm(@PathVariable("mallId") String mallId,
                                                @RequestBody PackageConfirmReq req);

    @PostMapping("/{mallId}/orderFulfillmentSync")
    OutecoPublishResultDto<OrderInfoSyncResp> orderFulfillmentSync(@PathVariable("mallId") String mallId
            , @RequestParam("mallRegion") String mallRegion
            , @RequestBody OrderInfoSyncReq req);

    @GetMapping("/getPickupRules")
    BaseResp<String> getPickupRules(@RequestParam("mallId") String mallId,
                                    @RequestParam("mallRegion") String mallRegion,
                                    @RequestParam("packageSn") String packageSn);

    @PostMapping("/logisticsScanformCreate")
    BaseResp<LogisticsScanformCreateResp> logisticsScanformCreate(
        @RequestParam("mallId") String mallId,
        @RequestParam("mallRegion") String mallRegion,
        @RequestBody LogisticsScanformCreateReq req
    );

    @PostMapping("/create/logisticsScanformCreate")
    BaseResp<LogisticsScanformCreateResp> logisticsScanformCreateNew(
        @RequestBody LogisticsScanformCreateParam param
    );
}
