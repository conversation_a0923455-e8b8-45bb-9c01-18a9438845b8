package com.sdsdiy.userapi.dto.crm.req;

import com.sdsdiy.common.base.entity.dto.BasePageSelect;
import com.sdsdiy.common.base.entity.dto.OrderItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/29
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class CrmCluePageReqDTO extends BasePageSelect {
    private static final long serialVersionUID = -3804148158949838319L;
    private Long updateUid;
    private List<Long> permissionsUserIds;

    @ApiModelProperty("关键词：联系人、电话、地址")
    private String keyword;

    @ApiModelProperty("商户号")
    private List<String> merchantNo;
    //主要用来查询注册后商户名和线索商户名不同的商户
    private List<Long> merchantIds;

    private List<Long> notInMerchantIds;

    @ApiModelProperty("客户来源")
    private Long sourceId;
    @ApiModelProperty("客户来源ids")
    private List<Long> sourceIds;

    @ApiModelProperty("省市区id")
    private Long regionId;
    private Integer areaType;

    private String merchantType;
    @ApiModelProperty("意向度:A、B、C、D")
    private String intentionLevel;
    @ApiModelProperty("绑定业务员id")
    private Long crmUserId;
    @ApiModelProperty("初始业务员id")
    private Long initCrmUserId;
    @ApiModelProperty("录入开始时间：yyyy-MM-dd")
    private String createStartDate;
    @ApiModelProperty("录入结束时间：yyyy-MM-dd")
    private String createEndDate;
    @ApiModelProperty("已删除线索传1")
    private Integer showDelete;
    @ApiModelProperty("删除开始时间：yyyy-MM-dd")
    private String deleteStartDate;
    @ApiModelProperty("删除结束时间：yyyy-MM-dd")
    private String deleteEndDate;
    @ApiModelProperty("线索ids")
    private List<Long> ids;
    @ApiModelProperty("会员等级")
    private String merchantMemberLevel;
    @ApiModelProperty("主营平台")
    private String mainStorePlatformCode;
    @ApiModelProperty("主营分类")
    private Long mianProductCategoryId;

    @ApiModelProperty(value = "公司规模，起始值，1、5、11、51、101")
    private Integer companySize;
    @ApiModelProperty("会员等级")
    private Integer membershipLevel;

    @ApiModelProperty(value = "是否下单")
    private Boolean payOrderState;

    @ApiModelProperty(value = "最近下单开始时间")
    private String payOrderBeginTime;

    @ApiModelProperty(value = "最近下单结束时间")
    private String payOrderEndTime;

    @ApiModelProperty(value = "最近设计开始时间")
    private String designBeginTime;

    @ApiModelProperty(value = "最近设计结束时间")
    private String designEndTime;

    @ApiModelProperty(value = "排序")
    private List<OrderItem> orderItems;


    @ApiModelProperty(value = "未跟进天数")
    private Integer traceDay;


    @ApiModelProperty(value = "未跟进天数")
    private Date traceDate;


    @ApiModelProperty(value = "客户等级")
    private String customerLevel;
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "商户注册开始时间")
    private String registerBeginTime;

    @ApiModelProperty(value = "商户注册结束时间")
    private String registerEndTime;

    public Integer getShowDelete() {
        return this.showDelete == null ? 0 : this.showDelete;
    }

    @ApiModelProperty(value = "是否有跟进 true:有跟进 false:未跟进")
    private Boolean hasTrackRecord;
    @ApiModelProperty("是否买过9.9会员：1-是")
    private Integer buyMemberV1FirstPrice;
}
