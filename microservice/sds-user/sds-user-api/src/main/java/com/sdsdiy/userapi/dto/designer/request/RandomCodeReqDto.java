package com.sdsdiy.userapi.dto.designer.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/6/21
 */
@Data
public class RandomCodeReqDto implements Serializable {

    @Size(min = 1 , max = 16)
    private String merchantNo;
    @NotBlank
    private String funcName;
    @NotBlank
    private String phone;
}
