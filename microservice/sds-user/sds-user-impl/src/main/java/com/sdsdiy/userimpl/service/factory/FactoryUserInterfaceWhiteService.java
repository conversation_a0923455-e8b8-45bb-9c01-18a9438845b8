package com.sdsdiy.userimpl.service.factory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sdsdiy.userdata.dto.factory.FactorySysUserOtherTokenDTO;
import com.sdsdiy.userdata.dto.factory.TenantWxSessionDTO;
import com.sdsdiy.userimpl.config.TencentWXTemplate;
import com.sdsdiy.userimpl.entity.po.factory.FactorySysUserOtherToken;
import com.sdsdiy.userimpl.entity.po.factory.FactoryUserInterfaceWhite;
import com.sdsdiy.userimpl.mapper.factory.FactorySysUserOtherTokenMapper;
import com.sdsdiy.userimpl.mapper.factory.FactoryUserInterfaceWhiteMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/08/26
 */
@Slf4j
@Service
@DS("master")
public class FactoryUserInterfaceWhiteService extends ServiceImpl<FactoryUserInterfaceWhiteMapper, FactoryUserInterfaceWhite> {


    public Integer findPermissionByUserId(Long userId, Long factoryId) {
        return this.lambdaQuery().eq(FactoryUserInterfaceWhite::getId, userId).eq(FactoryUserInterfaceWhite::getFactoryId, factoryId).count();
    }

}
