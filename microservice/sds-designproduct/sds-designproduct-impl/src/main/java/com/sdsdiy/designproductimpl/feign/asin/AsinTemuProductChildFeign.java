package com.sdsdiy.designproductimpl.feign.asin;

import com.sdsdiy.asinapi.api.TemuProductChildApi;
import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import org.springframework.cloud.openfeign.FeignClient;

/***
 * <AUTHOR>
 * @date 2020/7/10 10:26
 */
@FeignClient(name = "service-asin", contextId = "AsinTemuProductChildFeign", url = MicroServiceEndpointConstant.SERVICE_ASIN)
public interface AsinTemuProductChildFeign extends TemuProductChildApi {

}
