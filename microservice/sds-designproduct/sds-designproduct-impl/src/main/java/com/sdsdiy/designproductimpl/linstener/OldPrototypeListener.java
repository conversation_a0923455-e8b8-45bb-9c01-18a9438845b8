//package com.sdsdiy.designproductimpl.linstener;
//
//import cn.hutool.json.JSONUtil;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.sdsdiy.asinapi.constant.EventConstant;
//import com.sdsdiy.core.mq.MqListenerRegisterCondition;
//import com.sdsdiy.designproductimpl.po.DesignProduct;
//import com.sdsdiy.designproductimpl.service.DesignProductService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Conditional;
//
//import org.springframework.kafka.support.Acknowledgment;
//import org.springframework.kafka.support.KafkaHeaders;
//import org.springframework.messaging.handler.annotation.Header;
//import org.springframework.stereotype.Service;
//
//import java.util.Optional;
//
//@Slf4j
//@Service
//@Conditional(MqListenerRegisterCondition.class)
////kafaka (topic = EventConstant.EVENT_DESIGN_PRODUCT_PROTOTYPE_CHANGE, consumerGroup = "EVENT_DESIGN_PRODUCT_PROTOTYPE_CHANGE_GROUP", selectorExpression = "prototypeChange")
//public class OldPrototypeListener  {
//
//    @Autowired
//    DesignProductService designProductService;
//
//
//    public void onMessage(DesignProduct message) {
//        log.info("产品id:{},成品id={},商户id={}", message.getPrototypeId(), message.getId(), message.getMerchantId());
//        LambdaUpdateWrapper<DesignProduct> updateWrapper = Wrappers.<DesignProduct>lambdaUpdate().eq(DesignProduct::getId, message.getId())
//                .eq(DesignProduct::getMerchantId, message.getMerchantId())
//                .set(DesignProduct::getPrototypeId, message.getPrototypeId());
//        designProductService.update(updateWrapper);
//        updateWrapper = null;
//        message = null;
//        long freeMemory = Runtime.getRuntime().freeMemory();
//        double freeMemoryD = (freeMemory / (1024.0 * 1024.0));
//        log.info("内存啊={}", freeMemoryD);
//    }
//
//    //@KafkaListener(topics = EventConstant.EVENT_DESIGN_PRODUCT_PROTOTYPE_CHANGE, groupId = "EVENT_DESIGN_PRODUCT_PROTOTYPE_CHANGE_GROUP")
//    public void onMessage(ConsumerRecord<String, String> record, Acknowledgment ack, @Header(KafkaHeaders.RECEIVED_TOPIC) String topic) {
//        if ("prototypeChange".equals(record.key())){
//            Optional<String> message = Optional.ofNullable(record.value());
//            if (message.isPresent()) {
//                onMessage(JSONUtil.toBean(message.get(), DesignProduct.class));
//            }
//        }
//        ack.acknowledge();
//    }
//
//}
