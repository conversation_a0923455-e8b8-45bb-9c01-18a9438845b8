package com.sdsdiy.designproductimpl.controller;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.designproductdata.dto.DesignProductImportSkuRelBatchReqDTO;
import com.sdsdiy.designproductdata.dto.DesignProductImportSkuRelRespDTO;
import com.sdsdiy.designproductimpl.DesignProductServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @Author: zmy
 * @Date: 2023/12/20 18:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {DesignProductServiceApplication.class})
public class DesignProductMatchSkuControllerTest {
    @Resource
    DesignProductMatchSkuController designProductMatchSkuController;

    @Test
    public void matchSku() {
        DesignProductImportSkuRelBatchReqDTO dto = JSON.parseObject("{\"merchantId\":2577,\"relReqDTOS\":[{\"codeId\":\"reflective-3.2x12\",\"importSku\":\"reflective-3.2x12\"}],\"platform\":\"amz\"}", DesignProductImportSkuRelBatchReqDTO.class);

        List<DesignProductImportSkuRelRespDTO> designProductImportSkuRelRespDTOS = designProductMatchSkuController.matchSku(dto);
        System.out.println(JSON.toJSONString(designProductImportSkuRelRespDTOS));
    }
}