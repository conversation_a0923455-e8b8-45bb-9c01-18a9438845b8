package com.sdsdiy.paymentimpl.service.transaction.balance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentimpl.bo.BalanceOperatorBo;
import com.sdsdiy.paymentimpl.bo.TransactionOperateRelatedEntityBo;
import com.sdsdiy.paymentimpl.bo.WalletType;
import com.sdsdiy.paymentimpl.entity.MerchantBill;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.entity.Refund;
import com.sdsdiy.paymentimpl.entity.TransactionEntry;
import com.sdsdiy.paymentimpl.service.MerchantBillGenerateService;
import com.sdsdiy.paymentimpl.service.transaction.IBalanceLog;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
public abstract class AbstractBalanceOperate implements IBalanceOperate {


    public static MerchantBill generateBillByPayment(
        Payment payment,
        IBalanceLog balanceLog,
        String disposeRole
    ) {
        MerchantBill merchantBill = MerchantBillGenerateService.generateBaseBillByPayment(payment);
        merchantBill.setChangedBalance(balanceLog.getChangedBalance());
        merchantBill.setChangedGift(balanceLog.getChangedBonus());
        merchantBill.setDisposeBalanceAndBonus(disposeRole, balanceLog.getDisposeBalance(), balanceLog.getDisposeBonus());

        return merchantBill;
    }

    public static MerchantBill generateBillByRefund(
        Refund refund,
        IBalanceLog balanceLog,
        String disposeRole
    ) {
        MerchantBill merchantBill = MerchantBillGenerateService.generateBaseBillByRefund(refund);
        merchantBill.setChangedBalance(balanceLog.getChangedBalance());
        merchantBill.setChangedGift(balanceLog.getChangedBonus());
        merchantBill.setDisposeBalanceAndBonus(disposeRole, balanceLog.getDisposeBalance(), balanceLog.getDisposeBonus());

        return merchantBill;
    }


    public void generateBillForPayment(
        Payment payment,
        IBalanceLog balanceLog,
        TransactionOperateRelatedEntityBo relatedEntity
    ) {
        Integer billType = payment.getBillType();
        if (PaymentBillTypeEnum.NONE.getStatus().equals(billType)) {
            return;
        }
        if (PaymentBillTypeEnum.DIRECT.getStatus().equals(billType)) {
            MerchantBill bill = generateBillByPayment(payment, balanceLog, this.getRole());
            bill.setBillIndexOfTrade(relatedEntity.getBillIndexAndIncr());
            relatedEntity.getMerchantBills().add(bill);
        } else if (PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus().equals(billType)) {
            List<MerchantBill> bills = this.generateBillByTransactionEntryAndPayment(payment, balanceLog, relatedEntity);
            relatedEntity.getMerchantBills().addAll(bills);
        }
    }

    public void generateBillForRefund(Refund refund,
                                      IBalanceLog balanceLog,
                                      TransactionOperateRelatedEntityBo relatedEntity) {
        Integer billType = refund.getBillType();
        if (PaymentBillTypeEnum.NONE.getStatus().equals(billType)) {
            return;
        }
        if (PaymentBillTypeEnum.DIRECT.getStatus().equals(billType)) {
            MerchantBill bill = generateBillByRefund(refund, balanceLog, this.getRole());
            bill.setBillIndexOfTrade(relatedEntity.getBillIndexAndIncr());
            relatedEntity.getMerchantBills().add(bill);
        } else if (PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus().equals(billType)) {
            List<MerchantBill> bills = this.generateBillByTransactionEntryAndRefund(refund, balanceLog, relatedEntity);
            relatedEntity.getMerchantBills().addAll(bills);
        }
    }


    public List<MerchantBill> generateBillByTransactionEntryAndPayment(
        Payment payment,
        IBalanceLog balanceLog,
        TransactionOperateRelatedEntityBo relatedEntity
    ) {
        if (CollUtil.isEmpty(relatedEntity.getTransactionEntryList())) {
            return Collections.emptyList();
        }

        List<MerchantBill> bills = new ArrayList<>();

        String currentTrade = payment.getTradeNo();
        BigDecimal originBalance = balanceLog.getOriginBalance();
        BigDecimal originBonus = balanceLog.getOriginBonus();

        for (TransactionEntry entry : relatedEntity.getTransactionEntryList()) {
            if (!currentTrade.equals(entry.getTradeNo())) {
                continue;
            }
            MerchantBill merchantBill = MerchantBillGenerateService.generateBaseBillByPayment(payment);

            BigDecimal changeBalance = entry.getBalance();
            BigDecimal changeBonus = entry.getBonus();

            BigDecimal disposeBalance = BigDecimal.ZERO;
            BigDecimal disposeBonus = BigDecimal.ZERO;

            if (balanceLog.amountNotChanged()) {
                disposeBalance = originBalance;
                disposeBonus = originBonus;
            } else {
                if (TransactionEntryTypeEnum.PAY.getValue().equals(entry.getType())) {
                    disposeBalance = NumberUtil.sub(originBalance, changeBalance);
                    disposeBonus = NumberUtil.sub(originBonus, changeBonus);
                } else if (TransactionEntryTypeEnum.REFUND.getValue().equals(entry.getType())) {
                    disposeBalance = NumberUtil.add(originBalance, changeBalance);
                    disposeBonus = NumberUtil.add(originBonus, changeBonus);
                }
            }

            // 当前合并交易后是payment，但是单笔交易entry又是退款，需要变更账单类型
            if (TransactionEntryTypeEnum.REFUND.getValue().equals(entry.getType())) {
                merchantBill.setPurposeType(PurposeType.REFUND.getCode());
            }
            if (CharSequenceUtil.isNotEmpty(entry.getPurposeType())) {
                merchantBill.setPurposeType(entry.getPurposeType());
            }
            merchantBill.setChangedBalance(changeBalance);
            merchantBill.setChangedGift(changeBonus);
            merchantBill.setDisposeBalanceAndBonus(this.getRole(), disposeBalance, disposeBonus);

            merchantBill.setBizNo(entry.getBizNoForBill());
            merchantBill.setOrderName(entry.getTitle());
            merchantBill.setRemarks(entry.getRemark());

            originBalance = disposeBalance;
            originBonus = disposeBonus;

            merchantBill.setBillIndexOfTrade(relatedEntity.getBillIndexAndIncr());
            bills.add(merchantBill);
        }
        return bills;
    }


    public List<MerchantBill> generateBillByTransactionEntryAndRefund(
        Refund refund,
        IBalanceLog balanceLog,
        TransactionOperateRelatedEntityBo relatedEntity
    ) {
        if (CollUtil.isEmpty(relatedEntity.getTransactionEntryList())) {
            return Collections.emptyList();
        }

        List<MerchantBill> bills = new ArrayList<>();

        String currentTrade = refund.getTradeNo();
        BigDecimal originBalance = balanceLog.getOriginBalance();
        BigDecimal originBonus = balanceLog.getOriginBonus();

        for (TransactionEntry entry : relatedEntity.getTransactionEntryList()) {
            if (!currentTrade.equals(entry.getTradeNo())) {
                continue;
            }
            MerchantBill merchantBill = MerchantBillGenerateService.generateBaseBillByRefund(refund);

            BigDecimal changeBalance = entry.getBalance();
            BigDecimal changeBonus = entry.getBonus();

            BigDecimal disposeBalance = BigDecimal.ZERO;
            BigDecimal disposeBonus = BigDecimal.ZERO;

            if (balanceLog.amountNotChanged()) {
                disposeBalance = originBalance;
                disposeBonus = originBonus;
            } else {
                if (TransactionEntryTypeEnum.PAY.getValue().equals(entry.getType())) {
                    disposeBalance = NumberUtil.sub(originBalance, changeBalance);
                    disposeBonus = NumberUtil.sub(originBonus, changeBonus);
                } else if (TransactionEntryTypeEnum.REFUND.getValue().equals(entry.getType())) {
                    disposeBalance = NumberUtil.add(originBalance, changeBalance);
                    disposeBonus = NumberUtil.add(originBonus, changeBonus);
                }
            }

            // 当前合并交易后是payment，但是单笔交易entry又是退款，需要变更账单类型
            if (TransactionEntryTypeEnum.PAY.getValue().equals(entry.getType())) {
                merchantBill.setPurposeType(PurposeType.BUY_PRODUCT.getCode());
            }
            if (CharSequenceUtil.isNotEmpty(entry.getPurposeType())) {
                merchantBill.setPurposeType(entry.getPurposeType());
            }

            merchantBill.setChangedBalance(changeBalance);
            merchantBill.setChangedGift(changeBonus);
            merchantBill.setDisposeBalanceAndBonus(this.getRole(), disposeBalance, disposeBonus);

            merchantBill.setBizNo(entry.getBizNoForBill());
            merchantBill.setOrderName(entry.getTitle());
            merchantBill.setRemarks(entry.getRemark());

            originBalance = disposeBalance;
            originBonus = disposeBonus;
            merchantBill.setBillIndexOfTrade(relatedEntity.getBillIndexAndIncr());

            bills.add(merchantBill);
        }
        return bills;
    }


    public static void checkBalanceEnough(BalanceOperatorBo balanceOperatorBo, WalletType walletType, TransactionOperateRelatedEntityBo relatedEntityBo) {
        if (!BalanceOperator.SUBTRACT.equals(balanceOperatorBo.getOperatorEnum())) {
            return;
        }

        if (BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType().equals(walletType.calculateBalanceUsedTypeForOperate())) {
            MerchantBalanceOperateImpl.checkMerchantBalance(relatedEntityBo, walletType.getMerchantId(), balanceOperatorBo);
        } else if (BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType().equals(walletType.calculateBalanceUsedTypeForOperate())) {
            MerchantBalanceOperateImpl.checkUserAccountBalance(relatedEntityBo, walletType.getMerchantUserId(), balanceOperatorBo);
            MerchantBalanceOperateImpl.checkMerchantBalance(relatedEntityBo, walletType.getMerchantId(), balanceOperatorBo);
        } else if (BalanceUsedType.TENANT_BALANCE.getUsedType().equals(walletType.calculateBalanceUsedTypeForOperate())) {
            TenantBalanceOperateImpl.checkTenantBalance(relatedEntityBo, walletType.getTenantId(), balanceOperatorBo);
        } else if (BalanceUsedType.TENANT_DISTRIBUTION_BALANCE.getUsedType().equals(walletType.calculateBalanceUsedTypeForOperate())) {
            TenantDisBalanceOperateImpl.checkTenantDisBalance(relatedEntityBo, walletType.getDisTenantId(), walletType.getSupTenantId(), balanceOperatorBo);
        }
    }
}
