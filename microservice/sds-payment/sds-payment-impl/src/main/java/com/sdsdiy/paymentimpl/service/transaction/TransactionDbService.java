package com.sdsdiy.paymentimpl.service.transaction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.paymentapi.constant.BalanceUsedType;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import com.sdsdiy.paymentapi.constant.TradeNoEnum;
import com.sdsdiy.paymentapi.constant.TransactionPayTypeEnum;
import com.sdsdiy.paymentimpl.bo.CreateTransactionSavedEntityBo;
import com.sdsdiy.paymentimpl.bo.TransactionOperateRelatedEntityBo;
import com.sdsdiy.paymentimpl.bo.TransactionWalletEntityBo;
import com.sdsdiy.paymentimpl.entity.*;
import com.sdsdiy.paymentimpl.manager.*;
import com.sdsdiy.paymentimpl.service.MerchantBillV2Service;
import com.sdsdiy.paymentimpl.util.TradeNoUtil;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionDbService {

    private final TransactionEntryManage transactionEntryMapperManager;
    private final PaymentMapperManager paymentMapperManager;
    private final PaymentOnlineConfigMapperManager paymentOnlineConfigMapperManager;
    private final RefundMapperManager refundMapperManager;
    private final MerchantBillMapperManager merchantBillMapperManager;
    private final TenantDistributorWalletManager tenantDistributorWalletManager;
    private final MerchantMapperManager merchantMapperManager;
    private final TenantWalletMapperManager tenantWalletMapperManager;
    private final MerchantUserAccountMapperManager merchantUserAccountMapperManager;
    private final MerchantBalanceLogMapperManager merchantBalanceLogMapperManager;
    private final TenantBalanceLogMapperManager tenantBalanceLogMapperManager;
    private final TenantDistributionBalanceLogManager tenantDistributionBalanceLogManager;
    private final MerchantUserAccountLogMapperManager merchantUserAccountLogMapperManager;
    private final SubTransactionTaskManage subTransactionTaskManage;

    private void addRelatedTenantWallet(long tenantId, Map<Long, TenantWallet> tenantWalletMap) {
        if (tenantWalletMap.containsKey(tenantId)) {
            return;
        }
        TenantWallet tenantWallet = tenantWalletMapperManager.getById(tenantId);
        Assert.validateNull(tenantWallet, "租户钱包信息获取失败");
        tenantWalletMap.put(tenantId, tenantWallet);
    }

    private void addRelatedMerchant(long merchantId, Map<Long, Merchant> merchantMap) {
        if (merchantMap.containsKey(merchantId)) {
            return;
        }
        Merchant merchant = merchantMapperManager.getById(merchantId);
        Assert.validateNull(merchant, "商户信息获取失败");
        merchantMap.put(merchantId, merchant);
    }
    
    public void putWalletInfoToMap(
        List<? extends BaseTransaction> payments,
        Map<Long, MerchantUserAccount> merchantUserAccountMap,
        Map<Long, Merchant> merchantMap,
        Map<Long, TenantWallet> tenantWalletMap,
        Map<String, TenantDisWallet> tenantDisWalletMap
    ) {
        for (BaseTransaction payment : payments) {
            if (PaymentRoleEnum.TENANT.getCode().equals(payment.getSourceRole())) {
                addRelatedTenantWallet(payment.getSourceTenantId(), tenantWalletMap);
            } else if (PaymentRoleEnum.MERCHANT.getCode().equals(payment.getSourceRole())) {
                addRelatedMerchant(payment.getSourceMerchantId(), merchantMap);
            }
            if (PaymentRoleEnum.TENANT.getCode().equals(payment.getTargetRole())) {
                addRelatedTenantWallet(payment.getTargetTenantId(), tenantWalletMap);
            } else if (PaymentRoleEnum.MERCHANT.getCode().equals(payment.getTargetRole())) {
                addRelatedMerchant(payment.getTargetMerchantId(), merchantMap);
            }
            if (BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType().equals(payment.getBalanceType())) {
                if (payment.getSourceUserId() != null && payment.getSourceUserId() > 0L) {
                    MerchantUserAccount merchantUserAccount = merchantUserAccountMapperManager.findOneByMerchantUserId(payment.getSourceUserId());
                    Assert.validateNull(merchantUserAccount, "用户账户不能为空merchantUserAccount" + payment.getSourceUserId() + ", no=" + payment.getBizNo());
                    merchantUserAccountMap.put(merchantUserAccount.getMerchantUserId(), merchantUserAccount);
                } else if (payment.getTargetUserId() != null && payment.getTargetUserId() > 0L) {
                    MerchantUserAccount merchantUserAccount = merchantUserAccountMapperManager.findOneByMerchantUserId(payment.getTargetUserId());
                    Assert.validateNull(merchantUserAccount, "用户账户不能为空merchantUserAccount" + payment.getTargetUserId() + ", no=" + payment.getBizNo());
                    merchantUserAccountMap.put(merchantUserAccount.getMerchantUserId(), merchantUserAccount);
                }
            }

            if (PaymentRoleEnum.TENANT_SUP.getCode().equals(payment.getSourceRole()) && PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getTargetRole())) {
                TenantDisWallet tenantDisWallet = tenantDistributorWalletManager.getByDisAndSupTenantId(payment.getTargetTenantId(), payment.getSourceTenantId());
                Assert.validateNull(tenantDisWallet, "租户分销钱包不能为空" + payment.getTargetTenantId() + "-" + payment.getSourceTenantId());
                String key = TransactionOperateRelatedEntityBo.tenantDisWalletMapKey(tenantDisWallet.getDisTenantId(), tenantDisWallet.getSupTenantId());
                tenantDisWalletMap.put(key, tenantDisWallet);
            }

            if (PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getSourceRole()) && PaymentRoleEnum.TENANT_SUP.getCode().equals(payment.getTargetRole())) {
                TenantDisWallet tenantDisWallet = tenantDistributorWalletManager.getByDisAndSupTenantId(payment.getSourceTenantId(), payment.getTargetTenantId());
                Assert.validateNull(tenantDisWallet, "租户分销钱包不能为空" + payment.getSourceTenantId() + "-" + payment.getTargetTenantId());
                String key = TransactionOperateRelatedEntityBo.tenantDisWalletMapKey(tenantDisWallet.getDisTenantId(), tenantDisWallet.getSupTenantId());
                tenantDisWalletMap.put(key, tenantDisWallet);
            }
        }
    }


    public void attachRelatedWalletInfo(
        List<? extends BaseTransaction> payments,
        TransactionWalletEntityBo walletEntityBo
    ) {
        putWalletInfoToMap(payments, walletEntityBo.getMerchantUserAccountMap(), walletEntityBo.getMerchantMap(), walletEntityBo.getTenantWalletMap(), walletEntityBo.getTenantDisWalletMap());
//        for (BaseTransaction payment : payments) {
//            if (PaymentRoleEnum.TENANT.getCode().equals(payment.getSourceRole())) {
//                addRelatedTenantWallet(payment.getSourceTenantId(), walletEntityBo.getTenantWalletMap());
//            } else if (PaymentRoleEnum.MERCHANT.getCode().equals(payment.getSourceRole())) {
//                addRelatedMerchant(payment.getSourceMerchantId(), walletEntityBo.getMerchantMap());
//            }
//            if (PaymentRoleEnum.TENANT.getCode().equals(payment.getTargetRole())) {
//                addRelatedTenantWallet(payment.getTargetTenantId(), walletEntityBo.getTenantWalletMap());
//            } else if (PaymentRoleEnum.MERCHANT.getCode().equals(payment.getTargetRole())) {
//                addRelatedMerchant(payment.getTargetMerchantId(), walletEntityBo.getMerchantMap());
//            }
//            if (BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType().equals(payment.getBalanceType())) {
//                MerchantUserAccount merchantUserAccount = merchantUserAccountMapperManager.findOneByMerchantUserId(payment.getSourceUserId());
//                Assert.validateNull(merchantUserAccount, "用户账户不能为空merchantUserAccount" + payment.getSourceUserId());
//                walletEntityBo.getMerchantUserAccountMap().put(merchantUserAccount.getMerchantUserId(), merchantUserAccount);
//            }
//
//            if (PaymentRoleEnum.TENANT_SUP.getCode().equals(payment.getSourceRole()) && PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getTargetRole())) {
//                TenantDisWallet tenantDisWallet = tenantDistributorWalletManager.getByDisAndSupTenantId(payment.getTargetTenantId(), payment.getSourceTenantId());
//                Assert.validateNull(tenantDisWallet, "租户分销钱包不能为空" + payment.getTargetTenantId() + "-" + payment.getSourceTenantId());
//                String key = TransactionOperateRelatedEntityBo.tenantDisWalletMapKey(tenantDisWallet.getDisTenantId(), tenantDisWallet.getSupTenantId());
//                walletEntityBo.getTenantDisWalletMap().put(key, tenantDisWallet);
//            }
//
//            if (PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getSourceRole()) && PaymentRoleEnum.TENANT_SUP.getCode().equals(payment.getTargetRole())) {
//                TenantDisWallet tenantDisWallet = tenantDistributorWalletManager.getByDisAndSupTenantId(payment.getSourceTenantId(), payment.getTargetTenantId());
//                Assert.validateNull(tenantDisWallet, "租户分销钱包不能为空" + payment.getSourceTenantId() + "-" + payment.getTargetTenantId());
//                String key = TransactionOperateRelatedEntityBo.tenantDisWalletMapKey(tenantDisWallet.getDisTenantId(), tenantDisWallet.getSupTenantId());
//                walletEntityBo.getTenantDisWalletMap().put(key, tenantDisWallet);
//            }
//        }
    }

    public static Pair<String, String> genTradeNo(String parentTradeNo, Integer payType) {
        boolean mainTransaction = TransactionPayTypeEnum.MAIN.getValue().equals(payType);
        if (mainTransaction) {
            return new Pair<>("", parentTradeNo);
        }

        String subTradeNo = TradeNoUtil.generateTradeNo(TradeNoEnum.SUB);
        return new Pair<>(parentTradeNo, subTradeNo);
    }


    private List<SubTransactionTask> saveSubTransactionsForMainPayment(String mainTradeNo) {
        List<SubTransactionTask> subTransactionTasks = new ArrayList<>();

        // 查询所有子交易payment
        List<Payment> subPayments = paymentMapperManager.findAllByParentTradeNo(mainTradeNo);
        for (Payment subPayment : subPayments) {
            SubTransactionTask subTransactionTask = new SubTransactionTask()
                .setParentTradeNo(subPayment.getParentTradeNo())
                .setTradeNo(subPayment.getTradeNo())
                .setPaymentId(subPayment.getId())
                .setCreatedAt(System.currentTimeMillis())
                .setProcessCount(0)
//                .setSuccessAt(System.currentTimeMillis())
                .setRemark("主交易处理成功后自动生成");
            subTransactionTasks.add(subTransactionTask);
        }

        // 查询所有子交易refund
        List<Refund> subRefunds = refundMapperManager.findAllByParentTradeNo(mainTradeNo);
        for (Refund subRefund : subRefunds) {
            SubTransactionTask subTransactionTask = new SubTransactionTask()
                .setParentTradeNo(subRefund.getParentTradeNo())
                .setTradeNo(subRefund.getTradeNo())
                .setRefundId(subRefund.getId())
                .setCreatedAt(System.currentTimeMillis())
                .setProcessCount(0)
//                .setSuccessAt(System.currentTimeMillis())
                .setRemark("主交易处理成功后自动生成");
            subTransactionTasks.add(subTransactionTask);
        }

        // 保存子交易记录
        if (CollUtil.isNotEmpty(subTransactionTasks)) {
            subTransactionTaskManage.saveBatch(subTransactionTasks);
        }
        return subTransactionTasks;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    public void saveAfterOperateFinish(TransactionOperateRelatedEntityBo relatedEntityBo, String currentTradeNo, boolean mainTransaction, Set<String> currentSuccessTradeNos) {
        log.info("saveAfterOperateFinish currentTradeNo={}, mainTransaction={}, saved={}", currentTradeNo, mainTransaction, JSON.toJSONString(relatedEntityBo));
        boolean dbResult = false;
        if (CollUtil.isNotEmpty(relatedEntityBo.getMerchantMap())) {
            for (Merchant merchant : relatedEntityBo.getMerchantMap().values()) {
                if (relatedEntityBo.calculateBalanceUpdatedMerchantIds().contains(merchant.getId())) {
                    dbResult = merchantMapperManager.updateById(merchant);
                    Assert.validateFalse(dbResult, "更新商户余额失败");
                }
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getMerchantUserAccountMap())) {
            for (MerchantUserAccount userAccount : relatedEntityBo.getMerchantUserAccountMap().values()) {
                if (relatedEntityBo.calculateBalanceUpdatedUserAccount().contains(userAccount.getId())) {
                    dbResult = merchantUserAccountMapperManager.updateById(userAccount);
                    Assert.validateFalse(dbResult, "更新商户用户余额失败");
                }
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getTenantWalletMap())) {
            for (TenantWallet tenantWallet : relatedEntityBo.getTenantWalletMap().values()) {
                if (relatedEntityBo.calculateBalanceUpdatedTenantIds().contains(tenantWallet.getId())) {
                    dbResult = tenantWalletMapperManager.updateById(tenantWallet);
                    Assert.validateFalse(dbResult, "更新租户余额失败");
                }
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getTenantDisWalletMap())) {
            for (TenantDisWallet tenantDisWallet : relatedEntityBo.getTenantDisWalletMap().values()) {
                String key = TransactionOperateRelatedEntityBo.tenantDisWalletMapKey(tenantDisWallet.getDisTenantId(), tenantDisWallet.getSupTenantId());
                if (relatedEntityBo.calculateBalanceUpdatedDisWallets().contains(key)) {
                    dbResult = tenantDistributorWalletManager.updateById(tenantDisWallet);
                    Assert.validateFalse(dbResult, "更新租户分销余额失败");
                }
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getWantOperatePayments())) {
            for (Payment payment : relatedEntityBo.getWantOperatePayments()) {
                dbResult = paymentMapperManager.updateById(payment);
                Assert.validateFalse(dbResult, "更新支付单失败");
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getWantOperateRefunds())) {
            for (Refund refund : relatedEntityBo.getWantOperateRefunds()) {
                dbResult = refundMapperManager.updateById(refund);
                Assert.validateFalse(dbResult, "更新付款单失败");
            }
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getMerchantBills())) {
            // 根据billIndexOfTrade降序排序
            List<MerchantBill> sortedMerchantBills = relatedEntityBo.getMerchantBills().stream()
                .sorted((bill1, bill2) -> {
                    Integer index1 = bill1.getBillIndexOfTrade();
                    Integer index2 = bill2.getBillIndexOfTrade();
                    // 处理null值，null值排在最后
                    if (index1 == null && index2 == null) return 0;
                    if (index1 == null) return 1;
                    if (index2 == null) return -1;
                    // 降序排序
                    return index1.compareTo(index2);
                })
                .collect(Collectors.toList());
            for (MerchantBill bill : sortedMerchantBills) {
                MerchantBillV2Service.refreshAmountChangeRoleOfBill(bill);
                MerchantBillV2Service.calculateAmountChangeTypeOfBill(bill);
                MerchantBillV2Service.calculateAndSetRelatedMerchantTenantSaas(bill);
            }
            merchantBillMapperManager.saveBatch(sortedMerchantBills);
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getMerchantBalanceLogs())) {
            merchantBalanceLogMapperManager.saveBatch(relatedEntityBo.getMerchantBalanceLogs());
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getUserAccountLogs())) {
            merchantUserAccountLogMapperManager.saveBatch(relatedEntityBo.getUserAccountLogs());
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getTenantBalanceLogs())) {
            tenantBalanceLogMapperManager.saveBatch(relatedEntityBo.getTenantBalanceLogs());
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getTenantDisBalanceLogs())) {
            tenantDistributionBalanceLogManager.saveBatch(relatedEntityBo.getTenantDisBalanceLogs());
        }
        if (mainTransaction) {
            if (currentSuccessTradeNos.contains(currentTradeNo)) {
                List<SubTransactionTask> subTransactionTasks = saveSubTransactionsForMainPayment(currentTradeNo);
                relatedEntityBo.setSubTransactionTasks(subTransactionTasks);
            }
        }
    }


    @GlobalTransactional(rollbackFor = Exception.class)
    public CreateTransactionSavedEntityBo saveForCreateTransaction(CreateTransactionSavedEntityBo entityBo) {
        if (CollUtil.isNotEmpty(entityBo.getPaymentList())) {
            paymentMapperManager.saveBatch(entityBo.getPaymentList());
        }

        if (CollUtil.isNotEmpty(entityBo.getRefundList())) {
            refundMapperManager.saveBatch(entityBo.getRefundList());
        }

        savePaymentOnlineConfigs(entityBo.getPaymentOnlineConfigs(), entityBo.getPaymentList());
        saveTransactionEntries(entityBo.getTransactionEntries(), entityBo.getPaymentList(), entityBo.getRefundList());
        return entityBo;
    }

    private void savePaymentOnlineConfigs(List<PaymentOnlineConfig> paymentOnlineConfigs, List<Payment> paymentList) {
        if (CollUtil.isEmpty(paymentOnlineConfigs) || CollUtil.isEmpty(paymentList)) {
            return;
        }

        Map<String, Long> tradeNoToPaymentIdMap = paymentList.stream()
            .collect(Collectors.toMap(Payment::getTradeNo, Payment::getId, (existing, replacement) -> existing));

        for (PaymentOnlineConfig config : paymentOnlineConfigs) {
            Long paymentId = tradeNoToPaymentIdMap.get(config.getTradeNo());
            if (paymentId != null) {
                config.setId(paymentId);
                paymentOnlineConfigMapperManager.save(config);
            }
        }
    }

    private void saveTransactionEntries(List<TransactionEntry> transactionEntries,
                                        List<Payment> paymentList, List<Refund> refundList) {
        if (CollUtil.isEmpty(transactionEntries)) {
            return;
        }

        log.info("保存交易条目，数量: {}", transactionEntries.size());

        Map<String, String> paymentBizNoToTradeNoMap = new HashMap<>();
        Map<String, String> paymentBizNoToParentTradeNoMap = new HashMap<>();
        Map<String, String> refundBizNoToTradeNoMap = new HashMap<>();
        Map<String, String> refundBizNoToParentTradeNoMap = new HashMap<>();

        if (CollUtil.isNotEmpty(paymentList)) {
            for (Payment payment : paymentList) {
                paymentBizNoToTradeNoMap.put(payment.getBizNo(), payment.getTradeNo());
                paymentBizNoToParentTradeNoMap.put(payment.getBizNo(), payment.getParentTradeNo());
            }
        }

        if (CollUtil.isNotEmpty(refundList)) {
            for (Refund refund : refundList) {
                refundBizNoToTradeNoMap.put(refund.getBizNo(), refund.getTradeNo());
                refundBizNoToParentTradeNoMap.put(refund.getBizNo(), refund.getParentTradeNo());
            }
        }

        for (TransactionEntry entry : transactionEntries) {
            if (StrUtil.isNotBlank(entry.getPaymentBizNo())) {
                String tradeNo = paymentBizNoToTradeNoMap.get(entry.getPaymentBizNo());
                String parentTradeNo = paymentBizNoToParentTradeNoMap.get(entry.getPaymentBizNo());
                entry.setTradeNo(tradeNo);
                entry.setParentTradeNo(parentTradeNo);
            } else if (StrUtil.isNotBlank(entry.getRefundBizNo())) {
                String tradeNo = refundBizNoToTradeNoMap.get(entry.getRefundBizNo());
                String parentTradeNo = refundBizNoToParentTradeNoMap.get(entry.getRefundBizNo());
                entry.setTradeNo(tradeNo);
                entry.setParentTradeNo(parentTradeNo);
            }
        }

        transactionEntryMapperManager.saveBatch(transactionEntries);
    }

}
