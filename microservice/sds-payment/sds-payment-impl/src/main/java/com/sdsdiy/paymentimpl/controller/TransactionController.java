package com.sdsdiy.paymentimpl.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.sdsdiy.paymentapi.api.TransactionApi;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.dto.RefundDto;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentimpl.bo.TransactionCreateEntityBo;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.entity.Refund;
import com.sdsdiy.paymentimpl.manager.PaymentMapperManager;
import com.sdsdiy.paymentimpl.service.transaction.TransactionPayService;
import com.sdsdiy.paymentimpl.service.transaction.TransactionRefundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
public class TransactionController implements TransactionApi {

    private final PaymentMapperManager paymentMapperManager;
    private final TransactionPayService transactionPayService;
    private final TransactionRefundService transactionRefundService;

    @Override
    public PaymentDto queryPayment(Long paymentId) {
        Payment byId = paymentMapperManager.getById(paymentId);
        return BeanUtil.copyProperties(byId, PaymentDto.class);
    }

    @Override
    public PaymentDto createPayment(MultiTransactionCreateParam param) {
        Payment forPay = transactionPayService.createForPay(param);
        return BeanUtil.toBean(forPay, PaymentDto.class);
    }

    @Override
    public RefundDto createRefund(MultiTransactionCreateParam param) {
        Refund forRefund = transactionPayService.createForRefund(param);
        return BeanUtil.toBean(forRefund, RefundDto.class);
    }

    @Override
    public PaymentDto operateTransaction(Long paymentId) {
        Payment payment = transactionPayService.processPayment(paymentId);
        return BeanUtil.toBean(payment, PaymentDto.class);
    }

    @Override
    public RefundDto operateRefund(Long refundId) {
        Refund refund = transactionPayService.processRefund(refundId);
        return BeanUtil.toBean(refund, RefundDto.class);
    }

    @Override
    public PaymentDto refreshPaymentPaidStatus(Long paymentId) {
        Payment payment = transactionPayService.refreshPaymentPaidStatus(paymentId);
        return BeanUtil.toBean(payment, PaymentDto.class);
    }

    @Override
    public String systemRollbackPayment(String tradeNo, Long saasUserId) {
        TransactionCreateEntityBo createEntityBo = transactionRefundService.generateRefundByTradeNo(tradeNo, saasUserId);
        return JSON.toJSONString(createEntityBo);
    }

    @Override
    public void adminSystemRefundPayment(String tradeNo, Long saasUserId) {
        transactionRefundService.systemRefund(tradeNo, saasUserId);
    }
}
