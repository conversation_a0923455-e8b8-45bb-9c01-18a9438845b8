package com.sdsdiy.paymentimpl.service.pay;

import com.sdsdiy.paymentapi.constant.PaymentMethodEnum;
import com.sdsdiy.paymentapi.dto.PaymentCreateDto;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.lakala.PaidNotifyBaseParam;
import com.sdsdiy.paymentimpl.bo.CustomerPaidResultBo;
import com.sdsdiy.paymentimpl.bo.PayRelatedEntityBo;
import com.sdsdiy.paymentimpl.bo.ProcessPaidNotifyResultBo;
import com.sdsdiy.paymentimpl.bo.TransactionOperateRelatedEntityBo;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.entity.po.IPlatformPaidNotify;

public interface IPayment {

    PaymentMethodEnum getMethod();

    PaymentCreateDto create(PaymentParam param);

    void operatePaymentAfterPaid(Payment payment, PayRelatedEntityBo relatedEntity);

    void operate(Payment payment, TransactionOperateRelatedEntityBo operateRelatedEntityBo);


    CustomerPaidResultBo queryPaidResultFromPaidPlatform(Payment payment);

    IPlatformPaidNotify savePlatformNotifyParam(PaidNotifyBaseParam param);

    ProcessPaidNotifyResultBo validNotify(IPlatformPaidNotify param);

    boolean saveNotifyResult(ProcessPaidNotifyResultBo resultBo);


    default String processNotify(PaidNotifyBaseParam param) {
        IPlatformPaidNotify iPlatformPaidNotify = this.savePlatformNotifyParam(param);
        ProcessPaidNotifyResultBo resultBo = this.validNotify(iPlatformPaidNotify);
        resultBo.setPaidNotify(iPlatformPaidNotify);
        
        boolean success = this.saveNotifyResult(resultBo);
        return this.adaptNotifyReturnContent(success);
    }

    String adaptNotifyReturnContent(boolean success);

}
