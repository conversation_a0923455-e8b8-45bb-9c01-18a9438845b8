package com.sdsdiy.paymentimpl.controller;

import com.sdsdiy.common.base.valid.VersionDeprecatedUtil;
import com.sdsdiy.core.base.util.ConvertUtil;
import com.sdsdiy.paymentapi.api.PaymentApi;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.PaymentsCreateParam;
import com.sdsdiy.paymentapi.param.PaymentsPayParam;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.manager.PaymentMapperManager;
import com.sdsdiy.paymentimpl.service.PaymentCreateService;
import com.sdsdiy.paymentimpl.service.PaymentPayService;
import com.sdsdiy.paymentimpl.service.PaymentService;
import com.sdsdiy.paymentimpl.service.alipay.AlipayRefundService;
import com.sdsdiy.paymentimpl.service.pay.CustomerPaidResultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
public class PaymentController implements PaymentApi {
    
    private final PaymentMapperManager paymentMapperManager;
    private final PaymentCreateService paymentCreateService;
    private final PaymentPayService paymentPayService;
    private final CustomerPaidResultService customerPaidResultService;
    private final AlipayRefundService alipayRefundService;
    
    @Override
    public PaymentDto get(Long id) {
        Payment entity = paymentMapperManager.getById(id);
        return PaymentService.entityToDto(entity);
    }

    @Override
    public PaymentDto create(PaymentParam param) {
        VersionDeprecatedUtil.promptError("20250822");
        Payment entity = new Payment();
        BeanUtils.copyProperties(param, entity);
        paymentMapperManager.save(entity);
        return PaymentService.entityToDto(entity);
    }

    @Override
    public List<PaymentDto> batchCreate(PaymentsCreateParam param) {
        VersionDeprecatedUtil.promptError("20250822");
        return paymentCreateService.createMulti(param.getParams());
    }

    @Override
    public List<PaymentDto> batchPayAndRecordsBill(PaymentsPayParam param) {
        VersionDeprecatedUtil.promptError("20250822");
        log.info("batch pay and bill ids={}", param.getPaymentIds());
        return paymentPayService.beginPayAndRecordBills(param.getPaymentIds());
    }

    @Override
    public List<PaymentDto> batchPay(PaymentsPayParam param) {
        VersionDeprecatedUtil.promptError("20250822");
        log.info("batch pay ids={}", param.getPaymentIds());
        return paymentPayService.beginPay(param.getPaymentIds());
    }

    @Override
    public PaymentDto refreshPayStatus(Long paymentId) {
        VersionDeprecatedUtil.promptError("20250822");
        log.info("refresh pay status id={}", paymentId);
        Payment payment = customerPaidResultService.queryCustomerPaidResultAndRefreshPayment(paymentId);
        return ConvertUtil.dtoConvert(payment, PaymentDto.class);
    }

    @Override
    public void adminRefundAlipay(Long paymentId) {
        VersionDeprecatedUtil.promptError("20250822");
        alipayRefundService.refundByPaymentId(paymentId);
    }
}
