package com.sdsdiy.paymentimpl.manager;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sdsdiy.paymentimpl.bo.MerchantBalanceQueryBo;
import com.sdsdiy.paymentimpl.entity.MerchantBalanceLog;
import com.sdsdiy.paymentimpl.mapper.MerchantBalanceLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 商户的金额变动记录(MerchantBalanceLog)表服务接口
 *
 * <AUTHOR>
 * @since 2021-10-31 14:48:33
 */
@Service
@Slf4j
@DS("common")
public class MerchantBalanceLogMapperManager extends ServiceImpl<MerchantBalanceLogMapper, MerchantBalanceLog> {

    public List<MerchantBalanceLog> findAllByTime(long beginTime, long endTime) {
        return lambdaQuery()
            .ge(MerchantBalanceLog::getCreateTimestamp, beginTime)
            .le(MerchantBalanceLog::getCreateTimestamp, endTime)
            .list();
    }

    public List<MerchantBalanceLog> findAll(MerchantBalanceQueryBo queryBo) {
        return lambdaQuery()
            .ge(queryBo.getBeginTime() != null, MerchantBalanceLog::getCreateTimestamp, queryBo.getBeginTime())
            .le(queryBo.getEndTime() != null, MerchantBalanceLog::getCreateTimestamp, queryBo.getEndTime())
            .eq(queryBo.getMerchantId() != null, MerchantBalanceLog::getMerchantId, queryBo.getMerchantId())
            .list();
    }
}
