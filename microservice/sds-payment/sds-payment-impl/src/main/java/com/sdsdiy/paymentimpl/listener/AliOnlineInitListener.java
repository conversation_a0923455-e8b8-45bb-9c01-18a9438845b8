//package com.sdsdiy.paymentimpl.listener;
//
//import com.sdsdiy.common.base.constant.EventConstant;
//import com.sdsdiy.core.mq.MqListenerRegisterCondition;
//import com.sdsdiy.paymentimpl.manager.OrderFinanceExportManage;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//import org.springframework.context.annotation.Conditional;
//
//import org.springframework.kafka.support.Acknowledgment;
//import org.springframework.kafka.support.KafkaHeaders;
//import org.springframework.messaging.handler.annotation.Header;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//
///**
// * 订单ES索引文档更新消息监听
// * <AUTHOR>
// */
//@Service
//@Slf4j
//@Conditional(MqListenerRegisterCondition.class)
////kafaka (topic = OrderEventRefreshConstant.SYSTEM_AUTO_IMPORT_ORDER_UPDATE, consumerGroup = "GID_CONSUMER_ORDER_AUTO_IMPORT")
//public class AliOnlineInitListener {
//    @Resource
//    private OrderFinanceExportManage orderFinanceExportManage;
//
//
//    public void updateCartAmazonS3Image() {
//        log.info("orderFinanceManage updateCartAmazonS3Image begin");
//        orderFinanceExportManage.updateS3Image();
//        log.info("orderFinanceManage updateCartAmazonS3Image end" );
//    }
//
//    //@KafkaListener(topics = EventConstant.EVENT_ALIYUN_ONLINE, groupId = "GID_ORDER_FINANCE_EXPORT_FILE")
//    public void onMessage(ConsumerRecord<?, String> record, Acknowledgment ack, @Header(KafkaHeaders.RECEIVED_TOPIC) String topic) {
//        updateCartAmazonS3Image();
//        ack.acknowledge();
//    }
//}