package com.sdsdiy.paymentimpl.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdsdiy.paymentimpl.entity.bill.MerchantToTenantBillMonthlyAmount;

/**
 * 商户账单的金额统计(MerchantToTenantBillMonthlyAmount)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 10:10:14
 */
@DS("common")
public interface MerchantToTenantBillMonthlyAmountMapper extends BaseMapper<MerchantToTenantBillMonthlyAmount> {
}
