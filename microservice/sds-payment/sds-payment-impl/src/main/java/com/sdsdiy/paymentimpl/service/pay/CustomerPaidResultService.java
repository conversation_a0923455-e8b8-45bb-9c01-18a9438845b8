package com.sdsdiy.paymentimpl.service.pay;

import cn.hutool.core.util.ObjectUtil;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.paymentapi.constant.PaymentStatusEnum;
import com.sdsdiy.paymentimpl.bo.CustomerPaidResultBo;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.manager.PaymentMapperManager;
import com.sdsdiy.paymentimpl.service.PaymentPayFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerPaidResultService {

    private final PaymentMapperManager paymentMapperManager;


    public Payment queryCustomerPaidResultAndRefreshPayment(long paymentId) {
        Payment payment = paymentMapperManager.getById(paymentId);
        if (ObjectUtil.isNull(payment)) {
            throw new BusinessException("Payment不存在，请校验数据");
        }
        if (!PaymentStatusEnum.WAIT.getStatus().equals(payment.getStatus())) {
            return payment;
        }

        IPayment payImpl = PaymentPayFactory.getPayImpl(payment.getMethod());
        if (payImpl == null) {
            throw new BusinessException("未知的支付类型");
        }

        CustomerPaidResultBo customerPaidResultBo = payImpl.queryPaidResultFromPaidPlatform(payment);
        return refreshPaymentStatusByCustomerPayResult(customerPaidResultBo, payment);
    }


    private Payment refreshPaymentStatusByCustomerPayResult(CustomerPaidResultBo customerPaidResultBo, Payment payment) {
        if (customerPaidResultBo == null || Boolean.FALSE.equals(customerPaidResultBo.getPaid())) {
            return payment;
        }

        payment.setStatus(PaymentStatusEnum.PAID.getStatus());
        payment.setPayTime(System.currentTimeMillis());
        payment.setPayChannel(customerPaidResultBo.getPayChannel());
        boolean dbResult = paymentMapperManager.updateById(payment);
        Assert.validateFalse(dbResult, "更新支付订单的支付状态失败，请稍后再试");
        return payment;
    }
}
