package com.sdsdiy.paymentimpl.entity;


import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentimpl.bo.BalanceOperatorBo;
import com.sdsdiy.paymentimpl.bo.WalletType;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "payment")
@Accessors(chain = true)
public class Payment extends BaseTransaction {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String bizNo;
    private String bizNoForBill;
    private String tradeNo;
    private String parentTradeNo;


    public String getRealBizNoForBill() {
        return CharSequenceUtil.isEmpty(this.bizNoForBill) ? this.bizNo : this.bizNoForBill;
    }

    private String method;

    private String title;

    private String imgUrl;

    private Integer status;
    
    private Long createdTime;
    private Long payTime;
    private Long expiredTime;

    private BigDecimal totalAmount;
    private BigDecimal balance;
    private BigDecimal bonus;

    private String sourceRole;
    private Long sourceTenantId;
    private Long sourceMerchantId;
    private Long sourceUserId;
    private Boolean sourceOpenOnlinePay;
    private Boolean targetOpenOnlinePay;
    private Long targetTenantId;
    private Long targetMerchantId;
    private String targetRole;

    @TableField("merchant_balance_used_type")
    private Integer balanceType;

    private Integer balanceOperateFinish;
    private Integer billType;

    private String purposeType;
    private String detailPurpose;

    private Long operateUserId;
    private String operateRole;

    /**
     * 操作对象角色
     * {@link com.sdsdiy.paymentapi.constant.PaymentRoleEnum}
     */
    private String operateTargetRole;

    /*** 操作对象角色ID */
    private Long operateTargetRoleId;

    @Version
    private Integer lockVersion;

    private String appId;

    private String remark;

    @ApiModelProperty("支付渠道，比如微信，支付宝，银联")
    private String payChannel;

    @ApiModelProperty("第三方订单号，比如支付宝订单号，拉卡拉订单号")
    private String alipayTradeNo;

    @ApiModelProperty("保存第三方应用的信息")
    @TableField(exist = false)
    private PaymentOnlineConfig paymentOnlineConfig;


    @Override
    public void setTargetOpenOnlinePay(Boolean targetOpenOnlinePay) {
        this.targetOpenOnlinePay = targetOpenOnlinePay;
    }

    @Override
    public TransactionEntryTypeEnum getTransactionPayType() {
        return TransactionEntryTypeEnum.PAY;
    }

    @Override
    public void setSourceOpenOnlinePay(Boolean sourceOpenOnlinePay) {
        this.sourceOpenOnlinePay = sourceOpenOnlinePay;
    }

    public boolean needCheckPaidStatus() {
        if (!PaymentStatusEnum.WAIT.getStatus().equals(this.status)) {
            return false;
        }
        if (PaymentMethodEnum.ALI_PAY.getCode().equals(this.method)
            && PaymentMethodEnum.LAKALA.getCode().equals(this.method)) {
            return false;
        }

        return true;
    }

    public boolean isMainTransaction() {
        return CharSequenceUtil.isEmpty(this.getParentTradeNo());
    }


    public void setTradeNos(Pair<String, String> tradeNos) {
        this.parentTradeNo = tradeNos.getKey();
        this.tradeNo = tradeNos.getValue();
    }

    public BalanceOperatorBo generateBalanceOperatorBo() {
        BalanceOperatorBo balanceOperatorBo = new BalanceOperatorBo()
            .setChangeBalance(this.getBalance())
            .setTradeNo(this.getTradeNo())
            .setOperatorEnum(BalanceOperator.SUBTRACT)
            .setBillType(this.getBillType())
            .setChangeGiftMoney(this.getBonus());
        if (PaymentMethodEnum.OFFLINE.getCode().equals(this.getMethod())) {
            balanceOperatorBo.setOperatorEnum(BalanceOperator.NO_CHANGE);
            return balanceOperatorBo;
        }
        if (!PaymentMethodEnum.BALANCE.getCode().equals(this.getMethod())) {
            balanceOperatorBo.setOperatorEnum(BalanceOperator.NO_CHANGE);
        }
        if (PurposeType.RECHARGE.getCode().equals(this.getPurposeType())) {
            balanceOperatorBo.setOperatorEnum(BalanceOperator.ADD);
        }

        return balanceOperatorBo;
    }


    public WalletType generateWalletType() {
        WalletType walletType = new WalletType()
            .setWalletType(this.getSourceRole())
            .setTenantId(this.getSourceTenantId())
            .setMerchantId(this.getSourceMerchantId())
            .setMerchantUserId(this.getSourceUserId());
        if (PaymentRoleEnum.TENANT_DIS.getCode().equals(this.getSourceRole())) {
            walletType.setTenantId(0L);
            walletType.setMerchantId(0L);
            walletType.setMerchantUserId(0L);
            walletType.setDisTenantId(this.getSourceTenantId());
            walletType.setSupTenantId(this.getTargetTenantId());
        }
        return walletType;
    }


    @Override
    public Long getTargetUserId() {
        return 0L;
    }
}
