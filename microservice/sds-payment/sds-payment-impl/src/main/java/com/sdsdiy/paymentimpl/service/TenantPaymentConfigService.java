package com.sdsdiy.paymentimpl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sds.platform.sdk.lakala.LakalaHttpClient;
import com.sds.platform.sdk.lakala.LakalaServerException;
import com.sds.platform.sdk.lakala.order.OrderBaseRespBody;
import com.sds.platform.sdk.lakala.order.create.OrderCreateRespData;
import com.sds.platform.sdk.lakala.util.LakalaAmountUtil;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.valid.ManualValidUtil;
import com.sdsdiy.core.base.util.StringUtils;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.paymentapi.constant.PaymentMethodEnum;
import com.sdsdiy.paymentapi.dto.*;
import com.sdsdiy.paymentapi.param.*;
import com.sdsdiy.paymentimpl.bo.OpenOnlinePayRelatedEntityBo;
import com.sdsdiy.paymentimpl.entity.*;
import com.sdsdiy.paymentimpl.entity.po.LakalaAppIdApiPrivateKeyRel;
import com.sdsdiy.paymentimpl.feign.TenantPodFeign;
import com.sdsdiy.paymentimpl.manager.*;
import com.sdsdiy.paymentimpl.manager.lakala.LakalaAppIdApiPrivateKeyRelManager;
import com.sdsdiy.paymentimpl.service.lakala.LakalaClientConfService;
import com.sdsdiy.paymentimpl.service.lakala.LakalaService;
import com.sdsdiy.paymentimpl.service.lakala.LklOrderService;
import com.sdsdiy.paymentimpl.util.TradeNoUtil;
import com.sdsdiy.userapi.dto.base.MerchantSysUserRespDto;
import com.sdsdiy.userapi.dto.tenant.TenantMerchantOptionRespDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TenantPaymentConfigService {

    @Autowired
    private TenantAlipayConfMapperManager tenantAlipayConfMapperManager;

    @Autowired
    private TenantLakalaConfMapperManager tenantLakalaConfMapperManager;

    @Autowired
    private TenantBankCardMapperManager tenantBankCardMapperManager;

    @Autowired
    private TenantPaymentConfigDbService tenantPaymentConfigDbService;

    @Autowired
    private FreePayMerchantMapperManager freePayMerchantMapperManager;

    @Autowired
    private TenantPodFeign tenantPodFeign;

    @Autowired
    private TenantWalletMapperManager tenantWalletMapperManager;

    @Autowired
    private MerchantMapperManager merchantMapperManager;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private LakalaClientConfService lakalaClientConfService;
    @Autowired
    private LakalaService lakalaService;
    @Autowired
    private LklOrderService lklOrderService;
    @Autowired
    private LakalaAppIdApiPrivateKeyRelManager lakalaAppIdApiPrivateKeyRelManager;

    private void checkConf(TenantOnlinePayParam param) {
        if (ObjectUtil.isNotNull(param.getBankCard())) {
            ManualValidUtil.check(param.getBankCard());
        }
        if (ObjectUtil.isNotNull(param.getAlipayConf())) {
            ManualValidUtil.check(param.getAlipayConf());
        }
    }

    private void checkOnlineConf(TenantOnlinePayConfParam param) {
        if (PaymentMethodEnum.LAKALA.getCode().equals(param.getPayType())) {
            ManualValidUtil.check(param.getLakalaConfs());
        } else if (PaymentMethodEnum.ALI_PAY.getCode().equals(param.getPayType())) {
            ManualValidUtil.check(param.getAlipayConf());
        } else {
            Assert.wrong("支付方式异常");
        }


    }

    /**
     * 获取免支付配置所有的商户名字
     *
     * @param tenantId
     * @return
     */
    public List<FreePayMerchantNameDto> getTotalMerchantName(long tenantId) {
        List<FreePayMerchantNameDto> merchantNameDtos = new ArrayList<>();

        List<TenantMerchantOptionRespDto> tenantMerchantOptionRespDtos = tenantPodFeign.merchantOption(tenantId, "", "merchantSysUsers");
        for (TenantMerchantOptionRespDto respDto : tenantMerchantOptionRespDtos) {
            FreePayMerchantNameDto merchantNameDto = new FreePayMerchantNameDto();
            merchantNameDto.setMerchantId(respDto.getId());
            merchantNameDto.setMerchantName(respDto.getName());

            List<FreePayUserNameDto> userNameDtos = new ArrayList<>();
            for (MerchantSysUserRespDto userRespDto : respDto.getMerchantSysUsers()) {
                FreePayUserNameDto userNameDto = FreePayUserNameDto.builder()
                        .merchantId(userRespDto.getMerchantId())
                        .userId(userRespDto.getId())
                        .userName(userRespDto.getUsername())
                        .build();
                userNameDtos.add(userNameDto);
            }

            merchantNameDto.setUsers(userNameDtos);
            merchantNameDtos.add(merchantNameDto);
        }

        return merchantNameDtos;
    }

    /**
     * 获取当前的在线支付配置
     *
     * @param tenantId
     * @return
     */
    public TenantOnlinePayDto getConfig(long tenantId) {
        OpenOnlinePayRelatedEntityBo relatedEntityBo = new OpenOnlinePayRelatedEntityBo();
        relatedEntityBo.setTenantId(tenantId);

        TenantWallet tenantWallet = tenantWalletMapperManager.getById(tenantId);
        relatedEntityBo.setOpened(tenantWallet.getOpenOnlinePay().equals(BasePoConstant.YES));
        relatedEntityBo.setMerchantPayType(tenantWallet.getMerchantPayType());

        TenantAlipayConf alipayConf = tenantAlipayConfMapperManager.findOneByTenantId(tenantId);
        if (ObjectUtil.isNotNull(alipayConf)) {
            relatedEntityBo.setAlipayConf(alipayConf);
        }
        List<TenantLakalaConf> lakalaConfs = tenantLakalaConfMapperManager.findListByTenantId(tenantId);
        if (CollUtil.isNotEmpty(lakalaConfs)) {
            relatedEntityBo.setLakalaConfs(lakalaConfs);
        }
        TenantBankCard bankCard = tenantBankCardMapperManager.findOneByTenant(tenantId);
        if (ObjectUtil.isNotNull(bankCard)) {
            relatedEntityBo.setBankCard(bankCard);
        }

        List<FreePayMerchant> freePayMerchants = freePayMerchantMapperManager.findAllByTenantId(tenantId);
        relatedEntityBo.setFreePayMerchants(freePayMerchants);

        return genOnlinePayDtoByChangedEntity(relatedEntityBo);
    }

    /**
     * 租户打开在线支付配置
     *
     * @param param
     * @return
     */
    public TenantOnlinePayDto openOnlinePay(TenantOnlinePayParam param) {
        Assert.wrong("方法禁用");
        return null;
//        long tenantId = param.getTenantId();
//        TenantWallet tenantWallet = tenantWalletMapperManager.getById(tenantId);
//        boolean alreadyOpen = tenantWallet.getOpenOnlinePay().equals(BasePoConstant.YES);
//        boolean wantOpen = ObjectUtil.isNotNull(param.getAlipayConf()) && Boolean.TRUE.equals(param.getAlipayConf().getEnable());
//        if (!wantOpen && alreadyOpen) {
//            throw new BusinessException("支付状态异常");
//        }
//
//        checkConf(param);
//
//        TenantAlipayConf changedAlipayConf = generateChangedAlipayConfig(tenantId, param.getAlipayConf());
//        TenantBankCard changedBankCard = generateChangedBankCards(tenantId, param.getBankCard());
//        List<FreePayMerchant> changedFreePayMerchants = genChangedFreePayMerchants(tenantId, param.getFreePayMerchants());
//
//        OpenOnlinePayRelatedEntityBo relatedEntityBo = new OpenOnlinePayRelatedEntityBo();
//        relatedEntityBo.setTenantId(tenantId);
//        relatedEntityBo.setOperateUserId(param.getOperateUserId());
//        relatedEntityBo.setBankCard(changedBankCard);
//        relatedEntityBo.setAlipayConf(changedAlipayConf);
//        relatedEntityBo.setFreePayMerchants(changedFreePayMerchants);
//
//        relatedEntityBo.setTenantWallet(tenantWallet);
//
//        if (!alreadyOpen && wantOpen) {
//            relatedEntityBo.setMerchants(merchantMapperManager.findAllByTenantId(tenantId));
//            tenantPaymentConfigDbService.updateWhenFirstOpenOnlinePay(relatedEntityBo);
//        } else {
//            tenantPaymentConfigDbService.updateConfWhenNotFirstOpen(relatedEntityBo);
//        }
//
//        return genOnlinePayDtoByChangedEntity(relatedEntityBo);
    }


    /**
     * 租户打开在线支付配置 支付宝或拉卡拉
     *
     * @param param
     * @return
     */
    public TenantOnlinePayDto updateOnlinePay(TenantOnlinePayConfParam param) {
        long tenantId = param.getTenantId();
        TenantWallet tenantWallet = tenantWalletMapperManager.getById(tenantId);
        boolean alreadyOpen = tenantWallet.getOpenOnlinePay().equals(BasePoConstant.YES);
        boolean wantOpen = ObjectUtil.isNotNull(param.getEnable()) && Boolean.TRUE.equals(param.getEnable());
        if (!wantOpen && alreadyOpen) {
            throw new BusinessException("支付状态异常");
        }
        if (wantOpen || alreadyOpen) {
            checkOnlineConf(param);
        }


        OpenOnlinePayRelatedEntityBo relatedEntityBo = new OpenOnlinePayRelatedEntityBo();
        relatedEntityBo.setTenantId(tenantId);
        relatedEntityBo.setOperateUserId(param.getOperateUserId());
        if (PaymentMethodEnum.LAKALA.getCode().equals(param.getPayType())) {
            List<TenantOnlinePayLakalaConfParam> lakalaConfs = param.getLakalaConfs();
            validateLakalaConf(lakalaConfs);
            lakalaConfs.forEach(lakalaConf -> lakalaConf.setOperatorUid(param.getOperatorUid()));
            List<TenantLakalaConf> changedLakalaConfs = generateChangedLakalaConfig(tenantId, lakalaConfs);
            relatedEntityBo.setLakalaConfs(changedLakalaConfs);
        } else if (PaymentMethodEnum.ALI_PAY.getCode().equals(param.getPayType())) {
            param.getAlipayConf().setOperatorUid(param.getOperatorUid());
            TenantAlipayConf changedAlipayConf = generateChangedAlipayConfig(tenantId, param.getAlipayConf());
            relatedEntityBo.setAlipayConf(changedAlipayConf);
        } else {
            Assert.wrong("支付方式异常");
        }
        tenantWallet.setMerchantPayType(param.getPayType());


        relatedEntityBo.setTenantWallet(tenantWallet);

        if (!alreadyOpen && wantOpen) {
            relatedEntityBo.setMerchants(merchantMapperManager.findAllByTenantId(tenantId));
            tenantPaymentConfigDbService.updateWhenFirstOpenOnlinePay(relatedEntityBo);
        } else {
            tenantPaymentConfigDbService.updateConfWhenNotFirstOpen(relatedEntityBo);
        }

        return genOnlinePayDtoByChangedEntity(relatedEntityBo);
    }

    private void validateLakalaConf(List<TenantOnlinePayLakalaConfParam> lakalaConfParams) {
        Assert.validateEmpty(lakalaConfParams, "至少保留一个账号");
        boolean duplicateMerchantNo = lakalaConfParams.size() > lakalaConfParams.stream().map(TenantOnlinePayLakalaConfParam::getMerchantNo).collect(Collectors.toSet()).size();
        boolean duplicateTermNo = lakalaConfParams.size() >  lakalaConfParams.stream().map(TenantOnlinePayLakalaConfParam::getTermNo).collect(Collectors.toSet()).size();
        Assert.validateTrue(duplicateMerchantNo, "不可设置一样的merchantNo");
        Assert.validateTrue(duplicateTermNo, "不可设置一样的termNo");
        for (TenantOnlinePayLakalaConfParam lakalaConfParam : lakalaConfParams) {
            if (StrUtil.isBlank(lakalaConfParam.getMerchantNo())
                    || StrUtil.isBlank(lakalaConfParam.getTermNo())
                    || null == lakalaConfParam.getTermNo()) {
                throw new BusinessException("存在未填写的收款账号");
            }
        }
        int sum = lakalaConfParams.stream().mapToInt(TenantOnlinePayLakalaConfParam::getWeight).sum();
        Assert.validateTrue(sum != 100, "收款比例之和不为100");

    }


    /**
     * 租户打开在线支付配置 支付宝或拉卡拉
     *
     * @param param
     * @return
     */
    public TenantOnlinePayDto updateFreeMerchants(TenantOnlinePayParam param) {
        long tenantId = param.getTenantId();
        List<FreePayMerchant> changedFreePayMerchants = genChangedFreePayMerchants(tenantId, param.getFreePayMerchants());
        OpenOnlinePayRelatedEntityBo relatedEntityBo = new OpenOnlinePayRelatedEntityBo();
        relatedEntityBo.setTenantId(tenantId);
        relatedEntityBo.setOperateUserId(param.getOperateUserId());
        relatedEntityBo.setFreePayMerchants(changedFreePayMerchants);
        tenantPaymentConfigDbService.updateFreePayMerchants(relatedEntityBo);

        return genOnlinePayDtoByChangedEntity(relatedEntityBo);
    }

    /**
     * 租户打开在线支付配置 支付宝或拉卡拉
     *
     * @param param
     * @return
     */
    public TenantOnlinePayDto updateBandCard(TenantBankCardParam param) {
        long tenantId = param.getTenantId();
        ManualValidUtil.check(param);
        TenantBankCard changedBankCard = generateChangedBankCards(tenantId, param);
        OpenOnlinePayRelatedEntityBo relatedEntityBo = new OpenOnlinePayRelatedEntityBo();
        relatedEntityBo.setTenantId(tenantId);
        relatedEntityBo.setOperateUserId(param.getOperateUserId());
        relatedEntityBo.setBankCard(changedBankCard);
        tenantPaymentConfigDbService.updateBankCard(relatedEntityBo);

        return genOnlinePayDtoByChangedEntity(relatedEntityBo);
    }


    /**
     * 根据相关的数据生成在线支付dto
     *
     * @param relatedEntityBo
     * @return
     */
    private TenantOnlinePayDto genOnlinePayDtoByChangedEntity(OpenOnlinePayRelatedEntityBo relatedEntityBo) {
        TenantOnlinePayDto onlinePayDto = new TenantOnlinePayDto();

        List<FreePayMerchantDto> freePayMerchantDtos = generateFreePayMerchantDto(relatedEntityBo.getTenantId(), relatedEntityBo.getFreePayMerchants());
        onlinePayDto.setFreePayMerchants(freePayMerchantDtos);
        onlinePayDto.setOpened(relatedEntityBo.getOpened());
        onlinePayDto.setMerchantPayType(relatedEntityBo.getMerchantPayType());

        if (ObjectUtil.isNotNull(relatedEntityBo.getAlipayConf())) {
            TenantAlipayConfDto alipayConfDto = new TenantAlipayConfDto();
            BeanUtils.copyProperties(relatedEntityBo.getAlipayConf(), alipayConfDto);
            onlinePayDto.setAlipayConf(alipayConfDto);
        }
        if (CollUtil.isNotEmpty(relatedEntityBo.getLakalaConfs())) {
            List<TenantLakalaConfDto> tenantLakalaConfDtos = BeanUtil.copyToList(relatedEntityBo.getLakalaConfs(), TenantLakalaConfDto.class);
            onlinePayDto.setLakalaConfs(tenantLakalaConfDtos);
        }

        if (ObjectUtil.isNotNull(relatedEntityBo.getBankCard())) {
            TenantBankCardDto bankCardDto = new TenantBankCardDto();
            BeanUtils.copyProperties(relatedEntityBo.getBankCard(), bankCardDto);
            onlinePayDto.setBankCard(bankCardDto);
        }
        return onlinePayDto;
    }

    /**
     * 修改免支付配置时，生成变更的数据
     *
     * @param tenantId
     * @param params
     * @return
     */
    private List<FreePayMerchant> genChangedFreePayMerchants(long tenantId, List<FreePayMerchantCreateParam> params) {
        List<FreePayMerchant> oldFreePayMerchant = freePayMerchantMapperManager.findAllByTenantId(tenantId);
        oldFreePayMerchant.forEach(item -> item.setDeleted(BasePoConstant.YES));
        List<FreePayMerchant> changedFreePayMerchants = generateFreePayMerchantEntitiesByCreateParams(params);
        if (CollUtil.isNotEmpty(oldFreePayMerchant)) {
            changedFreePayMerchants.addAll(oldFreePayMerchant);
        }
        return changedFreePayMerchants;
    }

    /**
     * 修改银行卡配置时，生成变更的数据
     *
     * @param tenantId
     * @param param
     * @return
     */
    private TenantBankCard generateChangedBankCards(long tenantId, TenantBankCardParam param) {
        if (ObjectUtil.isNull(param)) {
            return null;
        }
        TenantBankCard olfConf = tenantBankCardMapperManager.findOneByTenant(tenantId);
        if (ObjectUtil.isNotNull(olfConf)) {
            olfConf.setBankAddress(param.getBankAddress());
            olfConf.setBankName(param.getBankName());
            olfConf.setContactPhone(param.getContactPhone());
            olfConf.setCompanyName(param.getCompanyName());
            olfConf.setCardNo(param.getCardNo());
        } else {
            olfConf = new TenantBankCard();
            BeanUtils.copyProperties(param, olfConf);
            olfConf.setCreateUid(param.getOperatorUid());
        }

        olfConf.setUpdateUid(param.getOperatorUid());
        olfConf.setEnable(param.getEnable());
        return olfConf;
    }

    /**
     * 生成修改的支付宝配置
     *
     * @param tenantId
     * @param param
     * @return
     */
    private TenantAlipayConf generateChangedAlipayConfig(long tenantId, TenantAlipayConfParam param) {
        if (ObjectUtil.isNull(param)) {
            return null;
        }

        TenantAlipayConf alipayConf = tenantAlipayConfMapperManager.findOneByTenantId(tenantId);

        if (ObjectUtil.isNotNull(alipayConf)) {
            alipayConf.setAlipayPublicKey(param.getAlipayPublicKey());
            if (StrUtil.isNotEmpty(param.getMerchantPrivateKey())) {
                alipayConf.setMerchantPrivateKey(param.getMerchantPrivateKey());
            }
            alipayConf.setAppId(param.getAppId());
        } else {
            alipayConf = new TenantAlipayConf();
            BeanUtils.copyProperties(param, alipayConf);
            alipayConf.setTenantId(tenantId);
            alipayConf.setCreateUid(param.getOperatorUid());

        }

        alipayConf.setUpdateUid(param.getOperatorUid());
        alipayConf.setEnable(param.getEnable());

        return alipayConf;
    }

    /**
     * 生成修改的支付宝配置
     *
     * @param tenantId
     * @param params
     * @return
     */
    private List<TenantLakalaConf> generateChangedLakalaConfig(long tenantId, List<TenantOnlinePayLakalaConfParam> params) {
        if (CollUtil.isEmpty(params)) {
            return null;
        }
        List<TenantLakalaConf> newTenantLakalaConfs = new ArrayList<>();
        List<LakalaAppIdApiPrivateKeyRel> relList = lakalaAppIdApiPrivateKeyRelManager.findAll();
        Map<String, LakalaAppIdApiPrivateKeyRel> relMap = relList.stream().collect(Collectors.toMap(LakalaAppIdApiPrivateKeyRel::getAppId, Function.identity(), (v1, v2) -> v1));
        for (TenantOnlinePayLakalaConfParam param : params) {
            Assert.validateTrue(!relMap.containsKey(param.getAppId()), param.getAppId() + "appId无效");
        }

        Map<String, TenantOnlinePayLakalaConfParam> paramMap = params.stream().collect(Collectors.toMap(TenantOnlinePayLakalaConfParam::getMerchantNo, Function.identity(), (v1, v2) -> v1));
        
        List<TenantLakalaConf> tenantLakalaConfs = tenantLakalaConfMapperManager.findListByTenantId(tenantId);
        Map<String, TenantLakalaConf> confMap = tenantLakalaConfs.stream().collect(Collectors.toMap(TenantLakalaConf::getMerchantNo, Function.identity()));

        for (TenantOnlinePayLakalaConfParam param : params) {
            TenantLakalaConf tenantLakalaConf = confMap.get(param.getMerchantNo());
            if (ObjectUtil.isNotNull(tenantLakalaConf)) {
                if (param.getId() != null) {
                    tenantLakalaConf.setId(param.getId());
                }
                tenantLakalaConf.setMerchantNo(param.getMerchantNo());
                tenantLakalaConf.setTermNo(param.getTermNo());
                tenantLakalaConf.setAppId(param.getAppId());
                tenantLakalaConf.setApiPrivateKey(relMap.get(param.getAppId()).getApiPrivateKey());
                tenantLakalaConf.setSerialNo(relMap.get(param.getAppId()).getSerialNo());
                tenantLakalaConf.setWeight(param.getWeight());
            } else {
                tenantLakalaConf = new TenantLakalaConf();
                tenantLakalaConf.setTenantId(tenantId);
                tenantLakalaConf.setCreateUid(param.getOperatorUid());
                tenantLakalaConf.setApiPrivateKey(relMap.get(param.getAppId()).getApiPrivateKey());
                tenantLakalaConf.setSerialNo(relMap.get(param.getAppId()).getSerialNo());
                BeanUtils.copyProperties(param, tenantLakalaConf);
            }
            newTenantLakalaConfs.add(tenantLakalaConf);

        }

        return newTenantLakalaConfs;
    }

    /**
     * 根据参数生成新的entity
     *
     * @param createParams
     * @return
     */
    private List<FreePayMerchant> generateFreePayMerchantEntitiesByCreateParams(List<FreePayMerchantCreateParam> createParams) {
        if (CollUtil.isEmpty(createParams)) {
            return new ArrayList<>();
        }
        List<FreePayMerchant> changedFreePayMerchants = new ArrayList<>();
        for (FreePayMerchantCreateParam freePayParam : createParams) {
            FreePayMerchant freePayMerchant = FreePayMerchant.builder()
                    .merchantId(freePayParam.getMerchantId())
                    .tenantId(freePayParam.getTenantId())
                    .userId(freePayParam.getUserId())
                    .createUserId(freePayParam.getCreateUserId())
                    .deleted(BasePoConstant.NO)
                    .enable(freePayParam.getEnable())
                    .build();
            changedFreePayMerchants.add(freePayMerchant);
        }
        return changedFreePayMerchants;
    }

    /**
     * 生成免支付配置
     *
     * @param tenantId
     * @param freePayMerchants
     * @return
     */
    private List<FreePayMerchantDto> generateFreePayMerchantDto(long tenantId, List<FreePayMerchant> freePayMerchants) {
        if (CollUtil.isEmpty(freePayMerchants)) {
            return Collections.emptyList();
        }

        List<FreePayMerchantDto> dtos = new ArrayList<>();
        List<TenantMerchantOptionRespDto> tenantMerchantOptionRespDtos = tenantPodFeign.merchantOption(tenantId, "", "merchantSysUsers");
        Map<Long, String> merchantIdToNameMap = new HashMap<>(4);
        Map<Long, String> userIdToNameMap = new HashMap<>(16);
        tenantMerchantOptionRespDtos.forEach(item -> {
            merchantIdToNameMap.put(item.getId(), item.getName());
            for (MerchantSysUserRespDto merchantSysUser : item.getMerchantSysUsers()) {
                userIdToNameMap.put(merchantSysUser.getId(), merchantSysUser.getUsername());
            }
        });

        long merchantId = 0L;
        long userId = 0L;
        String userName = "";
        String merchantName = "";
        for (FreePayMerchant freePayMerchant : freePayMerchants) {
            if (freePayMerchant.getDeleted().equals(BasePoConstant.YES)) {
                continue;
            }

            // 名字为null是被删除的情况
            merchantId = freePayMerchant.getMerchantId();
            merchantName = merchantIdToNameMap.get(merchantId);
            if (ObjectUtil.isNull(merchantName)) {
                continue;
            }

            userId = freePayMerchant.getUserId();
            userName = "";
            if (userId != 0) {
                userName = userIdToNameMap.get(userId);
                if (ObjectUtil.isNull(userName)) {
                    continue;
                }
            }

            FreePayMerchantDto dto = FreePayMerchantDto.builder()
                    .merchantName(merchantName)
                    .userName(userName)
                    .merchantId(freePayMerchant.getMerchantId())
                    .userId(freePayMerchant.getUserId())
                    .tenantId(freePayMerchant.getTenantId())
                    .enable(freePayMerchant.getEnable())
                    .build();
            dtos.add(dto);
        }
        return dtos;
    }

    /**
     * 验证商户账号有效性
     *
     * @param param
     * @param tenantId
     */
    public String validateMerchantNo(TenantOnlinePayConfParam param, Long tenantId) {
        String tradeNo = TradeNoUtil.getUniqTradeNo();
        long lakalaSupportAmount = LakalaAmountUtil.realAmountToLakalaSupportAmount(new BigDecimal(1));
        String title = "商户账号校验支付单";
        Integer expiredSecond = 180;

        TenantOnlinePayLakalaConfParam lakalaConf = param.getLakalaConf();
        if (StringUtils.isBlank(lakalaConf.getAppId())
                || StringUtils.isBlank(lakalaConf.getMerchantNo())
                || StringUtils.isBlank(lakalaConf.getTermNo())) {
            throw new BusinessException("请先维护完整的商户收款信息");
        }
        LakalaAppIdApiPrivateKeyRel rel = lakalaAppIdApiPrivateKeyRelManager.findByAppId(lakalaConf.getAppId());
        Assert.validateNull(rel, "appId无效");
        TenantLakalaConf tenantLakalaConf = new TenantLakalaConf();
        tenantLakalaConf.setMerchantNo(lakalaConf.getMerchantNo());
        tenantLakalaConf.setAppId(lakalaConf.getAppId());
        tenantLakalaConf.setTermNo(lakalaConf.getTermNo());
        tenantLakalaConf.setApiPrivateKey(rel.getApiPrivateKey());
        tenantLakalaConf.setSerialNo(rel.getSerialNo());
        LakalaClientConfigParam lakalaClientConfigParam = lakalaClientConfService.getTenantLakalaClient(tenantLakalaConf);

        LakalaHttpClient lakalaClient = lakalaService.getLakalaClient(lakalaClientConfigParam);

        OrderBaseRespBody<OrderCreateRespData> res = null;
        try {
            res = lklOrderService.create(lakalaClient,
                    tenantLakalaConf.getMerchantNo(),
                    lakalaClientConfigParam.getTermNo(),
                    tradeNo,
                    lakalaSupportAmount,
                    title,
                    expiredSecond,
                    null);
            Assert.validateNull(res, "创建支付单失败");
            log.info("创建支付单结果 res {}", res);
            if (!"000000".equals(res.getCode())) {
                throw new BusinessException("商户号生成支付单失败，请检查配置信息");
            }
            return res.getRespData().getCounterUrl();
        } catch (LakalaServerException e) {
            String respBody = e.getRespBody();
            JSONObject jsonObject = JSONUtil.parseObj(respBody);
            log.info("创建支付单失败 res {}", respBody);
            throw new BusinessException(String.valueOf(jsonObject.get("msg")));
        }


    }
}
