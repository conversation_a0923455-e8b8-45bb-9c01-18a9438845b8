package com.sdsdiy.paymentimpl.service.pay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.payment.PaymentTagConst;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentapi.dto.PaymentCreateDto;
import com.sdsdiy.paymentapi.dto.msg.PaymentAlipayCallbackMqMsg;
import com.sdsdiy.paymentapi.param.AlipayClientConfigParam;
import com.sdsdiy.paymentapi.param.AlipayTradeCreateParam;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.lakala.PaidNotifyBaseParam;
import com.sdsdiy.paymentimpl.bo.*;
import com.sdsdiy.paymentimpl.entity.*;
import com.sdsdiy.paymentimpl.entity.po.IPlatformPaidNotify;
import com.sdsdiy.paymentimpl.manager.AlipayCallbackMapperManager;
import com.sdsdiy.paymentimpl.manager.PaymentMapperManager;
import com.sdsdiy.paymentimpl.service.*;
import com.sdsdiy.paymentimpl.service.alipay.AlipayClientConfService;
import com.sdsdiy.paymentimpl.service.alipay.AlipayRefundService;
import com.sdsdiy.paymentimpl.service.alipay.AlipayService;
import com.sdsdiy.paymentimpl.service.transaction.TransactionCheckService;
import com.sdsdiy.paymentimpl.service.transaction.TransactionDbService;
import com.sdsdiy.paymentimpl.service.transaction.balance.BalanceOperateFactory;
import com.sdsdiy.paymentimpl.service.transaction.balance.IBalanceOperate;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AlipayPaymentImpl extends AbstractPayment {

    private final BalanceOperateFactory balanceOperateFactory;
    private final AlipayClientConfService alipayClientConfService;
    private final AlipayService alipayService;
    private final AlipayRefundService alipayRefundService;
    private final PreCreatePaymentQueryService preCreatePaymentQueryService;
    private final AlipayCallbackMapperManager alipayCallbackMapperManager;
    private final RocketMQTemplate rocketMQTemplate;
    private final PaymentService paymentService;
    private final PaymentMapperManager paymentMapperManager;

    @Autowired
    @Lazy
    private AlipayPaymentImpl alipayPaymentImpl;

    @Override
    public PaymentMethodEnum getMethod() {
        return PaymentMethodEnum.ALI_PAY;
    }

    @Override
    public Payment generate(String parentTradeNo, PaymentParam param, TransactionPayRelatedEntityBo relatedEntityBo) {
        Pair<String, String> tradeNos = TransactionDbService.genTradeNo(parentTradeNo, param.getPayType());

        BigDecimal balance = NumberUtil.add(param.getBalance(), param.getBonus());
        param.setBalance(balance);
        param.setBonus(BigDecimal.ZERO);

        OnlinePayAccountChooseParam chooseParam = OnlinePayAccountChooseParam.builder()
            .targetTenantId(param.getTargetTenantId())
            .targetRole(param.getTargetRole())
            .amount(param.getBalance())
            .build();
        AlipayClientConfigParam alipayClientConfigParam = alipayClientConfService.chooseTenantAlipayAccount(chooseParam);

        int timeoutExpress = param.getExpiredSecond() / 60;
        AlipayTradeCreateParam alipayTradeCreateParam = AlipayTradeCreateParam.builder()
            .outTradeNo("")
            .subject(param.getTitle())
            .totalAmount(param.getBalance())
            .alipayClientConf(alipayClientConfigParam)
            .timeoutExpress(timeoutExpress + "m")
            .build();

        AlipayService.AlipayTrade alipayTrade = alipayService.generatePreCreateTrade(alipayTradeCreateParam);

        PaymentOnlineConfig onlineConfig = new PaymentOnlineConfig()
            .setPaymentConfig(JSON.toJSONString(alipayClientConfigParam))
            .setPaymentType(getMethod().getCode())
            .setTenantId(chooseParam.getTargetTenantId())
            .setTradeNo(tradeNos.getValue());

        Payment payment = BeanUtil.toBean(param, Payment.class);
        payment.setTradeNos(tradeNos);
        payment.setAppId(alipayClientConfigParam.getAppId());
        payment.setPayChannel(PayChannelEnum.ALI_PAY.getCode());
        payment.setAlipayTradeNo(alipayTrade.getOutTradeNo());
        payment.setPaymentOnlineConfig(onlineConfig);
        payment.setStatus(PaymentStatusEnum.WAIT_PAY.getStatus());
        payment.setTotalAmount(NumberUtil.add(payment.getBalance(), payment.getBonus()));
        TransactionCheckService.refreshPaymentSourceOpenOnlinePay(payment, relatedEntityBo);

        return payment;
    }

    @Override
    public void operatePaymentAfterPaid(Payment payment, PayRelatedEntityBo relatedEntity) {
        Assert.validateFalse(payment.getSourceRole().equals(PaymentRoleEnum.MERCHANT.getCode())
            || payment.getSourceRole().equals(PaymentRoleEnum.TENANT.getCode()), "付款人信息错误"
        );

        log.info("alipay payment trade_no={} id={}, payment={}, related={}", payment.getTradeNo(), payment.getId(), JSON.toJSONString(payment), JSON.toJSONString(relatedEntity));
        Merchant sourceMerchant = relatedEntity.getMerchantMap().get(payment.getSourceMerchantId());
        TenantWallet sourceTenant = relatedEntity.getTenantWalletMap().get(payment.getSourceTenantId());
        boolean onlinePayValid = PaymentCheckService.checkPaymentOnlinePayValid(payment, relatedEntity);
        // 如果打开了线上支付，支付宝发起退款
        if (!onlinePayValid) {
            log.info("alipay payment open_online_pay invalid refund payment={}", payment.getId());
            alipayRefundService.refund(payment);
            throw new BusinessException("已开启线上收款，请稍后刷新重试(支付宝支付订单已经退款)。");
        }

        MerchantBillDisposeMoney disposeMoney = MerchantBillGenerateService.generateDisposeMoney(sourceTenant, sourceMerchant);

        // 租户充值
        if (PaymentRoleEnum.TENANT.getCode().equals(payment.getSourceRole())) {
            Assert.validateNull(sourceTenant, "付款租户不能为空");
            BalanceOperatorBo tenantBalanceOperatorBo = BalanceOperatorBo.builder()
                .bizNo(payment.getBizNo())
                .operatorEnum(BalanceOperator.NO_CHANGE)
                .tradeNo(payment.getTradeNo())
                .tenantId(payment.getSourceTenantId())
                .changeBalance(payment.getBalance())
                .changeGiftMoney(payment.getBonus())
                .build();
            if (PurposeType.RECHARGE.getCode().equals(payment.getPurposeType())) {
                tenantBalanceOperatorBo.setOperatorEnum(BalanceOperator.ADD);
            }

            TenantBalanceLog balanceLog = TenantBalanceService.calculateBalanceAndGenLog(sourceTenant, tenantBalanceOperatorBo);
            relatedEntity.getTenantBalanceLogs().add(balanceLog);

            disposeMoney.setDisposeTenantGift(balanceLog.getDisposeBonus())
                .setDisposeTenantBalance(balanceLog.getDisposeBalance());
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(payment.getSourceRole())) {
            Assert.validateNull(sourceMerchant, "付款商户不能为空");
            BalanceOperatorBo tenantBalanceOperatorBo = BalanceOperatorBo.builder()
                .bizNo(payment.getBizNo())
                .operatorEnum(BalanceOperator.NO_CHANGE)
                .tradeNo(payment.getTradeNo())
                .merchantId(payment.getSourceMerchantId())
                .changeBalance(payment.getBalance())
                .changeGiftMoney(payment.getBonus())
                .build();
            if (PurposeType.RECHARGE.getCode().equals(payment.getPurposeType())) {
                tenantBalanceOperatorBo.setOperatorEnum(BalanceOperator.ADD);
            }

            MerchantBalanceLog balanceLog = MerchantBalanceService.calculateBalanceAndGenLog(sourceMerchant, tenantBalanceOperatorBo);
            relatedEntity.getMerchantBalanceLogs().add(balanceLog);

            disposeMoney.setDisposeMerchantGift(balanceLog.getDisposeBonus())
                .setDisposeMerchantBalance(balanceLog.getDisposeBalance());
        }

        payment.setBalanceOperateFinish(BasePoConstant.YES);
        payment.setPayChannel(PayChannelEnum.ALI_PAY.getCode());

        AlipayDailyPayeeAmount alipayDailyPayeeAmount = new AlipayDailyPayeeAmount();
        alipayDailyPayeeAmount.setAppId(payment.getAppId());
        alipayDailyPayeeAmount.setPayeeAmount(payment.getTotalAmount());
        alipayDailyPayeeAmount.setDaily(Integer.parseInt(DateUtil.date().toString("yyyyMMdd")));
        relatedEntity.setAlipayDailyPayeeAmount(alipayDailyPayeeAmount);

        if (Boolean.TRUE.equals(relatedEntity.getRecordBills())) {
            MerchantBill merchantBill = MerchantBillGenerateService.genByPayment(payment, disposeMoney);
            relatedEntity.getMerchantBills().add(merchantBill);
        }
    }

    @Override
    public void operate(Payment payment, TransactionOperateRelatedEntityBo operateRelatedEntityBo) {
        WalletType walletType = new WalletType()
            .setWalletType(payment.getSourceRole())
            .setTenantId(payment.getSourceTenantId())
            .setMerchantId(payment.getSourceMerchantId())
            .setMerchantUserId(payment.getSourceUserId());
        if (PaymentRoleEnum.TENANT_DIS.getCode().equals(payment.getSourceRole())) {
            walletType.setTenantId(0L);
            walletType.setMerchantId(0L);
            walletType.setMerchantUserId(0L);
            walletType.setDisTenantId(payment.getSourceTenantId());
            walletType.setSupTenantId(payment.getTargetTenantId());
        }
        
        IBalanceOperate strategy = balanceOperateFactory.getStrategy(payment.getSourceRole());
        strategy.doForPayment(payment, operateRelatedEntityBo);
        payment.setBalanceOperateFinish(BasePoConstant.YES);
    }

    @Override
    public CustomerPaidResultBo queryPaidResultFromPaidPlatform(Payment payment) {
        boolean alipayTradePaidSuccess = preCreatePaymentQueryService.checkAlipayPaymentIsPaid(
            payment.getTradeNo(), payment.getTargetRole(), payment.getTargetTenantId(), payment.getAppId()
        );

        CustomerPaidResultBo customerPaidResultBo = new CustomerPaidResultBo();
        customerPaidResultBo.setPaid(alipayTradePaidSuccess);
        customerPaidResultBo.setPayChannel(PayChannelEnum.ALI_PAY.getCode());
        return customerPaidResultBo;
    }

    @Override
    public IPlatformPaidNotify savePlatformNotifyParam(PaidNotifyBaseParam param) {
        return null;
    }

    @Override
    public boolean saveNotifyResult(ProcessPaidNotifyResultBo resultBo) {
        return false;
    }


    private boolean checkPayment(AlipayCallback alipayCallback, Payment payment) {
        if (BasePoConstant.YES.equals(payment.getBalanceOperateFinish())) {
            alipayCallback.setErrMsg(CustomerPaidResultNotifyOperateStatus.PAYMENT_OPERATE.getDesc());
            alipayCallback.setOperateStatus(CustomerPaidResultNotifyOperateStatus.PAYMENT_OPERATE.getStatus());
            return true;
        }

        if (PaymentStatusEnum.PAID.getStatus().equals(payment.getStatus())) {
            alipayCallback.setErrMsg(CustomerPaidResultNotifyOperateStatus.PAYMENT_PAID.getDesc());
            alipayCallback.setOperateStatus(CustomerPaidResultNotifyOperateStatus.PAYMENT_PAID.getStatus());
            return true;
        }


        if (!payment.getStatus().equals(PaymentStatusEnum.WAIT_PAY.getStatus())) {
            alipayCallback.setErrMsg("支付宝订单不是待支付状态");
            return false;
        }

        AlipayTradeQueryResponse queryResponse = null;
        try {
            queryResponse = preCreatePaymentQueryService.alipayTradeQuery(payment.getTradeNo(), payment.getTargetRole(), payment.getTargetTenantId(), payment.getAppId());
        } catch (Exception e) {
            alipayCallback.setErrMsg(ExceptionUtil.stacktraceToOneLineString(e));
            return false;
        }
        if (!queryResponse.isSuccess()) {
            alipayCallback.setErrMsg(queryResponse.getBody());
            return false;
        }

        JSONObject json = new JSONObject();
        json.put("body", queryResponse.getBody());
        json.put("code", queryResponse.getCode());
        json.put("subMsg", queryResponse.getSubMsg());
        json.put("subCode", queryResponse.getSubCode());
        alipayCallback.setErrMsg(json.toJSONString());
        if (CharSequenceUtil.isNotEmpty(queryResponse.getTradeStatus())) {
            alipayCallback.setTradeStatus(queryResponse.getTradeStatus());
        } else {
            alipayCallback.setTradeStatus(CharSequenceUtil.EMPTY);
        }

        return AlipayTradeStatus.SUCCESS.getCode().equals(alipayCallback.getTradeStatus())
            || AlipayTradeStatus.FINISHED.getCode().equals(alipayCallback.getTradeStatus());
    }


    public AlipayCallback doCallback(String outTradeNo, String tradeStatus) {
        AlipayCallback alipayCallback = new AlipayCallback();
        alipayCallback.setTradeNo(outTradeNo);
        alipayCallback.setTradeStatus(tradeStatus);
        alipayCallback.setPaymentId(0L);
        alipayCallback.setOperateStatus(CustomerPaidResultNotifyOperateStatus.WAITING.getStatus());

        Payment payment = getValidPayment(outTradeNo);
        if (ObjectUtil.isNull(payment)) {
            log.error("alipay callback failed payment null");
            alipayCallback.setErrMsg("payment异常，找不到合法的payment");
            alipayCallback.setPaymentId(0L);
            alipayCallback.setOperateStatus(CustomerPaidResultNotifyOperateStatus.FAIL.getStatus());
            alipayCallbackMapperManager.save(alipayCallback);
            return alipayCallback;
        }

        PaymentAlipayCallbackMqMsg msg = PaymentAlipayCallbackMqMsg.builder()
            .paymentId(payment.getId())
            .bizNo(payment.getBizNo())
            .detailPurpose(payment.getDetailPurpose())
            .purposeType(payment.getPurposeType())
            .tradeNo(payment.getTradeNo())
            .build();

        alipayCallback.setPaymentId(payment.getId());
        boolean checkResult = checkPayment(alipayCallback, payment);
        if (!checkResult) {
            log.error("alipay callback failed check payment false");
            alipayCallback.setOperateStatus(CustomerPaidResultNotifyOperateStatus.FAIL.getStatus());
            alipayCallbackMapperManager.save(alipayCallback);
            return alipayCallback;
        }

        // 已经操作完成直接返回
        if (CustomerPaidResultNotifyOperateStatus.PAYMENT_OPERATE.getStatus().equals(alipayCallback.getOperateStatus())) {
            log.error("alipay callback failed payment operate finish");
            alipayCallbackMapperManager.save(alipayCallback);
            return alipayCallback;
        }

        // 未操作完成但是已经支付则再次发送MQ消息
        if (CustomerPaidResultNotifyOperateStatus.PAYMENT_PAID.getStatus().equals(alipayCallback.getOperateStatus())) {
            alipayCallbackMapperManager.save(alipayCallback);
            rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PAYMENT, PaymentTagConst.ALIPAY_CALLBACK, msg);
            return alipayCallback;
        }

        PayRelatedEntityBo relatedEntity = paymentService.genPayRelatedEntityByPayments(Collections.singletonList(payment), false);
        boolean onlinePayValid = PaymentCheckService.checkPaymentOnlinePayValid(payment, relatedEntity);
        if (!onlinePayValid) {
            log.info("alipay payment open_online_pay invalid refund payment={}", payment.getId());
            alipayRefundService.refund(payment);
            alipayCallback.setOperateStatus(CustomerPaidResultNotifyOperateStatus.SUCCESS.getStatus());
            payment.setStatus(PaymentStatusEnum.REFUNDED.getStatus());
            payment.setPayTime(System.currentTimeMillis());
            alipayPaymentImpl.updateDb(payment, alipayCallback);
            return alipayCallback;
        }

        alipayCallback.setOperateStatus(CustomerPaidResultNotifyOperateStatus.SUCCESS.getStatus());
        payment.setStatus(PaymentStatusEnum.PAID.getStatus());
        payment.setPayTime(System.currentTimeMillis());
        alipayPaymentImpl.updateDb(payment, alipayCallback);
        rocketMQTemplate.sendNormal(RocketMqTopicConst.EVENT_PAYMENT, PaymentTagConst.ALIPAY_CALLBACK, msg);
        return alipayCallback;
    }

    @GlobalTransactional
    public void updateDb(Payment payment, AlipayCallback alipayCallback) {
        boolean dbSql = paymentMapperManager.updateById(payment);
        Assert.validateFalse(dbSql, "支付宝回调更新Payment失败(会重试，可以忽略)");
        dbSql = alipayCallbackMapperManager.save(alipayCallback);
        Assert.validateFalse(dbSql, "支付宝回调更新回调记录失败");
    }

    @Override
    public PaymentCreateDto create(PaymentParam param) {
        AlipaySupportPayeeTypeEnum payeeTypeEnum = AlipaySupportPayeeTypeEnum.getByPayeeType(param.getAlipaySupportPayeeType());
        Assert.validateNull(payeeTypeEnum, "支付宝收款方式不能为空");

        OnlinePayAccountChooseParam chooseParam = OnlinePayAccountChooseParam.builder()
            .targetTenantId(param.getTargetTenantId())
            .targetRole(param.getTargetRole())
            .amount(param.getBalance())
            .build();
        AlipayClientConfigParam alipayClientConfigParam = alipayClientConfService.chooseTenantAlipayAccount(chooseParam);

        int timeoutExpress = param.getExpiredSecond() / 60;
        AlipayTradeCreateParam alipayTradeCreateParam = AlipayTradeCreateParam.builder()
            .outTradeNo("")
            .subject(param.getTitle())
            .totalAmount(param.getTotalAmount())
            .alipayClientConf(alipayClientConfigParam)
            .timeoutExpress(timeoutExpress + "m")
            .build();

        AlipayService.AlipayTrade alipayTrade = alipayService.generatePreCreateTrade(alipayTradeCreateParam);

        PaymentCreateDto payment = PaymentCreateService.paramToEntity(param, JSON.toJSONString(alipayClientConfigParam));
        payment.setAppId(alipayClientConfigParam.getAppId());
        payment.setImgUrl(alipayTrade.getImgUrl());
        payment.setPayChannel(PayChannelEnum.ALI_PAY.getCode());
        payment.setAlipayTradeNo(alipayTrade.getOutTradeNo());

        return payment;
    }
}
