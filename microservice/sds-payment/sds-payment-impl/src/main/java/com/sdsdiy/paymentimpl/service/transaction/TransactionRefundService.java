package com.sdsdiy.paymentimpl.service.transaction;

import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.redis.lock.LockUtil;
import com.sdsdiy.paymentimpl.manager.*;
import com.sdsdiy.paymentimpl.service.transaction.notify.OnlinePayNotifyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionRefundService {

    private final TransactionCheckService transactionCheckService;
    private final TransactionDbService transactionDbService;
    private final PaymentMapperManager paymentMapperManager;
    private final LockUtil lockUtil;
    private final TenantWalletMapperManager tenantWalletMapperManager;
    private final MerchantMapperManager merchantMapperManager;
    private final MerchantUserAccountMapperManager merchantUserAccountMapperManager;
    private final TenantDistributorWalletManager tenantDistributorWalletManager;
    private final TransactionEntryManage transactionEntryManage;
    private final RefundMapperManager refundMapperManager;
    private final RocketMQTemplate rocketMQTemplate;
    private final OnlinePayNotifyFactory onlinePayNotifyFactory;
    private final SubTransactionTaskManage subTransactionTaskManage;


    public void generateRefundByTradeNo(String tradeNo) {
    }
}
