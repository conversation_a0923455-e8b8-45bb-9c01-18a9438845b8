package com.sdsdiy.paymentimpl.service.transaction;

import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.redis.lock.LockUtil;
import com.sdsdiy.paymentapi.constant.PaymentStatusEnum;
import com.sdsdiy.paymentapi.constant.PurposeType;
import com.sdsdiy.paymentapi.constant.TransactionPayTypeEnum;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.entity.Refund;
import com.sdsdiy.paymentimpl.manager.*;
import com.sdsdiy.paymentimpl.service.transaction.notify.OnlinePayNotifyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionRefundService {

    private final TransactionCheckService transactionCheckService;
    private final TransactionDbService transactionDbService;
    private final PaymentMapperManager paymentMapperManager;
    private final LockUtil lockUtil;
    private final TenantWalletMapperManager tenantWalletMapperManager;
    private final MerchantMapperManager merchantMapperManager;
    private final MerchantUserAccountMapperManager merchantUserAccountMapperManager;
    private final TenantDistributorWalletManager tenantDistributorWalletManager;
    private final TransactionEntryManage transactionEntryManage;
    private final RefundMapperManager refundMapperManager;
    private final RocketMQTemplate rocketMQTemplate;
    private final OnlinePayNotifyFactory onlinePayNotifyFactory;
    private final SubTransactionTaskManage subTransactionTaskManage;
    private final TransactionPayService transactionPayService;


    public void generateRefundByTradeNo(String tradeNo) {
        log.info("开始根据tradeNo进行交易回退, tradeNo={}", tradeNo);

        // 1. 查询主交易
        Payment mainPayment = paymentMapperManager.findOneTradeNo(tradeNo);
        Refund mainRefund = refundMapperManager.findOneTradeNo(tradeNo);

        if (mainPayment == null && mainRefund == null) {
            log.warn("未找到对应的主交易记录, tradeNo={}", tradeNo);
            throw new BusinessException("未找到对应的交易记录");
        }

        // 2. 确定主交易类型并检查状态
        boolean isPaymentMain = mainPayment != null;
        if (isPaymentMain) {
            // 主交易是payment，检查是否已完成
            if (!BasePoConstant.yes(mainPayment.getBalanceOperateFinish())
                || !PaymentStatusEnum.PAID.getStatus().equals(mainPayment.getStatus())) {
                log.warn("主交易payment未完成，无法进行回退, tradeNo={}, status={}, balanceOperateFinish={}",
                    tradeNo, mainPayment.getStatus(), mainPayment.getBalanceOperateFinish());
                throw new BusinessException("主交易未完成，无法进行回退");
            }

            // 创建主退款交易
            createRefundForPayment(mainPayment);

            // 查询并处理子payment交易
            List<Payment> subPayments = paymentMapperManager.findAllByParentTradeNo(tradeNo);
            for (Payment subPayment : subPayments) {
                if (BasePoConstant.yes(subPayment.getBalanceOperateFinish())
                    && PaymentStatusEnum.PAID.getStatus().equals(subPayment.getStatus())) {
                    createRefundForPayment(subPayment);
                }
            }

        } else {
            // 主交易是refund，检查是否已完成
            if (!BasePoConstant.yes(mainRefund.getBalanceOperateFinish())
                || !PaymentStatusEnum.REFUNDED.getStatus().equals(mainRefund.getStatus())) {
                log.warn("主交易refund未完成，无法进行回退, tradeNo={}, status={}, balanceOperateFinish={}",
                    tradeNo, mainRefund.getStatus(), mainRefund.getBalanceOperateFinish());
                throw new BusinessException("主交易未完成，无法进行回退");
            }

            // 创建主支付交易（回退退款）
            createPaymentForRefund(mainRefund);

            // 查询并处理子refund交易
            List<Refund> subRefunds = refundMapperManager.findAllByParentTradeNo(tradeNo);
            for (Refund subRefund : subRefunds) {
                if (BasePoConstant.yes(subRefund.getBalanceOperateFinish())
                    && PaymentStatusEnum.REFUNDED.getStatus().equals(subRefund.getStatus())) {
                    createPaymentForRefund(subRefund);
                }
            }
        }

        log.info("交易回退处理完成, tradeNo={}", tradeNo);
    }

    /**
     * 为Payment创建对应的Refund回退交易
     */
    private void createRefundForPayment(Payment payment) {
        log.info("为Payment创建Refund回退交易, paymentId={}, tradeNo={}", payment.getId(), payment.getTradeNo());

        try {
            // 构建RefundParam
            RefundParam refundParam = RefundParam.builder()
                .payType(payment.isMainTransaction() ? TransactionPayTypeEnum.MAIN.getValue() : TransactionPayTypeEnum.SUB.getValue())
                .operateRole(payment.getOperateRole())
                .operateUserId(payment.getOperateUserId())
                .balanceType(payment.getBalanceType())
                .billType(payment.getBillType())
                .operateTargetRole(payment.getOperateTargetRole())
                .operateTargetRoleId(payment.getOperateTargetRoleId())
                .remark("系统自动回退: " + payment.getTradeNo())
                .bizNo(payment.getBizNo() + "_ROLLBACK")
                .bizNoForBill(payment.getBizNoForBill() + "_ROLLBACK")
                .balance(payment.getBalance())
                .bonus(payment.getBonus())
                // 注意：退款时source和target角色互换
                .sourceTenantId(payment.getTargetTenantId())
                .sourceMerchantId(payment.getTargetMerchantId())
                .targetTenantId(payment.getSourceTenantId())
                .targetMerchantId(payment.getSourceMerchantId())
                .targetUserId(payment.getSourceUserId())
                .sourceRole(payment.getTargetRole())
                .targetRole(payment.getSourceRole())
                .subject("回退交易: " + payment.getTitle())
                .purposeType(PurposeType.REFUND.getCode())
                .detailPurpose(payment.getDetailPurpose())
                .payMethod(payment.getMethod())
                .paymentId(payment.getId())
                .build();

            // 创建并执行退款交易
            MultiTransactionCreateParam createParam = new MultiTransactionCreateParam();
            createParam.getRefundList().add(refundParam);

            Refund refund = transactionPayService.createForRefund(createParam);
            if (refund != null) {
                // 执行退款
                transactionPayService.processRefund(refund.getId());
                log.info("Payment回退交易创建并执行成功, paymentId={}, refundId={}", payment.getId(), refund.getId());
            }

        } catch (Exception e) {
            log.error("为Payment创建Refund回退交易失败, paymentId={}, tradeNo={}", payment.getId(), payment.getTradeNo(), e);
            throw new BusinessException("创建回退交易失败: " + e.getMessage());
        }
    }

    /**
     * 为Refund创建对应的Payment回退交易
     */
    private void createPaymentForRefund(Refund refund) {
        log.info("为Refund创建Payment回退交易, refundId={}, tradeNo={}", refund.getId(), refund.getTradeNo());

        try {
            // 构建PaymentParam
            PaymentParam paymentParam = PaymentParam.builder()
                .payType(refund.isMainTransaction() ? TransactionPayTypeEnum.MAIN.getValue() : TransactionPayTypeEnum.SUB.getValue())
                .billType(refund.getBillType())
                .detailPurpose(refund.getDetailPurpose())
                .sourceRole(refund.getTargetRole()) // 注意：回退时source和target角色互换
                .balanceType(refund.getBalanceType())
                .targetRole(refund.getSourceRole())
                .bizNo(refund.getBizNo() + "_ROLLBACK")
                .bizNoForBill(refund.getBizNoForBill() + "_ROLLBACK")
                .method(refund.getPayMethod())
                .title("回退交易: " + refund.getSubject())
                .purposeType(refund.getPurposeType())
                .balance(refund.getBalance())
                .bonus(refund.getBonus())
                .sourceTenantId(refund.getTargetTenantId())
                .sourceMerchantId(refund.getTargetMerchantId())
                .sourceUserId(refund.getTargetUserId())
                .targetTenantId(refund.getSourceTenantId())
                .targetMerchantId(refund.getSourceMerchantId())
                .operateRole(refund.getOperateRole())
                .operateUserId(refund.getOperateUserId())
                .operateTargetRole(refund.getOperateTargetRole())
                .operateTargetRoleId(refund.getOperateTargetRoleId())
                .remark("系统自动回退: " + refund.getTradeNo())
                .build();

            // 创建并执行支付交易
            MultiTransactionCreateParam createParam = new MultiTransactionCreateParam();
            createParam.getPaymentList().add(paymentParam);

            Payment payment = transactionPayService.createForPay(createParam);
            if (payment != null) {
                // 执行支付
                transactionPayService.processPayment(payment.getId());
                log.info("Refund回退交易创建并执行成功, refundId={}, paymentId={}", refund.getId(), payment.getId());
            }

        } catch (Exception e) {
            log.error("为Refund创建Payment回退交易失败, refundId={}, tradeNo={}", refund.getId(), refund.getTradeNo(), e);
            throw new BusinessException("创建回退交易失败: " + e.getMessage());
        }
    }
}
