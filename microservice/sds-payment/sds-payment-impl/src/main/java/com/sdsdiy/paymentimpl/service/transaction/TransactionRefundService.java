package com.sdsdiy.paymentimpl.service.transaction;

import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.redis.lock.LockUtil;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentimpl.bo.TransactionCreateEntityBo;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.entity.Refund;
import com.sdsdiy.paymentimpl.manager.*;
import com.sdsdiy.paymentimpl.service.transaction.notify.OnlinePayNotifyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionRefundService {

    private final TransactionCheckService transactionCheckService;
    private final TransactionDbService transactionDbService;
    private final PaymentMapperManager paymentMapperManager;
    private final LockUtil lockUtil;
    private final TenantWalletMapperManager tenantWalletMapperManager;
    private final MerchantMapperManager merchantMapperManager;
    private final MerchantUserAccountMapperManager merchantUserAccountMapperManager;
    private final TenantDistributorWalletManager tenantDistributorWalletManager;
    private final TransactionEntryManage transactionEntryManage;
    private final RefundMapperManager refundMapperManager;
    private final RocketMQTemplate rocketMQTemplate;
    private final OnlinePayNotifyFactory onlinePayNotifyFactory;
    private final SubTransactionTaskManage subTransactionTaskManage;
    private final TransactionPayService transactionPayService;


    public void systemRefund(String tradeNo, Long saasUserId) {
        TransactionCreateEntityBo createEntityBo = generateRefundByTradeNo(tradeNo, saasUserId);
        for (Refund refund : createEntityBo.getRefundList()) {
            if (refund.isMainTransaction()) {
                log.info("systemRefund processRefund id={}", refund.getId());
                transactionPayService.processRefund(refund.getId());
            }
        }
    }

    public TransactionCreateEntityBo generateRefundByTradeNo(String tradeNo, long saasUSerId) {
        log.info("开始根据tradeNo生成回退数据, tradeNo={}", tradeNo);

        // 1. 查询主交易
        Payment mainPayment = paymentMapperManager.findOneTradeNo(tradeNo);
        if (mainPayment == null) {
            throw new BusinessException("未找到对应的交易记录");
        }
        if (!BasePoConstant.yes(mainPayment.getBalanceOperateFinish())
            || !PaymentStatusEnum.PAID.getStatus().equals(mainPayment.getStatus())) {
            throw new BusinessException("Payment" + mainPayment.getId() + "未完成");
        }

        List<Payment> totalPayments = paymentMapperManager.findAllByParentTradeNo(tradeNo);
        List<Refund> subRefunds = refundMapperManager.findAllByParentTradeNo(tradeNo);
        totalPayments.add(mainPayment);

        MultiTransactionCreateParam createParam = new MultiTransactionCreateParam();
        for (Payment subPayment : totalPayments) {
            if (!BasePoConstant.yes(subPayment.getBalanceOperateFinish())
                || !PaymentStatusEnum.PAID.getStatus().equals(mainPayment.getStatus())) {
                throw new BusinessException("Payment" + subPayment.getId() + "未完成");
            }
            RefundParam subRefundParam = buildRefundParamFromPayment(subPayment, saasUSerId);
            createParam.getRefundList().add(subRefundParam);
        }
        for (Refund subRefund : subRefunds) {
            if (!BasePoConstant.yes(subRefund.getBalanceOperateFinish())
                || !PaymentStatusEnum.PAID.getStatus().equals(mainPayment.getStatus())) {
                throw new BusinessException("Refund" + subRefund.getId() + "未完成");
            }
            PaymentParam subPaymentParam = buildPaymentParamFromRefund(subRefund, saasUSerId);
            createParam.getPaymentList().add(subPaymentParam);
        }

        return transactionPayService.create(TradeNoEnum.REFUND, createParam);
    }

    /**
     * 根据Payment构建RefundParam
     */
    private RefundParam buildRefundParamFromPayment(Payment payment, long saasUserId) {
        return RefundParam.builder()
            .payType(payment.isMainTransaction() ? TransactionPayTypeEnum.MAIN.getValue() : TransactionPayTypeEnum.SUB.getValue())
            .operateRole(PaymentRoleEnum.SYSTEM.getCode())
            .operateUserId(saasUserId)
            .balanceType(payment.getBalanceType())
            .billType(payment.getBillType())
            .operateTargetRole(payment.getOperateTargetRole())
            .operateTargetRoleId(payment.getOperateTargetRoleId())
            .remark("系统自动回退: " + payment.getRealBizNoForBill())
            .bizNo(payment.getBizNo())
            .bizNoForBill(payment.getBizNoForBill())
            .balance(payment.getBalance())
            .bonus(payment.getBonus())
            // 注意：退款时source和target角色互换
            .sourceTenantId(payment.getTargetTenantId())
            .sourceMerchantId(payment.getTargetMerchantId())
            .targetTenantId(payment.getSourceTenantId())
            .targetMerchantId(payment.getSourceMerchantId())
            .targetUserId(payment.getSourceUserId())
            .sourceRole(payment.getTargetRole())
            .targetRole(payment.getSourceRole())
            .subject("回退交易: " + payment.getTitle())
            .purposeType(PurposeType.REFUND.getCode())
            .detailPurpose(DetailPurpose.ADMIN_SYSTEM_ROLLBACK_TRADE.getCode())
            .payMethod(payment.getMethod())
            .paymentId(payment.getId())
            .build();
    }

    /**
     * 根据Refund构建PaymentParam
     */
    private PaymentParam buildPaymentParamFromRefund(Refund refund, long saasUserId) {
        return PaymentParam.builder()
            .payType(refund.isMainTransaction() ? TransactionPayTypeEnum.MAIN.getValue() : TransactionPayTypeEnum.SUB.getValue())
            .billType(refund.getBillType())
            .detailPurpose(DetailPurpose.ADMIN_SYSTEM_ROLLBACK_TRADE.getCode())
            .sourceRole(refund.getTargetRole()) // 注意：回退时source和target角色互换
            .balanceType(refund.getBalanceType())
            .targetRole(refund.getSourceRole())
            .bizNo(refund.getBizNo())
            .bizNoForBill(refund.getBizNoForBill())
            .method(refund.getPayMethod())
            .title("回退交易: " + refund.getSubject())
            .purposeType(PurposeType.OTHER.getCode())
            .balance(refund.getBalance())
            .bonus(refund.getBonus())
            .sourceTenantId(refund.getTargetTenantId())
            .sourceMerchantId(refund.getTargetMerchantId())
            .sourceUserId(refund.getTargetUserId())
            .targetTenantId(refund.getSourceTenantId())
            .targetMerchantId(refund.getSourceMerchantId())
            .operateRole(PaymentRoleEnum.SYSTEM.getCode())
            .operateUserId(saasUserId)
            .operateTargetRole(refund.getOperateTargetRole())
            .operateTargetRoleId(refund.getOperateTargetRoleId())
            .remark("系统自动回退: " + refund.getRealBizNoForBill())
            .build();
    }
}
