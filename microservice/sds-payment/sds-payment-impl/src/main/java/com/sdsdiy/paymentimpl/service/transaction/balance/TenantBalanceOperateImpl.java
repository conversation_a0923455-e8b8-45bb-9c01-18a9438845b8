package com.sdsdiy.paymentimpl.service.transaction.balance;

import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import com.sdsdiy.paymentimpl.bo.BalanceOperatorBo;
import com.sdsdiy.paymentimpl.bo.TransactionOperateRelatedEntityBo;
import com.sdsdiy.paymentimpl.bo.WalletType;
import com.sdsdiy.paymentimpl.entity.Payment;
import com.sdsdiy.paymentimpl.entity.Refund;
import com.sdsdiy.paymentimpl.entity.TenantBalanceLog;
import com.sdsdiy.paymentimpl.entity.TenantWallet;
import com.sdsdiy.paymentimpl.service.TenantBalanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class TenantBalanceOperateImpl extends AbstractBalanceOperate {

    @Override
    public String getRole() {
        return PaymentRoleEnum.TENANT.getCode();
    }

    @Override
    public void doForPayment(Payment payment, TransactionOperateRelatedEntityBo relatedEntity) {
        WalletType walletType = payment.generateWalletType();

        BalanceOperatorBo balanceOperatorBo = payment.generateBalanceOperatorBo();

        Long tenantId = walletType.getTenantId();
        TenantWallet tenantWallet = relatedEntity.findTenantWallet(tenantId);

        checkTenantBalance(relatedEntity, tenantId, balanceOperatorBo);

        TenantBalanceLog balanceLog = TenantBalanceService.calculateBalanceAndGenLog(tenantWallet, balanceOperatorBo);
        relatedEntity.getTenantBalanceLogs().add(balanceLog);

        this.generateBillForPayment(payment, balanceLog, relatedEntity);
    }

    @Override
    public void doForRefund(Refund refund, TransactionOperateRelatedEntityBo relatedEntity) {
        WalletType walletType = refund.generateWalletType();
        BalanceOperatorBo balanceOperatorBo = refund.generateBalanceOperatorBo();

        TenantWallet tenantWallet = relatedEntity.findTenantWallet(walletType.getTenantId());
        if (tenantWallet == null) {
            throw new BusinessException("未找到租户钱包信息，tenantId: " + walletType.getTenantId());
        }

        TenantBalanceLog balanceLog = TenantBalanceService.calculateBalanceAndGenLog(tenantWallet, balanceOperatorBo);
        relatedEntity.getTenantBalanceLogs().add(balanceLog);

        this.generateBillForRefund(refund, balanceLog, relatedEntity);
    }


    public static void checkTenantBalance(
        TransactionOperateRelatedEntityBo relatedEntity,
        Long tenantId,
        BalanceOperatorBo balanceOperatorBo
    ) {
        TenantWallet entity = relatedEntity.getTenantWalletMap().get(tenantId);
        if (entity == null) {
            throw new BusinessException("未找到租户钱包信息，Id: " + tenantId);
        }
        if (balanceOperatorBo.noNeedCheckBalanceEnough()) {
            return;
        }
        if (entity.getBalance().compareTo(balanceOperatorBo.getChangeBalance()) < 0) {
            throw new BusinessException("saas钱包余额不足，请联系管理员");
        }
        if (entity.getGiftMoney().compareTo(balanceOperatorBo.getChangeGiftMoney()) < 0) {
            throw new BusinessException("saas钱包赠送金不足，请联系管理员");
        }
    }
}
