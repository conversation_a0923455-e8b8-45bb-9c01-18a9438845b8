package com.sdsdiy.paymentimpl.service.refund;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.sds.platform.sdk.lakala.order.LakalaOrderStatusEnum;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentapi.param.lakala.LakalaRefundParam;
import com.sdsdiy.paymentapi.vo.LakalaRefundVo;
import com.sdsdiy.paymentimpl.bo.*;
import com.sdsdiy.paymentimpl.entity.*;
import com.sdsdiy.paymentimpl.entity.po.LakalaOrder;
import com.sdsdiy.paymentimpl.manager.LakalaOrderManage;
import com.sdsdiy.paymentimpl.manager.PaymentMapperManager;
import com.sdsdiy.paymentimpl.service.MerchantBalanceService;
import com.sdsdiy.paymentimpl.service.TenantBalanceService;
import com.sdsdiy.paymentimpl.service.lakala.LakalaRefundService;
import com.sdsdiy.paymentimpl.service.transaction.TransactionDbService;
import com.sdsdiy.paymentimpl.service.transaction.balance.BalanceOperateFactory;
import com.sdsdiy.paymentimpl.service.transaction.balance.IBalanceOperate;
import com.sdsdiy.paymentimpl.service.user.MerchantUserAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
@RequiredArgsConstructor
@Slf4j
public class LakalaRefundImpl implements IRefund {

    private final BalanceOperateFactory balanceOperateFactory;
    private final LakalaRefundService lakalaRefundService;
    private final PaymentMapperManager paymentMapperManager;
    private final LakalaOrderManage lakalaOrderManage;

    @Override
    public PaymentMethodEnum paymentChannel() {
        return PaymentMethodEnum.LAKALA;
    }

    @Override
    public void refund(Refund refund, RefundRelatedEntityBo relatedEntityBo) {
        BalanceOperatorBo balanceOperatorBo = BalanceOperatorBo.builder()
            .bizNo(refund.getBizNo())
            .changeGiftMoney(refund.getBonus())
            .changeBalance(refund.getBalance())
            .operatorEnum(BalanceOperator.NO_CHANGE)
            .tradeNo(refund.getTradeNo())
            .build();

        Payment refundPayment = relatedEntityBo.getPaymentById(refund.getPaymentId());
        if (refundPayment != null) {
            refundPayment.setStatus(PaymentStatusEnum.REFUNDED.getStatus());
        }

        MerchantBillDisposeMoney merchantBillDisposeMoney = new MerchantBillDisposeMoney();

        if (PaymentRoleEnum.TENANT.getCode().equals(refund.getTargetRole())) {
            balanceOperatorBo.setTenantId(refund.getTargetTenantId());
            TenantBalanceLog balanceLog = TenantBalanceService.calculateBalanceAndGenLog(relatedEntityBo.getTenantWallet(), balanceOperatorBo);
            relatedEntityBo.setTenantBalanceChanged(true);
            relatedEntityBo.setTenantBalanceLog(balanceLog);

            merchantBillDisposeMoney.setDisposeTenantBalance(balanceLog.getDisposeBalance());
            merchantBillDisposeMoney.setDisposeTenantGift(balanceLog.getDisposeBonus());

            RefundHandleService.recordBill(relatedEntityBo, refund, merchantBillDisposeMoney);
            refund.setBalanceOperateFinish(BasePoConstant.YES);
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(refund.getTargetRole())) {
            if (MerchantPayBalanceUsedType.ASSIGN_BALANCE.getUsedType().equals(refund.getBalanceType())) {
                MerchantBalanceLog balanceLog = MerchantBalanceService.calculateAssignedBalanceAndGenLog(relatedEntityBo.getMerchant(), balanceOperatorBo);
                relatedEntityBo.setMerchantBalanceChanged(true);

                relatedEntityBo.setMerchantBalanceLog(balanceLog);
                MerchantUserAccountLog userAccountLog = MerchantUserAccountService.calculateBalanceAndGenLog(relatedEntityBo.getMerchantUserAccount(), balanceOperatorBo);
                relatedEntityBo.setUserAccountLog(userAccountLog);
                relatedEntityBo.setMerchantUserAccountBalanceChanged(true);

                merchantBillDisposeMoney.setDisposeMerchantBalance(balanceLog.getDisposeBalance());
                merchantBillDisposeMoney.setDisposeMerchantGift(balanceLog.getDisposeBonus());
            } else {
                MerchantBalanceLog balanceLog = MerchantBalanceService.calculateBalanceAndGenLog(relatedEntityBo.getMerchant(), balanceOperatorBo);
                relatedEntityBo.setMerchantBalanceChanged(true);
                relatedEntityBo.setMerchantBalanceLog(balanceLog);
                merchantBillDisposeMoney.setDisposeMerchantBalance(balanceLog.getDisposeBalance());
                merchantBillDisposeMoney.setDisposeMerchantGift(balanceLog.getDisposeBonus());
            }

            RefundHandleService.recordBill(relatedEntityBo, refund, merchantBillDisposeMoney);
            refund.setBalanceOperateFinish(BasePoConstant.YES);
        }
    }

    @Override
    public void doRefund(Refund refund, TransactionOperateRelatedEntityBo relatedEntityBo) {
        WalletType walletType = new WalletType()
            .setWalletType(refund.getTargetRole())
            .setTenantId(refund.getTargetTenantId())
            .setMerchantId(refund.getTargetMerchantId())
            .setMerchantUserId(refund.getTargetUserId());
        if (PaymentRoleEnum.TENANT_DIS.getCode().equals(refund.getTargetRole())) {
            walletType.setTenantId(0L);
            walletType.setSupTenantId(refund.getSourceTenantId());
            walletType.setDisTenantId(refund.getTargetTenantId());
        }

        Long paymentId = refund.getPaymentId();
        Payment payment = refundLakalaPayment(paymentId);

        // 如果原先是充值，需要把余额回退
        if (PurposeType.RECHARGE.getCode().equals(payment.getPurposeType())) {
            refund.setAdminSpecifiedBalanceOperator(BalanceOperator.SUBTRACT);
        }
        
        IBalanceOperate strategy = balanceOperateFactory.getStrategy(refund.getTargetRole());
        strategy.doForRefund(refund, relatedEntityBo);
        refund.setBalanceOperateFinish(BasePoConstant.YES);
        refund.setPayChannel(payment.getPayChannel());
        refund.setStatus(PaymentStatusEnum.REFUNDED.getStatus());
    }

    @Override
    public Refund generate(String parentTradeNo, RefundParam param, TransactionPayRelatedEntityBo relatedEntityBo) {
        Pair<String, String> tradeNos = TransactionDbService.genTradeNo(parentTradeNo, param.getPayType());

        Refund entity = BeanUtil.toBean(param, Refund.class);
        entity.setStatus(PaymentStatusEnum.WAIT_PAY.getStatus())
            .setBalanceOperateFinish(BasePoConstant.NO)
            .setPayMethod(PaymentMethodEnum.LAKALA.getCode());
        entity.setTradeNos(tradeNos);
        entity.setCreateTimestamp(System.currentTimeMillis());

        return entity;
    }


    private Payment refundLakalaPayment(long paymentId) {
        Payment payment = paymentMapperManager.getById(paymentId);
        Assert.validateNull(payment, "Payment不存在");
        if (!PaymentMethodEnum.LAKALA.getCode().equals(payment.getMethod())) {
            throw new BusinessException("Payment不是拉卡拉订单");
        }

        String lakalaOrderNo = payment.getAlipayTradeNo();

        LakalaOrder lakalaOrder = lakalaOrderManage.findOneByLakalaOrderNo(lakalaOrderNo);
        Assert.validateNull(lakalaOrder, "拉卡拉订单不存在");
        if (!LakalaOrderStatusEnum.SUCCESS.getCode().equals(lakalaOrder.getLakalaOrderStatus())) {
            throw new BusinessException("拉卡拉订单状态不是成功");
        }

        BigDecimal totalAmount = payment.getTotalAmount();
        LakalaRefundParam refundParam = new LakalaRefundParam();
        refundParam.setAmount(totalAmount);
        refundParam.setReason("系统操作退款");
        refundParam.setOperateUserId(0L);
        refundParam.setOriginLakalaAccTradeNo(lakalaOrder.getLakalaAccTradeNo());
        LakalaRefundVo lakalaRefundVo = lakalaRefundService.refund(refundParam);
        log.info("lakala refund result={}, paymentId={}", JSON.toJSONString(lakalaRefundVo), payment.getId());
        if (!LakalaRefundStatusEnum.SUCCESS.getStatus().equals(lakalaRefundVo.getStatus())) {
            throw new BusinessException("Lakala退款失败");
        }

        return payment;
    }
}
