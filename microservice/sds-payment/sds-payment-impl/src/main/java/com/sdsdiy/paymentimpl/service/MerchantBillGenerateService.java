package com.sdsdiy.paymentimpl.service;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.MerchantBillDisposeMoney;
import com.sdsdiy.paymentimpl.entity.*;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class MerchantBillGenerateService {

    /**
     * 生成处理后余额（租户或者商户）
     *
     * @param tenantWallet
     * @param merchant
     * @return
     */
    @Deprecated
    public static MerchantBillDisposeMoney generateDisposeMoney(TenantWallet tenantWallet, Merchant merchant) {
        MerchantBillDisposeMoney disposeMoney = new MerchantBillDisposeMoney();
        if (ObjectUtil.isNotNull(tenantWallet)) {
            disposeMoney.setDisposeTenantGift(tenantWallet.getGiftMoney())
                .setDisposeTenantBalance(tenantWallet.getBalance());
        }
        if (ObjectUtil.isNotNull(merchant)) {
            disposeMoney.setDisposeMerchantGift(merchant.getFreeGold())
                .setDisposeMerchantBalance(merchant.getBalance());
        }
        return disposeMoney;
    }

    @Deprecated
    public static AmountChangeType calculateAmountChangeTypeOfMerchant(String sourceRole, String targetRole, String purposeTypeCode) {
        Optional<PurposeType> purposeTypeOptional = PurposeType.getByCode(purposeTypeCode);
        if (!purposeTypeOptional.isPresent()) {
            return AmountChangeType.ERROR;
        }

        PurposeType purposeType = purposeTypeOptional.get();
        if (PaymentRoleEnum.MERCHANT.getCode().equals(sourceRole)) {
            switch (purposeType) {
                case RECHARGE:
                    return AmountChangeType.RECHARGE;
                case WITHDRAW:
                    return AmountChangeType.WITHDRAW;
                case BUY_SERVICE:
                case BUY_PRODUCT:
                case BUY_MATERIAL:
                case CAL_VOLUME:
                case ADMIN_PREPAY_SHIPPING:
                case CUSTOMS_DUTY:
                case OTHER:
                case CHANGE_WAYBILL:
                case JIT_SHIPPING_COST:
                    return AmountChangeType.EXPEND;
                case PAYOUT:
                case REFUND:
                    return AmountChangeType.ERROR;
                default:
                    break;
            }
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(targetRole)) {
            switch (purposeType) {
                case RECHARGE:
                    return AmountChangeType.RECHARGE;
                case WITHDRAW:
                    return AmountChangeType.WITHDRAW;
                case BUY_SERVICE:
                case BUY_PRODUCT:
                case BUY_MATERIAL:
                case CAL_VOLUME:
                case ADMIN_PREPAY_SHIPPING:
                case CUSTOMS_DUTY:
                case OTHER:
                case CHANGE_WAYBILL:
                case JIT_SHIPPING_COST:
                    return AmountChangeType.ERROR;
                case PAYOUT:
                case REFUND:
                    return AmountChangeType.INCOME;
                default:
                    break;
            }
        }
        return AmountChangeType.NONE;
    }

    /**
     * 计算当前payment的商户的金额变动类型
     *
     * @param payment
     * @return
     */
    @Deprecated
    public static AmountChangeType calculateAmountChangeTypeOfMerchant(Payment payment) {
        return calculateAmountChangeTypeOfMerchant(payment.getSourceRole(), payment.getTargetRole(), payment.getPurposeType());
    }

    @Deprecated
    public static AmountChangeType getChangeTypeOfTenant(String sourceRole, String targetRole, String purposeTypeCode) {
        Optional<PurposeType> purposeTypeOptional = PurposeType.getByCode(purposeTypeCode);
        if (!purposeTypeOptional.isPresent()) {
            return AmountChangeType.ERROR;
        }
        PurposeType purposeType = purposeTypeOptional.get();
        if (sourceRole.equals(PaymentRoleEnum.MERCHANT.getCode()) && targetRole.equals(PaymentRoleEnum.TENANT.getCode())) {
            switch (purposeType) {
                case RECHARGE:
                    return AmountChangeType.RECHARGE;
                case WITHDRAW:
                    return AmountChangeType.WITHDRAW;
                case BUY_SERVICE:
                case BUY_PRODUCT:
                case BUY_MATERIAL:
                case CHANGE_WAYBILL:
                case OTHER:
                case CUSTOMS_DUTY:
                case FINISHED_DIFF:
                case CAL_VOLUME:
                case ADMIN_PREPAY_SHIPPING:
                case JIT_SHIPPING_COST:
                    return AmountChangeType.INCOME;
                case PAYOUT:
                case REFUND:
                    return AmountChangeType.ERROR;
                default:
                    break;
            }
        } else if (sourceRole.equals(PaymentRoleEnum.TENANT.getCode()) && targetRole.equals(PaymentRoleEnum.MERCHANT.getCode())) {
            switch (purposeType) {
                case RECHARGE:
                    return AmountChangeType.RECHARGE;
                case WITHDRAW:
                    return AmountChangeType.WITHDRAW;
                case BUY_SERVICE:
                case BUY_PRODUCT:
                case BUY_MATERIAL:
                case CHANGE_WAYBILL:
                case OTHER:
                case CUSTOMS_DUTY:
                case ADMIN_PREPAY_SHIPPING:
                case CAL_VOLUME:
                case JIT_SHIPPING_COST:
                    return AmountChangeType.ERROR;
                case PAYOUT:
                case REFUND:
                    return AmountChangeType.EXPEND;
                default:
                    break;
            }
        }
        if (sourceRole.equals(PaymentRoleEnum.TENANT.getCode()) && targetRole.equals(PaymentRoleEnum.SAAS.getCode())) {
            switch (purposeType) {
                case RECHARGE:
                    return AmountChangeType.RECHARGE;
                case WITHDRAW:
                    return AmountChangeType.WITHDRAW;
                case BUY_SERVICE:
                case BUY_PRODUCT:
                case BUY_MATERIAL:
                case CHANGE_WAYBILL:
                case OTHER:
                case CUSTOMS_DUTY:
                case CAL_VOLUME:
                case ADMIN_PREPAY_SHIPPING:
                case FINISHED_DIFF:
                case JIT_SHIPPING_COST:
                    return AmountChangeType.EXPEND;
                case PAYOUT:
                case REFUND:
                    return AmountChangeType.ERROR;
                default:
                    break;
            }
        }
        if (sourceRole.equals(PaymentRoleEnum.SAAS.getCode()) && targetRole.equals(PaymentRoleEnum.TENANT.getCode())) {
            switch (purposeType) {
                case RECHARGE:
                    return AmountChangeType.RECHARGE;
                case WITHDRAW:
                    return AmountChangeType.WITHDRAW;
                case BUY_SERVICE:
                case BUY_PRODUCT:
                case BUY_MATERIAL:
                    return AmountChangeType.EXPEND;
                case PAYOUT:
                case REFUND:
                    return AmountChangeType.INCOME;
                case CHANGE_WAYBILL:
                case OTHER:
                case CUSTOMS_DUTY:
                case ADMIN_PREPAY_SHIPPING:
                case CAL_VOLUME:
                case JIT_SHIPPING_COST:
                    return AmountChangeType.ERROR;
                default:
                    break;
            }
        }
        return AmountChangeType.NONE;
    }

    @Deprecated
    public static AmountChangeType calculateAmountChangeTypeOfTenant(Payment payment) {
        return getChangeTypeOfTenant(payment.getSourceRole(), payment.getTargetRole(), payment.getPurposeType());
    }

    @Deprecated
    public static AmountChangeType calculateAmountChangeTypeOfSaas(String sourceRole, String targetRole, String purposeTypeCode) {
        Optional<PurposeType> purposeTypeOptional = PurposeType.getByCode(purposeTypeCode);
        if (!purposeTypeOptional.isPresent()) {
            return AmountChangeType.ERROR;
        }
        PurposeType purposeType = purposeTypeOptional.get();
        if (sourceRole.equals(PaymentRoleEnum.SAAS.getCode())) {
            switch (purposeType) {
                case RECHARGE:
                    return AmountChangeType.RECHARGE;
                case WITHDRAW:
                    return AmountChangeType.WITHDRAW;
                case REFUND:
                case PAYOUT:
                    return AmountChangeType.EXPEND;
                case BUY_PRODUCT:
                case BUY_SERVICE:
                case BUY_MATERIAL:
                case CHANGE_WAYBILL:
                case OTHER:
                case CUSTOMS_DUTY:
                case ADMIN_PREPAY_SHIPPING:
                case CAL_VOLUME:
                case JIT_SHIPPING_COST:
                    return AmountChangeType.ERROR;
                default:
                    break;
            }
        } else if (targetRole.equals(PaymentRoleEnum.SAAS.getCode())) {
            switch (purposeType) {
                case RECHARGE:
                    return AmountChangeType.RECHARGE;
                case WITHDRAW:
                    return AmountChangeType.WITHDRAW;
                case BUY_SERVICE:
                case BUY_PRODUCT:
                case BUY_MATERIAL:
                case ADMIN_PREPAY_SHIPPING:
                case CHANGE_WAYBILL:
                case CAL_VOLUME:
                case CUSTOMS_DUTY:
                case FINISHED_DIFF:
                case OTHER:
                case JIT_SHIPPING_COST:
                    return AmountChangeType.INCOME;
                case REFUND:
                case PAYOUT:
                    return AmountChangeType.ERROR;
                default:
                    break;
            }
        }
        return AmountChangeType.NONE;
    }


    private static AmountChangeType calculateAmountChangeTypeOfSaas(Payment payment) {
        return calculateAmountChangeTypeOfSaas(payment.getSourceRole(), payment.getTargetRole(), payment.getPurposeType());
    }


    public static MerchantBill generateBaseBillByPayment(Payment payment) {
        return new MerchantBill()
            .setBizNo(CharSequenceUtil.isBlank(payment.getBizNoForBill()) ? payment.getBizNo() : payment.getBizNoForBill())
            .setTradeNo(payment.getTradeNo())
            .setTargetRole(payment.getTradeNo())
            .setOrderName(payment.getTitle())
            .setCreateTimestamp(System.currentTimeMillis())
            .setSourceRole(payment.getSourceRole())
            .setSourceTenantId(payment.getSourceTenantId())
            .setSourceMerchantId(payment.getSourceMerchantId())
            .setSourceUserId(payment.getSourceUserId())
            .setTargetRole(payment.getTargetRole())
            .setTargetTenantId(payment.getTargetTenantId())
            .setTargetMerchantId(payment.getTargetMerchantId())
            .setPaymentMethod(payment.getMethod())
            .setPayChannel(payment.getPayChannel())
            .setPurposeType(payment.getPurposeType())
            .setDetailPurpose(payment.getDetailPurpose())
            .setOperateRole(payment.getOperateRole())
            .setOperateUserId(payment.getOperateUserId())
            .setOperateTargetRole(payment.getOperateTargetRole())
            .setOperateTargetRoleId(payment.getOperateTargetRoleId())
            .setRemarks(payment.getRemark());
    }


    public static MerchantBill generateBaseBillByRefund(Refund refund) {
        return new MerchantBill()
            .setBizNo(CharSequenceUtil.isBlank(refund.getBizNoForBill()) ? refund.getBizNo() : refund.getBizNoForBill())
            .setTradeNo(refund.getTradeNo())
            .setOrderName(refund.getSubject())
            .setCreateTimestamp(System.currentTimeMillis())
            .setSourceRole(refund.getSourceRole())
            .setSourceTenantId(refund.getSourceTenantId())
            .setSourceMerchantId(refund.getSourceMerchantId())
            .setSourceUserId(refund.getSourceUserId())
            .setTargetRole(refund.getTargetRole())
            .setTargetTenantId(refund.getTargetTenantId())
            .setTargetMerchantId(refund.getTargetMerchantId())
            .setPaymentMethod(refund.getPayMethod())
            .setPayChannel(refund.getPayChannel())
            .setPurposeType(refund.getPurposeType())
            .setDetailPurpose(refund.getDetailPurpose())
            .setOperateRole(refund.getOperateRole())
            .setOperateUserId(refund.getOperateUserId())
            .setOperateTargetRole(refund.getOperateTargetRole())
            .setOperateTargetRoleId(refund.getOperateTargetRoleId())
            .setRemarks(refund.getRemark());
    }


    public static void calculateAndRefreshAmountChangeType(MerchantBill bill) {
        String sourceRole = bill.getSourceRole();
        String targetRole = bill.getTargetRole();
        String purposeTypeCode = bill.getPurposeType();

        AmountChangeType changeTypeOfMerchant = calculateAmountChangeTypeOfMerchant(sourceRole, targetRole, purposeTypeCode);
        AmountChangeType changeTypeOfTenant = getChangeTypeOfTenant(sourceRole, targetRole, purposeTypeCode);
        AmountChangeType changeTypeOfSaas = calculateAmountChangeTypeOfSaas(sourceRole, targetRole, purposeTypeCode);

        bill.setAmountChangeTypeOfMerchant(changeTypeOfMerchant.getCode());
        bill.setAmountChangeTypeOfTenant(changeTypeOfTenant.getCode());
        bill.setAmountChangeTypeOfSaas(changeTypeOfSaas.getCode());
    }

    @Deprecated
    public static MerchantBill genByPayment(Payment payment, MerchantBillDisposeMoney disposeMoney) {
        AmountChangeType changeTypeOfMerchant = calculateAmountChangeTypeOfMerchant(payment);
        AmountChangeType changeTypeOfTenant = calculateAmountChangeTypeOfTenant(payment);
        AmountChangeType changeTypeOfSaas = calculateAmountChangeTypeOfSaas(payment);

        MerchantBill bill = MerchantBill.builder()
            .bizNo(payment.getBizNo())
            .tradeNo(payment.getTradeNo())
            .orderName(payment.getTitle())
            .createTimestamp(System.currentTimeMillis())
            .sourceRole(payment.getSourceRole())
            .sourceTenantId(payment.getSourceTenantId())
            .sourceMerchantId(payment.getSourceMerchantId())
            .sourceUserId(payment.getSourceUserId())
            .targetRole(payment.getTargetRole())
            .targetTenantId(payment.getTargetTenantId())
            .targetMerchantId(payment.getTargetMerchantId())
            .paymentMethod(payment.getMethod())
            .payChannel(payment.getPayChannel())
            .changedBalance(payment.getBalance())
            .changedGift(payment.getBonus())
            .purposeType(payment.getPurposeType())
            .amountChangeTypeOfMerchant(changeTypeOfMerchant.getCode())
            .amountChangeTypeOfTenant(changeTypeOfTenant.getCode())
            .amountChangeTypeOfSaas(changeTypeOfSaas.getCode())
            .detailPurpose(payment.getDetailPurpose())
            .disposeTenantBalance(disposeMoney.getDisposeTenantBalance())
            .disposeTenantGift(disposeMoney.getDisposeTenantGift())
            .disposeMerchantBalance(disposeMoney.getDisposeMerchantBalance())
            .disposeMerchantGift(disposeMoney.getDisposeMerchantGift())
            .operateRole(payment.getOperateRole())
            .operateUserId(payment.getOperateUserId())
            .operateTargetRole(payment.getOperateTargetRole())
            .operateTargetRoleId(payment.getOperateTargetRoleId())
            .remarks(payment.getRemark())
            .build();

        if (PaymentMethodEnum.ALI_PAY.getCode().equals(bill.getPaymentMethod())
            || PaymentMethodEnum.LAKALA.getCode().equals(bill.getPaymentMethod())) {
            bill.setChangedBalance(payment.getTotalAmount());
        }

        MerchantBillService.calculateAndSetRoleAndRoleId(bill);
        MerchantBillService.calculateAndSetRelatedMerchantTenantSaas(bill);
        return bill;
    }

    @Deprecated
    public static MerchantBill genSaasToTenantRechargeBill(TenantBalanceLog balanceLog, Long operateUserId, MerchantBillDisposeMoney disposeMoney) {
        MerchantBill bill = MerchantBill.builder()
            .bizNo(balanceLog.getBizNo())
            .tradeNo(balanceLog.getTradeNo())
            .changedGift(balanceLog.getChangedBonus())
            .changedBalance(balanceLog.getChangedBalance())
            .purposeType(PurposeType.RECHARGE.getCode())
            .paymentMethod(PaymentMethodEnum.BALANCE.getCode())
            .targetRole(PaymentRoleEnum.TENANT.getCode())
            .targetTenantId(balanceLog.getTenantId())
            .targetMerchantId(0L)
            .targetUserId(0L)
            .sourceRole(PaymentRoleEnum.SAAS.getCode())
            .sourceTenantId(0L)
            .sourceMerchantId(0L)
            .sourceUserId(0L)
            .orderName(DetailPurpose.SAAS_RECHARGE_TO_TENANT.getDesc())
            .amountChangeTypeOfMerchant(AmountChangeType.NONE.getCode())
            .amountChangeTypeOfTenant(AmountChangeType.RECHARGE.getCode())
            .amountChangeTypeOfSaas(AmountChangeType.RECHARGE.getCode())
            .detailPurpose(DetailPurpose.SAAS_RECHARGE_TO_TENANT.getCode())
            .createTimestamp(System.currentTimeMillis())
            .disposeTenantBalance(disposeMoney.getDisposeTenantBalance())
            .disposeTenantGift(disposeMoney.getDisposeTenantGift())
            .disposeMerchantBalance(disposeMoney.getDisposeMerchantBalance())
            .disposeMerchantGift(disposeMoney.getDisposeMerchantGift())
            .remarks(balanceLog.getRemark())
            .operateRole(PaymentRoleEnum.SAAS.getCode())
            .operateUserId(operateUserId)
            .operateTargetRole(PaymentRoleEnum.TENANT.getCode())
            .operateTargetRoleId(balanceLog.getTenantId())
            .build();
        MerchantBillService.calculateAndSetRoleAndRoleId(bill);
        MerchantBillService.calculateAndSetRelatedMerchantTenantSaas(bill);
        return bill;
    }


    /**
     * SAAS给租户充值的账单
     *
     * @param balanceLog
     * @param operateUserId
     * @return
     */
    @Deprecated
    public static MerchantBill genSaasToTenantSubBill(TenantBalanceLog balanceLog, Long operateUserId, MerchantBillDisposeMoney disposeMoney) {
        MerchantBill bill = MerchantBill.builder()
            .bizNo(balanceLog.getBizNo())
            .tradeNo(balanceLog.getTradeNo())
            .changedGift(balanceLog.getChangedBonus())
            .changedBalance(balanceLog.getChangedBalance())
            .purposeType(PurposeType.BUY_PRODUCT.getCode())
            .paymentMethod(PaymentMethodEnum.BALANCE.getCode())
            .targetRole(PaymentRoleEnum.TENANT.getCode())
            .targetTenantId(balanceLog.getTenantId())
            .targetMerchantId(0L)
            .targetUserId(0L)
            .sourceRole(PaymentRoleEnum.SAAS.getCode())
            .sourceTenantId(0L)
            .sourceMerchantId(0L)
            .sourceUserId(0L)
            .orderName(DetailPurpose.SAAS_SUB_FROM_TENANT.getDesc())
            .amountChangeTypeOfMerchant(AmountChangeType.NONE.getCode())
            .amountChangeTypeOfTenant(AmountChangeType.EXPEND.getCode())
            .amountChangeTypeOfSaas(AmountChangeType.EXPEND.getCode())
            .detailPurpose(DetailPurpose.SAAS_SUB_FROM_TENANT.getCode())
            .createTimestamp(System.currentTimeMillis())
            .disposeTenantBalance(disposeMoney.getDisposeTenantBalance())
            .disposeTenantGift(disposeMoney.getDisposeTenantGift())
            .disposeMerchantBalance(disposeMoney.getDisposeMerchantBalance())
            .disposeMerchantGift(disposeMoney.getDisposeMerchantGift())
            .remarks(balanceLog.getRemark())
            .operateRole(PaymentRoleEnum.SAAS.getCode())
            .operateUserId(operateUserId)
            .operateTargetRole(PaymentRoleEnum.TENANT.getCode())
            .operateTargetRoleId(balanceLog.getTenantId())
            .build();
        MerchantBillService.calculateAndSetRoleAndRoleId(bill);
        MerchantBillService.calculateAndSetRelatedMerchantTenantSaas(bill);
        return bill;
    }

    @Deprecated
    public static MerchantBill genAfterResendTenantToSaasBill(TenantBalanceLog balanceLog, Long operateUserId,
                                                              MerchantBillDisposeMoney disposeMoney, String orderName) {
        MerchantBill bill = MerchantBill.builder()
            .bizNo(balanceLog.getBizNo())
            .tradeNo(balanceLog.getTradeNo())
            .changedGift(balanceLog.getChangedBonus())
            .changedBalance(balanceLog.getChangedBalance())
            .purposeType(PurposeType.BUY_PRODUCT.getCode())
            .paymentMethod(PaymentMethodEnum.BALANCE.getCode())
            .targetRole(PaymentRoleEnum.SAAS.getCode())
            .targetTenantId(0L)
            .targetMerchantId(0L)
            .targetUserId(0L)
            .sourceRole(PaymentRoleEnum.TENANT.getCode())
            .sourceTenantId(balanceLog.getTenantId())
            .sourceMerchantId(0L)
            .sourceUserId(0L)
            .orderName(orderName)
            .amountChangeTypeOfMerchant(AmountChangeType.NONE.getCode())
            .amountChangeTypeOfTenant(AmountChangeType.EXPEND.getCode())
            .amountChangeTypeOfSaas(AmountChangeType.INCOME.getCode())
            .detailPurpose(DetailPurpose.AFTER_RESEND_TENANT_TO_SAAS.getCode())
            .createTimestamp(System.currentTimeMillis())
            .disposeTenantBalance(disposeMoney.getDisposeTenantBalance())
            .disposeTenantGift(disposeMoney.getDisposeTenantGift())
            .disposeMerchantBalance(disposeMoney.getDisposeMerchantBalance())
            .disposeMerchantGift(disposeMoney.getDisposeMerchantGift())
            .remarks(balanceLog.getRemark())
            .operateRole(PaymentRoleEnum.SAAS.getCode())
            .operateUserId(operateUserId)
            .operateTargetRole(PaymentRoleEnum.TENANT.getCode())
            .operateTargetRoleId(balanceLog.getTenantId())
            .build();
        MerchantBillService.calculateAndSetRoleAndRoleId(bill);
        MerchantBillService.calculateAndSetRelatedMerchantTenantSaas(bill);
        return bill;
    }

    public static Optional<MerchantBill> genOpenOnlinePayTenantRechargeBill(
        TenantWallet tenantWallet, String bizNo, String tradeNo, BigDecimal changedBalance, BigDecimal changedGift
    ) {
        if (changedBalance.compareTo(BigDecimal.ZERO) == 0
            && changedGift.compareTo(BigDecimal.ZERO) == 0
        ) {
            return Optional.empty();
        }

        MerchantBill bill = MerchantBill.builder()
            .bizNo(bizNo)
            .tradeNo(tradeNo)
            .changedGift(changedGift)
            .changedBalance(changedBalance)
            .purposeType(PurposeType.RECHARGE.getCode())
            .paymentMethod(PaymentMethodEnum.BALANCE.getCode())
            .targetRole(PaymentRoleEnum.TENANT.getCode())
            .targetTenantId(tenantWallet.getId())
            .targetMerchantId(0L)
            .targetUserId(0L)
            .amountChangeTypeOfMerchant(AmountChangeType.NONE.getCode())
            .amountChangeTypeOfTenant(AmountChangeType.RECHARGE.getCode())
            .amountChangeTypeOfSaas(AmountChangeType.RECHARGE.getCode())
            .sourceRole(PaymentRoleEnum.SAAS.getCode())
            .orderName(SubjectContentType.MERCHANT_SAAS_BALANCE_TRANSFER_TENANT_BALANCE.getTemplate())
            .detailPurpose(DetailPurpose.TENANT_OPEN_ONLINE_PAY.getCode())
            .createTimestamp(System.currentTimeMillis())
            .disposeMerchantBalance(BigDecimal.ZERO)
            .disposeMerchantGift(BigDecimal.ZERO)
            .disposeTenantBalance(tenantWallet.getBalance())
            .disposeTenantGift(tenantWallet.getGiftMoney())
            .operateRole(PaymentRoleEnum.SYSTEM.getCode())
            .operateUserId(0L)
            .operateTargetRole(PaymentRoleEnum.TENANT.getCode())
            .operateTargetRoleId(tenantWallet.getId())
            .build();
        MerchantBillV2Service.refreshAmountChangeRoleOfBill(bill);
        MerchantBillV2Service.calculateAmountChangeTypeOfBill(bill);
        MerchantBillV2Service.calculateAndSetRelatedMerchantTenantSaas(bill);
        return Optional.of(bill);
    }

    public static Optional<MerchantBill> genOpenOnlinePayMerchantWithdrawBill(
        Merchant merchant, String bizNo, String tradeNo
    ) {
        if (merchant.getBalance().compareTo(BigDecimal.ZERO) == 0
            && merchant.getFreeGold().compareTo(BigDecimal.ZERO) == 0
        ) {
            return Optional.empty();
        }

        MerchantBill bill = MerchantBill.builder()
            .bizNo(bizNo)
            .tradeNo(tradeNo)
            .changedGift(merchant.getFreeGold())
            .changedBalance(merchant.getBalance())
            .purposeType(PurposeType.WITHDRAW.getCode())
            .paymentMethod(PaymentMethodEnum.BALANCE.getCode())
            .sourceRole(PaymentRoleEnum.SAAS.getCode())
            .sourceTenantId(0L)
            .sourceMerchantId(0L)
            .sourceUserId(0L)
            .targetRole(PaymentRoleEnum.MERCHANT.getCode())
            .targetTenantId(merchant.getTenantId())
            .targetMerchantId(merchant.getId())
            .targetUserId(0L)
            .amountChangeTypeOfMerchant(AmountChangeType.WITHDRAW.getCode())
            .amountChangeTypeOfTenant(AmountChangeType.NONE.getCode())
            .amountChangeTypeOfSaas(AmountChangeType.WITHDRAW.getCode())
            .orderName(SubjectContentType.MERCHANT_SAAS_BALANCE_TRANSFER_TENANT_BALANCE.getTemplate())
            .detailPurpose(DetailPurpose.TENANT_OPEN_ONLINE_PAY.getCode())
            .createTimestamp(System.currentTimeMillis())
            .disposeMerchantBalance(BigDecimal.ZERO)
            .disposeMerchantGift(BigDecimal.ZERO)
            .operateRole(PaymentRoleEnum.SYSTEM.getCode())
            .operateUserId(0L)
            .operateTargetRole(PaymentRoleEnum.MERCHANT.getCode())
            .operateTargetRoleId(merchant.getId())
            .build();
        MerchantBillV2Service.refreshAmountChangeRoleOfBill(bill);
        MerchantBillV2Service.calculateAmountChangeTypeOfBill(bill);
        MerchantBillV2Service.calculateAndSetRelatedMerchantTenantSaas(bill);
        return Optional.of(bill);
    }

    /**
     * 打开线上支付开关，生成商户充值账单，如果变动金额为0，则不生成
     *
     * @param merchant
     * @param bizNo
     * @param tradeNo
     * @param disposeTenantBalance
     * @param disposeTenantGift
     * @return
     */
    public static Optional<MerchantBill> genOpenOnlinePayMerchantRechargeBill(
        Merchant merchant,
        String bizNo,
        String tradeNo,
        BigDecimal disposeTenantBalance,
        BigDecimal disposeTenantGift
    ) {
        if (merchant.getBalance().compareTo(BigDecimal.ZERO) == 0
            && merchant.getFreeGold().compareTo(BigDecimal.ZERO) == 0
        ) {
            return Optional.empty();
        }

        MerchantBill bill = MerchantBill.builder()
            .bizNo(bizNo)
            .tradeNo(tradeNo)
            .changedGift(merchant.getFreeGold())
            .changedBalance(merchant.getBalance())
            .purposeType(PurposeType.RECHARGE.getCode())
            .paymentMethod(PaymentMethodEnum.BALANCE.getCode())
            .targetRole(PaymentRoleEnum.MERCHANT.getCode())
            .targetTenantId(merchant.getTenantId())
            .targetMerchantId(merchant.getId())
            .targetUserId(0L)
            .amountChangeTypeOfMerchant(AmountChangeType.RECHARGE.getCode())
            .amountChangeTypeOfTenant(AmountChangeType.RECHARGE.getCode())
            .amountChangeTypeOfSaas(AmountChangeType.NONE.getCode())
            .sourceRole(PaymentRoleEnum.TENANT.getCode())
            .sourceTenantId(merchant.getTenantId())
            .sourceMerchantId(0L)
            .sourceUserId(0L)
            .orderName(SubjectContentType.MERCHANT_SAAS_BALANCE_TRANSFER_TENANT_BALANCE.getTemplate())
            .detailPurpose(DetailPurpose.TENANT_OPEN_ONLINE_PAY.getCode())
            .createTimestamp(System.currentTimeMillis())
            .disposeMerchantBalance(merchant.getBalance())
            .disposeMerchantGift(merchant.getFreeGold())
            .disposeTenantGift(disposeTenantGift)
            .disposeTenantBalance(disposeTenantBalance)
            .operateRole(PaymentRoleEnum.SYSTEM.getCode())
            .operateUserId(0L)
            .operateTargetRole(PaymentRoleEnum.MERCHANT.getCode())
            .operateTargetRoleId(merchant.getId())
            .build();
        MerchantBillV2Service.refreshAmountChangeRoleOfBill(bill);
        MerchantBillV2Service.calculateAmountChangeTypeOfBill(bill);
        MerchantBillV2Service.calculateAndSetRelatedMerchantTenantSaas(bill);
        return Optional.of(bill);
    }


    @Deprecated
    public static MerchantBill genByRefund(Refund refund, MerchantBillDisposeMoney disposeMoney) {
        AmountChangeType changeTypeOfMerchant = calculateAmountChangeTypeOfMerchant(refund.getSourceRole(), refund.getTargetRole(), refund.getPurposeType());
        AmountChangeType changeTypeOfTenant = getChangeTypeOfTenant(refund.getSourceRole(), refund.getTargetRole(), refund.getPurposeType());
        AmountChangeType changeTypeOfSaas = calculateAmountChangeTypeOfSaas(refund.getSourceRole(), refund.getTargetRole(), refund.getPurposeType());

        MerchantBill bill = MerchantBill.builder()
            .bizNo(refund.getBizNo())
            .tradeNo(refund.getTradeNo())
            .orderName(refund.getSubject())
            .createTimestamp(System.currentTimeMillis())
            .sourceRole(refund.getSourceRole())
            .sourceTenantId(refund.getSourceTenantId())
            .sourceMerchantId(refund.getSourceMerchantId())
            .sourceUserId(refund.getSourceUserId())
            .targetRole(refund.getTargetRole())
            .targetTenantId(refund.getTargetTenantId())
            .targetMerchantId(refund.getTargetMerchantId())
            .paymentMethod(refund.getPayMethod())
            .changedBalance(refund.getBalance())
            .changedGift(refund.getBonus())
            .purposeType(refund.getPurposeType())
            .amountChangeTypeOfMerchant(changeTypeOfMerchant.getCode())
            .amountChangeTypeOfTenant(changeTypeOfTenant.getCode())
            .amountChangeTypeOfSaas(changeTypeOfSaas.getCode())
            .detailPurpose(refund.getDetailPurpose())
            .disposeTenantBalance(disposeMoney.getDisposeTenantBalance())
            .disposeTenantGift(disposeMoney.getDisposeTenantGift())
            .disposeMerchantBalance(disposeMoney.getDisposeMerchantBalance())
            .disposeMerchantGift(disposeMoney.getDisposeMerchantGift())
            .operateRole(refund.getOperateRole())
            .operateUserId(refund.getOperateUserId())
            .build();

        if (PaymentRoleEnum.MERCHANT.getCode().equals(refund.getTargetRole())) {
            bill.setOperateTargetRoleId(refund.getTargetMerchantId());
            bill.setOperateTargetRole(refund.getTargetRole());
        } else if (PaymentRoleEnum.TENANT.getCode().equals(refund.getTargetRole())) {
            bill.setOperateTargetRoleId(refund.getTargetTenantId());
            bill.setOperateTargetRole(refund.getTargetRole());
        }

        MerchantBillService.calculateAndSetRoleAndRoleId(bill);
        MerchantBillService.calculateAndSetRelatedMerchantTenantSaas(bill);
        return bill;
    }
}
