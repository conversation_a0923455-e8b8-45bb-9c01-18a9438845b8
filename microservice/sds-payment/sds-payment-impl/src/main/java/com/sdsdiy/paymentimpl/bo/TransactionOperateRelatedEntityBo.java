package com.sdsdiy.paymentimpl.bo;

import cn.hutool.core.collection.CollUtil;
import com.sdsdiy.paymentimpl.entity.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.*;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TransactionOperateRelatedEntityBo extends TransactionWalletEntityBo {
    
    private Integer billIndex = 1;

    public Integer getBillIndexAndIncr() {
        return billIndex++;
    }

    private List<Refund> wantOperateRefunds = new ArrayList<>();
    private List<Payment> wantOperatePayments = new ArrayList<>();

    private List<TransactionEntry> transactionEntryList = new ArrayList<>();

    private List<TenantBalanceLog> tenantBalanceLogs = new ArrayList<>();
    private List<MerchantBalanceLog> merchantBalanceLogs = new ArrayList<>();
    private List<MerchantUserAccountLog> userAccountLogs = new ArrayList<>();
    private List<TenantDisBalanceLog> tenantDisBalanceLogs = new ArrayList<>();

    private List<MerchantBill> merchantBills = new ArrayList<>();

    private List<SubTransactionTask> subTransactionTasks = new ArrayList<>();


    public void addTenantDisWallet(TenantDisWallet tenantDisWallet) {
        String key = tenantDisWalletMapKey(tenantDisWallet.getDisTenantId(), tenantDisWallet.getSupTenantId());
        this.getTenantDisWalletMap().put(key, tenantDisWallet);
    }

    public TenantDisWallet getTenantDisWallet(long disTenantId, long supTenantId) {
        String key = tenantDisWalletMapKey(disTenantId, supTenantId);
        return this.getTenantDisWalletMap().get(key);
    }

    public TenantWallet findTenantWallet(long tenantId) {
        return this.getTenantWalletMap().get(tenantId);
    }


    public static String tenantDisWalletMapKey(Long disTenantId, Long supTenantId) {
        return disTenantId + "-" + supTenantId;
    }

    public Set<Long> calculateBalanceUpdatedMerchantIds() {
        if (CollUtil.isEmpty(merchantBalanceLogs)) {
            return Collections.emptySet();
        }
        Set<Long> merchantIds = new HashSet<>();
        for (MerchantBalanceLog log : merchantBalanceLogs) {
            merchantIds.add(log.getMerchantId());
        }
        return merchantIds;
    }

    public Set<Long> calculateBalanceUpdatedTenantIds() {
        if (CollUtil.isEmpty(tenantBalanceLogs)) {
            return Collections.emptySet();
        }
        Set<Long> ids = new HashSet<>();
        for (TenantBalanceLog log : tenantBalanceLogs) {
            ids.add(log.getTenantId());
        }
        return ids;
    }

    public Set<Long> calculateBalanceUpdatedUserAccount() {
        if (CollUtil.isEmpty(userAccountLogs)) {
            return Collections.emptySet();
        }
        Set<Long> ids = new HashSet<>();
        for (MerchantUserAccountLog log : userAccountLogs) {
            ids.add(log.getMerchantUserAccountId());
        }
        return ids;
    }

    public Set<String> calculateBalanceUpdatedDisWallets() {
        if (CollUtil.isEmpty(tenantDisBalanceLogs)) {
            return Collections.emptySet();
        }
        Set<String> ids = new HashSet<>();
        for (TenantDisBalanceLog log : tenantDisBalanceLogs) {
            ids.add(tenantDisWalletMapKey(log.getDisTenantId(), log.getSupTenantId()));
        }
        return ids;
    }
}
