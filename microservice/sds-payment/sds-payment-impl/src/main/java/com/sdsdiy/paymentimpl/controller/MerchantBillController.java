package com.sdsdiy.paymentimpl.controller;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.common.base.entity.dto.BaseDownloadDTO;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.valid.VersionDeprecatedUtil;
import com.sdsdiy.core.base.util.ConvertUtil;
import com.sdsdiy.paymentapi.api.MerchantBillApi;
import com.sdsdiy.paymentapi.constant.AmountChangeType;
import com.sdsdiy.paymentapi.dto.*;
import com.sdsdiy.paymentapi.param.bill.BillQueryParam;
import com.sdsdiy.paymentapi.param.bill.MerchantBillsCreateParam;
import com.sdsdiy.paymentimpl.entity.MerchantBill;
import com.sdsdiy.paymentimpl.manager.MerchantBillMapperManager;
import com.sdsdiy.paymentimpl.service.MerchantBillGenerateService;
import com.sdsdiy.paymentimpl.service.MerchantBillService;
import com.sdsdiy.paymentimpl.service.MerchantBillV2Service;
import com.sdsdiy.statdata.constant.DataExportRecordTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 支付记录表(Payment)表控制层
 *
 * <AUTHOR>
 * @since 2021-10-29 17:30:06
 */
@RestController
@Slf4j
@RequiredArgsConstructor
public class MerchantBillController implements MerchantBillApi {
    
    private final MerchantBillMapperManager merchantBillMapperManager;
    private final MerchantBillV2Service merchantBillV2Service;


    @Override
    public List<MerchantBillDto> batchCreate(MerchantBillsCreateParam param) {
        VersionDeprecatedUtil.promptError("20250822");
        log.info("merchant bill batch create, params={}", JSON.toJSONString(param));
        List<MerchantBill> merchantBills = ConvertUtil.dtoConvert(param.getParams(), MerchantBill.class);

        AmountChangeType amountChangeType;
        for (MerchantBill bill : merchantBills) {
            amountChangeType = MerchantBillGenerateService.calculateAmountChangeTypeOfMerchant(bill.getSourceRole(), bill.getTargetRole(), bill.getPurposeType());
            bill.setAmountChangeTypeOfMerchant(amountChangeType.getCode());
            amountChangeType = MerchantBillGenerateService.getChangeTypeOfTenant(bill.getSourceRole(), bill.getTargetRole(), bill.getPurposeType());
            bill.setAmountChangeTypeOfTenant(amountChangeType.getCode());
            amountChangeType = MerchantBillGenerateService.calculateAmountChangeTypeOfSaas(bill.getSourceRole(), bill.getTargetRole(), bill.getPurposeType());
            bill.setAmountChangeTypeOfSaas(amountChangeType.getCode());
            bill.setCreateTimestamp(System.currentTimeMillis());
            MerchantBillService.calculateAndSetRoleAndRoleId(bill);
            MerchantBillService.calculateAndSetRelatedMerchantTenantSaas(bill);
        }

        log.info("merchant bill batch create, saved entities={}", JSON.toJSONString(merchantBills));
        boolean saveBatchResult = merchantBillMapperManager.saveBatch(merchantBills);
        if (!saveBatchResult) {
            throw new BusinessException("账单保存失败");
        }

        return ConvertUtil.dtoConvert(merchantBills, MerchantBillDto.class);
    }

    @Override
    public PageResultDto<MerchantBillVo> billQueryPage(BillQueryParam param) {
        return merchantBillV2Service.billQueryPage(param);
    }

    @Override
    public List<MerchantBillVo> billQueryList(BillQueryParam param) {
        return merchantBillV2Service.billQueryList(param);
    }

    @Override
    public BaseDownloadDTO billQueryExport(BillQueryParam param, DataExportRecordTypeEnum exportType) {
        return merchantBillV2Service.billQueryExportV2(param, exportType);
    }

    @Override
    public BillMonthlyAmountOfMerchantVo monthlyAmountStatForMerchant(BillQueryParam param) {
        return merchantBillV2Service.calculateAndGenerateMonthlyAmount(param);
    }

    @Override
    public SaasTotalBillMonthlyAmountDto monthlyAmountStatForSaasPlatformBill(BillQueryParam param) {
        return merchantBillV2Service.monthlyAmountStatForSaasPlatformBill(param);
    }

    @Override
    public TenantTotalBillMonthlyAmountDto monthlyAmountStatForPodTenantBill(BillQueryParam param) {
        return merchantBillV2Service.monthlyAmountStatForPodTenantBill(param);
    }
}
