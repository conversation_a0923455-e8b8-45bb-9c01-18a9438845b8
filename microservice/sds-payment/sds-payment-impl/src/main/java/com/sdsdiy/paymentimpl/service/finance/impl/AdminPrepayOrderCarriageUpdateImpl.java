package com.sdsdiy.paymentimpl.service.finance.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.base.Joiner;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.orderapi.dto.AdminPrepaidOrderNoLogisticsRespDto;
import com.sdsdiy.paymentapi.constant.OrderFinanceBillPeriodTypeEnum;
import com.sdsdiy.paymentapi.dto.msg.OrderFinanceUpdateMsg;
import com.sdsdiy.paymentimpl.bo.FinanceUpdateResultBo;
import com.sdsdiy.paymentimpl.entity.OrderFinance;
import com.sdsdiy.paymentimpl.entity.OrderFinanceBillPeriod;
import com.sdsdiy.paymentimpl.entity.OrderFinanceLogistics;
import com.sdsdiy.paymentimpl.feign.AdminPrepaidFeign;
import com.sdsdiy.paymentimpl.manager.OrderFinanceBillPeriodManage;
import com.sdsdiy.paymentimpl.manager.OrderFinanceLogisticsManage;
import com.sdsdiy.paymentimpl.manager.OrderFinanceManage;
import com.sdsdiy.paymentimpl.service.finance.base.AbstractFinance;
import com.sdsdiy.paymentimpl.service.finance.related.AdminPrepayOrderCarriageUpdateRelatedEntity;
import com.sdsdiy.paymentimpl.service.finance.saved.AdminPrepayOrderCarriageUpdateSavedEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class AdminPrepayOrderCarriageUpdateImpl extends AbstractFinance<AdminPrepayOrderCarriageUpdateRelatedEntity, AdminPrepayOrderCarriageUpdateSavedEntity> {

    private final OrderFinanceManage orderFinanceManage;
    private final OrderFinanceBillPeriodManage orderFinanceBillPeriodManage;
    private final AdminPrepaidFeign adminPrepaidFeign;
    private final OrderFinanceLogisticsManage orderFinanceLogisticsManage;

    @Override
    public OrderFinanceBillPeriodTypeEnum getBillPeriodType() {
        return OrderFinanceBillPeriodTypeEnum.ADMIN_PREPAID_CARRIAGE_NO_UPDATE;
    }

    @Override
    public AdminPrepayOrderCarriageUpdateRelatedEntity generateRelatedEntity(OrderFinanceUpdateMsg msg) {
        AdminPrepayOrderCarriageUpdateRelatedEntity relatedEntity = new AdminPrepayOrderCarriageUpdateRelatedEntity();

        String orderNo = msg.getOrderNo();

        // 寄付
        AdminPrepaidOrderNoLogisticsRespDto logisticsInfoByOrderNo = adminPrepaidFeign.getLogisticsInfoByOrderNo(0L, orderNo);
        relatedEntity.setAdminPrepaidOrderNoLogistics(logisticsInfoByOrderNo);

        List<OrderFinanceBillPeriod> billPeriods = orderFinanceBillPeriodManage.findByOrderNo(msg.getOrderNo());
        OrderFinanceBillPeriod firstBillPeriod = null;
        for (OrderFinanceBillPeriod billPeriod : billPeriods) {
            if (billPeriod.getBillPeriodType().equals(OrderFinanceBillPeriodTypeEnum.ORDER_FINISH.getCode())
                || billPeriod.getBillPeriodType().equals(OrderFinanceBillPeriodTypeEnum.AFTER_SALE_RESEND.getCode())) {
                firstBillPeriod = billPeriod;
                break;
            }
        }
        Assert.validateNull(firstBillPeriod, "财务订单明细不存在");

        relatedEntity.setBillPeriod(firstBillPeriod);

        long periodId = firstBillPeriod.getId();
        long orderFinanceId = firstBillPeriod.getOrderFinanceId();

        OrderFinance orderFinance = orderFinanceManage.getById(orderFinanceId);
        relatedEntity.setOrderFinance(orderFinance);

        List<OrderFinanceLogistics> financeLogistics = orderFinanceLogisticsManage.findAllByPeriodId(periodId);
        relatedEntity.setFinanceLogistics(financeLogistics);

        return relatedEntity;
    }

    @Override
    public AdminPrepayOrderCarriageUpdateSavedEntity generateSavedEntity(AdminPrepayOrderCarriageUpdateRelatedEntity relatedEntity) {
        AdminPrepayOrderCarriageUpdateSavedEntity savedEntity = new AdminPrepayOrderCarriageUpdateSavedEntity();

        savedEntity.setOriginFinance(relatedEntity.getOrderFinance());

        OrderFinanceBillPeriod originPeriod = relatedEntity.getBillPeriod();
        savedEntity.setOriginPeriod(originPeriod);

        AdminPrepaidOrderNoLogisticsRespDto adminPrepaidOrderNoLogistics = relatedEntity.getAdminPrepaidOrderNoLogistics();
        List<OrderFinanceLogistics> originLogistics = relatedEntity.getFinanceLogistics();
        for (OrderFinanceLogistics financeLogistics : originLogistics) {
            String carriageNo = Joiner.on(",").join(adminPrepaidOrderNoLogistics.getCarriageNos());
            financeLogistics.setOrderCarriageNo(carriageNo);
            if (adminPrepaidOrderNoLogistics.getLogisticsServiceProviderId() > 0) {
                financeLogistics.setServiceProvider(adminPrepaidOrderNoLogistics.getLogisticsServiceProvider());
            }

            financeLogistics.setAdminPrepaidBatchNo(adminPrepaidOrderNoLogistics.getBatchNo());
            financeLogistics.setUpdatedAt(System.currentTimeMillis());
        }
        savedEntity.setOriginLogistics(originLogistics);

        return savedEntity;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("common")
    public FinanceUpdateResultBo saveToDb(AdminPrepayOrderCarriageUpdateSavedEntity savedEntity) {
        OrderFinanceBillPeriod originBillPeriod = savedEntity.getOriginPeriod();
        long orderFinanceId = originBillPeriod.getOrderFinanceId();
        long billPeriodId = originBillPeriod.getId();

        boolean result = orderFinanceLogisticsManage.updateBatchById(savedEntity.getOriginLogistics());
        Assert.validateFalse(result, "对账物流更新失败");

        FinanceUpdateResultBo savedResult = new FinanceUpdateResultBo();
        savedResult.setFinanceId(orderFinanceId);
        savedResult.setBillPeriodId(billPeriodId);
        return savedResult;
    }


    @Override
    public void checkSavedEntities(AdminPrepayOrderCarriageUpdateSavedEntity savedEntity) {
    }


    @Override
    public void checkRelatedEntities(AdminPrepayOrderCarriageUpdateRelatedEntity relatedEntities) {
        Assert.validateNull(relatedEntities.getOrderFinance(), "原财务订单不存在");
        Assert.validateNull(relatedEntities.getFinanceLogistics(), "原财务物流信息不存在");
        Assert.validateNull(relatedEntities.getBillPeriod(), "原财务订单账期不存在");
    }


    @Override
    public void checkMsg(OrderFinanceUpdateMsg msg) {
    }
}
