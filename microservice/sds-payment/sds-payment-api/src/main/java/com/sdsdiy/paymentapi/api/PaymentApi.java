package com.sdsdiy.paymentapi.api;

import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.PaymentsCreateParam;
import com.sdsdiy.paymentapi.param.PaymentsPayParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 支付记录表(Payment)表api接口
 *
 * <AUTHOR>
 * @since 2021-10-29 17:29:34
 */
@Api(tags = "")
@RequestMapping("/microservice/payment")
@Validated
@Deprecated
public interface PaymentApi {

    /**
     * 根据ID查找
     *
     * @param id
     *
     * @return
     */
    @ApiOperation("")
    @GetMapping("/{id}")
    @Deprecated
    PaymentDto get(@PathVariable Long id);

    /**
     * 创建
     *
     * @param param
     *
     * @return
     */
    @PostMapping("")
    @Deprecated
    PaymentDto create(
        @RequestBody PaymentParam param
    );

    /**
     * 批量创建
     *
     * @param param
     *
     * @return
     */
    @PostMapping("/batchCreate")
    @Deprecated
    List<PaymentDto> batchCreate(
        @RequestBody @Validated PaymentsCreateParam param
    );

    /**
     * 订单支付并且记录对应账单
     *
     * @param param
     *
     * @return
     */
    @PostMapping("/batchPayAndRecordBills")
    @Deprecated
    List<PaymentDto> batchPayAndRecordsBill(
        @RequestBody PaymentsPayParam param
    );

    /**
     * 支付（不生成账单，账单另外提供接口给业务方生成）
     *
     * @param param
     *
     * @return
     */
    @PostMapping("/batchPay")
    @Deprecated
    List<PaymentDto> batchPay(
        @RequestBody PaymentsPayParam param
    );

    /**
     * 查询第三方订单支付状态
     *
     * @param paymentId
     *
     * @return
     */
    @GetMapping("/{paymentId}/refreshPayStatus")
    @Deprecated
    PaymentDto refreshPayStatus(
        @PathVariable Long paymentId
    );

    @GetMapping("/adminRefundAlipay")
    @Deprecated
    void adminRefundAlipay(@RequestParam Long paymentId);
}
