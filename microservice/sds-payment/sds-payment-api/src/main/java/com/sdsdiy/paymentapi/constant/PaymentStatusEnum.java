package com.sdsdiy.paymentapi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

@Getter
@AllArgsConstructor
public enum PaymentStatusEnum {
    WAIT(1, "等待", "wait"),
    PAID(2, "已经支付", "success"),
    REFUNDED(6, "已经退款", "refunded"),
    WAIT_REFUND(7, "等待退款", "refunding"),
    INVALID(99, "无效", "invalid"),
    ;

    private final Integer status;
    private final String desc;
    private final String strStatus;

    public static Optional<PaymentStatusEnum> getByStatus(int status) {
        return Arrays.stream(PaymentStatusEnum.values()).filter(item -> item.getStatus() == status).findFirst();
    }
}
