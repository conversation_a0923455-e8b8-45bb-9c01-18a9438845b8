package com.sdsdiy.paymentapi.param;


import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.paymentapi.constant.BalanceUsedType;
import com.sdsdiy.paymentapi.constant.PaymentRoleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PaymentParam {

    /**变更开始*/
    /*** {@link com.sdsdiy.paymentapi.constant.TransactionPayTypeEnum}*/
    @ApiModelProperty(value = "区分是否是主交易", notes = "主交易业务方需要手动调用，子交易由支付系统自动处理")
    @NotBlank(message = "payType不能为空")
    private Integer payType;

    /*** {@link com.sdsdiy.paymentapi.constant.PaymentBillTypeEnum}*/
    @ApiModelProperty("记账类型，0-不记，1-直接根据Payment生成账单，2-根据transaction_entry记账，由业务生成")
    @NotNull(message = "billType不能为空")
    private Integer billType;

    /*** {@link com.sdsdiy.paymentapi.constant.DetailPurpose} */
    @ApiModelProperty("详细的作用类型")
    @NotNull(message = "detailPurpose不能为空")
    private String detailPurpose;

    /*** {@link com.sdsdiy.paymentapi.constant.PaymentRoleEnum} */
    @NotNull(message = "sourceRole不能为空")
    private String sourceRole;

    /*** {@link com.sdsdiy.paymentapi.constant.BalanceUsedType} */
    @ApiModelProperty("使用的余额类型")
    private Integer balanceType;

    /*** {@link com.sdsdiy.paymentapi.constant.PaymentRoleEnum} */
    @NotNull(message = "targetRole不能为空")
    private String targetRole;
    /**变更结束*/


    /**以下不动*/
    @NotBlank(message = "bizNo不能为空")
    private String bizNo;
    private String bizNoForBill;

    @NotBlank(message = "method不能为空")
    private String method;

    @ApiModelProperty(value = "标题", notes = "第三方支付支付宝/LAKALA以这个作为订单标题。如果是直接记账，以此作为账单标题")
    @NotBlank(message = "title不能为空")
    private String title;


    /*** {@link com.sdsdiy.paymentapi.constant.PurposeType} */
    @NotBlank(message = "purposeType不能为空")
    @ApiModelProperty("大类的用途")
    private String purposeType;


    /*** {@link com.sdsdiy.paymentapi.constant.AlipaySupportPayeeTypeEnum} */
    @Deprecated
    private Integer alipaySupportPayeeType;

    @ApiModelProperty("totalAmount=balance+freeGold")
    @NotNull(message = "totalAmount不能为空")
    @Deprecated
    private BigDecimal totalAmount;
    @NotNull(message = "balance不能为空")
    private BigDecimal balance = BigDecimal.ZERO;
    @NotNull(message = "bonus不能为空")
    private BigDecimal bonus = BigDecimal.ZERO;

    @NotNull(message = "sourceTenantId不能为空")
    private Long sourceTenantId = 0L;
    @NotNull(message = "sourceMerchantId不能为空")
    private Long sourceMerchantId = 0L;
    @NotNull(message = "sourceUserId不能为空")
    private Long sourceUserId = 0L;

    /*** {@link com.sdsdiy.paymentapi.constant.MerchantPayBalanceUsedType} */
    @Deprecated
    private Integer merchantBalanceUsedType;

    @NotNull(message = "targetTenantId不能为空")
    private Long targetTenantId = 0L;
    @NotNull(message = "targetMerchantId不能为空")
    private Long targetMerchantId = 0L;

    /*** 操作者角色*/
    @NotNull(message = "operateRole不能为空")
    private String operateRole;
    @NotNull(message = "operateUserId不能为空")
    private Long operateUserId;

    /**
     * 操作对象角色，
     * - 比如购买订单，那这边应该是商户
     * - 租户给商户购买服务，这边也应该是商户
     * - 租户找SAAS购买服务，这边是租户
     * - 即，当前交易对应的业务处理对象
     * {@link com.sdsdiy.paymentapi.constant.PaymentRoleEnum}
     */
    @NotEmpty(message = "operateTargetRole不能为空")
    private String operateTargetRole;
    @NotNull(message = "operateTargetRoleId不能为空")
    private Long operateTargetRoleId;

    /*** 备注 */
    private String remark;

    private Integer expiredSecond = 180;


    public void checkParam() {
        this.checkSource();
        this.checkTarget();
    }

    public void checkSource() {
        if (PaymentRoleEnum.SAAS.getCode().equals(this.getSourceRole())) {
            Assert.validateFalse(this.getSourceTenantId() == 0 && this.getSourceMerchantId() == 0 && this.getSourceUserId() == 0, "sourceRole异常");
        } else if (PaymentRoleEnum.TENANT.getCode().equals(this.getSourceRole())) {
            Assert.validateFalse(this.getSourceTenantId() > 0 && this.getSourceMerchantId() == 0 && this.getSourceUserId() == 0, "sourceRole异常");
        } else if (PaymentRoleEnum.TENANT_DIS.getCode().equals(this.getSourceRole())) {
            Assert.validateFalse(this.getSourceTenantId() > 0 && this.getSourceMerchantId() == 0 && this.getSourceUserId() == 0, "sourceRole异常");
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(this.getSourceRole())) {
            if (BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType().equals(this.getBalanceType())) {
                Assert.validateFalse(this.getSourceTenantId() > 0 && this.getSourceMerchantId() > 0 && this.getSourceUserId() == 0, "sourceRole异常");
            } else if (BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType().equals(this.getBalanceType())) {
                Assert.validateFalse(this.getSourceTenantId() > 0 && this.getSourceMerchantId() > 0 && this.getSourceUserId() > 0, "sourceRole异常");
            }
        } else {
            throw new BusinessException("sourceRole异常");
        }
    }

    public void checkTarget() {
        if (PaymentRoleEnum.SAAS.getCode().equals(this.getTargetRole())) {
            Assert.validateFalse(this.getTargetTenantId() == 0 && this.getTargetMerchantId() == 0, "targetRole异常");
        } else if (PaymentRoleEnum.TENANT.getCode().equals(this.getTargetRole())) {
            Assert.validateFalse(this.getTargetTenantId() > 0 && this.getTargetMerchantId() == 0, "targetRole异常");
        } else if (PaymentRoleEnum.TENANT_SUP.getCode().equals(this.getTargetRole())) {
            Assert.validateFalse(this.getTargetTenantId() > 0 && this.getTargetMerchantId() == 0, "targetRole异常");
        } else if (PaymentRoleEnum.MERCHANT.getCode().equals(this.getTargetRole())) {
            if (BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType().equals(this.getBalanceType())) {
                Assert.validateFalse(this.getTargetTenantId() > 0 && this.getTargetMerchantId() > 0, "targetRole异常");
            } else if (BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType().equals(this.getBalanceType())) {
                Assert.validateFalse(this.getTargetTenantId() > 0 && this.getTargetMerchantId() > 0, "targetRole异常");
            }
        } else {
            throw new BusinessException("targetRole异常");
        }
    }
}
