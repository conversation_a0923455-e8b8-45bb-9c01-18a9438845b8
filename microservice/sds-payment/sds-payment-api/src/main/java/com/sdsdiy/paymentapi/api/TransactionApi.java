package com.sdsdiy.paymentapi.api;

import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.dto.RefundDto;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(tags = "")
@RequestMapping("/microservice/payment/transaction")
@Validated
public interface TransactionApi {

    @ApiOperation("获取Payment信息")
    @GetMapping("/queryPayment")
    PaymentDto queryPayment(@RequestParam Long paymentId);

    @ApiOperation("支付")
    @PostMapping("/createPayment")
    PaymentDto createPayment(@RequestBody MultiTransactionCreateParam param);

    @ApiOperation("退款")
    @PostMapping("/createRefund")
    RefundDto createRefund(@RequestBody MultiTransactionCreateParam param);

    @ApiOperation("处理交易")
    @GetMapping("/operateTransaction")
    PaymentDto operateTransaction(@RequestParam Long paymentId);

    @ApiOperation("处理交易")
    @GetMapping("/operateTransactionByRefundId")
    RefundDto operateRefund(@RequestParam Long refundId);

    @ApiOperation("刷新payment支付状态")
    @GetMapping("/refreshPaymentPaidStatus")
    PaymentDto refreshPaymentPaidStatus(@RequestParam Long paymentId);

    @ApiOperation("系统回退")
    @GetMapping("/systemRollbackPayment")
    String systemRollbackPayment(@RequestParam String tradeNo, @RequestParam Long saasUserId);

    @ApiOperation("系统回退")
    @GetMapping("/adminSystemRefundPayment")
    void adminSystemRefundPayment(@RequestParam String tradeNo, @RequestParam Long saasUserId);
}
