package com.sdsdiy.paymentapi.param;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TransactionEntryParam {
    private String paymentBizNo;
    private String refundBizNo;
    private String purposeType;

    @ApiModelProperty(value = "账单BizNo")
    private String bizNoForBill;

    private BigDecimal balance;
    private BigDecimal bonus;


    /*** {@link com.sdsdiy.paymentapi.constant.TransactionEntryTypeEnum}*/
    @ApiModelProperty(value = "是支付，还是退款")
    private Integer type;

    private String title;
    private String remark;
}
