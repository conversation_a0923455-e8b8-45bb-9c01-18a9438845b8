package com.sdsdiy.orderdata.dto;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 商户端订单查询条件
 *
 * <AUTHOR>
 */
@Data
public class PodOrderQueryDto {
    @ApiModelProperty(hidden = true)
    private Long tenantUserId;
    // 租户id
    @ApiModelProperty(hidden = true)
    private Long tenantId;
    // 我的订单 关键词，搜索订单号，生产单号，运单号，逗号分隔
    private String keywords;
    // 分销订单 关键词，搜索订单号，第三方订单号，产品名称，逗号分隔
    private String distributionKeywords;
    /**分销fba订单关键词 搜索订单号，收货人*/
    private String fbaDistributionKeywords;
    private Long merchantId;
    // 商家用户ID，逗号分隔
    private String merchantIds;
    // 创建时间 - 开始
    private String createdDateBegin;
    // 创建时间 - 结束
    private String createdDateEnd;
    // 支付时间 - 开始
    private String payDateBegin;
    // 支付时间 - 结束
    private String payDateEnd;
    // 确认时间 - 开始
    private String confirmDateBegin;
    // 运单号状态编码
    private Integer carriageStatus;
    // 确认时间 - 结束
    private String confirmDateEnd;
    // 完成时间 - 开始
    private String finishDateBegin;
    // 完成时间 - 结束
    private String finishDateEnd;
    // 厂商ID,逗号分隔
    private String factoryIds;
    // 物流id，多个用逗号隔开
    private String logisticsIds;
    // 产品名称
    private String productName;
    // 预警类型，EarlyWarningTypeEnum
    private String earlyWarningType;
    // 发货区域
    private Long issuingBayAreaId;
    private List<Long> bayIds;
    /**1公有，sds产品的订单,2订单私有,租户自己的订单*/
    private Integer productionType;
    // 订单状态编码
    private String orderStatusCode;
    private Integer manuscriptStatus;

    // 跟商户端advanceOrderStatusCodes不同，pod预上网不变为已完成
    private String podOrderStatusCodes;

    //运单号状态
    private String carriageNoStatus;
    //类型
    private String type;
    /**
     * 非某些订单状态，逗号隔开
     */
    private String notOrderStatus;

    //近3个月
    private String orderTimeRangeType;
    //订单类型
    private String originType;

    /**
     * 排序方式（截止发货时间 latestShipTime ，最早发货时间earliestShipTime ，下单时间thirdCreateTime）（升序 + ，  降序 -）多个逗号隔开，默认按下单时间降序
     */
    private String sort;
    private Integer page = 1;
    private Integer size = 10;

    private String nullFieldQuery;
    @ApiModelProperty("产品名称或产品编号")
    /**产品名称或产品编号精准搜索*/
    private String productKeywords;
    /**物流来源 sds物流：PRODUCT_TENANT，租户物流 ORDER_TENANT*/
    private String logisticsSource;

    /**fba入仓步骤*/
    private String packingStep;
    @ApiModelProperty("分销商id")
    private String distributor;
    @ApiModelProperty("是否分销订单")
    private Integer isDistribution;
    @ApiModelProperty("供应商id")
    private Long supplier;

    private String lastLogisticsStatus;
    private Integer productLabelChanged;
    private Integer boxLabelChanged;
    private String status;
    @ApiModelProperty("订单的额外状态 1api同步中，2api同步失败，3api同步成功")
    private Integer extendStatus;
    private Long bayId;
    @ApiModelProperty("面单缴费状态")
    private String scanFormStatus;

    private List<Long> merchantIdList = new ArrayList<>();
    private List<Long> orderIds = new ArrayList<>();
    private List<Long> issuingBayIds = new ArrayList<>();
    
    public String toJsonString(){
        return JSON.toJSONString(this);
    }
}
