package com.sdsdiy.orderapi.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.base.entity.dto.BasePO;
import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

/**
 * (OrderRemark)实体类
 *
 * <AUTHOR>
 * @since 2020-08-04 18:24:12
 */
@Data
public class OrderItemMapDto extends BaseDTO {

    @DtoDefault
    Long orderId;
    @DtoDefault
    Long orderItemId;
    @DtoDefault
    Long newOrderItemId;

}