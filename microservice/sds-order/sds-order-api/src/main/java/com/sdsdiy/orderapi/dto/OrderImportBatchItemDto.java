package com.sdsdiy.orderapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 导入批次子项记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-11
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "OrderImportBatchItemDto", description = "导入批次子项记录表DTO")
public class OrderImportBatchItemDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "导入批次ID")
    private Long orderImportBatchId;

    @ApiModelProperty(value = "导入批次编号")
    private String orderImportBatchNo;

    @ApiModelProperty(value = "订单编号")
    private String outOrderNo;

    @ApiModelProperty(value = "失败原因")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "更新时间")
    private Long updatedAt;

    @ApiModelProperty(value = "状态，0-处理中，1-成功，2-失败")
    private Integer status;




}
