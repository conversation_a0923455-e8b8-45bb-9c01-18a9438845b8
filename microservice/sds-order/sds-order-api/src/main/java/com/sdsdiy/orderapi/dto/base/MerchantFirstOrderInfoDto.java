package com.sdsdiy.orderapi.dto.base;

import com.sdsdiy.common.dtoconvert.annotation.DtoDefault;
import lombok.Data;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 商户首单信息(MerchantFirstOrderInfoDto)RespDto类
 *
 * <AUTHOR>
 * @since 2021-09-17 17:41:26
 */
@Data
public class MerchantFirstOrderInfoDto {
    /**
     * id
     */
    @DtoDefault
    private Long id;
    /**
     * 商户id
     */
    @DtoDefault
    private Long merchantId;
    /**
     * 订单id
     */
    @DtoDefault
    private Long orderId;
    /**
     * 编码
     */
    @DtoDefault
    private String no;

}
