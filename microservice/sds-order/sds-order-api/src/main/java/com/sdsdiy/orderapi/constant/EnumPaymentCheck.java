package com.sdsdiy.orderapi.constant;

/**
 * @author: bin_lin
 * @date: 2021/1/4 11:16
 * @desc:
 */
public enum EnumPaymentCheck {
    //
    PAINED("已支付",EnumPaymentCheckErrorType.ERROR),
    // 之前文案：没有可用的仓位信息，产品要求改为：订单内的产品履约工厂产能已达到本日上限，因为产品说没有发货仓的只有达到上限的情况
    NO_BAY("订单内产品无供应关系，异仓或订单内产品履约工厂产能已达本日上限",EnumPaymentCheckErrorType.ERROR),
    NO_LOGISTICS("物流信息未设置",EnumPaymentCheckErrorType.ERROR),
    ORDER_ITEM_NO_MATCH("子单未匹配",EnumPaymentCheckErrorType.ERROR),
    PRODUCT_IN_VALID("产品已停产",EnumPaymentCheckErrorType.ERROR),
    ADDRESS("地址信息不足",EnumPaymentCheckErrorType.ERROR),
    IOSS_WRONG("未填写ioss税号",EnumPaymentCheckErrorType.ERROR),
    FBA_PERMISSION_DENIED("该子账号没有支付fba订单权限",EnumPaymentCheckErrorType.ERROR),
    BAN_USE_DHL("地址含po box关键字不可使用DHL物流",EnumPaymentCheckErrorType.ERROR),
    ORDER_QUOTA_NOT_ENOUGH("订单额度不足,请联系客服",EnumPaymentCheckErrorType.ERROR),
    PROTOTYPE_UPDATE("模型数据已经更新，请重新设计后支付",EnumPaymentCheckErrorType.ERROR),
    OFFICIAL_DESIGN_PRODUCT_PROTOTYPE_UPDATE("模型数据已经更新，请重新设计后支付",EnumPaymentCheckErrorType.ERROR),
    TENANT_LOGISTICS_SOURCE_CHANGE("物流发生变动",EnumPaymentCheckErrorType.ERROR),
    SMALL_ORDER_NOT_MEET_MIN_NUM("产品不满足起批量",EnumPaymentCheckErrorType.ERROR),
    SUPPLY_CHAIN_TYPE_DIFFERENCE("部分产品不支持所选的生产流程", EnumPaymentCheckErrorType.ERROR),
    TEMU_ISSUING_BAY_NOT_MAPPING("当前订单发货仓未映射temu仓库", EnumPaymentCheckErrorType.ERROR),

    BLACK_LIST("地址或买家疑似黑名单", EnumPaymentCheckErrorType.WARN),
    MATERIAL_NOT_ENOUGH("素材尺寸不足可能打印质量不佳", EnumPaymentCheckErrorType.WARN),
    ORDER_SEND("订单已发货，结算后将不会自动更新运单号，如有需要请自行同步", EnumPaymentCheckErrorType.WARN),
    DESIGN_PRODUCT_DELETE("成品已删除", EnumPaymentCheckErrorType.WARN),
    PRODUCT_HOLIDAY_ONGING("产品休假中", EnumPaymentCheckErrorType.WARN),

    PRODUCT_FACTORY_LIMIT("订单内的产品履约工厂产能已达到本日上限", EnumPaymentCheckErrorType.ERROR),

    TEMU_FULLY_ITEM_CANCEL("存在已取消的子单，请先删除再支付", EnumPaymentCheckErrorType.ERROR),
    TEMU_US2CA_BC_BBC("该订单属于美发加BC+BBC类型，无可发货物流", EnumPaymentCheckErrorType.ERROR),

    SHEIN_PACKAGE_NO_MISS("包裹号获取失败，无法下单", EnumPaymentCheckErrorType.ERROR),
    COOPERATIVE_WAREHOUSE_NOT_AUTHORIZE("合作仓未开启合作仓下单模式", EnumPaymentCheckErrorType.ERROR),
    MICRO_DESIGN_STATUS_WAIT_OR_ERROR("订单设计中/设计失败，请手动设计或关联成品后支付", EnumPaymentCheckErrorType.ERROR),
    ORDER_ITEM_NOT_EQUAL_ALLOW_QUANTITY("修改的产品数量不等于线上订单剩余可发货产品件数", EnumPaymentCheckErrorType.ERROR),
    DISTRIBUTION_TENANT_WALLET_NOT_ENOUGH("租户授信钱包余额不足", EnumPaymentCheckErrorType.ERROR),
    TENANT_PRICE_UPSIDE_DOWN("订单供应价倒挂，请联系管理员", EnumPaymentCheckErrorType.ERROR)
    ;
    private String msg;
    private EnumPaymentCheckErrorType errorType;

    EnumPaymentCheck(String msg, EnumPaymentCheckErrorType errorType) {
        this.msg = msg;
        this.errorType = errorType;
    }

    public static boolean reachLimitRefreshOrder(EnumPaymentCheck enumPaymentCheck) {
        return NO_BAY.equals(enumPaymentCheck) || PRODUCT_FACTORY_LIMIT.equals(enumPaymentCheck);
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public EnumPaymentCheckErrorType getErrorType() {
        return errorType;
    }

    public void setErrorType(EnumPaymentCheckErrorType errorType) {
        this.errorType = errorType;
    }
}