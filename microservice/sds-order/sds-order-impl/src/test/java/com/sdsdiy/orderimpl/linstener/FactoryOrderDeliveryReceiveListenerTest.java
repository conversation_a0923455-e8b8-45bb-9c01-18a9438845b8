package com.sdsdiy.orderimpl.linstener;

import com.sdsdiy.orderapi.constant.event.message.factoryorder.FactoryOrderReceiveMessage;
import com.sdsdiy.orderimpl.OrderServiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @description:
 * @Author: zmy
 * @Date: 2023/7/11 16:03
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {OrderServiceApplication.class})
public class FactoryOrderDeliveryReceiveListenerTest {
    @Resource
    private FactoryOrderDeliveryReceiveListener factoryOrderDeliveryReceiveListener;

    @Test
    public void onMessage() {
        FactoryOrderReceiveMessage msg=new FactoryOrderReceiveMessage();
        msg.setFactoryNo("2001020000160333");
        factoryOrderDeliveryReceiveListener.onMessage(msg);
    }
}