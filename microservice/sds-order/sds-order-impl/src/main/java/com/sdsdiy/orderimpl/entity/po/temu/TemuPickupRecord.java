//package com.sdsdiy.orderimpl.entity.po.temu;
//
//import com.baomidou.mybatisplus.annotation.IdType;
//import com.baomidou.mybatisplus.annotation.TableId;
//import com.baomidou.mybatisplus.annotation.TableName;
//import java.time.LocalDateTime;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import lombok.experimental.Accessors;
//
///**
// * <p>
// * 分拣批次-temu预约揽收批次
// * </p>
// *
// * <AUTHOR>
// * @since 2025-07-22
// */
//@NoArgsConstructor
//@AllArgsConstructor
//@Data
//@Accessors(chain = true)
//@TableName("temu_pickup_record")
//@ApiModel(value = "TemuPickUpRecord对象", description = "分拣批次-temu预约揽收批次")
//public class TemuPickupRecord {
//
//
//    @TableId(value = "id", type = IdType.INPUT)
//    private Long id;
//
//    @ApiModelProperty(value = "分拣id")
//    private Long orderLogisticsClassifyBatchId;
//
//    @ApiModelProperty(value = "店铺id")
//    private Long merchantStoreId;
//
//    @ApiModelProperty(value = "temu店铺授权区域")
//    private String region;
//
//    @ApiModelProperty(value = "创建时间")
//    private LocalDateTime createTime;
//
//    @ApiModelProperty(value = "预约最早时间 秒")
//    private Long pickupStartTime;
//
//    @ApiModelProperty(value = "预约最晚时间 秒")
//    private Long pickupEndTime;
//
//    @ApiModelProperty(value = "预约状态")
//    private String pickupStatus;
//
//    @ApiModelProperty(value = "报错信息")
//    private String temuErrorMsg;
//
//    @ApiModelProperty("是否已经提交给temu")
//    private Integer isSubmitted;
//}
