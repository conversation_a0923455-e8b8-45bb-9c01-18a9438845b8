package com.sdsdiy.orderimpl.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import lombok.Data;


/**
 * 订单物流信息表(OrderCarriage)实体类
 *
 * <AUTHOR>
 * @since 2021-03-24 17:20:47
 */
@Data
@TableName(value = "order_carriage_extend_info", schema = "sds_mc_order")
public class OrderCarriageExtendInfo extends BaseMapperPO {
    /**
     * order_carriage表id
     */
    private Long id;
    /**
     * 失败重试次数任务的
     */
    private Integer failRetryNumByTask;

}