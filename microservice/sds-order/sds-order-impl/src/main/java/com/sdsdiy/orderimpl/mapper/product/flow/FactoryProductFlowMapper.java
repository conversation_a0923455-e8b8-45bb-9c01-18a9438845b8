package com.sdsdiy.orderimpl.mapper.product.flow;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdsdiy.orderimpl.entity.po.product.flow.FactoryProductFlow;

/**
 * 订单收货地址记录(Address)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-07-07 16:49:05
 */
@DS("common")
public interface FactoryProductFlowMapper extends BaseMapper<FactoryProductFlow> {

}