package com.sdsdiy.orderimpl.bo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.beust.jcommander.internal.Lists;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.BeanUtils;
import com.sdsdiy.common.base.helper.IdGenerator;
import com.sdsdiy.core.util.OrderCodeUtil;
import com.sdsdiy.orderapi.constant.OfflinePayPaymentConstant;
import com.sdsdiy.orderapi.constant.OfflinePayRecordConstant;
import com.sdsdiy.orderapi.dto.adminprepaid.OfflinePayCommitResp;
import com.sdsdiy.orderapi.dto.offlinepay.OfflineBatchPayDetailResp;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayBatchPayParam;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayDetailResp;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayParam;
import com.sdsdiy.orderimpl.entity.po.OfflinePayAmountDetail;
import com.sdsdiy.orderimpl.entity.po.OfflinePayPayment;
import com.sdsdiy.orderimpl.entity.po.OfflinePayRecord;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.sdsdiy.paymentapi.param.PaymentParam;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.paymentapi.constant.DetailPurpose.REPAYMENT_BILL_BATCH_PAID;

/**
 * @author: bin_lin
 * @date: 2023/10/12 19:02
 * @desc:
 */
public class OfflinePayPaymentGen {

    private static final int ALIPAY_QR_CODE_EXPIRED_SECOND = 60 * 60 * 2;

    public static OfflinePayPayment generateOfflinePayPaymentPo(OfflinePayParam param, OfflinePayRecord offlinePayRecord) {
        OfflinePayPayment payPayment = new OfflinePayPayment();
        payPayment.setId(IdGenerator.nextId());
        payPayment.setPayBatchNo(param.getBizNo());
        payPayment.setAmount(param.getAmount());
        payPayment.setTradeType(param.getTradeType());
        payPayment.setPayType(OfflinePayPaymentConstant.PayTypeEnum.ONE_PAY.name());
        payPayment.setPaymentMethod(param.getPaymentMethod());
        payPayment.setRemark(offlinePayRecord.getRemark());
        payPayment.setDetail(param.getDetail());
        payPayment.setCreateUid(param.getUid());
        payPayment.setUpdateUid(param.getUid());
        payPayment.setStatus(OfflinePayRecordConstant.StatusEnum.UN_PAID.name());
        return payPayment;
    }

    public static OfflinePayPayment genOfflinePayPaymentPo(Collection<OfflinePayRecord> offlinePayRecords, OfflinePayBatchPayParam param) {
        Optional<BigDecimal> reduce = offlinePayRecords.stream().map(OfflinePayRecord::getAmount).reduce(BigDecimal::add);
        OfflinePayPayment payPayment = new OfflinePayPayment();
        payPayment.setId(IdGenerator.nextId());
        payPayment.setPayBatchNo(OrderCodeUtil.getOrderIdByUUId());
        payPayment.setAmount(reduce.orElse(BigDecimal.ZERO));
        payPayment.setPaymentMethod(param.getPaymentMethod());
        payPayment.setPayType(param.getPayType());

        if (OfflinePayPaymentConstant.PayTypeEnum.BATCH_PAY.name().equals(param.getPayType())) {
            List<OfflineBatchPayDetailResp> details = Lists.newArrayList();
            for (OfflinePayRecord record : offlinePayRecords) {
                OfflineBatchPayDetailResp batchPayDetailResp = new OfflineBatchPayDetailResp();
                batchPayDetailResp.setOfflinePayRecordId(record.getId());
                batchPayDetailResp.setTradeTypeStr(OfflinePayRecordConstant.TradeTypeEnum.getDisplayName(record.getTradeType()));
                batchPayDetailResp.setAmount(record.getAmount());
                details.add(batchPayDetailResp);
            }
            payPayment.setDetail(JSONUtil.toJsonStr(details));
            payPayment.setRemark("");
        } else {
            Optional<OfflinePayRecord> firstOfflineRecord = offlinePayRecords.stream().findFirst();
            payPayment.setDetail(firstOfflineRecord.orElse(new OfflinePayRecord()).getDetail());
            payPayment.setRemark(firstOfflineRecord.orElse(new OfflinePayRecord()).getRemark());
        }
        payPayment.setCreateUid(param.getUid());
        payPayment.setUpdateUid(param.getUid());
        payPayment.setStatus(OfflinePayRecordConstant.StatusEnum.NONE.name());
        return payPayment;
    }


    public static PaymentParam genTenantToTenantParam(OfflinePayBatchPayParam param,
                                                      OfflinePayPayment payment,
                                                      OfflinePayAmountDetail offlinePayAmountDetail,
                                                      Long belongUserId) {

        PaymentParam paymentParam = new PaymentParam();
        paymentParam.setBizNo(OrderCodeUtil.getOrderIdByUUId())
                .setMethod(payment.getPaymentMethod())
                .setTitle("补款账单")
                .setPurposeType(PurposeType.BUY_PRODUCT.getCode())
                .setRemark(payment.getRemark())
                .setDetailPurpose(REPAYMENT_BILL_BATCH_PAID.getCode());

        paymentParam.setPayType(TransactionPayTypeEnum.SUB.getValue());
        paymentParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        //操作账单相关
        if (!PaymentRoleEnum.MERCHANT.getCode().equals(param.getOrigin())) {
            paymentParam.setOperateUserId(param.getUid());
            paymentParam.setOperateRole(param.getOrigin());
        } else {
            paymentParam.setOperateUserId(0L);
            paymentParam.setOperateRole(PaymentRoleEnum.SYSTEM.getCode());
        }
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(param.getMerchantId());

        //资金流向
        paymentParam.setSourceTenantId(offlinePayAmountDetail.getSourceTenantId());
        paymentParam.setSourceMerchantId(offlinePayAmountDetail.getSourceMerchantId());
        paymentParam.setSourceRole(offlinePayAmountDetail.getSourceRole());
        paymentParam.setSourceUserId(0L);

        paymentParam.setTargetTenantId(offlinePayAmountDetail.getTargetTenantId());
        paymentParam.setTargetMerchantId(offlinePayAmountDetail.getTargetMerchantId());
        paymentParam.setTargetRole(offlinePayAmountDetail.getTargetRole());

        paymentParam.setBalance(offlinePayAmountDetail.getAmount());
        paymentParam.setBonus(BigDecimal.ZERO);
        if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(payment.getPaymentMethod()) || PaymentMethodEnum.OFFLINE.getCode().equalsIgnoreCase(payment.getPaymentMethod())) {
            paymentParam.setBalanceType(BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType());
            if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(payment.getPaymentMethod()) && belongUserId != null && belongUserId > 0) {
                paymentParam.setSourceUserId(belongUserId);
                paymentParam.setBalanceType(BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType());
            }
        } else if (PaymentMethodEnum.ALI_PAY.getCode().equalsIgnoreCase(payment.getPaymentMethod()) || PaymentMethodEnum.LAKALA.getCode().equalsIgnoreCase(payment.getPaymentMethod())) {
            paymentParam.setExpiredSecond(ALIPAY_QR_CODE_EXPIRED_SECOND);
            paymentParam.setBalanceType(BalanceUsedType.NO_USE_BALANCE.getUsedType());
        } else {
            Assert.wrong("支付类型暂不支持！");
        }
        return paymentParam;
    }

    public static PaymentParam genMerchantToTenantParam(OfflinePayBatchPayParam param,
                                                        OfflinePayPayment payment,
                                                        List<OfflinePayAmountDetail> offlinePayAmountDetails,
                                                        Long belongUserId) {

        OfflinePayAmountDetail offlinePayAmountDetail = offlinePayAmountDetails.get(0);
        BigDecimal payAmountTotal = offlinePayAmountDetails.stream().map(OfflinePayAmountDetail::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        PaymentParam paymentParam = new PaymentParam();

        paymentParam.setBizNo(payment.getPayBatchNo())
                .setMethod(payment.getPaymentMethod())
                .setTitle("补款账单")
                .setPurposeType(PurposeType.BUY_PRODUCT.getCode())
                .setRemark(payment.getRemark())
                .setDetailPurpose(REPAYMENT_BILL_BATCH_PAID.getCode());

        paymentParam.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        paymentParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        //操作账单相关
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(param.getMerchantId());
        paymentParam.setOperateUserId(param.getUid());
        paymentParam.setOperateRole(param.getOrigin());

        //资金流向
        paymentParam.setSourceTenantId(offlinePayAmountDetail.getSourceTenantId());
        paymentParam.setSourceMerchantId(offlinePayAmountDetail.getSourceMerchantId());
        paymentParam.setSourceRole(offlinePayAmountDetail.getSourceRole());
        paymentParam.setSourceUserId(0L);

        paymentParam.setTargetTenantId(offlinePayAmountDetail.getTargetTenantId());
        paymentParam.setTargetMerchantId(offlinePayAmountDetail.getTargetMerchantId());
        paymentParam.setTargetRole(offlinePayAmountDetail.getTargetRole());

        paymentParam.setBonus(BigDecimal.ZERO);
        paymentParam.setBalance(payAmountTotal);
        if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(payment.getPaymentMethod()) || PaymentMethodEnum.OFFLINE.getCode().equalsIgnoreCase(payment.getPaymentMethod())) {
            paymentParam.setBalanceType(BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType());
            if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(payment.getPaymentMethod()) && belongUserId != null && belongUserId > 0) {
                paymentParam.setSourceUserId(belongUserId);
                paymentParam.setBalanceType(BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType());
            }
        } else if (PaymentMethodEnum.ALI_PAY.getCode().equalsIgnoreCase(payment.getPaymentMethod())
                || PaymentMethodEnum.LAKALA.getCode().equalsIgnoreCase(payment.getPaymentMethod())) {
//            paymentParam.setBalance(BigDecimal.ZERO);
            paymentParam.setExpiredSecond(ALIPAY_QR_CODE_EXPIRED_SECOND);
            paymentParam.setBalanceType(BalanceUsedType.NO_USE_BALANCE.getUsedType());
        } else {
            Assert.wrong("支付类型暂不支持！");
        }
        return paymentParam;
    }


    public static void formatOfflinePayPaymentPo(OfflinePayPayment payment, PaymentDto paymentDto) {
        payment.setPaymentUrl(paymentDto.getImgUrl());
        payment.setPaymentOverdueTime(paymentDto.getExpiredTime());
        payment.setPaymentTime(new Date());
        payment.setPaymentId(paymentDto.getId());
        if (!PaymentMethodEnum.needWaitCustomerPaid(payment.getPaymentMethod())) {
            payment.setStatus(OfflinePayRecordConstant.StatusEnum.PAID.name());
        } else {
            payment.setPaymentOverdueTime(System.currentTimeMillis() + ALIPAY_QR_CODE_EXPIRED_SECOND);
            payment.setStatus(OfflinePayRecordConstant.StatusEnum.DURING.name());
        }
    }

    public static OfflinePayCommitResp generateOfflinePayCommitResp(OfflinePayPayment payment, List<OfflinePayRecord> offlinePayRecords) {
        OfflinePayCommitResp offlinePayCommitResp = new OfflinePayCommitResp();
        offlinePayCommitResp.setIsSuccess(YES);
        offlinePayCommitResp.setPaymentMethod(payment.getPaymentMethod());
        offlinePayCommitResp.setDetail(payment.getDetail());
        offlinePayCommitResp.setRemark(payment.getRemark());
        offlinePayCommitResp.setPaymentUrl(payment.getPaymentUrl());
        offlinePayCommitResp.setId(payment.getId());
        offlinePayCommitResp.setStatus(payment.getStatus());
        offlinePayCommitResp.setAmount(payment.getAmount());
        offlinePayCommitResp.setPaymentId(payment.getPaymentId());
        offlinePayCommitResp.setPayType(payment.getPayType());
        if (CollectionUtil.isNotEmpty(offlinePayRecords)) {
            List<OfflinePayDetailResp.OfflinePayDetailDto> details = BeanUtils.toList(offlinePayRecords, OfflinePayDetailResp.OfflinePayDetailDto.class);
            offlinePayCommitResp.setDetails(details);
        }
        return offlinePayCommitResp;
    }
}
