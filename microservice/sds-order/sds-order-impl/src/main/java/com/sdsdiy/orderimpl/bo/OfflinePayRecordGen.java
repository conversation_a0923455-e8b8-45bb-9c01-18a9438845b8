package com.sdsdiy.orderimpl.bo;

import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.common.base.helper.IdGenerator;
import com.sdsdiy.orderapi.constant.EnumOrderPayType;
import com.sdsdiy.orderapi.constant.OfflinePayRecordConstant;
import com.sdsdiy.orderapi.dto.offlinepay.OfflinePayParam;
import com.sdsdiy.orderimpl.entity.po.OfflinePayAmountDetail;
import com.sdsdiy.orderimpl.entity.po.OfflinePayPayment;
import com.sdsdiy.orderimpl.entity.po.OfflinePayRecord;
import com.sdsdiy.orderimpl.feign.user.MerchantUserAccountFeign;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import com.sdsdiy.paymentapi.param.PaymentParam;
import com.sdsdiy.paymentapi.param.refund.RefundDetailParam;
import com.sdsdiy.userapi.constant.EnumNotificationTitle;
import com.sdsdiy.userapi.constant.MerchantUserAccountConstant;
import com.sdsdiy.userapi.constant.MsgModule;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.NotificationDTO;
import com.sdsdiy.userapi.dto.UserAccountBalanceResp;
import org.apache.commons.compress.utils.Lists;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.sdsdiy.common.base.constant.BasePoConstant.YES;
import static com.sdsdiy.paymentapi.constant.DetailPurpose.OFFLINE_PAY_REFUND;

/**
 * @author: bin_lin
 * @date: 2023/2/28 19:39
 * @desc:
 */
public class OfflinePayRecordGen {

    private static final int ALIPAY_QR_CODE_EXPIRED_SECOND = 60 * 60 * 2;

    public static OfflinePayRecord generateOfflinePayPo(OfflinePayParam param, MerchantRespDto merchant) {
        OfflinePayRecord offlinePayRecord = new OfflinePayRecord();
        offlinePayRecord.setId(IdGenerator.nextId());
        offlinePayRecord.setMerchantId(param.getMerchantId());
        offlinePayRecord.setMerchantTenantId(merchant.getTenantId());
        offlinePayRecord.setPayBatchNo(param.getBizNo());
        offlinePayRecord.setOrigin(param.getOrigin());
        offlinePayRecord.setTradeType(param.getTradeType());
        offlinePayRecord.setAmount(param.getAmount());
        offlinePayRecord.setPaymentMethod(param.getPaymentMethod());
        offlinePayRecord.setRemark(param.getRemark());
        offlinePayRecord.setDetail(param.getDetail());
        offlinePayRecord.setCreateUid(param.getUid());
        offlinePayRecord.setUpdateUid(param.getUid());
        offlinePayRecord.setTenantId(param.getTenantId());
        offlinePayRecord.setStatus(OfflinePayRecordConstant.StatusEnum.NONE.name());
        if (PaymentMethodEnum.OFFLINE.getCode().equals(param.getPaymentMethod())) {
            offlinePayRecord.setIsOrderOffline(YES);
        }
        return offlinePayRecord;
    }
    
    public static RefundDetailParam generatePaymentRefundParam(OfflinePayRecord one, String subject) {
        RefundDetailParam refundParam = new RefundDetailParam();
        refundParam.setPaymentId(one.getPaymentId());
        refundParam.setPaymentMethod(one.getPaymentMethod());
        refundParam.setSubject(subject);
        refundParam.setDetailPurpose(OFFLINE_PAY_REFUND.getCode());
        refundParam.setRecordBill(true);
        refundParam.setOperateRole(PaymentRoleEnum.SYSTEM.getCode());
        return refundParam;
    }

    public static Optional<NotificationDTO> generateNotification(OfflinePayRecord record) {
        NotificationDTO notificationDTO = new NotificationDTO();
        notificationDTO.setMsgModule(MsgModule.SEND_NOTIFICATION);
        notificationDTO.setMerchantId(record.getMerchantId());
        if (PaymentMethodEnum.balancePay(record.getPaymentMethod())) {
            if (PurposeType.ADMIN_PREPAY_SHIPPING.getCode().equals(record.getTradeType())) {
                notificationDTO.setNotificationTitle(EnumNotificationTitle.ADMIN_PREPAID_OFFLINE_PAY_BALANCE_DEBIT);
                notificationDTO.setContent(String.format(EnumNotificationTitle.ADMIN_PREPAID_OFFLINE_PAY_BALANCE_DEBIT.getContent(), record.getDetail(), record.getAmount()));
            } else {
                PurposeType purposeType = PurposeType.getByCode(record.getTradeType()).orElse(PurposeType.OTHER);
                notificationDTO.setNotificationTitle(EnumNotificationTitle.OFFLINE_PAY_BALANCE_DEBIT);
                String content = String.format(EnumNotificationTitle.OFFLINE_PAY_BALANCE_DEBIT.getContent(), purposeType.getDesc(), record.getAmount(), record.getDetail());
                if (!StringUtils.isEmpty(record.getRemark())) {
                    content = content + ",备注:" + record.getRemark();
                }
                notificationDTO.setContent(content);
            }
            return Optional.of(notificationDTO);
        }
        if (PaymentMethodEnum.ALI_PAY.getCode().equals(record.getPaymentMethod()) || PaymentMethodEnum.LAKALA.getCode().equals(record.getPaymentMethod())) {
            if (PurposeType.ADMIN_PREPAY_SHIPPING.getCode().equals(record.getTradeType())) {
                notificationDTO.setNotificationTitle(EnumNotificationTitle.ADMIN_PREPAID_OFFLINE_PAY_ALI_PAY);
                notificationDTO.setContent(String.format(EnumNotificationTitle.ADMIN_PREPAID_OFFLINE_PAY_ALI_PAY.getContent(), record.getDetail(), record.getAmount()));
            } else {
                PurposeType purposeType = PurposeType.getByCode(record.getTradeType()).orElse(PurposeType.OTHER);
                notificationDTO.setNotificationTitle(EnumNotificationTitle.OFFLINE_PAY_ALI_PAY);
                String content = String.format(EnumNotificationTitle.OFFLINE_PAY_ALI_PAY.getContent(), purposeType.getDesc(), record.getAmount(), record.getDetail());
                if (!StringUtils.isEmpty(record.getRemark())) {
                    content = content + ",备注:" + record.getRemark();
                }
                if (PaymentMethodEnum.LAKALA.getCode().equals(record.getPaymentMethod())) {
                    content = content.replace("支付宝", "拉卡拉");
                }
                notificationDTO.setContent(content);
            }
            return Optional.of(notificationDTO);
        }
        return Optional.empty();
    }

    public static Optional<NotificationDTO> generateRefundNotification(OfflinePayRecord record) {
        NotificationDTO notificationDTO = new NotificationDTO();
        notificationDTO.setMsgModule(MsgModule.SEND_NOTIFICATION);
        notificationDTO.setMerchantId(record.getMerchantId());
        PurposeType purposeType = PurposeType.getByCode(record.getTradeType()).orElse(PurposeType.OTHER);
        if (PaymentMethodEnum.ALI_PAY.getCode().equals(record.getPaymentMethod())) {
            if (PurposeType.ADMIN_PREPAY_SHIPPING.getCode().equals(record.getTradeType())) {
                notificationDTO.setNotificationTitle(EnumNotificationTitle.OFFLINE_PAY_ALI_REFUND);
                notificationDTO.setContent(String.format(EnumNotificationTitle.OFFLINE_PAY_ALI_REFUND.getContent(), purposeType.getDesc()) + "，批次号详情：" + record.getDetail());
            } else {
                notificationDTO.setNotificationTitle(EnumNotificationTitle.OFFLINE_PAY_ALI_REFUND);
                String content = String.format(EnumNotificationTitle.OFFLINE_PAY_ALI_REFUND.getContent(), purposeType.getDesc());
                notificationDTO.setContent(content);
            }
            return Optional.of(notificationDTO);
        }
        return Optional.empty();
    }

    public static void formatOfflinePayRecordsPo(List<OfflinePayRecord> offlinePayRecords, OfflinePayPayment offlinePayPayment) {
        for (OfflinePayRecord offlinePayRecord : offlinePayRecords) {
            if (OfflinePayRecordConstant.StatusEnum.FAILED.name().equalsIgnoreCase(offlinePayPayment.getStatus())) {
                offlinePayRecord.setPaymentId(0L);
                offlinePayRecord.setPayBatchNo("");
                offlinePayRecord.setPaymentMethod("");
                offlinePayRecord.setOfflinePayPaymentId(offlinePayPayment.getId());
                offlinePayRecord.setStatus(OfflinePayRecordConstant.StatusEnum.UN_PAID.name());
                continue;
            }
            offlinePayRecord.setPaymentId(offlinePayPayment.getPaymentId());
            offlinePayRecord.setOfflinePayPaymentId(offlinePayPayment.getId());
            offlinePayRecord.setPayBatchNo(offlinePayPayment.getPayBatchNo());
            offlinePayRecord.setPaymentMethod(offlinePayPayment.getPaymentMethod());
            offlinePayRecord.setStatus(offlinePayPayment.getStatus());
            offlinePayRecord.setErrorLog(offlinePayPayment.getErrorLog());
            offlinePayRecord.setPaymentOverdueTime(offlinePayPayment.getPaymentOverdueTime());
            offlinePayRecord.setPaymentTime(offlinePayPayment.getPaymentTime());
            offlinePayRecord.setPaymentUrl(offlinePayPayment.getPaymentUrl());
            offlinePayRecord.setCloseTime(offlinePayPayment.getCloseTime());
        }
    }

    public static OfflinePayAmountDetail generateDefaultAmounts(OfflinePayRecord record) {
        OfflinePayAmountDetail offlinePayAmountDetail = new OfflinePayAmountDetail();
        offlinePayAmountDetail.setOfflinePayRecordId(record.getId());
        if (!record.getMerchantTenantId().equals(record.getTenantId())) {
            offlinePayAmountDetail.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
            offlinePayAmountDetail.setSourceTenantId(record.getTenantId());
            offlinePayAmountDetail.setSourceMerchantId(record.getMerchantId());
            offlinePayAmountDetail.setTargetTenantId(0L);
            offlinePayAmountDetail.setTargetMerchantId(0L);
            offlinePayAmountDetail.setTargetRole(PaymentRoleEnum.SAAS.getCode());
        } else {
            offlinePayAmountDetail.setSourceTenantId(record.getTenantId());
            offlinePayAmountDetail.setSourceMerchantId(record.getMerchantId());
            offlinePayAmountDetail.setTargetTenantId(record.getMerchantTenantId());
            offlinePayAmountDetail.setTargetMerchantId(0L);
            offlinePayAmountDetail.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
            offlinePayAmountDetail.setTargetRole(PaymentRoleEnum.TENANT.getCode());
        }
        offlinePayAmountDetail.setAmount(record.getAmount());
        return offlinePayAmountDetail;
    }

    public static PaymentParam genPaymentTenantToTenantParam(OfflinePayParam param, MerchantRespDto merchant, BigDecimal tenantToSaasAmount) {
        PaymentParam paymentParam = new PaymentParam();
        paymentParam
                .setBizNo(param.getBizNo())
                .setMethod(param.getPaymentMethod())
                .setTitle(param.getDetail())
                .setPurposeType(param.getTradeType())
                .setRemark(param.getRemark());
        paymentParam.setPayType(TransactionPayTypeEnum.SUB.getValue());
        paymentParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        //操作账单相关
        paymentParam.setSourceRole(PaymentRoleEnum.TENANT_DIS.getCode());
        paymentParam.setSourceTenantId(merchant.getTenantId());
        paymentParam.setSourceMerchantId(0L);
        paymentParam.setSourceUserId(0L);

        paymentParam.setTargetTenantId(param.getTenantId());
        paymentParam.setTargetMerchantId(0L);
        paymentParam.setTargetRole(PaymentRoleEnum.TENANT_SUP.getCode());

        if (!PaymentRoleEnum.MERCHANT.getCode().equals(param.getOrigin())) {
            paymentParam.setOperateUserId(param.getUid());
            paymentParam.setOperateRole(param.getOrigin());
        } else {
            paymentParam.setOperateUserId(0L);
            paymentParam.setOperateRole(PaymentRoleEnum.SYSTEM.getCode());
        }
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(param.getMerchantId());

        //金额相关
        paymentParam.setBonus(BigDecimal.ZERO);
        paymentParam.setBalance(tenantToSaasAmount);
        paymentParam.setBalanceType(BalanceUsedType.TENANT_DISTRIBUTION_BALANCE.getUsedType());

        if (PaymentMethodEnum.ALI_PAY.getCode().equalsIgnoreCase(param.getPaymentMethod())
                || PaymentMethodEnum.LAKALA.getCode().equalsIgnoreCase(param.getPaymentMethod())) {
            paymentParam.setExpiredSecond(ALIPAY_QR_CODE_EXPIRED_SECOND);
            paymentParam.setBalanceType(BalanceUsedType.NO_USE_BALANCE.getUsedType());
        }
        paymentParam.setDetailPurpose(DetailPurpose.OFFLINE_PAY.getCode());
        return paymentParam;
    }

    public static PaymentParam generatePaymentMerchantToTenantParam(OfflinePayParam param, MerchantRespDto merchant, MerchantUserAccountFeign merchantUserAccountFeign) {
        PaymentParam paymentParam = new PaymentParam();

        paymentParam.setBizNo(param.getBizNo())
                .setMethod(param.getPaymentMethod())
                .setTitle(param.getDetail())
                .setPurposeType(param.getTradeType())
                .setRemark(param.getRemark());

        paymentParam.setPayType(TransactionPayTypeEnum.MAIN.getValue());
        paymentParam.setBillType(PaymentBillTypeEnum.GENERATE_TRANSACTION.getStatus());
        //操作账单相关
        paymentParam.setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setOperateTargetRoleId(param.getMerchantId());
        paymentParam.setOperateUserId(param.getUid());
        paymentParam.setOperateRole(param.getOrigin());

        paymentParam.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        paymentParam.setSourceTenantId(merchant.getTenantId());
        paymentParam.setSourceMerchantId(param.getMerchantId());

        paymentParam.setSourceUserId(0L);
        paymentParam.setTargetRole(PaymentRoleEnum.TENANT.getCode());
        paymentParam.setTargetTenantId(merchant.getTenantId());
        paymentParam.setTargetMerchantId(0L);
        //金额相关
        paymentParam.setBalance(param.getAmount());
        paymentParam.setBonus(BigDecimal.ZERO);
        if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(param.getPaymentMethod())
                || PaymentMethodEnum.OFFLINE.getCode().equalsIgnoreCase(param.getPaymentMethod())) {
            paymentParam.setBalanceType(BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType());

            if (PaymentMethodEnum.BALANCE.getCode().equalsIgnoreCase(param.getPaymentMethod()) && PaymentRoleEnum.MERCHANT.getCode().equals(param.getOrigin())) {
                UserAccountBalanceResp userAccountBalanceResp = merchantUserAccountFeign.getUserBalance(param.getMerchantId(), param.getUid());
                if (MerchantUserAccountConstant.ALLOCATION.equalsIgnoreCase(userAccountBalanceResp.getBalanceType())) {
                    paymentParam.setSourceUserId(param.getUid());
                    paymentParam.setBalanceType(BalanceUsedType.MERCHANT_ASSIGN_BALANCE.getUsedType());
                }
            }
        } else if (PaymentMethodEnum.ALI_PAY.getCode().equalsIgnoreCase(param.getPaymentMethod()) ||
                PaymentMethodEnum.LAKALA.getCode().equalsIgnoreCase(param.getPaymentMethod())) {
//            paymentParam.setBalance(BigDecimal.ZERO);
            paymentParam.setExpiredSecond(ALIPAY_QR_CODE_EXPIRED_SECOND);
            paymentParam.setBalanceType(BalanceUsedType.NO_USE_BALANCE.getUsedType());
        } else {
            Assert.wrong("只能使用余额支付与支付宝支付");
        }
        paymentParam.setDetailPurpose(DetailPurpose.OFFLINE_PAY.getCode());
        return paymentParam;
    }


    public static String getTradeFlow(Long merchantTenantId, Long tenantId, Map<Long, TenantDistributorWalletDto> supTenantIdWalletMap) {
        TenantDistributorWalletDto tenantDistributorWalletDto = supTenantIdWalletMap.get(tenantId);
        return getTradeFlow(merchantTenantId, tenantId, tenantDistributorWalletDto);
    }


    public static String getTradeFlow(Long merchantTenantId, Long tenantId, TenantDistributorWalletDto tenantDistributorWalletDto) {
        //属于租户的直接到租户
        if (Objects.equals(merchantTenantId, tenantId)) {
            return EnumOrderPayType.MERCHANT_OWNTENANT.getValue();
        }
        //源归属不一致的则判断是否线上收款,线上收款的先到tenant再到Saas
        if (tenantDistributorWalletDto != null) {
            return EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.getValue();
        } else {
            return EnumOrderPayType.MERCHANT_OWNTENANT.getValue();
        }
    }


    public static List<OfflinePayAmountDetail> generateDefaultAmounts(OfflinePayRecord record, String tradeFlow) {
        List<OfflinePayAmountDetail> details = Lists.newArrayList();
        //根据流向
        if (EnumOrderPayType.MERCHANT_OWNTENANT.getValue().equals(tradeFlow)) {
            OfflinePayAmountDetail offlinePayAmountDetail = new OfflinePayAmountDetail();
            offlinePayAmountDetail.setOfflinePayRecordId(record.getId());
            offlinePayAmountDetail.setSourceTenantId(record.getTenantId());
            offlinePayAmountDetail.setSourceMerchantId(record.getMerchantId());
            offlinePayAmountDetail.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
            offlinePayAmountDetail.setSourceUserId(0L);
            offlinePayAmountDetail.setTargetTenantId(record.getMerchantTenantId());
            offlinePayAmountDetail.setTargetRole(PaymentRoleEnum.TENANT.getCode());
            offlinePayAmountDetail.setTargetMerchantId(0L);
            offlinePayAmountDetail.setAmount(record.getAmount());
            details.add(offlinePayAmountDetail);

        } else if (EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.getValue().equals(tradeFlow)) {
            OfflinePayAmountDetail merchantToTenantDetail = new OfflinePayAmountDetail();
            merchantToTenantDetail.setOfflinePayRecordId(record.getId());
            merchantToTenantDetail.setSourceTenantId(record.getMerchantTenantId());
            merchantToTenantDetail.setSourceMerchantId(record.getMerchantId());
            merchantToTenantDetail.setSourceUserId(0L);
            merchantToTenantDetail.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
            merchantToTenantDetail.setTargetTenantId(record.getMerchantTenantId());
            merchantToTenantDetail.setTargetMerchantId(0L);
            merchantToTenantDetail.setTargetRole(PaymentRoleEnum.TENANT.getCode());
            merchantToTenantDetail.setAmount(record.getAmount());
            details.add(merchantToTenantDetail);

            OfflinePayAmountDetail tenantToSassDetail = new OfflinePayAmountDetail();
            tenantToSassDetail.setOfflinePayRecordId(record.getId());
            tenantToSassDetail.setSourceTenantId(record.getMerchantTenantId());
            tenantToSassDetail.setSourceMerchantId(0L);
            tenantToSassDetail.setSourceUserId(0L);
            tenantToSassDetail.setSourceRole(PaymentRoleEnum.TENANT_DIS.getCode());
            tenantToSassDetail.setTargetTenantId(record.getTenantId());
            tenantToSassDetail.setTargetMerchantId(0L);
            tenantToSassDetail.setTargetRole(PaymentRoleEnum.TENANT_SUP.getCode());
            tenantToSassDetail.setAmount(record.getAmount());
            details.add(tenantToSassDetail);
        } else {
            throw new BusinessException("交易流向异常！");
        }
        return details;
    }

    public static Collection<OfflinePayAmountDetail> generateAmountDetails(OfflinePayRecord record, BigDecimal tenantToSaasAmount) {
        ArrayList<OfflinePayAmountDetail> details = Lists.newArrayList();
        OfflinePayAmountDetail merchantToTenantDetail = new OfflinePayAmountDetail();
        merchantToTenantDetail.setOfflinePayRecordId(record.getId());
        merchantToTenantDetail.setSourceTenantId(record.getMerchantTenantId());
        merchantToTenantDetail.setSourceMerchantId(record.getMerchantId());
        merchantToTenantDetail.setSourceUserId(0L);
        merchantToTenantDetail.setSourceRole(PaymentRoleEnum.MERCHANT.getCode());
        merchantToTenantDetail.setTargetTenantId(record.getMerchantTenantId());
        merchantToTenantDetail.setTargetMerchantId(0L);
        merchantToTenantDetail.setTargetRole(PaymentRoleEnum.TENANT.getCode());
        merchantToTenantDetail.setAmount(record.getAmount());
        details.add(merchantToTenantDetail);

        OfflinePayAmountDetail tenantToSassDetail = new OfflinePayAmountDetail();
        tenantToSassDetail.setOfflinePayRecordId(record.getId());
        tenantToSassDetail.setSourceTenantId(record.getMerchantTenantId());
        tenantToSassDetail.setSourceMerchantId(0L);
        tenantToSassDetail.setSourceUserId(0L);
        tenantToSassDetail.setSourceRole(PaymentRoleEnum.TENANT.getCode());
        tenantToSassDetail.setTargetTenantId(0L);
        tenantToSassDetail.setTargetMerchantId(0L);
        tenantToSassDetail.setTargetRole(PaymentRoleEnum.SAAS.getCode());
        tenantToSassDetail.setAmount(tenantToSaasAmount);
        details.add(tenantToSassDetail);
        return details;
    }


}
