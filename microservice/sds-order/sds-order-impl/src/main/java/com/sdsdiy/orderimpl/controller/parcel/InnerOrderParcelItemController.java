package com.sdsdiy.orderimpl.controller.parcel;


import com.sdsdiy.orderapi.api.parcel.OrderParcelItemApi;
import com.sdsdiy.orderimpl.service.parcel.OrderParcelItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 订单包裹明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023/09/21
 */
@Log4j2
@RestController()
@RequiredArgsConstructor
public class InnerOrderParcelItemController implements OrderParcelItemApi {
    private final OrderParcelItemService orderParcelItemService;

    @Override
    public void updateFactorOrderNoByOrderItemId(Long orderItemId, String factoryOrderNo) {
        this.orderParcelItemService.updateFactorOrderNoByOrderItemId(orderItemId, factoryOrderNo);
    }

}

