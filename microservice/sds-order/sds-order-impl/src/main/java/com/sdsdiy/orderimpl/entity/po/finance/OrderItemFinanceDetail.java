package com.sdsdiy.orderimpl.entity.po.finance;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sdsdiy.common.base.entity.dto.BaseMapperPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 订单版本
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/14
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "order_item_finance_detail", schema = "sds_mc_order")
public class OrderItemFinanceDetail extends BaseMapperPO {

    private static final long serialVersionUID = 1L;


    @TableId(type = IdType.INPUT)
    private Long id;
    private Long orderId;
    private Long payTime;
    private Long finishTime;
    private Long productFinishTime;
    private Long updateTime;
    private Long merchantId;
    private Long factoryId;
    private Long merchantTenantId;
    private Long productOutTime;
    private Long orderTenantId;
    private BigDecimal price;
    private Integer num;
    private Long productId;
    private BigDecimal productAmount;
    private BigDecimal serviceAmount;
    private BigDecimal sdsIncomeProduct;
    private BigDecimal sdsIncomeLogistics;
    private BigDecimal sdsIncomeService;
    private BigDecimal sdsIncomeMaterialService;
    private BigDecimal declarationAmount;
    private BigDecimal clearanceFeeAmount;
    private BigDecimal sdsCostProduct;
    private BigDecimal sdsCostLogistics;
    private BigDecimal sdsAfterServiceCostAmount;
    private BigDecimal factoryAfterServiceCostAmount;
    private BigDecimal sdsFreeGold;
    private BigDecimal sdsCostFactoryLogistics;

    private BigDecimal factoryCompensation;
    private String platform;
    private BigDecimal weight;
    private String transferType;
    private Integer status;



}
