package com.sdsdiy.orderimpl.service.fbagoods;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.orderapi.dto.fbagoods.FbaGoodsSaleReqDto;
import com.sdsdiy.orderimpl.entity.po.FbaGoodsSaleCountDto;
import com.sdsdiy.orderimpl.entity.po.Order;
import com.sdsdiy.orderimpl.entity.po.OrderItem;
import com.sdsdiy.orderimpl.entity.po.fbagoods.FbaGoodsSale;
import com.sdsdiy.orderimpl.entity.po.order.OnlineOrderItem;
import com.sdsdiy.orderimpl.mapper.onlineorder.OnlineOrderItemReadMapper;
import com.sdsdiy.orderimpl.service.OrderItemService;
import com.sdsdiy.orderimpl.service.OrderService;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @Author: zmy
 * @Date: 2022/5/10 14:46
 */
@Service
@Log4j2
public class FbaGoodsSaleOrderStatService {
    @Resource
    private OnlineOrderItemReadMapper onlineOrderItemReadMapper;
    @Resource
    private FbaGoodsSaleService fbaGoodsSaleService;
    @Resource
    private FbaGoodsService fbaGoodsService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private OrderService orderService;
    @Resource
    private OrderItemService orderItemService;

    /**
     * 创建或更新货件销量
     */
    public void updateFbaGoodsSaleStat(Long merchantId, List<Long> storeIds) {
        if (CollUtil.isEmpty(storeIds)) {
            return;
        }
        DateTime now = DateUtil.date();
        //7天时间
        DateTime past7DateTime = DateUtil.offsetDay(now, -7);
        //14天时间
        DateTime past14DateTime = DateUtil.offsetDay(now, -14);
        //30天时间
        DateTime past30DateTime = DateUtil.offsetDay(now, -30);
        //获取店铺的订单统计
        log.info("fba销量time merchantId={} 7time={},14time={},30time={},storeIds={}",merchantId,past7DateTime,past14DateTime,past30DateTime,JSON.toJSONString(storeIds));
        List<FbaGoodsSaleCountDto> sevenCounts = onlineOrderItemReadMapper.groupBySku(storeIds, past7DateTime,now);
        List<FbaGoodsSaleCountDto> fourteenCounts = onlineOrderItemReadMapper.groupBySku(storeIds, past14DateTime, now);
        List<FbaGoodsSaleCountDto> thirtyCounts = onlineOrderItemReadMapper.groupBySku(storeIds, past30DateTime, now);
        log.info("fba销量count merchantId={} 7count={},14count={},30count={}",merchantId,JSON.toJSONString(sevenCounts),JSON.toJSONString(fourteenCounts),JSON.toJSONString(thirtyCounts));

        createSale(merchantId, sevenCounts, fourteenCounts, thirtyCounts);
    }

    @NotNull
    private Map<String, FbaGoodsSale> getExistFbaGoodsSaleMap(Long merchantId, List<FbaGoodsSaleCountDto> counts) {
        List<String> skus = counts.stream().map(dto -> dto.getSku()).distinct().collect(Collectors.toList());
        List<Long> storeIdList = counts.stream().map(dto -> dto.getStoreId()).distinct().collect(Collectors.toList());
        List<String> sites = counts.stream().map(dto -> dto.getSite()).distinct().collect(Collectors.toList());

        Map<String, FbaGoodsSale> skuFbaGoodsSaleMap = getExistSkuMap(merchantId, skus, storeIdList, sites);
        return skuFbaGoodsSaleMap;
    }

    /**
     * 根据sku更新货件销量
     */
    public void updateFbaGoodsSaleStatBySku(Long merchantId, String sku) {
        if (StrUtil.isBlank(sku)) {
            return;
        }
        List<String> skus = Arrays.asList(sku.split(",")).stream().collect(Collectors.toList());
        DateTime now = DateUtil.date();
        //7天时间
        DateTime past7DateTime = DateUtil.offsetDay(now, -7);
        //14天时间
        DateTime past14DateTime = DateUtil.offsetDay(now, -14);
        //30天时间
        DateTime past30DateTime = DateUtil.offsetDay(now, -30);
        //获取店铺的订单统计
        List<FbaGoodsSaleCountDto> sevenCounts = onlineOrderItemReadMapper.getBySkus(skus, now, past7DateTime);
        List<FbaGoodsSaleCountDto> fourteenCounts = onlineOrderItemReadMapper.getBySkus(skus, now, past14DateTime);
        List<FbaGoodsSaleCountDto> thirtyCounts = onlineOrderItemReadMapper.getBySkus(skus, now, past30DateTime);

        createSale(merchantId, sevenCounts, fourteenCounts, thirtyCounts);
    }

    private void createSale(Long merchantId, List<FbaGoodsSaleCountDto> sevenCounts, List<FbaGoodsSaleCountDto> fourteenCounts, List<FbaGoodsSaleCountDto> thirtyCounts) {
        List<FbaGoodsSaleCountDto> allCounts = Lists.newArrayList();
        allCounts.addAll(sevenCounts);
        allCounts.addAll(fourteenCounts);
        allCounts.addAll(thirtyCounts);
        Map<String, FbaGoodsSale> skuFbaGoodsSaleMap = getExistFbaGoodsSaleMap(merchantId, allCounts);
        List<FbaGoodsSaleReqDto> dtos = Lists.newArrayList();

        Map<String, FbaGoodsSaleCountDto> sevenCountsMap = sevenCounts.stream().collect(Collectors.toMap(f -> getSkuStoreIdSiteKey(f.getSku(), f.getStoreId(), f.getSite()), f -> f, (a, b) -> b));
        Map<String, FbaGoodsSaleCountDto> fourteenCountsMap = fourteenCounts.stream().collect(Collectors.toMap(f -> getSkuStoreIdSiteKey(f.getSku(), f.getStoreId(), f.getSite()), f -> f, (a, b) -> b));
        Map<String, FbaGoodsSaleCountDto> thirtyCountsMap = thirtyCounts.stream().collect(Collectors.toMap(f -> getSkuStoreIdSiteKey(f.getSku(), f.getStoreId(), f.getSite()), f -> f, (a, b) -> b));

        Map<String, List<FbaGoodsSaleCountDto>> keyCountDtosMap = allCounts.stream().collect(Collectors.groupingBy(f -> getSkuStoreIdSiteKey(f.getSku(), f.getStoreId(), f.getSite())));
        for (String key : keyCountDtosMap.keySet()) {
            FbaGoodsSaleReqDto dto = getFbaGoodsSaleReqDto(merchantId, sevenCountsMap, fourteenCountsMap, thirtyCountsMap, skuFbaGoodsSaleMap, key);
            if (null != dto) {
                dtos.add(dto);
            }
        }
        log.info("createSale fba销量，merchantId={},dto size={}",merchantId,dtos.size());
        transactionTemplate.execute(transactionStatus -> {
            //更新销量
            fbaGoodsSaleService.createOrUpdate(dtos);
            //更新日均销量、可售天数、建议补货数
            fbaGoodsService.updateCalculateSaleInfo(merchantId, dtos);
            return Boolean.TRUE;
        });
    }

    @NotNull
    private FbaGoodsSaleReqDto getFbaGoodsSaleReqDto(Long merchantId,
                                                     Map<String, FbaGoodsSaleCountDto> sevenCountMap,
                                                     Map<String, FbaGoodsSaleCountDto> fourteenCountMap,
                                                     Map<String, FbaGoodsSaleCountDto> thirtyCountMap,
                                                     Map<String, FbaGoodsSale> skuFbaGoodsSaleMap, String key) {
        FbaGoodsSaleReqDto dto = new FbaGoodsSaleReqDto();
        FbaGoodsSale fbaGoodsSale = skuFbaGoodsSaleMap.get(key);
        if (null != fbaGoodsSale) {
            dto.setId(fbaGoodsSale.getId());
        } else {
            if (null == thirtyCountMap.get(key)) {
                return null;
            }
        }
        FbaGoodsSaleCountDto saleCountDto = thirtyCountMap.get(key);
        dto.setSku(saleCountDto.getSku());
        dto.setMerchantStoreId(saleCountDto.getStoreId());
        dto.setSite(saleCountDto.getSite());
        dto.setMerchantId(merchantId);
        dto.setSevenSaleNum(getCount(sevenCountMap, key));
        dto.setFourteenSaleNum(getCount(fourteenCountMap, key));
        dto.setThirtySaleNum(getCount(thirtyCountMap, key));
        return dto;
    }

    private int getCount(Map<String, FbaGoodsSaleCountDto> countMap, String key) {
        FbaGoodsSaleCountDto countDto = countMap.get(key);
        int count = null != countDto ? countDto.getCount() : 0;
        return count;
    }

    public static String getSkuStoreIdSiteKey(String sku, Long storeId, String site) {
        return sku + "_" + storeId + "_" + site;
    }

    @NotNull
    private Map<String, FbaGoodsSale> getExistSkuMap(Long merchantId, List<String> skus, List<Long> storeIds, List<String> sites) {
        Map<String, FbaGoodsSale> skuFbaGoodsSaleMap = Maps.newHashMap();
        List<FbaGoodsSale> existFbaGoodsSales = fbaGoodsSaleService.getBySkus(merchantId, skus, storeIds, sites);
        if (CollUtil.isNotEmpty(existFbaGoodsSales)) {
            skuFbaGoodsSaleMap = existFbaGoodsSales.stream().collect(Collectors.toMap(f -> getSkuStoreIdSiteKey(f.getSku(),f.getMerchantStoreId(),f.getSite()) , f -> f, (a, b) -> b));
        }
        return skuFbaGoodsSaleMap;
    }

    /**
     * 旧数据-初始化生产中订单产品件数
     */
    public void initInProductionNum(Long merchantId) {
        List<FbaGoodsSaleCountDto> counts = onlineOrderItemReadMapper.getInProductionNumByMerchantId(merchantId);
        Map<String, FbaGoodsSale> skuFbaGoodsSaleMap = getExistFbaGoodsSaleMap(merchantId, counts);
        Map<String, FbaGoodsSaleCountDto> countsMap = counts.stream().collect(Collectors.toMap(f -> getSkuStoreIdSiteKey(f.getSku(), f.getStoreId(), f.getSite()), f -> f, (a, b) -> b));

        List<FbaGoodsSaleReqDto> dtos = Lists.newArrayList();
        for (Map.Entry<String, FbaGoodsSaleCountDto> entry : countsMap.entrySet()) {
            FbaGoodsSaleReqDto dto = getFbaGoodsSaleReqDto(merchantId, skuFbaGoodsSaleMap, entry);
            if (null != dto) {
                dtos.add(dto);
            }
        }
        log.info("GROUP_FBA_GOODS_IN_PRODUCTION_NUM_MERCHANT Listener dtos={}", CollUtil.isNotEmpty(dtos) ? JSON.toJSONString(dtos) : "空");
        //更新生产中订单产品数
        fbaGoodsSaleService.createOrUpdate(dtos);
    }

    @NotNull
    private FbaGoodsSaleReqDto getFbaGoodsSaleReqDto(Long merchantId, Map<String, FbaGoodsSale> skuFbaGoodsSaleMap, Map.Entry<String, FbaGoodsSaleCountDto> entry) {
        String key = entry.getKey();
        FbaGoodsSaleCountDto fbaGoodsSaleCountDto = entry.getValue();
        FbaGoodsSaleReqDto dto = new FbaGoodsSaleReqDto();
        FbaGoodsSale fbaGoodsSale = skuFbaGoodsSaleMap.get(key);
        if (null != fbaGoodsSale) {
            dto.setId(fbaGoodsSale.getId());
        } else {
            dto.setSku(fbaGoodsSaleCountDto.getSku());
            dto.setMerchantStoreId(fbaGoodsSaleCountDto.getStoreId());
            dto.setSite(fbaGoodsSaleCountDto.getSite());
            dto.setMerchantId(merchantId);
        }
        dto.setInProductionNum(fbaGoodsSaleCountDto.getCount());
        return dto;
    }

    /**
     * 付款、取消、完成更新生产中订单产品件数
     */
    public void updateInProductionNum(Long orderId) {
        Order order = orderService.findById(orderId);
        if (null == order || !order.getMerchantStorePlatformCode().equals(MerchantStorePlatformEnum.AMZ.getCode())) {
            return;
        }
        List<OrderItem> orderItems = orderItemService.getOrderItemByOrderId(orderId);
        if (CollUtil.isEmpty(orderItems)) {
            return;
        }
        List<Long> onlineOrderItemIds = orderItems.stream().map(item -> item.getOriginId()).collect(Collectors.toList());
        List<OnlineOrderItem> onlineOrderItems = onlineOrderItemReadMapper.findAllByIdIn(onlineOrderItemIds);
        if (CollUtil.isEmpty(onlineOrderItems)) {
            return;
        }
        Long merchantId = order.getMerchantId();
        Long merchantStoreId = order.getMerchantStoreId();
        String site = order.getSite();
        List<String> skus = onlineOrderItems.stream().map(item -> item.getSku()).collect(Collectors.toList());

        List<FbaGoodsSaleCountDto> counts = onlineOrderItemReadMapper.getInProductionNumBySkus(merchantId, skus, site, merchantStoreId);
        Map<String, FbaGoodsSale> skuFbaGoodsSaleMap = getExistFbaGoodsSaleMap(merchantId, counts);
        Map<String, FbaGoodsSaleCountDto> countsMap = counts.stream().collect(Collectors.toMap(f -> getSkuStoreIdSiteKey(f.getSku(), f.getStoreId(), f.getSite()), f -> f, (a, b) -> b));

        List<FbaGoodsSaleReqDto> dtos = Lists.newArrayList();
        for (Map.Entry<String, FbaGoodsSaleCountDto> entry : countsMap.entrySet()) {
            FbaGoodsSaleReqDto dto = getFbaGoodsSaleReqDto(merchantId, skuFbaGoodsSaleMap, entry);
            if (null != dto) {
                dtos.add(dto);
            }
        }
        log.info("GROUP_FBA_GOODS_IN_PRODUCTION_NUM_MERCHANT Listener dtos={}", CollUtil.isNotEmpty(dtos) ? JSON.toJSONString(dtos) : "空");
        //更新生产中订单产品数
        fbaGoodsSaleService.createOrUpdate(dtos);
    }

}
