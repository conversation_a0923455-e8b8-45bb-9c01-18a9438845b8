package com.sdsdiy.orderimpl.feign;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.logisticsapi.api.LogisticsProductApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @author: bin_lin
 * @date: 2021/1/23 14:05
 * @desc:
 */
@FeignClient(name = "service-logistics", contextId = "LogisticsProductFeign", url = MicroServiceEndpointConstant.SERVICE_LOGISTICS)
public interface LogisticsProductFeign extends LogisticsProductApi {


}
