package com.sdsdiy.orderimpl.manager.factoryorder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Maps;
import com.google.common.collect.Sets;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.CommonStatus;
import com.sdsdiy.common.base.entity.dto.*;
import com.sdsdiy.common.base.enums.CarriageStatusEnum;
import com.sdsdiy.common.base.enums.CountryEnum;
import com.sdsdiy.common.base.enums.EuropeUnionEnum;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.helper.*;
import com.sdsdiy.common.consts.MerchantStoreTypeEnum;
import com.sdsdiy.core.base.util.DateUtil;
import com.sdsdiy.core.base.util.StringUtils;
import com.sdsdiy.core.mq.core.MqSendUtil;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.util.ExceptionUtils;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayRespDto;
import com.sdsdiy.issuingbaydata.dto.qc.QcOperateRecordDTO;
import com.sdsdiy.issuingbaydata.dto.qc.QcRecordAddDTO;
import com.sdsdiy.logisticsapi.constant.TenantLogisticsConstant;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum;
import com.sdsdiy.orderapi.constant.*;
import com.sdsdiy.orderapi.constant.event.OrderProgressConstant;
import com.sdsdiy.orderapi.constant.event.message.BaseEventMessage;
import com.sdsdiy.orderapi.constant.event.message.admin.AdminAllCheckFailedProductMessage;
import com.sdsdiy.orderapi.constant.event.message.admin.AdminCheckFailedProductMessage;
import com.sdsdiy.orderapi.constant.event.message.admin.AdminCheckMessage;
import com.sdsdiy.orderapi.dto.ImportedPlatformOrderFbaRespDto;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderapi.dto.OrderDTO;
import com.sdsdiy.orderapi.dto.base.FactoryOrderExtraResp;
import com.sdsdiy.orderapi.dto.order.*;
import com.sdsdiy.orderapi.dto.orderfba.FbaItemProductLabelPrintedRespDto;
import com.sdsdiy.orderapi.dto.tenant.TenantLogisticsOrderRespDto;
import com.sdsdiy.orderapi.enums.DownTypeEnum;
import com.sdsdiy.orderapi.enums.FactoryOperateEnum;
import com.sdsdiy.orderapi.enums.FactoryOrderStatusEnum;
import com.sdsdiy.orderapi.enums.OrderOriginEnum;
import com.sdsdiy.orderapi.util.OrderRemarkUtil;
import com.sdsdiy.orderdata.constant.FactoryOrderQcConstant;
import com.sdsdiy.orderdata.constant.ParcelConstant;
import com.sdsdiy.orderdata.constant.aliexpress.AliexpressJitConstant;
import com.sdsdiy.orderdata.constant.order.OrderExtendValueEnum;
import com.sdsdiy.orderdata.constant.order.PlatformOrderExtendValueEnum;
import com.sdsdiy.orderdata.dto.ImportProductDto;
import com.sdsdiy.orderdata.dto.OrderItemRespDto;
import com.sdsdiy.orderdata.dto.aliexpress.AliexpressJitOrderDTO;
import com.sdsdiy.orderdata.dto.change.OrderItemChangeHistoryAddDTO;
import com.sdsdiy.orderdata.dto.msg.QualityCheckMessageDTO;
import com.sdsdiy.orderdata.dto.order.OrderItemSupplyChainDTO;
import com.sdsdiy.orderdata.dto.order.amount.OrderAmountCalResultDTO;
import com.sdsdiy.orderdata.dto.order.amount.OrderRefundReqDTO;
import com.sdsdiy.orderdata.dto.parcel.OrderParcelDTO;
import com.sdsdiy.orderdata.dto.parcel.OrderParcelItemDTO;
import com.sdsdiy.orderdata.dto.parcel.message.OrderParcelPackMessageDTO;
import com.sdsdiy.orderdata.dto.qc.*;
import com.sdsdiy.orderdata.enums.*;
import com.sdsdiy.orderimpl.entity.PlatformOrderExtend;
import com.sdsdiy.orderimpl.entity.bo.QcBatchPrepareBO;
import com.sdsdiy.orderimpl.entity.bo.QcFactoryOrderQueryBO;
import com.sdsdiy.orderimpl.entity.bo.QcResultBO;
import com.sdsdiy.orderimpl.entity.po.OrderItem;
import com.sdsdiy.orderimpl.entity.po.*;
import com.sdsdiy.orderimpl.entity.po.aliexpress.AliexpressJitOrder;
import com.sdsdiy.orderimpl.entity.po.aliexpress.AliexpressOrderItem;
import com.sdsdiy.orderimpl.entity.po.orderitem.OrderItemProductLabel;
import com.sdsdiy.orderimpl.environment.EnvironmentVariables;
import com.sdsdiy.orderimpl.feign.*;
import com.sdsdiy.orderimpl.feign.issuingbay.QcOperateRecordFeign;
import com.sdsdiy.orderimpl.feign.payment.TransactionFeign;
import com.sdsdiy.orderimpl.feign.product.ProductSupplyFeign;
import com.sdsdiy.orderimpl.feign.user.IssuingBayUserFeign;
import com.sdsdiy.orderimpl.feign.user.TenantFeign;
import com.sdsdiy.orderimpl.linstener.OrderEventService;
import com.sdsdiy.orderimpl.manager.OrderExtendInfoMapperManager;
import com.sdsdiy.orderimpl.manager.PlatformOrderExtendManage;
import com.sdsdiy.orderimpl.manager.order.OrderAmountCalculateManager;
import com.sdsdiy.orderimpl.manager.order.OrderProgressManager;
import com.sdsdiy.orderimpl.manager.order.OrderShipmentManager;
import com.sdsdiy.orderimpl.manager.order.OrderStatusManager;
import com.sdsdiy.orderimpl.manager.orderitem.OrderItemProductLabelManager;
import com.sdsdiy.orderimpl.manager.parcel.OrderParcelManager;
import com.sdsdiy.orderimpl.manager.parcel.OrderParcelPackManager;
import com.sdsdiy.orderimpl.service.*;
import com.sdsdiy.orderimpl.service.aliexpress.AliexpressJitOrderService;
import com.sdsdiy.orderimpl.service.aliexpress.AliexpressJitPackageService;
import com.sdsdiy.orderimpl.service.aliexpress.AliexpressOrderItemService;
import com.sdsdiy.orderimpl.service.carriage.OrderCarriageService;
import com.sdsdiy.orderimpl.service.change.OrderItemChangeHistoryService;
import com.sdsdiy.orderimpl.service.factoryorder.FactoryOrderRefuseService;
import com.sdsdiy.orderimpl.service.order.OrderItemSupplyChainService;
import com.sdsdiy.orderimpl.service.order.OrderVersionService;
import com.sdsdiy.orderimpl.service.order.PrepaidBoxReadService;
import com.sdsdiy.orderimpl.service.orderfba.FbaItemProductLabelPrintedService;
import com.sdsdiy.orderimpl.service.outplatform.OutPlatformPurchaseOrderService;
import com.sdsdiy.orderimpl.service.parcel.OrderParcelItemService;
import com.sdsdiy.orderimpl.service.parcel.OrderParcelService;
import com.sdsdiy.orderimpl.service.product.flow.FlowStepUserLogService;
import com.sdsdiy.orderimpl.service.task.FactoryTaskService;
import com.sdsdiy.orderimpl.service.tenant.TenantLogisticsOrderService;
import com.sdsdiy.orderimpl.util.ParcelUtil;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.RefundDto;
import com.sdsdiy.paymentapi.param.MultiTransactionCreateParam;
import com.sdsdiy.paymentapi.param.RefundParam;
import com.sdsdiy.paymentapi.param.RefundsCreateParam;
import com.sdsdiy.productapi.dto.FactoryDto;
import com.sdsdiy.productapi.dto.ProductDto;
import com.sdsdiy.productapi.dto.ProductSupplyDTO;
import com.sdsdiy.productdata.dto.ProductRespDto;
import com.sdsdiy.productdata.dto.ProductSupplyFactoryIdsProductIdsDTO;
import com.sdsdiy.productdata.dto.product.ProductPackExplainDTO;
import com.sdsdiy.publishapi.dto.MerchantStoreRespDTO;
import com.sdsdiy.statapi.constant.manuscript.ManuscriptFeedbackStatusConstant;
import com.sdsdiy.userapi.constant.enums.DistributionProductLogisticsSourceEnum;
import com.sdsdiy.userapi.dto.MerchantRespDto;
import com.sdsdiy.userapi.dto.tenant.TenantRespDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/25
 */
@Log4j2
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired, @Lazy})
public class FactoryOrderQcManager {
    private final OrderService orderService;
    private final FactoryOrderService factoryOrderService;
    private final FbaItemProductLabelPrintedService fbaItemProductLabelPrintedService;
    private final FactoryOrderExtraService factoryOrderExtraService;
    private final IssuingBayUserFeign issuingBayUserFeign;
    private final ProductFeign productFeign;
    private final FactoryFeign factoryFeign;
    private final QcOperateRecordFeign qcOperateRecordFeign;
    private final OrderParcelService orderParcelService;
    private final OrderParcelItemService orderParcelItemService;
    private final AddressService addressService;
    private final IssuingBayFeign issuingBayFeign;
    private final WarehouseService warehouseService;
    private final OrderAmountService orderAmountService;
    private final TenantLogisticsOrderService tenantLogisticsOrderService;
    private final TenantFeign tenantFeign;
    private final OrderParcelPackManager orderParcelPackManager;
    private final OrderRemarkService orderRemarkService;
    private final FactoryOrderStatusService factoryOrderStatusService;
    private final MerchantFeign merchantFeign;
    private final TenantLogisticsFeign tenantLogisticsFeign;
    private final ProductSupplyFeign productSupplyFeign;
    private final OrderItemSupplyChainService orderItemSupplyChainService;
    private final FactoryOrderOperateRecordService factoryOrderOperateRecordService;
    private final OrderEventService orderEventService;
    private final RocketMQTemplate rocketMQTemplate;
    private final FactoryOrderRefuseService factoryOrderRefuseService;
    private final FlowStepUserLogService flowStepUserLogService;
    private final OrderItemService orderItemService;
    private final OrderItemMapService orderItemMapService;
    private final OrderItemResendForLoseService orderItemResendForLoseService;
    private final OrderItemProductionManuscriptService orderItemProductionManuscriptService;
    private final PrepaidBoxReadService prepaidBoxReadService;
    private final OrderFbaService orderFbaService;
    private final OrderAmountCalculateManager orderAmountCalculateManager;
    private final ManuscriptFeedbackRecordService manuscriptFeedbackRecordService;
    private final OrderEsService orderEsService;
    private final OrderEarlyWarningService orderEarlyWarningService;
    private final FactoryTaskService factoryTaskService;
    private final OrderStatusManager orderStatusManager;
    private final OrderItemChangeHistoryService orderItemChangeHistoryService;
    private final RefundFeign refundFeign;
    private final TransactionFeign transactionFeign;
    private final OrderProgressManager orderProgressManager;
    private final OrderItemPriceService orderItemPriceService;
    private final ImportedPlatformOrderFbaService importedPlatformOrderFbaService;
    private final OrderVersionService orderVersionService;
    private final OrderItemTransferHistoryService orderItemTransferHistoryService;
    private final AliexpressJitOrderService aliexpressJitOrderService;
    private final AliexpressOrderItemService aliexpressOrderItemService;
    private final AliexpressJitPackageService aliexpressJitPackageService;
    private final OrderCarriageService orderCarriageService;
    private final OrderShipmentManager orderShipmentManager;
    private final OrderItemProductLabelManager orderItemProductLabelManager;
    private final OutPlatformPurchaseOrderService outPlatformPurchaseOrderService;
    private final MerchantStoreFeign merchantStoreFeign;
    private final OrderExtendInfoMapperManager orderExtendInfoMapperManager;
    private final PlatformOrderExtendManage platformOrderExtendManage;
    private final EnvironmentVariables environmentVariables;


    /**
     * 补件
     * 重新生成一个新的子单/生产订单，客户订单还是原客户订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void qcLost(QcLostReqDTO reqDTO) {
        FactoryOrder factoryOrder = this.factoryOrderService.findById(reqDTO.getFactoryOrderId(), "no,merchantOrderNo,issuingBayId,num,status,orderItemId,beResendForLose");
        Assert.validateFalse(FactoryOrderStatusEnum.ACCOMPLISH.equalsCode(factoryOrder.getStatus()), "订单未完成，不可补件！");
        Assert.validateTrue(factoryOrder.getBeResendForLose(), "补件单，不可补件！");
        OrderDTO orderDTO = this.orderService.findByNo(factoryOrder.getMerchantOrderNo(), "originType,logisticsCodeId");
        DeliveryTypeEnum deliveryType = DeliveryTypeEnum.matchOrder(orderDTO.getOriginType(), orderDTO.getLogisticsCodeId());
        // 校验数量不能超过原单数量
        List<Long> lostOrderItemIds = this.orderItemResendForLoseService.findNewOrderItemIdsByOrderItemId(factoryOrder.getOrderItemId());
        List<FactoryOrder> lostFactoryOrderList = this.factoryOrderService.findByOrderItemIds(BaseListQueryDTO.of(lostOrderItemIds, "status,num"));
        int lostNum = lostFactoryOrderList.stream().filter(i -> !FactoryOrderStatusEnum.isQc(i.getStatus())).mapToInt(FactoryOrder::getNum).sum();
        lostOrderItemIds.add(factoryOrder.getOrderItemId());
        Map<Long, Integer> shippedItemQtyMap = this.orderStatusManager.shippedOrderItemQty(deliveryType, lostOrderItemIds);
        int shippedQty = shippedItemQtyMap.values().stream().mapToInt(i -> i).sum();
        // 补件中数量+已发货数量+请求数量
        // Assert.validateTrue(lostNum + shippedQty + reqDTO.getQty() > factoryOrder.getNum(), "数量超出！");
        if (deliveryType == DeliveryTypeEnum.JIT) {
            // 半托管回退状态
            boolean success = this.aliexpressJitOrderService.updateStatusRollbackInProduction(orderDTO.getId());
            Assert.validateFalse(success, "JIT已发货，无法补件");
        } else if (deliveryType == DeliveryTypeEnum.TEMU_FULLY) {
            boolean canQcLost = this.outPlatformPurchaseOrderService.checkCanQcLost(Collections.singleton(orderDTO.getId()));
            Assert.validateFalse(canQcLost, "TEMU全托管已创建发货单，无法补件");
        }

        // 生成补件子单
        OrderItem lostOrderItem = this.orderItemService.copyLostOrderItem(factoryOrder.getOrderItemId(), reqDTO.getQty(), factoryOrder.getOrderItemId());
        lostOrderItem.setBeResendForLose(BasePoConstant.YES);
        lostOrderItem.setTransferType(EnumOrderItemTransferType.LOSE_RESEND.getValue());
        lostOrderItem.setStatus(OrderStatus.UNPAIN.getStatus());
        this.orderItemService.savePo(lostOrderItem);
        // 子单id关系
        this.orderItemMapService.addMap(factoryOrder.getOrderItemId(), lostOrderItem.getId());
        // 复制稿件
        this.orderItemProductionManuscriptService.copyByLostOrderItem(factoryOrder.getOrderItemId(), lostOrderItem.getId());
        // 复制产能线
        this.orderItemSupplyChainService.copyByLostOrderItem(lostOrderItem);
        // 补单记录
        this.orderItemResendForLoseService.addHistory(factoryOrder, lostOrderItem);
        // 工厂单操作记录
        this.factoryOrderOperateRecordService.addOperate(factoryOrder.getNo(), FactoryOperateEnum.RTL, "", reqDTO.getQty());
        // 质检操作记录
        QcRecordAddDTO qcRecordAddDTO = new QcRecordAddDTO()
                .setFactoryOrderId(reqDTO.getFactoryOrderId())
                .setIssuingBayId(factoryOrder.getIssuingBayId())
                .setStatus(QcStatusEnum.LOST.code)
                .setQty(reqDTO.getQty())
                .setUserId(McContentHelper.getCurrentUserId())
                .setRemark(reqDTO.getRemark());
        this.qcOperateRecordFeign.saveDtoBatch(Collections.singletonList(qcRecordAddDTO));
        if (deliveryType == DeliveryTypeEnum.JIT || deliveryType == DeliveryTypeEnum.TEMU_FULLY) {
            // 更新es
            this.aliexpressJitPackageService.sendJitOrderEsUpdate(Collections.singleton(orderDTO.getId()));
        }
        // 补件不算质检操作，所以不用发质检的消息
    }

    @Transactional(rollbackFor = Exception.class)
    public BatchQcSaveResultDTO qcBatch(BatchQcSaveDTO reqDTO) {
        boolean isCheckParcel = BasePoConstant.YES.equals(reqDTO.getIsCheckParcel());
        boolean isRefund = BasePoConstant.YES.equals(reqDTO.getIsRefund());
        List<Long> allFactoryOrderIds = ListUtil.toValueList(BatchQcSaveDTO.CurrentShipItemDTO::getFactorOrderId, reqDTO.getCurrentShipItemList());
        QcBatchPrepareBO prepareBO = ((FactoryOrderQcManager) AopContext.currentProxy())
                .buildQcBatchPrepareBO(allFactoryOrderIds, isRefund);
        // 过滤掉不存在、已取消的工厂单
        List<BatchQcSaveDTO.CurrentShipItemDTO> reqItemList = ListUtil.filter(reqDTO.getCurrentShipItemList(), i -> {
            FactoryOrder fo = prepareBO.getOldFactoryOrderMap().get(i.getFactorOrderId());
            return fo != null && !FactoryOrderStatusEnum.isCancel(fo.getStatus());
        });
        Assert.validateEmpty(reqItemList, "工厂订单已全部取消");
        reqDTO.setCurrentShipItemList(reqItemList);
        List<Long> allPassIds = new ArrayList<>();
        List<QcResendReqDTO> resendList = new ArrayList<>();
        List<BaseIdQtyDTO> refundFactoryOrderList = new ArrayList<>();
        for (BatchQcSaveDTO.CurrentShipItemDTO i : reqDTO.getCurrentShipItemList()) {
            FactoryOrder fo = prepareBO.getOldFactoryOrderMap().get(i.getFactorOrderId());
            if (isCheckParcel) {
                if (!FactoryOrderStatusEnum.canQc(fo.getStatus()) && !FactoryOrderStatusEnum.finish(fo.getStatus())) {
                    continue;
                }
            } else {
                if (!FactoryOrderStatusEnum.canQc(fo.getStatus())) {
                    continue;
                }
            }
            QcStatusEnum qcStatus = null;
            int resendQty = 0;
            if (i.getLessQty() > 0) {
                qcStatus = isRefund ? QcStatusEnum.LESS_REFUND : QcStatusEnum.LESS;
                resendQty = i.getLessQty();
            } else if (i.getRejectQty() > 0) {
                qcStatus = isRefund ? QcStatusEnum.REJECT_REFUND : QcStatusEnum.REJECT;
                resendQty = i.getRejectQty();
            }
            if (resendQty > 0) {
                // 驳回的
                QcResendReqDTO resendReqDTO = new QcResendReqDTO().setFactoryOrderId(i.getFactorOrderId())
                        .setRefuseType(qcStatus.code).setQty(resendQty)
                        .setEvidenceImgList(Collections.emptyList()).setRemark("");
                resendList.add(resendReqDTO);
                if (isRefund) {
                    refundFactoryOrderList.add(new BaseIdQtyDTO(i.getFactorOrderId(), resendQty));
                }
            } else if (i.getPassQty() > 0) {
                // 全部通过的
                allPassIds.add(i.getFactorOrderId());
            }
            if (isCheckParcel) {
                Integer itemQty = prepareBO.getOldItemQtyMap().getOrDefault(i.getOrderItemId(), 0);
                Integer shippedQty = prepareBO.getShippedItemQtyMap().getOrDefault(i.getOrderItemId(), 0);
                if (itemQty > 0 && shippedQty < itemQty) {
                    // 未取消 且 未全部发货
                    i.setShipable(BasePoConstant.YES);
                }
            }
        }
        checkPartCancel(prepareBO, resendList);
        // 能否拆包：不能拆包的，可以质检通过，但是不能直接发货
        boolean parcelPack = checkBatchQcParcelPack(prepareBO, reqDTO, resendList);
        boolean resendRefund = refundFactoryOrderList.size() > 0;
        QcResultBO qcResultBO = new QcResultBO()
                .setOrderId(reqDTO.getOrderId()).setResendRefund(resendRefund);
        if (resendRefund) {
            // 提前把差价计算出来
            qcResultBO.setRefundAmountResult(this.qcRefundAmountCal(refundFactoryOrderList));
        }
        resendList.forEach(i -> this.qcResend(i, prepareBO, qcResultBO));
        this.qcPass(allPassIds, qcResultBO);
        if (allPassIds.size() > 0 || resendList.size() > 0) {
            this.qcResultHandler(prepareBO, qcResultBO);
        }
        BatchQcSaveResultDTO resultDTO = new BatchQcSaveResultDTO(reqDTO.getOrderId());
        log.info("parcelPack:{}, deliveryType:{}", parcelPack, prepareBO.getDeliveryType());
        if (isCheckParcel && parcelPack && prepareBO.getDeliveryType().batchQcShip) {
            // 匹配包裹发货，会自动拆包
            this.orderParcelPackManager.matchParcel(reqDTO, prepareBO, resultDTO);
        } else {
            // -1表示不能发货：比如FBA、寄付、JIT
            resultDTO.setAllMatchParcelId(BasePoConstant.IMPOSSIBLE_ID);
        }
        return resultDTO;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public QcBatchPrepareBO buildQcBatchPrepareBO(Collection<Long> factoryOrderIds, boolean refund) {
        List<FactoryOrder> factoryOrderList = this.factoryOrderService.findByIds(factoryOrderIds);
        Assert.validateEmpty(factoryOrderList, "工厂订单不存在");
        factoryOrderList.forEach(i -> {
            FactoryOrderStatusEnum factoryOrderStatus = FactoryOrderStatusEnum.getByStatus(i.getStatus());
            Assert.validateTrue(factoryOrderStatus == FactoryOrderStatusEnum.SHELVE, "该订单已被系统回收");
            Assert.validateTrue(factoryOrderStatus == FactoryOrderStatusEnum.UNCONFIRMED, "该订单还未接单，无法质检");
        });
        this.checkIssuingBayPermission(factoryOrderList.get(0).getIssuingBayId());
        OrderDTO orderDTO = this.orderService.findByNo(factoryOrderList.get(0).getMerchantOrderNo()
                , "originType,outCycleDate,outDate,merchantStorePlatformCode,merchantStoreId,beAfterServiceOrder,outOrderNo");
        Long orderId = orderDTO.getId();
        if (refund) {
            Assert.validateFalse(this.orderVersionService.match(orderId, OrderVersionEnum.SPRINT5_1), "驳回退款功能上线前的订单，不支持驳回退款");
            ProductDto product = this.productFeign.findById(factoryOrderList.get(0).getProductId(), "");
            Assert.validateEqual(factoryOrderList.get(0).getTenantId(), product.getTenantId(), "跨租户订单不能驳回退款");
        }
        DeliveryTypeEnum deliveryType = DeliveryTypeEnum.matchOrder(orderDTO.getOriginType(), orderDTO.getLogisticsCodeId());
        MerchantStorePlatformEnum storePlatformEnum = MerchantStorePlatformEnum.getByCode(orderDTO.getMerchantStorePlatformCode());
        // 所有子单
        List<OrderItem> orderItemList = this.orderItemService.findByOrderId(orderId);
        List<Long> orderItemIds = ListUtil.toValueList(OrderItem::getId, orderItemList);
        // 已发货数量
        Map<Long, Integer> shippedItemQtyMap = this.orderStatusManager.shippedOrderItemQty(deliveryType, orderItemIds);

        Map<Long, FactoryOrder> factoryOrderMap = ListUtil.toMap(FactoryOrder::getId, factoryOrderList);
        Map<Long, OrderItem> orderItemMap = ListUtil.toMap(OrderItem::getId, orderItemList);

        List<Long> originalItemIds = orderItemList.stream().map(OrderItem::getOriginalRflId)
                .filter(i -> NumberUtils.greaterZero(i) && !orderItemMap.containsKey(i)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(originalItemIds)) {
            // 将补件单的原单也查出来
            List<FactoryOrder> originalFactoryOrderList = this.factoryOrderService.findByOrderItemId(originalItemIds);
            originalFactoryOrderList.forEach(i -> {
                factoryOrderList.add(i);
                factoryOrderMap.put(i.getId(), i);
            });
        }
        // 生产单
        Set<Long> factoryIds = ListUtil.toValueSet(FactoryOrder::getFactoryId, factoryOrderList);
        List<FactoryDto> factoryList = this.factoryFeign.findByIds(IdsSearchHelper.of(factoryIds));
        Map<Long, FactoryDto> factoryMap = ListUtil.toMap(FactoryDto::getId, factoryList);

        List<PlatformOrderExtend> extendList = this.platformOrderExtendManage.findAllByOutId(orderDTO.getOutOrderNo(), orderDTO.getOriginType());
        Map<String, String> platformOrderExtendKeyMap = extendList.stream()
                .collect(Collectors.toMap(PlatformOrderExtend::getExtendKey, PlatformOrderExtend::getExtendValue, (a, b) -> b));

        Map<Long, Integer> orderItemQtyMap = ListUtil.toMap(OrderItem::getId, OrderItem::getNum, orderItemList);
        Map<Long, Integer> realItemQtyMap = ListUtil.toMap(OrderItem::getId, OrderItem::getNum, OrderItemService.filterRealItem(orderItemList));
        Map<Long, Long> orderItemIdFactoryOrderIdMap = ListUtil.toMap(FactoryOrder::getOrderItemId, FactoryOrder::getId, factoryOrderList);
        return new QcBatchPrepareBO().setOrderId(orderId)
                .setRefund(refund)
                .setOrderDTO(orderDTO).setDeliveryType(deliveryType)
                .setOldFactoryOrderMap(factoryOrderMap)
                .setOldOrderItemMap(orderItemMap)
                .setFactoryMap(factoryMap)
                .setOrderItemIdFactoryOrderIdMap(orderItemIdFactoryOrderIdMap)
                .setOldItemQtyMap(orderItemQtyMap)
                .setShippedItemQtyMap(shippedItemQtyMap)
//                .setTemuPopChoice(isTemuPopChoice)
                .setRealItemQtyMap(realItemQtyMap)
                .setStorePlatform(storePlatformEnum)
                .setPlatformOrderExtendKeyMap(platformOrderExtendKeyMap)
                ;
    }

    public void qcResend(QcResendReqDTO reqDTO, QcBatchPrepareBO prepareBO, QcResultBO qcResultBO) {
        Integer reqQty = reqDTO.getQty();
        boolean refund = QcStatusEnum.isRefund(reqDTO.getRefuseType());
        Assert.validateFalse(refund || QcStatusEnum.isResendNoRefund(reqDTO.getRefuseType()), "param refuseType error");
        Assert.validateFalse(NumberUtils.greaterZero(reqQty), "param qty error");
        FactoryOrder factoryOrder = prepareBO.getOldFactoryOrderMap().get(reqDTO.getFactoryOrderId());
        OrderItem orderItem = prepareBO.getOldOrderItemMap().get(factoryOrder.getOrderItemId());
        Integer shippedQty = prepareBO.getShippedItemQtyMap().getOrDefault(orderItem.getId(), 0);
        Assert.validateTrue(factoryOrder.getNum() < reqQty + shippedQty || orderItem.getNum() < reqQty + shippedQty, "退款问题件数量超过可退客户订单件数！");
        // 全部退款=取消
        boolean cancel = refund && (factoryOrder.getNum().equals(reqQty) || orderItem.getNum().equals(reqQty));
        long now = System.currentTimeMillis();
        FactoryOrderStatusEnum updateToStatus;
        FactoryOrder updateFactoryOrder = new FactoryOrder();
        updateFactoryOrder.setId(reqDTO.getFactoryOrderId());
        updateFactoryOrder.setRefuseType(reqDTO.getRefuseType());
        updateFactoryOrder.setRefuseNum(reqQty);
        if (refund) {
            // 驳回取消，原子单/工厂单扣除取消的数量，单子不变
            updateFactoryOrder.setNum(factoryOrder.getNum() - reqQty);
            updateFactoryOrder.setTotalPrice(DoubleUtils.mul(factoryOrder.getPrice(), updateFactoryOrder.getNum()));
            updateFactoryOrder.setNewTotalPrice(updateFactoryOrder.getTotalPrice());
            OrderItem updateOrderItem = new OrderItem();
            updateOrderItem.setId(orderItem.getId());
            updateOrderItem.setNum(orderItem.getNum() - reqQty);
            updateOrderItem.setAmount(NumberUtil.mul(updateOrderItem.getNum(), orderItem.getPrice()));
            updateOrderItem.setWeight(ParcelUtil.getItemWeight(orderItem.getWeight(), orderItem.getNum(), updateOrderItem.getNum()));
            updateOrderItem.setServiceAmount(NumberUtils.calValueByPercent(orderItem.getServiceAmount(), orderItem.getNum(), updateOrderItem.getNum(), 2));
            updateOrderItem.setMaterialServiceAmount(NumberUtils.calValueByPercent(orderItem.getMaterialServiceAmount(), orderItem.getNum(), updateOrderItem.getNum(), 2).doubleValue());
            if (cancel) {
                updateFactoryOrder.setTaskId(0L);
                updateToStatus = FactoryOrderStatusEnum.CANCEL;

                updateOrderItem.setStatus(OrderStatus.CANCEL.status);
                qcResultBO.getCancelOrderItemIds().add(factoryOrder.getOrderItemId());
            } else {
                // 部分取消，不要清taskId
                updateToStatus = FactoryOrderStatusEnum.ACCOMPLISH;
                updateOrderItem.setStatus(OrderStatus.FINISH.status);
                if (!NumberUtils.greaterZero(factoryOrder.getShipTime())) {
                    // 如果这单之前工厂没有发货过，则更新发货时间，并加一个发货记录
                    updateFactoryOrder.setShipTime(now);
                    FactoryOrderOperateRecord operateRecordShip = FactoryOrderOperateRecordService
                            .generateOperate(factoryOrder.getNo(), FactoryOperateEnum.SHIPMENTS, "", reqQty);
                    qcResultBO.getAddOperateRecordList().add(operateRecordShip);
                }
            }
            qcResultBO.getUpdateOrderItemList().add(updateOrderItem);
            if (QcStatusEnum.REJECT_REFUND.equalsCode(reqDTO.getRefuseType())) {
                qcResultBO.getRejectRefundItemQtyList().add(new BaseIdQtyDTO(orderItem.getId(), reqQty));
            } else if (QcStatusEnum.LESS_REFUND.equalsCode(reqDTO.getRefuseType())) {
                qcResultBO.getLessRefundItemQtyList().add(new BaseIdQtyDTO(orderItem.getId(), reqQty));
            }
        } else {
            // 仅打回，则重置状态=待确认，单子数量不变
            updateToStatus = FactoryOrderStatusEnum.UNCONFIRMED;
            updateFactoryOrder.setConfirmTime(0L);
            updateFactoryOrder.setShipTime(0L);
            updateFactoryOrder.setProductTime(0L);
            updateFactoryOrder.setIsPrint(0);
            updateFactoryOrder.setTaskId(0L);
            updateFactoryOrder.setPrintStatus(DownTypeEnum.NOT.getValue());
            updateFactoryOrder.setDownStatus(DownTypeEnum.NOT.getValue());

//            Long outCycleTime = this.outCycleTimeHandler(factoryOrder);
//            updateFactoryOrder.setOutCycleDate(new Date(outCycleTime));
//            updateFactoryOrder.setOutDate(outCycleTime);
            qcResultBO.getOnlyResendFactoryOrderIds().add(factoryOrder.getId());
        }
        updateFactoryOrder.setStatus(updateToStatus.status);
        qcResultBO.getUpdateFactoryOrderList().add(updateFactoryOrder);

        if (refund && factoryOrder.getBeResendForLose() && orderItem.getOriginalRflId() > 0) {
            // 补件单退款时，同步修改原单
            Long originalFactoryOrderId = prepareBO.getOrderItemIdFactoryOrderIdMap().get(orderItem.getOriginalRflId());
            FactoryOrder originalFactoryOrder = prepareBO.getOldFactoryOrderMap().get(originalFactoryOrderId);
            OrderItem originalOrderItem = prepareBO.getOldOrderItemMap().get(orderItem.getOriginalRflId());
            Integer originalShippedQty = prepareBO.getShippedItemQtyMap().getOrDefault(originalOrderItem.getId(), 0);
            Assert.validateTrue(originalFactoryOrder.getNum() < reqQty + originalShippedQty || originalOrderItem.getNum() < reqQty + originalShippedQty, "原单退款问题件数量超过可退客户订单件数！");
            boolean originalCancel = originalFactoryOrder.getNum().equals(reqQty) || originalOrderItem.getNum().equals(reqQty);
            FactoryOrder updateOriginalFactoryOrder = new FactoryOrder();
            updateOriginalFactoryOrder.setId(originalFactoryOrderId);
            updateOriginalFactoryOrder.setRefuseType(reqDTO.getRefuseType());
            updateOriginalFactoryOrder.setNum(originalFactoryOrder.getNum() - reqQty);
            updateOriginalFactoryOrder.setTotalPrice(DoubleUtils.mul(originalFactoryOrder.getPrice(), updateOriginalFactoryOrder.getNum()));
            updateOriginalFactoryOrder.setNewTotalPrice(updateOriginalFactoryOrder.getTotalPrice());

            OrderItem updateOriginalItem = new OrderItem();
            updateOriginalItem.setId(originalOrderItem.getId());
            updateOriginalItem.setNum(originalOrderItem.getNum() - reqQty);
            updateOriginalItem.setAmount(NumberUtil.mul(updateOriginalItem.getNum(), originalOrderItem.getPrice()));
            updateOriginalItem.setWeight(ParcelUtil.getItemWeight(originalOrderItem.getWeight(), originalOrderItem.getNum(), updateOriginalItem.getNum()));
            updateOriginalItem.setServiceAmount(NumberUtils.calValueByPercent(originalOrderItem.getServiceAmount(), originalOrderItem.getNum(), updateOriginalItem.getNum(), 2));
            updateOriginalItem.setMaterialServiceAmount(NumberUtils.calValueByPercent(originalOrderItem.getMaterialServiceAmount(), originalOrderItem.getNum(), updateOriginalItem.getNum(), 2).doubleValue());
            if (originalCancel) {
                updateOriginalFactoryOrder.setStatus(FactoryOrderStatusEnum.CANCEL.status);

                updateOriginalItem.setStatus(OrderStatus.CANCEL.status);
                qcResultBO.getCancelOrderItemIds().add(originalFactoryOrder.getOrderItemId());
            } else {
                updateOriginalFactoryOrder.setStatus(FactoryOrderStatusEnum.ACCOMPLISH.status);

                updateOriginalItem.setStatus(OrderStatus.FINISH.status);
            }
            qcResultBO.getUpdateOrderItemList().add(updateOriginalItem);
            if (QcStatusEnum.isRefund(originalFactoryOrder.getRefuseType())) {
                // 如果原单已经取消了部分，则要加上原来的数
                updateOriginalFactoryOrder.setRefuseNum(originalFactoryOrder.getRefuseNum() + reqQty);
            } else {
                updateOriginalFactoryOrder.setRefuseNum(reqQty);
            }

            if (QcStatusEnum.REJECT_REFUND.equalsCode(reqDTO.getRefuseType())) {
                qcResultBO.getRejectRefundItemQtyList().add(new BaseIdQtyDTO(originalOrderItem.getId(), reqQty));
            } else if (QcStatusEnum.LESS_REFUND.equalsCode(reqDTO.getRefuseType())) {
                qcResultBO.getLessRefundItemQtyList().add(new BaseIdQtyDTO(originalOrderItem.getId(), reqQty));
            }
            qcResultBO.getUpdateFactoryOrderList().add(updateOriginalFactoryOrder);
            qcResultBO.getUpdateFactoryTaskIds().add(originalFactoryOrder.getTaskId());
        }
        qcResultBO.getUpdateFactoryTaskIds().add(factoryOrder.getTaskId());

        QcStatusEnum qcStatus = QcStatusEnum.getByCode(reqDTO.getRefuseType());
        // 工厂单操作记录
        String remark = qcStatus.desc + reqQty + "件";
        FactoryOrderOperateRecord operateRecord = FactoryOrderOperateRecordService
                .generateOperate(factoryOrder.getNo(), FactoryOperateEnum.NO_PASS_GC, remark, reqQty);
        qcResultBO.getAddOperateRecordList().add(operateRecord);
        // 质检操作记录
        QcRecordAddDTO qcRecordAddDTO = new QcRecordAddDTO()
                .setFactoryOrderId(reqDTO.getFactoryOrderId())
                .setIssuingBayId(factoryOrder.getIssuingBayId())
                .setStatus(reqDTO.getRefuseType())
                .setQty(reqQty)
                .setUserId(McContentHelper.getCurrentUserId())
                .setRemark(reqDTO.getRemark())
                .setEvidenceImgList(reqDTO.getEvidenceImgList());
        qcResultBO.getQcRecordAddList().add(qcRecordAddDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void qcResultHandler(QcBatchPrepareBO prepareBO, QcResultBO qcResultBO) {
        McContentDTO currentContent = McContentHelper.getCurrentContent();
        Long currentUserId = currentContent.getUserId();
        OrderDTO orderDTO = prepareBO.getOrderDTO();
        Long orderId = prepareBO.getOrderId();
        long now = System.currentTimeMillis();
        FactoryOrder oldMinDate = this.factoryOrderService.minDateByMerchantOrderNo(orderDTO.getNo());
        // 更新工厂单
        qcResultBO.getUpdateFactoryOrderList().forEach(i -> {
            if (FactoryOrderStatusEnum.isCancel(i.getStatus())) {
                i.setCancelTime(now);
                i.setOutDate(0L);
                i.setOutUrgeDate(0L);
                i.setAddWarningDate(0L);
            } else if (FactoryOrderStatusEnum.ACCOMPLISH.equalsCode(i.getStatus())) {
                FactoryOrder oldFactoryOrder = prepareBO.getOldFactoryOrderMap().get(i.getId());
                i.setIsAllocation(1);
                i.setFinishedUser(currentUserId);
                i.setFinishedTime(now);
                i.setOutUrgeDate(oldMinDate.getOutUrgeDate());
                FactoryDto factoryDto = prepareBO.getFactoryMap().get(oldFactoryOrder.getFactoryId());
                if (!CommonStatus.OFFLINE.getStatus().equals(factoryDto.getCompensationStatus())) {
                    // 计算赔付金额
                    this.factoryOrderStatusService.getFactoryOrderOverTimePay(oldFactoryOrder, i.getNum());
                }
            }
        });
        this.factoryOrderService.updatePoBatch(qcResultBO.getUpdateFactoryOrderList());
        if (qcResultBO.getUpdateOrderItemList().size() > 0) {
            this.orderItemService.updatePoBatch(qcResultBO.getUpdateOrderItemList());
        }
        Long addWarningDate = null;
        List<Long> cancelOrderItemIds = qcResultBO.getCancelOrderItemIds();
        if (cancelOrderItemIds.size() > 0) {
            // 扣物料
//            if (FactoryOrderStatus.ACCOMPLISH_PRODUCTION.getStatus() == factoryOrder.getStatus()) {
//                this.rawMaterialService.changeRawMaterialByOrder(factoryOrder.getFactoryId(), factoryOrder.getProductId(), factoryOrder.getNo(), -factoryOrder.getCurrentNum());
//            }
            // 删除预警
            this.orderEarlyWarningService.delByOrderItemIds(cancelOrderItemIds);
            LocalDateTime maxWaringDate = this.orderEarlyWarningService.findMaxWaringDate(orderId);
            addWarningDate = maxWaringDate == null ? 0 : LocalDateTimeUtil.toEpochMilli(maxWaringDate);
            // 取消计划
            List<FactoryOrder> cancelFactoryOrderList = prepareBO.getOldFactoryOrderMap().values().stream()
                    .filter(i -> cancelOrderItemIds.contains(i.getOrderItemId())).collect(Collectors.toList());
            this.factoryTaskService.cancelFactoryOrders(cancelFactoryOrderList);
            // 删除反馈记录
            List<Long> delManuscriptFeedbackRecordIds = cancelFactoryOrderList.stream()
                    .filter(i -> ManuscriptFeedbackStatusConstant.YES == i.getManuscriptFeedbackStatus())
                    .map(FactoryOrder::getId).collect(Collectors.toList());
            this.manuscriptFeedbackRecordService.delByFactoryOrderId(delManuscriptFeedbackRecordIds);
        }
        if (CollUtil.isNotEmpty(qcResultBO.getOnlyResendFactoryOrderIds())) {
            List<FactoryOrder> onlyResendList = prepareBO.getOldFactoryOrderMap().values().stream()
                    .filter(i -> qcResultBO.getOnlyResendFactoryOrderIds().contains(i.getId())).collect(Collectors.toList());
            // 从原生产计划中移除
            this.factoryOrderRefuseService.doOrderRemoveFromTask(onlyResendList, currentUserId);
            // 删除工序
            this.flowStepUserLogService.deleteByFactoryOrderIds(qcResultBO.getOnlyResendFactoryOrderIds());
        }
        if (CollUtil.isNotEmpty(qcResultBO.getUpdateFactoryTaskIds())) {
            qcResultBO.getUpdateFactoryTaskIds().remove(0L);
            this.factoryTaskService.batchModifyFactoryTaskStaticInfo(qcResultBO.getUpdateFactoryTaskIds());
        }
        FactoryOrder minDate = this.factoryOrderService.minDateByMerchantOrderNo(orderDTO.getNo());
        // 更新客户订单超期时间
        Order updateOrder = new Order();
        updateOrder.setId(orderId);
        updateOrder.setTransactionCode(IdGenerator.nextStringId());
        updateOrder.setAddWarningDate(addWarningDate);
        updateOrder.setOutUrgeDate(CompareUtils.aElseB(minDate.getOutUrgeDate(), 0L));
        this.orderService.updatePo(updateOrder);
        // 退款
        this.qcRefundHandler(prepareBO, qcResultBO);
        if (qcResultBO.getResendRefund()) {
            // 重新调整包裹明细数量
            this.orderParcelPackManager.parcelItemAdjustment(orderId);
            // 更细子单租户金额
            List<Long> partCancelItemIds = ListUtil.toValueNewList(BaseIdQtyDTO::getId, qcResultBO.getLessRefundItemQtyList());
            qcResultBO.getRejectRefundItemQtyList().forEach(i -> partCancelItemIds.add(i.getId()));
            this.orderItemPriceService.updateTenantAmountByOrderItemIds(partCancelItemIds);
            // 记录子单数量变动
            this.orderItemChangeHistoryService.addHistory(new OrderItemChangeHistoryAddDTO().setOrderId(orderId)
                    .setItemList(qcResultBO.getLessRefundItemQtyList()).setChangeType(OrderItemChangeTypeEnum.QC_LESS_CANCEL));
            this.orderItemChangeHistoryService.addHistory(new OrderItemChangeHistoryAddDTO().setOrderId(orderId)
                    .setItemList(qcResultBO.getRejectRefundItemQtyList()).setChangeType(OrderItemChangeTypeEnum.QC_REJECT_CANCEL));
        }
        // 校验客户订单
        this.orderStatusManager.checkAndEndOrder(orderId);
        // 操作记录
        this.factoryOrderOperateRecordService.addPoBatch(qcResultBO.getAddOperateRecordList());
        if (CollUtil.isNotEmpty(qcResultBO.getQcRecordAddList())) {
            // 质检记录
            this.qcOperateRecordFeign.saveDtoBatch(qcResultBO.getQcRecordAddList());
            boolean allProduction = this.factoryOrderService.checkAllProduction(orderDTO.getNo(), true);
            // 进度、消息
            qcResultBO.getQcRecordAddList().forEach(i -> {
                if (QcStatusEnum.PASS.equalsCode(i.getStatus())) {
                    // 质检通过的内部发过消息了
                    return;
                }
                FactoryOrder factoryOrder = prepareBO.getOldFactoryOrderMap().get(i.getFactoryOrderId());
                String orderProgress = "";
                if (cancelOrderItemIds.contains(factoryOrder.getOrderItemId())) {
                    // 取消进度
                    orderProgress = this.sendQcCancelMessage(factoryOrder, i, orderId);
                } else if (QcStatusEnum.isRefund(i.getStatus())) {
                    // 退款进度
                    orderProgress = this.sendQcCancelMessage(factoryOrder, i, orderId);
                } else {
                    orderProgress = this.sendQcResendMessage(factoryOrder, i, orderId, allProduction);
                }
                // 质检操作统一消息
                QualityCheckMessageDTO messageDTO = new QualityCheckMessageDTO();
                messageDTO.setFactoryOrderId(factoryOrder.getId());
                messageDTO.setOrderItemId(factoryOrder.getOrderItemId());
                messageDTO.setOrderId(orderId);
                messageDTO.setOrderProgress(orderProgress);
                messageDTO.setOperateUserId(currentUserId);
                messageDTO.setQcStatus(i.getStatus());
                this.rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.ORDER_QUALITY_CHECK_TOPIC, messageDTO);
            });
        }
        // 更新订单es
        this.orderEsService.saveByDB(orderId, true);
        MqSendUtil.sendOrderFreshMsg(qcResultBO.getOrderId());
    }

    public void qcRefundHandler(QcBatchPrepareBO prepareBO, QcResultBO qcResultBO) {
        OrderAmountCalResultDTO amountResult = qcResultBO.getRefundAmountResult();
        if (amountResult == null) {
            return;
        }
        if (CompareUtils.leZero(amountResult.getDifference().getTotalAmount())) {
            return;
        }
        OrderAmountCalResultDTO.UserInfo userInfo = amountResult.getUserInfo();
        OrderAmountCalResultDTO.OrderInfo orderInfo = amountResult.getOrderInfo();

        List<RefundParam> refundList = new ArrayList<>();
        {
            // 租户退给商户
            OrderAmountCalResultDTO.DifferencePayment merchantDiff = amountResult.getDifferencePayment();
            PaymentMethodEnum payMethod = PaymentMethodEnum.isOffline(orderInfo.getOrderPaymentMethod())
                    ? PaymentMethodEnum.OFFLINE : PaymentMethodEnum.BALANCE;
            RefundParam toMerchant = new RefundParam();
            refundList.add(toMerchant);
            toMerchant.setPurposeType(PurposeType.REFUND.getCode())
                    .setDetailPurpose(DetailPurpose.TENANT_REFUND_TO_MERCHANT.getCode())
                    .setPayType(TransactionPayTypeEnum.MAIN.getValue())
                    .setPayMethod(payMethod.getCode())
                    .setBillType(PaymentBillTypeEnum.DIRECT.getStatus())
                    .setSubject("质检驳回/漏件退款")
                    .setBizNo(IdGenerator.nextStringId())
                    .setBizNoForBill(prepareBO.getOrderDTO().getNo())
                    .setBalanceType(BalanceUsedType.MERCHANT_COMMON_BALANCE.getUsedType())
                    .setBalance(merchantDiff.getBalance())
                    .setBonus(merchantDiff.getFreeGold());
            toMerchant.setOperateRole(PaymentRoleEnum.TENANT.getCode())
                    .setOperateUserId(McContentHelper.getCurrentUserId())
                    .setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode())
                    .setOperateTargetRoleId(userInfo.getOrderMerchantId());
            toMerchant.setSourceTenantId(userInfo.getOrderMerchantTenantId())
                    .setSourceMerchantId(0L)
                    .setSourceUserId(0L)
                    .setSourceRole(PaymentRoleEnum.TENANT.getCode());
            toMerchant.setTargetTenantId(userInfo.getOrderMerchantTenantId())
                    .setTargetMerchantId(userInfo.getOrderMerchantId())
                    .setTargetUserId(0L)
                    .setTargetRole(PaymentRoleEnum.MERCHANT.getCode());
        }
        if (EnumOrderPayType.MERCHANT_OWNTENANT_OTHERTENANT.equalsCode(orderInfo.getOrderPaymentType())
                && CompareUtils.gtZero(amountResult.getTenantDifference().getTotalAmount())) {
            // 供应租户 退给 分销租户
            OrderAmountCalResultDTO.DifferencePayment tenantDiff = amountResult.getTenantDifferencePayment();
            RefundParam toTenant = new RefundParam();
            refundList.add(toTenant);
            toTenant.setPurposeType(PurposeType.REFUND.getCode())
                    .setDetailPurpose(DetailPurpose.TENANT_REFUND_TO_TENANT_DIS.getCode())
                    .setPayType(TransactionPayTypeEnum.SUB.getValue())
                    .setPayMethod(PaymentMethodEnum.BALANCE.getCode())
                    .setBillType(PaymentBillTypeEnum.DIRECT.getStatus())
                    .setSubject("质检驳回/漏件退款")
                    .setBizNo(IdGenerator.nextStringId())
                    .setBizNoForBill(prepareBO.getOrderDTO().getNo())
                    .setBalanceType(BalanceUsedType.TENANT_DISTRIBUTION_BALANCE.getUsedType())
                    .setBalance(tenantDiff.getBalance())
                    .setBonus(tenantDiff.getFreeGold());
            toTenant.setOperateRole(PaymentRoleEnum.SYSTEM.getCode())
                    .setOperateUserId(0L)
                    .setOperateTargetRole(PaymentRoleEnum.MERCHANT.getCode())
                    .setOperateTargetRoleId(userInfo.getOrderMerchantId());
            toTenant.setSourceTenantId(userInfo.getProductTenantId())
                    .setSourceMerchantId(0L)
                    .setSourceUserId(McContentHelper.getCurrentUserId())
                    .setSourceRole(PaymentRoleEnum.TENANT_SUP.getCode());
            toTenant.setTargetTenantId(userInfo.getOrderMerchantTenantId())
                    .setTargetMerchantId(0L)
                    .setTargetUserId(0L)
                    .setTargetRole(PaymentRoleEnum.TENANT_DIS.getCode());
        }
        // 退款
        MultiTransactionCreateParam createParam = new MultiTransactionCreateParam();
        createParam.setRefundList(refundList);
        RefundDto refundDto = this.transactionFeign.createRefund(createParam);
        this.transactionFeign.operateRefund(refundDto.getId());
        this.orderAmountCalculateManager.orderAmountRefundUpdate(amountResult);
        this.orderAmountCalculateManager.recordChangeDetail(amountResult, refundDto.getTradeNo());
        this.orderProgressManager.qcRefund(prepareBO, qcResultBO);
    }

    private String sendQcCancelMessage(FactoryOrder factoryOrder, QcRecordAddDTO qcRecordAddDTO, Long orderId) {
        String orderProgress = OrderProgressConstant.ADMIN_CHECK_CANCEL;
        AdminCheckMessage message = new AdminCheckMessage();
        message.setOrderId(orderId);
        message.setFactoryOrderId(factoryOrder.getId());
        message.setRefuseType(qcRecordAddDTO.getStatus());
        message.setRefuseNum(qcRecordAddDTO.getQty());
        message.setRemarks(qcRecordAddDTO.getRemark());
        message.setEvidenceImgList(qcRecordAddDTO.getEvidenceImgList());
        message.setEid(factoryOrder.getId());
        message.setSendingTime(new Date());
        message.setOperatorUid(qcRecordAddDTO.getUserId());
        this.orderEventService.sendProcessMsgAfterCommit(message, orderProgress);
        return orderProgress;
    }

    private String sendQcResendMessage(FactoryOrder factoryOrder, QcRecordAddDTO qcRecordAddDTO, Long orderId, boolean allProduction) {
        String evidenceImgs = JSON.toJSONString(qcRecordAddDTO.getEvidenceImgList());
        String orderProgress;
        BaseEventMessage progressMsg;
        if (allProduction) {
            if (factoryOrder.getBeResendForLose()) {
                // 补件单
                orderProgress = OrderProgressConstant.ADMIN_CHECK_FAILED_PRODUCT;
                AdminCheckFailedProductMessage message = new AdminCheckFailedProductMessage();
                message.setNumber(qcRecordAddDTO.getQty());
                message.setOrderId(orderId);
                message.setFactoryOrderId(factoryOrder.getId());
                progressMsg = message;
            } else {
                // 全部生产完成质检不通过
                orderProgress = OrderProgressConstant.ADMIN_ALL_CHECK_FAILED_PRODUCT;
                AdminAllCheckFailedProductMessage message = new AdminAllCheckFailedProductMessage();
                message.setNumber(qcRecordAddDTO.getQty());
                message.setOrderId(orderId);
                message.setFactoryOrderId(factoryOrder.getId());
                message.setEvidenceImgs(evidenceImgs);
                message.setRemarks(qcRecordAddDTO.getRemark());
                progressMsg = message;
            }
        } else {
            // 未全部生产完成质检不通过
            orderProgress = OrderProgressConstant.ADMIN_CHECK_FAILED_PRODUCT;
            AdminCheckFailedProductMessage message = new AdminCheckFailedProductMessage();
            message.setNumber(qcRecordAddDTO.getQty());
            message.setOrderId(orderId);
            message.setFactoryOrderId(factoryOrder.getId());
            message.setEvidenceImgs(evidenceImgs);
            message.setRemarks(qcRecordAddDTO.getRemark());
            progressMsg = message;
        }
        // 订单进度消息
        progressMsg.setEid(factoryOrder.getId());
        progressMsg.setSendingTime(new Date());
        progressMsg.setOperatorUid(qcRecordAddDTO.getUserId());
        this.orderEventService.sendProcessMsgAfterCommit(progressMsg, orderProgress);
        return orderProgress;
    }

    public void qcPass(Long factoryOrderId) {
        QcResultBO qcResultBO = new QcResultBO();
        this.qcPass(Collections.singleton(factoryOrderId), qcResultBO);
        // 先这样，回头有空了看看能不能整合
        this.qcOperateRecordFeign.saveDtoBatch(qcResultBO.getQcRecordAddList());
    }

    public void qcPass(Collection<Long> factoryOrderIds, QcResultBO qcResultBO) {
        if (CollUtil.isEmpty(factoryOrderIds)) {
            return;
        }
        McContentDTO currentContent = McContentHelper.getCurrentContent();
        List<FactoryOrder> factoryOrderList = this.factoryOrderService.findByIds(factoryOrderIds);
        this.factoryOrderStatusService.toAccomplish(currentContent.getUserId(), currentContent.getUsername(), factoryOrderList);
        factoryOrderList.forEach(i -> {
            QcRecordAddDTO qcRecordAddDTO = new QcRecordAddDTO()
                    .setFactoryOrderId(i.getId())
                    .setIssuingBayId(i.getIssuingBayId())
                    .setStatus(QcStatusEnum.PASS.code)
                    .setUserId(currentContent.getUserId())
                    .setQty(i.getNum());
            qcResultBO.getQcRecordAddList().add(qcRecordAddDTO);
        });
    }


    public QcMerchantOrderRespDTO merchantOrderInfo(Long orderId, Long tenantId) {
        OrderDTO orderDTO = this.orderService.findById(orderId
                , "merchantId,merchantStoreId,status,outOrderNo,originType,originalAsNo,beAfterServiceOrder,addressId,isAdvance,compensationStatus,merchantStorePlatformCode,accomplishTime");
        Assert.validateNull(orderDTO, "订单不存在!");

        List<OrderParcelDTO> parcelList = this.orderParcelService.findDtoByOrderId(orderId, "logisticsId");
        if (CollUtil.isEmpty(parcelList)) {
            // 自动处理异常数据
            this.rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.ORDER_PARCEL_PACK_V2_TOPIC, new OrderParcelPackMessageDTO(orderId, BasePoConstant.YES));
            Assert.wrong("包裹数据初始化中，等几秒后再试");
        }
        Map<String, String> platformOrderExtendMap = this.platformOrderExtendManage.mapByOutId(orderDTO.getMerchantStorePlatformCode(), orderDTO.getOutOrderNo(), orderDTO.getOriginType());

        QcMerchantOrderRespDTO respDTO = new QcMerchantOrderRespDTO();
        respDTO.setOrderId(orderId).setOrderNo(orderDTO.getNo()).setOutOrderNo(orderDTO.getOutOrderNo()).setOrderStatus(orderDTO.getStatus());
        DeliveryTypeEnum deliveryType = DeliveryTypeEnum.matchOrder(orderDTO.getOriginType(), orderDTO.getLogisticsCodeId());
        MerchantRespDto merchantRespDto = this.merchantFeign.findDtoById(orderDTO.getMerchantId(), "");
        respDTO.setMerchantNo(merchantRespDto.getMerchantNo());
        respDTO.setExtendMap(this.orderExtendInfoMapperManager.mapByOrderId(orderId));
        long merchantStoreId = orderDTO.getMerchantStoreId();
        MerchantStoreRespDTO merchantStoreRespDTO = this.merchantStoreFeign.find(merchantStoreId, "");
        respDTO.setIsPopChoice(merchantStoreRespDTO.getIsPopChoice());
        respDTO.setMerchantStoreType(merchantStoreRespDTO.getType());
        respDTO.setMerchantStoreId(merchantStoreRespDTO.getId());
        respDTO.setMerchantStoreSellerId(merchantStoreRespDTO.getSellerId());

        Warehouse warehouse = this.warehouseService.findByOrderId(orderId);
        respDTO.setOrderOriginType(orderDTO.getOriginType());
        if (warehouse != null) {
            respDTO.setIssuingBayNo(warehouse.getLevel() + "-" + StringUtils.leftPad(warehouse.getNo().toString(), 4, "0"));
            if (DeliveryTypeEnum.FBA != deliveryType && DeliveryTypeEnum.JF != deliveryType) {
                respDTO.setPrintIssuingBayNoBtn(BasePoConstant.YES);
            }
        }

        respDTO.setOriginalAsNo(orderDTO.getOriginalAsNo());
        respDTO.setBeAfterServiceOrder(orderDTO.getBeAfterServiceOrder());
        respDTO.setIsAdvance(orderDTO.getIsAdvance()).setCompensationStatus(orderDTO.getCompensationStatus());
        respDTO.setOrderVersion(this.orderVersionService.getVersionByOrderId(orderId));
        respDTO.setDeliveryType(deliveryType.code);
        respDTO.setAddress(this.addressService.findById(orderDTO.getAddressId()));
        respDTO.setMerchantStorePlatformCode(orderDTO.getMerchantStorePlatformCode());
        // 发货方
        Map<Long, String> belongingTenantNameMap = this.mapShipmentBelongingTenantName(Collections.singletonList(orderDTO));
        respDTO.setShipmentBelongingTenantName(belongingTenantNameMap.get(orderId));
        // 物流信息
        Set<Long> logisticsIds = ListUtil.toValueSet(OrderParcelDTO::getLogisticsId, parcelList);
        List<TenantLogisticsRespDto> logisticsList = this.tenantLogisticsFeign.findByIds(new BaseListReqDto(logisticsIds));
        Set<Long> serviceProviderIds = ListUtil.toValueSet(TenantLogisticsRespDto::getServiceProviderId, logisticsList);
        respDTO.setIncludeDhl(BasePoConstant.yesOrNo(LogisticsServiceProviderEnum.includeDhl(serviceProviderIds)));
        this.formatQcMerchantOrderRespFbaBtn(respDTO, orderDTO, deliveryType);
        this.formatQcMerchantOrderRespJitBtn(respDTO, orderDTO, deliveryType);

        IssuingBayRespDto issuingBay = this.issuingBayFeign.findById(orderDTO.getIssuingBayId(), "area");
        respDTO.setIssuingBay(new BaseIdAndNameDTO(issuingBay.getId(), issuingBay.getName()));
        respDTO.setIssuingBayArea(new BaseIdAndNameDTO(issuingBay.getArea().getId(), issuingBay.getArea().getName()));
        List<OrderRemark> orderRemarkList = this.orderRemarkService.findByOrderIdForQc(orderId);
        boolean remind = orderRemarkList.stream().anyMatch(i -> BasePoConstant.YES.equals(i.getIsRemind()));
        respDTO.setIsRemind(BasePoConstant.yesOrNo(remind));
        respDTO.setRemarkList(ListUtil.copyProperties(orderRemarkList, QcMerchantOrderRespDTO.RemarkDTO.class));
        respDTO.getRemarkList().forEach(i -> {
            i.setCreatedTimeStr(DateUtil.longToString(i.getCreatedTime()));
            i.setRemark(OrderRemarkUtil.disposeRemark(i.getRemark()));
        });
        //是否分销订单
        boolean isDistribution = Objects.nonNull(orderDTO.getTenantId()) && !tenantId.equals(orderDTO.getTenantId());
        if (isDistribution) {
            TenantRespDto tenant = this.tenantFeign.getByTenantId(orderDTO.getTenantId());
            if (Objects.nonNull(tenant)) {
                respDTO.setDistributorName(tenant.getName());
            }
        }
        // 提示标签
        this.formatQcTipLabel(respDTO, deliveryType, platformOrderExtendMap);

        return respDTO;
    }

    public void formatQcTipLabel(QcMerchantOrderRespDTO respDTO, DeliveryTypeEnum deliveryType
            , Map<String, String> platformOrderExtendMap) {
        List<FactoryOrderQcConstant.TipLabel> tipLabelList = new ArrayList<>();
        if (!OrderVersionEnum.SPRINT5_1.match(respDTO.getOrderVersion())) {
            tipLabelList.add(FactoryOrderQcConstant.TipLabel.HistoryOrder);
        }
        if (respDTO.getBeAfterServiceOrder()) {
            tipLabelList.add(FactoryOrderQcConstant.TipLabel.AfterServiceOrder);
        }
        if (OrderConstant.ADVANCE.equals(respDTO.getIsAdvance())) {
            tipLabelList.add(FactoryOrderQcConstant.TipLabel.AdvanceOrder);
        }
        if (BasePoConstant.yes(respDTO.getCompensationStatus())) {
            tipLabelList.add(FactoryOrderQcConstant.TipLabel.CompensationOrder);
        }
        if (BasePoConstant.yes(respDTO.getIncludeDhl())) {
            tipLabelList.add(FactoryOrderQcConstant.TipLabel.DHL);
        }
        switch (deliveryType) {
            case FBA:
                tipLabelList.add(FactoryOrderQcConstant.TipLabel.DeliveryFBA);
                break;
            case ZT:
                tipLabelList.add(FactoryOrderQcConstant.TipLabel.DeliveryZT);
                break;
            case JF:
                tipLabelList.add(FactoryOrderQcConstant.TipLabel.DeliveryJF);
                break;
            case JIT:
                tipLabelList.add(FactoryOrderQcConstant.TipLabel.DeliveryJIT);
                break;
            case DOOR:
                tipLabelList.add(FactoryOrderQcConstant.TipLabel.DeliveryDOOR);
                break;
            default:
        }
        MerchantStorePlatformEnum storePlatform = MerchantStorePlatformEnum.getByCode(respDTO.getMerchantStorePlatformCode());
        if (storePlatform != null) {
            switch (storePlatform) {
                case OZON:
                    tipLabelList.add(FactoryOrderQcConstant.TipLabel.OZON);
                    break;
                case TEMU:
                    if (MerchantStoreTypeEnum.TEMU_SEMI.getCode().equals(respDTO.getMerchantStoreType())) {
                        tipLabelList.add(FactoryOrderQcConstant.TipLabel.TEMU_SEMI);
                    }
                    if (MerchantStoreTypeEnum.TEMU_FULLY.getCode().equals(respDTO.getMerchantStoreType())) {
                        tipLabelList.add(FactoryOrderQcConstant.TipLabel.TEMU_FULLY);
                        tipLabelList.add(FactoryOrderQcConstant.TipLabel.CommonGPSR);
                    }
                    if (MerchantStoreTypeEnum.TEMU_LOCAL.getCode().equals(respDTO.getMerchantStoreType())) {
                        tipLabelList.add(FactoryOrderQcConstant.TipLabel.TEMU_LOCAL);
                    }
                    break;
                case WALMART:
                    tipLabelList.add(FactoryOrderQcConstant.TipLabel.WALMART);
                    break;
                default:
            }
        }
        //速买通,temu全托以及亚马逊fba不需要
        if (respDTO.getAddress() != null) {
            String country = respDTO.getAddress().getCountry();
            if (CountryEnum.GB.equalsCode(country) || EuropeUnionEnum.isEuropeUnion(country)) {
                // 英国或欧盟
                if (MerchantStorePlatformEnum.SELL_FAST == storePlatform) {
                    if (deliveryType != DeliveryTypeEnum.JIT) {
                        // 速卖通 且 非JIT
                        tipLabelList.add(FactoryOrderQcConstant.TipLabel.AliexpressGPSR);
                    }
                } else if (deliveryType != DeliveryTypeEnum.FBA) {
                    //非FBA
                    tipLabelList.add(FactoryOrderQcConstant.TipLabel.CommonGPSR);
                }
            }
        }
        if (OrderExtendValueEnum.IMG_CHECK_STATUS_INFRINGEMENT.isMatch(respDTO.getExtendMap())) {
            // 侵权
            tipLabelList.add(FactoryOrderQcConstant.TipLabel.DESIGN_VIOLATION);
        }
        if (PlatformOrderExtendValueEnum.TEMU_Y2_ADVANCE_SALE.isMatch(platformOrderExtendMap)) {
            tipLabelList.add(FactoryOrderQcConstant.TipLabel.TEMU_Y2_ADVANCE_SALE);
        }
        if (StrUtil.isNotEmpty(respDTO.getDistributorName())) {
            tipLabelList.add(FactoryOrderQcConstant.TipLabel.DISTRIBUTION);
        }
        tipLabelList.sort(Comparator.comparing(FactoryOrderQcConstant.TipLabel::getSort));
        respDTO.setTipLabelList(tipLabelList.stream()
                .map(QcMerchantOrderRespDTO.TipLabel::of).distinct().collect(Collectors.toList()));
    }

    public void formatQcMerchantOrderRespFbaBtn(QcMerchantOrderRespDTO respDTO, OrderDTO orderDTO, DeliveryTypeEnum deliveryType) {
        if (DeliveryTypeEnum.FBA != deliveryType) {
            return;
        }
        boolean fbaZt = DeliveryTypeEnum.isFbaZt(orderDTO.getLogisticsCodeId());
        respDTO.setFbaZt(BasePoConstant.yesOrNo(fbaZt));
        respDTO.setFbaProductionCompletedBtn(BasePoConstant.yesOrNo(NumberUtils.greaterZero(orderDTO.getAccomplishTime())));
        OrderFba orderFba = this.orderFbaService.findByOrderId(orderDTO.getId());
        if (orderFba != null) {
            respDTO.setOrderFbaId(orderFba.getId());
            if (OrderFbaConstant.PRODUCTED_STATUS_FINSH == orderFba.getProductedStatus()) {
                respDTO.setFbaProductionCompletedBtn(BasePoConstant.NO);
                if (fbaZt) {
                    respDTO.setFbaPrintPickUpLabelBtn(BasePoConstant.YES.equals(orderFba.getTakeLabelPrinted()) ? 2 : 1);
                } else {
                    respDTO.setFbaShipBtn(BasePoConstant.YES);
                }
            }
        }
    }

    public void formatQcMerchantOrderRespJitBtn(QcMerchantOrderRespDTO respDTO, OrderDTO orderDTO, DeliveryTypeEnum deliveryType) {
        if (DeliveryTypeEnum.JIT != deliveryType || !NumberUtils.greaterZero(orderDTO.getAccomplishTime())) {
            return;
        }
        List<AliexpressJitOrderDTO> jitOrderList = this.aliexpressJitOrderService.findDtoByOrderId(respDTO.getOrderId(), "");
        if (CollUtil.isEmpty(jitOrderList)) {
            return;
        }
        boolean inProduction = jitOrderList.stream().anyMatch(i -> AliexpressJitConstant.StatusEnum.IN_PRODUCTION.equalsCode(i.getStatus()));
        // 生产中才可以打印
        respDTO.setJitProductionCompletedBtn(inProduction ? 1 : 2);
    }

    private void checkIssuingBayPermission(Long issuingBayId) {
        // 校验发货仓权限
        McContentDTO currentContent = McContentHelper.getCurrentContent();
        if (currentContent.getTenantId() != null && currentContent.getUserId() != null) {
            List<Long> permissionList = this.issuingBayUserFeign.permissionList(currentContent.getTenantId(), currentContent.getUserId(), null, null);
            Assert.validateFalse(permissionList.contains(issuingBayId), "无权限操作!");
        }
    }

    public QcFactoryOrderRespDTO batchQcFactoryOrderList(BatchQcReqDTO reqDTO) {
        boolean addAll = BasePoConstant.YES.equals(reqDTO.getAddAll());
        boolean fastQc = BasePoConstant.yes(reqDTO.getFastQc());
        Set<String> nos = new HashSet<>();
        if (StrUtil.isNotBlank(reqDTO.getAddNo())) {
            reqDTO.setAddNo(reqDTO.getAddNo().trim());
            if (addAll) {
                // 添加全部时，清空旧的数据，按新单子处理
                reqDTO.setFactorOrderNos("").setOrderNo("");
            }
            String merchantOrderNo;
            if (this.orderService.existNo(reqDTO.getAddNo())) {
                // 输入的是客户订单号
                merchantOrderNo = reqDTO.getAddNo();
            } else {
                // 输入的是生产单号
                FactoryOrder scanFactoryOrder = this.factoryOrderService.findByNo(reqDTO.getAddNo());
                Assert.validateNull(scanFactoryOrder, "生产单不存在");
                if (!fastQc && !addAll) {
                    Assert.validateFalse(FactoryOrderStatusEnum.BATCH_QC_SHOW_STATUS.contains(scanFactoryOrder.getStatus()), "生产未接单或已取消");
                }
                nos.add(reqDTO.getAddNo());
                merchantOrderNo = scanFactoryOrder.getMerchantOrderNo();
            }
            if (StrUtil.isBlank(reqDTO.getOrderNo())) {
                reqDTO.setOrderNo(merchantOrderNo);
            } else {
                Assert.validateEqual(merchantOrderNo, reqDTO.getOrderNo(), "该生产单号不属于同一个订单");
            }
        }
        if (StrUtil.isNotBlank(reqDTO.getFactorOrderNos())) {
            nos.addAll(Arrays.asList(reqDTO.getFactorOrderNos().split(StrUtil.COMMA)));
        }
        Assert.validateBlank(reqDTO.getOrderNo(), "单号不能为空");
        // 查出所有单
        List<FactoryOrder> allFactoryOrderList = this.factoryOrderService.findByMerchantOrderNo(reqDTO.getOrderNo());
        QcFactoryOrderQueryBO queryBO = new QcFactoryOrderQueryBO();
        queryBO.setNo(reqDTO.getAddNo()).setFactoryOrderList(allFactoryOrderList).setBatchQc(true);
        QcFactoryOrderRespDTO respDTO = this.buildQcFactoryOrderRespDTO(queryBO);
        // 可质检/发货列表
        List<QcFactoryOrderRespDTO.FactoryOrderDTO> qcOrShipableList = respDTO.getFactoryOrderList().stream()
                .filter(i -> {
                    if (i.getBeResendForLose() && queryBO.getAllShippedFactoryOrderNos().contains(i.getOriginalFactoryOrderNo())) {
                        // 原单全部发货了，则补件单不用返回
                        return false;
                    }
                    if (fastQc || addAll) {
                        // 快速质检、全部添加 全部返回
                        return true;
                    }
                    if (!FactoryOrderStatusEnum.BATCH_QC_SHOW_STATUS.contains(i.getStatus())) {
                        return false;
                    }
                    if (i.getCurrentQcQty() > 0) {
                        return true;
                    }
                    return !i.getAllShipped();
                }).collect(Collectors.toList());

        if (!fastQc && !addAll && StrUtil.isNotBlank(reqDTO.getAddNo())) {
            respDTO.getFactoryOrderList().stream().filter(i -> reqDTO.getAddNo().equals(i.getFactoryOrderNo())).findFirst()
                    .ifPresent(scan -> Assert.validateTrue(queryBO.getAllShippedFactoryOrderNos()
                                    .contains(scan.getBeResendForLose() ? scan.getOriginalFactoryOrderNo() : scan.getFactoryOrderNo())
                            , "订单已全部发货"));
            boolean allShip = respDTO.getFactoryOrderList().stream().allMatch(QcFactoryOrderRespDTO.FactoryOrderDTO::getAllShipped);
            Assert.validateTrue(allShip, "订单已全部发货");
            Assert.validateTrue(qcOrShipableList.size() == 0, "生产未接单或已取消");
        }
        // 是否全部展示，快速质检、全部添加或搜的是客户订单号
        boolean allShow = fastQc || addAll || reqDTO.getOrderNo().equals(reqDTO.getAddNo());
        // 标记本次扫码
        qcOrShipableList.forEach(i -> i.setIsCurrentScan(BasePoConstant.yesOrNo(allShow || nos.contains(i.getFactoryOrderNo()))));
        // 汇总工厂数
        formatFactoryDtoList(respDTO);
        // 工厂一旦发货则不可变更仓库
        respDTO.setCanChangeIssuingBay(BasePoConstant.yesOrNo(allFactoryOrderList.stream().noneMatch(i -> i.getShipTime() > 0)));
        return respDTO;
    }

    private static void formatFactoryDtoList(QcFactoryOrderRespDTO respDTO) {
        List<QcFactoryOrderRespDTO.FactoryOrderDTO> factoryOrderRespList = respDTO.getFactoryOrderList();
        // 总数，排除补单
        Map<Long, Integer> factoryTotalQtyMap = factoryOrderRespList.stream().filter(i -> !i.getBeResendForLose())
                .collect(Collectors.groupingBy(f -> f.getFactory().getId(), Collectors.summingInt(QcFactoryOrderRespDTO.FactoryOrderDTO::getQty)));
        // 已质检，排除补单
        Map<Long, Integer> factoryQcQtyMap = factoryOrderRespList.stream().filter(i -> !i.getBeResendForLose())
                .collect(Collectors.groupingBy(f -> f.getFactory().getId(), Collectors.summingInt(QcFactoryOrderRespDTO.FactoryOrderDTO::getQcQty)));
        // 已取消，排除补单
        Map<Long, Integer> factoryCancelQtyMap = factoryOrderRespList.stream().filter(i -> !i.getBeResendForLose())
                .collect(Collectors.groupingBy(f -> f.getFactory().getId(), Collectors.summingInt(i -> {
                    if (FactoryOrderStatusEnum.CANCEL.equalsCode(i.getStatus())) {
                        return i.getQty();
                    }
                    if (QcStatusEnum.isRefund(i.getRefuseType())) {
                        return i.getQcFailedQty();
                    }
                    return 0;
                })));
        Map<Long, QcFactoryOrderRespDTO.FactoryDTO> factoryRespMap = new HashMap<>(factoryOrderRespList.size());
        for (QcFactoryOrderRespDTO.FactoryOrderDTO factoryOrderDTO : factoryOrderRespList) {
            Long factoryId = factoryOrderDTO.getFactory().getId();
            QcFactoryOrderRespDTO.FactoryDTO factoryResp = factoryRespMap.get(factoryId);
            if (factoryResp == null) {
                factoryResp = new QcFactoryOrderRespDTO.FactoryDTO()
                        .setId(factoryId).setName(factoryOrderDTO.getFactory().getName())
                        .setCurrentQty(0)
                        .setQcQty(factoryQcQtyMap.get(factoryId))
                        .setCancelQty(factoryCancelQtyMap.get(factoryId))
                        .setQty(factoryTotalQtyMap.get(factoryId));
                factoryRespMap.put(factoryId, factoryResp);
            }
            if (BasePoConstant.YES.equals(factoryOrderDTO.getIsCurrentScan())) {
                Integer qty = factoryOrderDTO.getQty();
                if (QcStatusEnum.isRefund(factoryOrderDTO.getRefuseType())) {
                    // 驳回取消的，扣掉已取消的数量
                    qty -= factoryOrderDTO.getQcFailedQty();
                }
                // 累加 本次数量=订单数-已发货数
                factoryResp.setCurrentQty(qty - factoryOrderDTO.getShippedQty() + factoryResp.getCurrentQty());
            }
        }
        respDTO.setFactoryList(new ArrayList<>(factoryRespMap.values()));
    }

    public QcFactoryOrderRespDTO qcFactoryOrderList(String no) {
        List<FactoryOrder> factoryOrderList = this.factoryOrderService.findByNoOrMerchantOrderNoForQc(no);
        QcFactoryOrderQueryBO queryBO = new QcFactoryOrderQueryBO();
        queryBO.setNo(no).setFactoryOrderList(factoryOrderList).setBatchQc(false);
        QcFactoryOrderRespDTO respDTO = this.buildQcFactoryOrderRespDTO(queryBO);
        // 工厂一旦发货则不可变更仓库
        respDTO.setCanChangeIssuingBay(BasePoConstant.yesOrNo(factoryOrderList.stream().noneMatch(i -> i.getShipTime() > 0)));
        formatQcFactoryOrderRespDtoBtn(respDTO, queryBO);

        return respDTO;
    }


    private void formatQcFactoryOrderQueryBO(QcFactoryOrderQueryBO queryBO) {
        List<FactoryOrder> factoryOrderList = queryBO.getFactoryOrderList();
        Assert.validateEmpty(factoryOrderList, "该订单待还未生成生产单，不可质检");
        OrderDTO orderDTO = queryBO.getOrderDTO();
        if (orderDTO == null) {
            orderDTO = this.orderService.findByNo(factoryOrderList.get(0).getMerchantOrderNo()
                    , "status,originType,originId,merchantStorePlatformCode,merchantStoreId,addressId");
            queryBO.setOrderDTO(orderDTO);
        }
        this.qcCheckOrder(orderDTO);
        Long orderId = orderDTO.getId();
        boolean versionMatch = this.orderVersionService.match(orderId, OrderVersionEnum.SPRINT5_1);
        queryBO.setOldOrderData(!versionMatch)
                .setDeliveryType(DeliveryTypeEnum.matchOrder(orderDTO.getOriginType(), orderDTO.getLogisticsCodeId()));

        Set<Long> factoryIds = new HashSet<>();
        Set<Long> variantIds = new HashSet<>();
        List<Long> orderItemIds = new ArrayList<>();
        List<Long> factoryOrderIds = new ArrayList<>();
        factoryOrderList.forEach(i -> {
            factoryOrderIds.add(i.getId());
            factoryIds.add(i.getFactoryId());
            variantIds.add(i.getProductId());
            orderItemIds.add(i.getOrderItemId());
        });

        // 变体
        List<ProductRespDto> variantList = this.productFeign.getAllByIds(BaseListQueryDTO.of(variantIds
                , "name,size,colorName,textureId,texture,prototypeType"));
        Map<Long, ProductRespDto> variantMap = ListUtil.toMap(ProductRespDto::getId, variantList);
        // 包装信息
        Set<Long> parentIds = ListUtil.toValueSet(ProductRespDto::getParentId, variantList);
        List<ProductPackExplainDTO> explainList = this.productFeign.listProductPackExplainByProductIds(parentIds);
        Map<Long, String> packExplainMap = ListUtil.toMap(ProductPackExplainDTO::getProductId, ProductPackExplainDTO::getPackExplain, explainList);
        // 工厂
        Map<Long, BaseIdAndNameDTO> factoryMap = ListUtil.toMap(BaseIdAndNameDTO::getId, this.factoryFeign.findNameByIds(BaseListDto.of(factoryIds)));
        // 产品标签 是否打印过
        Set<Long> productLabelPrintedItemIds = new HashSet<>();
        if (DeliveryTypeEnum.FBA == queryBO.getDeliveryType()) {
            ImportedPlatformOrderFbaRespDto importedPlatformOrderFbaRespDto = this.importedPlatformOrderFbaService.getById(orderDTO.getOriginId(), "pasteLabel");
            if (importedPlatformOrderFbaRespDto != null && BasePoConstant.yes(importedPlatformOrderFbaRespDto.getPasteLabel())) {
                queryBO.setPrintProductLabelBtn(BasePoConstant.YES);
                List<FbaItemProductLabelPrintedRespDto> fbaPrintedList = this.fbaItemProductLabelPrintedService.getByItemIds(orderItemIds);
                fbaPrintedList.forEach(i -> productLabelPrintedItemIds.add(i.getOrderItemId()));
            }
        } else if (MerchantStorePlatformEnum.SELL_FAST.equalsCode(orderDTO.getMerchantStorePlatformCode())) {
            // 速卖通 jit 或者 欧盟+英国
            boolean printProductLabel = false;
            if (DeliveryTypeEnum.JIT == queryBO.getDeliveryType()) {
                List<AliexpressJitOrder> aliexpressJitOrderList = this.aliexpressJitOrderService.findByOrderId(orderId);
                queryBO.setAliexpressJitOrderList(aliexpressJitOrderList);
                printProductLabel = true;
            } else {
                Address address = this.addressService.findPoById(orderDTO.getAddressId(), "country");
                if (CountryEnum.GB.equalsCode(address.getCountry()) || EuropeUnionEnum.isEuropeUnion(address.getCountry())) {
                    // 英国+欧盟 打印标签
                    printProductLabel = true;
                }
            }
            if (printProductLabel) {
                List<AliexpressOrderItem> aliexpressOrderItemList = this.aliexpressOrderItemService.findByIds(orderItemIds);
                aliexpressOrderItemList.stream().filter(i -> NumberUtils.greaterZero(i.getFirstPrintTime())).forEach(i -> productLabelPrintedItemIds.add(i.getId()));
                queryBO.setPrintProductLabelBtn(BasePoConstant.YES);
            }
        } else {
            boolean printProductLabel = false;
            if (MerchantStorePlatformEnum.TEMU.equalsCode(orderDTO.getMerchantStorePlatformCode())) {
                // TEMU 全托管 导入订单 的才打印标签
                printProductLabel = !OrderOriginEnum.AUTO_IMPORT.equalsCode(orderDTO.getOriginType())
                        || DeliveryTypeEnum.TEMU_FULLY == queryBO.getDeliveryType();
            }
            if (!printProductLabel) {
                Address address = this.addressService.findPoById(orderDTO.getAddressId(), "country");
                if (CountryEnum.GB.equalsCode(address.getCountry()) || EuropeUnionEnum.isEuropeUnion(address.getCountry())) {
                    // 英国+欧盟 打印标签
                    printProductLabel = true;
                }
            }
            if (printProductLabel) {
                List<OrderItemProductLabel> itemProductLabelList = this.orderItemProductLabelManager.findByOrderItemIds(orderItemIds);
                itemProductLabelList.stream().filter(i -> NumberUtils.greaterZero(i.getPrintTime())).forEach(i -> productLabelPrintedItemIds.add(i.getOrderItemId()));
                queryBO.setPrintProductLabelBtn(BasePoConstant.YES);
            }
        }
        // 质检记录
        List<QcOperateRecordDTO> recordList = this.qcOperateRecordFeign.findLatestDtoByFactoryOrderIds(BaseListQueryDTO.of(factoryOrderIds, "userName"));
        Map<Long, QcOperateRecordDTO> qcRecordMap = ListUtil.toMap(QcOperateRecordDTO::getFactoryOrderId, recordList);
        // 操作记录（兼容旧数据）
        List<String> notRecordFactoryOrderNos = factoryOrderList.stream().filter(i -> !qcRecordMap.containsKey(i.getId())).map(FactoryOrder::getNo).collect(Collectors.toList());
        List<FactoryOrderOperateRecord> operateRecordList = this.factoryOrderOperateRecordService.findByNos(notRecordFactoryOrderNos, FactoryOperateEnum.PASS_GC.getOperate());
        Map<String, FactoryOrderOperateRecord> operateRecordMap = ListUtil.toMap(FactoryOrderOperateRecord::getNo, operateRecordList);
        // 免检
        Map<Long, FactoryOrderExtraResp> extraValueMap = this.factoryOrderExtraService.getFactoryOrderIdExtraValueMap(factoryOrderIds);
        // 子单
        List<OrderItemRespDto> orderItemList = this.orderItemService.findByOrderId(BaseListQueryDTO.of(orderId, "originalRflId,no,status,keyId,serial,importProduct,num"));
        Map<Long, OrderItemRespDto> orderItemMap = ListUtil.toMap(BaseDTO::getId, orderItemList);
        // 转移记录
        List<OrderItemTransferHistory> transferHistoryList = this.orderItemTransferHistoryService.findByOrderId(orderId, null);
        Map<Long, OrderItemTransferHistory> newItemIdTransferHistoryMap = ListUtil.toMap(OrderItemTransferHistory::getNewOrderItemId, transferHistoryList);
        Map<Long, OrderItemTransferHistory> oldItemIdTransferHistoryMap = ListUtil.toMap(OrderItemTransferHistory::getOrderItemId, transferHistoryList);
        // 子单供应链
        List<OrderItemSupplyChainDTO> itemSupplyChainList = this.orderItemSupplyChainService.findByIds(orderItemMap.keySet());
        Map<Long, String> itemSupplyChainMap = ListUtil.toMap(OrderItemSupplyChainDTO::getId, OrderItemSupplyChainDTO::getSupplyType, itemSupplyChainList);
        // 供应关系
        com.sdsdiy.productdata.dto.ProductSupplyFactoryIdsProductIdsDTO supplyReqDTO = new ProductSupplyFactoryIdsProductIdsDTO();
        supplyReqDTO.setFactoryIds(factoryIds).setProductIds(variantIds).setSupplyChainTypes(new HashSet<>(itemSupplyChainMap.values()));
        List<ProductSupplyDTO> productSupplyList = this.productSupplyFeign.findByFactoryIdsProductIds(supplyReqDTO);
        Map<String, ProductSupplyDTO> supplyMap = ListUtil.toMap(i -> i.getFactoryId() + "&" + i.getProductId() + "&" + i.getSupplyChainType(), productSupplyList);

        queryBO.setProductTenantId(variantList.get(0).getTenantId())
                .setVariantMap(variantMap)
                .setPackExplainMap(packExplainMap)
                .setProductLabelPrintedItemIds(productLabelPrintedItemIds)
                .setFactoryMap(factoryMap)
                .setQcRecordMap(qcRecordMap)
                .setOperateRecordMap(operateRecordMap)
                .setExtraValueMap(extraValueMap)
                .setOrderItemMap(orderItemMap)
                .setNewItemIdTransferHistoryMap(newItemIdTransferHistoryMap)
                .setOldItemIdTransferHistoryMap(oldItemIdTransferHistoryMap)
                .setItemSupplyChainMap(itemSupplyChainMap)
                .setSupplyMap(supplyMap)
        ;

    }

    public QcFactoryOrderRespDTO buildQcFactoryOrderRespDTO(QcFactoryOrderQueryBO queryBO) {
        this.formatQcFactoryOrderQueryBO(queryBO);
        // 包裹
        this.formatQueryBoParcelInfo(queryBO);
        List<FactoryOrder> factoryOrderList = queryBO.getFactoryOrderList();
        List<QcFactoryOrderRespDTO.FactoryOrderDTO> factoryOrderDtoList = new ArrayList<>();
        factoryOrderList.forEach(i -> {
            OrderItemRespDto orderItem = queryBO.getOrderItemMap().get(i.getOrderItemId());
            if (orderItem == null) {
                return;
            }
            if (FactoryOrderStatusEnum.isCancel(i.getStatus()) && !OrderStatus.isCancel(orderItem.getStatus())) {
                // 工厂取消，但商户单没取消，所以转移了，不返回
                return;
            }
            QcFactoryOrderRespDTO.FactoryOrderDTO factoryOrderDTO = new QcFactoryOrderRespDTO.FactoryOrderDTO();
            factoryOrderDTO.setTransferType(i.getTransferType()).setBeResendForLose(i.getBeResendForLose())
                    .setFactoryOrderId(i.getId()).setFactoryOrderNo(i.getNo()).setOrderItemId(i.getOrderItemId())
                    .setStatus(i.getStatus()).setMerchantOrderItemStatus(orderItem.getStatus());
            // 补件单/转移单 原单
            OrderItemTransferHistory transferHistory = queryBO.getNewItemIdTransferHistoryMap().get(orderItem.getId());
            factoryOrderDTO.setOriginalFactoryOrderNo(transferHistory != null ? transferHistory.getNo() : "");
            // 包裹
            factoryOrderDTO.setParcelList(formatQcParcelList(i, queryBO));
            // 数量、状态
            formatQcQtyAndStatus(i, orderItem, factoryOrderDTO, queryBO, queryBO.getOldItemIdTransferHistoryMap());
            // 免检
            FactoryOrderExtraResp extraResp = queryBO.getExtraValueMap().get(i.getId());
            factoryOrderDTO.setIsExemption(BasePoConstant.yesOrNo(extraResp != null && BasePoConstant.NO.equals(extraResp.getIsNeedQuality())));
            // 是否打印过标签
            factoryOrderDTO.setItemProductLabelPrinted(BasePoConstant.yesOrNo(queryBO.getProductLabelPrintedItemIds().contains(i.getOrderItemId())));
            // 质检记录
            QcOperateRecordDTO recordDTO = queryBO.getQcRecordMap().get(i.getId());
            if (recordDTO == null) {
                FactoryOrderOperateRecord oldRecord = queryBO.getOperateRecordMap().get(i.getNo());
                if (oldRecord != null) {
                    recordDTO = new QcOperateRecordDTO();
                    recordDTO.setId(oldRecord.getId());
                    recordDTO.setStatus(QcStatusEnum.PASS.code);
                    recordDTO.setRemark(oldRecord.getRemark());
                    recordDTO.setUserName(oldRecord.getUsername());
                    recordDTO.setQty(oldRecord.getNum());
                }
            }
            factoryOrderDTO.setQcRecord(BeanUtil.copyProperties(recordDTO, QcFactoryOrderRespDTO.QcRecordDTO.class));
            factoryOrderDTO.setFactory(queryBO.getFactoryMap().get(i.getFactoryId()));
            formatQcEndProductAndTexture(i, orderItem, factoryOrderDTO, queryBO);
            formatFactoryOrderDtoBtn(factoryOrderDTO, queryBO);
            factoryOrderDtoList.add(factoryOrderDTO);
        });
        // 排序
        formatQcFactoryOrderSort(queryBO.getNo(), factoryOrderDtoList);
        //返回对象
        QcFactoryOrderRespDTO respDTO = new QcFactoryOrderRespDTO();
        respDTO.setOrderId(queryBO.getOrderDTO().getId());
        respDTO.setDeliveryType(queryBO.getDeliveryType().code);
        respDTO.setIssuingBayId(queryBO.getOrderDTO().getIssuingBayId());
        respDTO.setFactoryOrderList(factoryOrderDtoList);
        boolean unQc = factoryOrderDtoList.stream().anyMatch(i -> !FactoryOrderStatusEnum.isQc(i.getStatus()));
        respDTO.setAllProductionCompleted(BasePoConstant.yesOrNo(!unQc));
        boolean allPrinted = factoryOrderDtoList.stream().allMatch(i ->
                // 打印过或者没按钮不需要打印的
                BasePoConstant.yes(i.getItemProductLabelPrinted()) || !BasePoConstant.yes(i.getPrintProductLabelBtn()));
        respDTO.setPrintProductLabelAllPrinted(BasePoConstant.yesOrNo(allPrinted));
        // 产品租户!=商户租户
        respDTO.setCrossTenantOrder(BasePoConstant.yesOrNo(!factoryOrderList.get(0).getTenantId().equals(queryBO.getProductTenantId())));
        return respDTO;
    }

    private void formatQueryBoParcelInfo(QcFactoryOrderQueryBO queryBO) {
        List<Long> orderItemIds = new ArrayList<>(queryBO.getOrderItemMap().keySet());
        List<OrderParcelDTO> parcelList = new ArrayList<>();
        List<OrderParcelItemDTO> parcelItemList = new ArrayList<>();
        Map<Long, List<OrderParcelItemDTO>> parcelItemMap = new HashMap<>(10);
        int sort = 1;
        switch (queryBO.getDeliveryType()) {
            case FBA:
                List<FbaBoxSimpleDto> fbaBoxList = this.orderFbaService.boxOutline(
                        new FbaBoxSimpleParam().setShowItem(true).setOrderItemIds(orderItemIds));
                for (FbaBoxSimpleDto i : fbaBoxList) {
                    parcelList.add(new OrderParcelDTO().setId(i.getOrderFbaBoxId())
                            .setStatus(i.getStatus()).setSortWeight(sort++));
                    List<OrderParcelItemDTO> itemList = i.getItems().stream().map(j -> new OrderParcelItemDTO()
                                    .setOrderParcelId(i.getOrderFbaBoxId()).setQty(j.getNum()).setOrderItemId(j.getOrderItemId()))
                            .collect(Collectors.toList());
                    parcelItemMap.put(i.getOrderFbaBoxId(), itemList);
                    parcelItemList.addAll(itemList);
                }
                break;
            case JF:
                List<PrepaidBoxSimpleDto> prepaidBoxList = this.prepaidBoxReadService.boxOutline(
                        new PrepaidBoxSimpleParam().setShowItem(true).setOrderItemIds(orderItemIds));
                for (PrepaidBoxSimpleDto i : prepaidBoxList) {
                    parcelList.add(new OrderParcelDTO().setId(i.getPrepaidBoxId())
                            .setStatus(i.getStatus()).setSortWeight(sort++).setBoxNo(i.getBoxNo()));
                    List<OrderParcelItemDTO> itemList = i.getItems().stream().map(j -> new OrderParcelItemDTO()
                                    .setOrderParcelId(i.getPrepaidBoxId()).setQty(j.getNum()).setOrderItemId(j.getOrderItemId()))
                            .collect(Collectors.toList());
                    parcelItemMap.put(i.getPrepaidBoxId(), itemList);
                    parcelItemList.addAll(itemList);
                }
                break;
            default:
                parcelList = this.orderParcelService.findDtoByOrderId(queryBO.getOrderDTO().getId(), "carriageNo");
                List<Long> ids = ListUtil.toValueList(OrderParcelDTO::getId, parcelList);
                parcelItemList = this.orderParcelItemService.findDtoByParcelIds(BaseListQueryDTO.of(ids));
                parcelItemMap = ListUtil.toMapValueList(OrderParcelItemDTO::getOrderItemId, parcelItemList);
        }
        Map<Long, OrderParcelDTO> parcelMap = ListUtil.toMap(OrderParcelDTO::getId, parcelList);
        // 已发货数
        Map<Long, Integer> shippedQtyMap = ParcelUtil.mapOrderItemShippedQty(parcelList, parcelItemList);
        queryBO.setParcelMap(parcelMap).setParcelItemMap(parcelItemMap).setShippedQtyMap(shippedQtyMap);
    }

    private static void formatQcFactoryOrderRespDtoBtn(QcFactoryOrderRespDTO respDTO, QcFactoryOrderQueryBO queryBO) {
        respDTO.setPrintOrderInfoBtn(BasePoConstant.YES);
        respDTO.setPrintBoxDetailBtn(BasePoConstant.YES);
    }

    private static void formatFactoryOrderDtoBtn(QcFactoryOrderRespDTO.FactoryOrderDTO factoryOrderDTO
            , QcFactoryOrderQueryBO queryBO) {
        // 是否显示打印产品标签按钮
        if (factoryOrderDTO.getBeResendForLose()) {
            // 补件的不显示
            factoryOrderDTO.setPrintProductLabelBtn(BasePoConstant.NO);
        } else {
            factoryOrderDTO.setPrintProductLabelBtn(queryBO.getPrintProductLabelBtn());
        }
        if (queryBO.getBatchQc()) {
            // 批量质检时不能单个操作
            return;
        }
        if (queryBO.getOldOrderData()) {
            factoryOrderDTO.setResendRefund(BasePoConstant.NO);
        }
        if (FactoryOrderStatusEnum.ACCOMPLISH.equalsCode(factoryOrderDTO.getStatus())
                && !OrderStatus.isCancel(factoryOrderDTO.getMerchantOrderItemStatus())
                && !factoryOrderDTO.getBeResendForLose()
                && DeliveryTypeEnum.FBA != queryBO.getDeliveryType()) {
            List<AliexpressJitOrder> aliexpressJitOrderList = queryBO.getAliexpressJitOrderList();
            if (aliexpressJitOrderList == null
                    || aliexpressJitOrderList.stream().allMatch(i -> AliexpressJitConstant.StatusEnum.CAN_CANCEL_STATUS.contains(i.getStatus()))) {
                // jit已预约不能补件
                // 已完成 且 子单没取消 且 非补件单
                factoryOrderDTO.setLostBtn(BasePoConstant.YES);
            }
        }
        if (FactoryOrderStatusEnum.canQc(factoryOrderDTO.getStatus())) {
            factoryOrderDTO.setQcPassBtn(BasePoConstant.YES);
            factoryOrderDTO.setResendBtn(BasePoConstant.YES);
        }
    }

    private static void formatQcFactoryOrderSort(final String no
            , List<QcFactoryOrderRespDTO.FactoryOrderDTO> factoryOrderDtoList) {
        // 先按状态、id排序
        factoryOrderDtoList.sort(Comparator.comparing(QcFactoryOrderRespDTO.FactoryOrderDTO::getStatus)
                .thenComparing(QcFactoryOrderRespDTO.FactoryOrderDTO::getFactoryOrderId));
        LinkedHashSet<String> sortNos = new LinkedHashSet<>();
        Map<String, List<QcFactoryOrderRespDTO.FactoryOrderDTO>> originalNoMap = ListUtil.toMapValueList(
                QcFactoryOrderRespDTO.FactoryOrderDTO::getOriginalFactoryOrderNo, factoryOrderDtoList);
        // 扫描单（前端传的单号）
        QcFactoryOrderRespDTO.FactoryOrderDTO scanOrder = factoryOrderDtoList.stream()
                .filter(i -> i.getFactoryOrderNo().equals(no)).findFirst().orElse(null);
        if (scanOrder != null) {
            // 扫描单
            // 置顶单号，如果扫描的是转换单，则替换成原单
            String topNo = CompareUtils.aElseB(scanOrder.getOriginalFactoryOrderNo(), no);
            sortNos.add(topNo);
            // 扫描单对应的转换单跟在后面
            originalNoMap.getOrDefault(topNo, Collections.emptyList())
                    .forEach(i -> sortNos.add(i.getFactoryOrderNo()));
            // 移除置顶单
            if (originalNoMap.get("") != null) {
                originalNoMap.get("").removeIf(i -> i.getFactoryOrderNo().equals(topNo));
            }
        }
        // 原单列表
        originalNoMap.getOrDefault("", Collections.emptyList()).forEach(i -> {
            sortNos.add(i.getFactoryOrderNo());
            // 对应的转移单
            originalNoMap.getOrDefault(i.getFactoryOrderNo(), Collections.emptyList())
                    .forEach(j -> sortNos.add(j.getFactoryOrderNo()));
        });
        if (sortNos.size() < factoryOrderDtoList.size()) {
            // 处理一些特殊情况，原单不返回的情况下，对应的转移单没加入排序表
            factoryOrderDtoList.forEach(i -> sortNos.add(i.getFactoryOrderNo()));
        }
        // 拍好顺序的工厂单号
        List<String> sortNoList = new ArrayList<>(sortNos);
        // 按单号赋值序号，再按序号排序
        factoryOrderDtoList.forEach(i -> i.setSortNo(sortNoList.indexOf(i.getFactoryOrderNo()) + 1));
        factoryOrderDtoList.sort(Comparator.comparing(QcFactoryOrderRespDTO.FactoryOrderDTO::getSortNo));
    }

    private static void formatQcEndProductAndTexture(FactoryOrder po, OrderItemRespDto orderItem
            , QcFactoryOrderRespDTO.FactoryOrderDTO dto, QcFactoryOrderQueryBO queryBO) {
        ProductRespDto variant = queryBO.getVariantMap().get(po.getProductId());
        ProductSupplyDTO supplyDTO = queryBO.getSupplyMap().get(po.getFactoryId() + "&" + po.getProductId()
                + "&" + queryBO.getItemSupplyChainMap().get(orderItem.getId()));
        String productSupplyCode = supplyDTO != null ? supplyDTO.getCode() : "";
        ImportProductDto importProduct = orderItem.getImportProduct();
        dto.setTexture(BeanUtil.copyProperties(variant.getTexture(), BaseIdAndNameDTO.class))
                .setResendRefund(BasePoConstant.yesOrNo(po.getTenantId().equals(variant.getTenantId())))
                // 成品
                .setEndProduct(new QcFactoryOrderRespDTO.EndProductDTO()
                        .setEndProductId(po.getEndProductId())
                        .setKeyId(orderItem.getKeyId())
                        .setSerial(orderItem.getSerial())
                        .setImportSku(importProduct != null ? importProduct.getImportSku() : "")
                        .setProductSupplyCode(productSupplyCode)
                        .setImgList(JSONUtil.isJsonArray(po.getImgs()) ? JSON.parseArray(po.getImgs(), String.class) : Collections.emptyList())
                        .setVariantId(po.getProductId())
                        .setColorName(variant.getColorName())
                        .setSize(variant.getSize())
                        .setProductName(variant.getName())
                        .setPrototypeType(variant.getPrototypeType())
                        .setPackExplain(queryBO.getPackExplainMap().get(variant.getParentId())));
    }

    private static List<QcFactoryOrderRespDTO.ParcelDTO> formatQcParcelList(FactoryOrder po
            , QcFactoryOrderQueryBO queryBO) {
        List<OrderParcelItemDTO> parcelItemList = queryBO.getParcelItemMap().getOrDefault(po.getOrderItemId(), Collections.emptyList());
        return parcelItemList.stream().map(item -> {
            QcFactoryOrderRespDTO.ParcelDTO dto = new QcFactoryOrderRespDTO.ParcelDTO();
            dto.setParcelId(item.getOrderParcelId());
            dto.setQty(item.getQty());
            OrderParcelDTO parcelDTO = queryBO.getParcelMap().get(item.getOrderParcelId());
            dto.setParcelName(ParcelConstant.formatName(parcelDTO.getSortWeight(), parcelDTO.getDeliveryType()));
            dto.setStatus(parcelDTO.getStatus());
            dto.setCarriageNo(parcelDTO.getBoxNo());
            return dto;
        }).collect(Collectors.toList());
    }

    private static void formatQcQtyAndStatus(FactoryOrder po, OrderItemRespDto orderItem
            , QcFactoryOrderRespDTO.FactoryOrderDTO dto, QcFactoryOrderQueryBO queryBO
            , Map<Long, OrderItemTransferHistory> oldItemIdTransferHistoryMap) {
        boolean isRefund = QcStatusEnum.isRefund(po.getRefuseType());
        boolean isQc = FactoryOrderStatusEnum.isQc(po.getStatus());
        dto.setRefuseType(po.getRefuseType())
                .setIsQc(BasePoConstant.yesOrNo(isQc))
                .setOrderItemQty(orderItem.getNum())
                // 已发货数
                .setShippedQty(queryBO.getShippedQtyMap().getOrDefault(orderItem.getId(), 0));
        dto.setAllShipped(dto.getShippedQty() > 0 && dto.getShippedQty() >= dto.getOrderItemQty());
        if (dto.getAllShipped()) {
            queryBO.getAllShippedFactoryOrderNos().add(po.getNo());
        }
        if (isRefund) {
            // 部分打回退款（原数量已被扣除，所以要加回来）
            dto.setQty(po.getNum() + po.getRefuseNum())
                    .setQcQty(po.getNum())
                    .setQcFailedQty(po.getRefuseNum());
        } else {
            OrderItemTransferHistory transferHistory = oldItemIdTransferHistoryMap.get(po.getOrderItemId());
            if (transferHistory != null && EnumOrderItemTransferType.REJECT.equalsCode(transferHistory.getTransferType())) {
                // 驳回转移的原单显示之前的数量
                dto.setQty(transferHistory.getNum());
            } else {
                dto.setQty(po.getNum());
            }
            dto.setQcFailedQty(po.getRefuseNum());
            if (FactoryOrderStatusEnum.ACCOMPLISH.equalsCode(po.getStatus())) {
                // 质检通过
                dto.setQcQty(po.getNum());
            } else if (QcStatusEnum.isResendNoRefund(po.getRefuseType())) {
                // 驳回/漏件
                dto.setQcQty(po.getNum() - po.getRefuseNum());
            } else {
                dto.setQcQty(0);
            }
        }
        if (FactoryOrderStatusEnum.canQc(po.getStatus())) {
            // 可质检数
            dto.setCurrentQcQty(po.getNum() - dto.getQcQty());
        } else {
            dto.setCurrentQcQty(0);
        }
    }


    public Map<Long, String> mapShipmentBelongingTenantName(List<OrderDTO> orders) {
        if (CollUtil.isEmpty(orders)) {
            return Collections.emptyMap();
        }
        List<Long> orderIds = orders.stream().map(OrderDTO::getId).collect(Collectors.toList());
        Map<Long, OrderAmountRespDTO> amountMap = this.orderAmountService.findMapByIds(orderIds);
        Set<Long> tenantIds = Sets.newHashSet();
        amountMap.values().forEach(i -> {
            tenantIds.add(i.getProductTenantId());
            tenantIds.add(i.getTenantId());
        });

        Map<Long, TenantLogisticsOrderRespDto> tenantLogisticsOrderMap = Maps.newHashMap();
        //分销订单
        List<Long> orderOrderTenantIds = orders.stream()
                .filter(o -> DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(o.getLogisticsSource()))
                .map(OrderDTO::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(orderOrderTenantIds)) {
            List<TenantLogisticsOrderRespDto> tenantLogisticsOrders = this.tenantLogisticsOrderService.getByIds(orderOrderTenantIds);
            tenantLogisticsOrders.forEach(i -> tenantLogisticsOrderMap.put(i.getId(), i));
        }
        Map<Long, String> tenantNameMap = ListUtil.toMapByBaseIdAndName(this.tenantFeign.findNameByIds(BaseListDto.of(tenantIds)));
        Map<Long, String> resultMap = new HashMap<>(orders.size());
        for (OrderDTO order : orders) {
            OrderAmountRespDTO amountAmount = amountMap.get(order.getId());
            if (amountAmount == null) {
                continue;
            }
            resultMap.put(order.getId(), tenantNameMap.get(amountAmount.getProductTenantId()));
            if (DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(order.getLogisticsSource())) {
                TenantLogisticsOrderRespDto logisticsOrder = tenantLogisticsOrderMap.get(order.getId());
                if (logisticsOrder != null && TenantLogisticsConstant.ShipmentPlaceTypeEnum.TENANT.name().equals(logisticsOrder.getShipmentPlaceType())) {
                    resultMap.put(order.getId(), tenantNameMap.get(amountAmount.getTenantId()));
                }
            }
        }
        return resultMap;
    }

    public QcResendRefundAmountDTO qcResendRefundAmount(Long factoryOrderId, Integer qty) {
        OrderAmountCalResultDTO calResultDTO = this.qcRefundAmountCal(Collections.singletonList(new BaseIdQtyDTO(factoryOrderId, qty)));
        if (BasePoConstant.YES.equals(calResultDTO.getWholeOrderLogisticsNotSupported())) {
            Assert.wrong("无法取消退款，取消后没有支持的物流，请先更换物流。");
        }
        OrderAmountCalResultDTO.Amount difference = calResultDTO.getDifference();
        QcResendRefundAmountDTO amountDTO = new QcResendRefundAmountDTO();
        amountDTO.setFactoryOrderId(factoryOrderId);
        if (CompareUtils.neZero(difference.getTotalAmount())) {
            amountDTO.setRefundTotalAmount(difference.getTotalAmount());
            if (CompareUtils.neZero(difference.getProductAmount())) {
                amountDTO.setRefundProductAmount(difference.getProductAmount());
            }
            if (CompareUtils.neZero(difference.getCarriageAmount())) {
                amountDTO.setRefundLogisticsAmount(difference.getCarriageAmount());
            }
            if (CompareUtils.neZero(difference.getServiceAmount().getTotalServiceAmount())) {
                // 退款金额大于剩余金额则取剩余金额
                BigDecimal originalServiceAmount = Optional.of(calResultDTO)
                        .map(OrderAmountCalResultDTO::getOriginal)
                        .map(OrderAmountCalResultDTO.Amount::getServiceAmount)
                        .map(OrderAmountCalResultDTO.ServiceAmount::getTotalServiceAmount)
                        .orElse(BigDecimal.ZERO);
                BigDecimal refundedServiceAmount = Optional.of(calResultDTO)
                        .map(OrderAmountCalResultDTO::getRefunded)
                        .map(OrderAmountCalResultDTO.Amount::getServiceAmount)
                        .map(OrderAmountCalResultDTO.ServiceAmount::getTotalServiceAmount)
                        .orElse(BigDecimal.ZERO);
                BigDecimal remainingServiceAmount = NumberUtil.sub(originalServiceAmount, refundedServiceAmount);
                BigDecimal differenceServiceAmount = difference.getServiceAmount().getTotalServiceAmount();
                BigDecimal actualRefundServiceAmount = NumberUtil.isGreater(differenceServiceAmount, remainingServiceAmount) ? remainingServiceAmount : differenceServiceAmount;

                amountDTO.setRefundServiceAmount(NumberUtils.greaterZero(actualRefundServiceAmount) ? actualRefundServiceAmount : BigDecimal.ZERO);
                // 运营改价，导致退的服务费为负数，此时特殊处理，显示0，总价也补上差价后显示
                BigDecimal amount = NumberUtil.sub(amountDTO.getRefundServiceAmount(), differenceServiceAmount);
                amountDTO.setRefundTotalAmount(NumberUtil.add(amountDTO.getRefundTotalAmount(), amount));
            }
            if (CompareUtils.neZero(difference.getMaterialServiceAmount())) {
                amountDTO.setRefundMaterialServiceAmount(difference.getMaterialServiceAmount());
            }
        }
        if (CompareUtils.ltZero(amountDTO.getRefundTotalAmount())) {
            QcResendRefundAmountDTO amountDto = new QcResendRefundAmountDTO();
            amountDto.setFactoryOrderId(factoryOrderId);
            return amountDto;
        }
        return amountDTO;
    }

    public OrderAmountCalResultDTO qcRefundAmountCal(List<BaseIdQtyDTO> cancelFactoryOrderList) {
        if (CollUtil.isEmpty(cancelFactoryOrderList)) {
            return new OrderAmountCalResultDTO();
        }
        Map<Long, Integer> idQtyMap = ListUtil.toMap(BaseIdQtyDTO::getId, BaseIdQtyDTO::getQty, cancelFactoryOrderList);
        List<FactoryOrder> factoryOrderList = this.factoryOrderService.findByIds(BaseListQueryDTO.of(idQtyMap.keySet(), "merchantOrderNo,orderItemId"));
        if (CollUtil.isEmpty(factoryOrderList)) {
            return new OrderAmountCalResultDTO();
        }
        List<BaseIdQtyDTO> cancelItemMap = factoryOrderList.stream()
                .map(i -> new BaseIdQtyDTO(i.getOrderItemId(), idQtyMap.get(i.getId()))).collect(Collectors.toList());
        OrderRefundReqDTO refundReqDTO = new OrderRefundReqDTO();
        refundReqDTO.setOrderNo(factoryOrderList.get(0).getMerchantOrderNo());
        refundReqDTO.setWholeOrderCancel(false);
        refundReqDTO.setCancelItemList(cancelItemMap);
        return this.orderAmountCalculateManager.qcRefundAdvise(refundReqDTO);
    }

    public OrderAmountCalResultDTO refundAmountCal(OrderRefundReqDTO orderRefundReqDTO) {
        if (CollUtil.isEmpty(orderRefundReqDTO.getCancelItemList())) {
            return new OrderAmountCalResultDTO();
        }
        OrderRefundReqDTO refundReqDTO = new OrderRefundReqDTO();
        refundReqDTO.setOrderNo(orderRefundReqDTO.getOrderNo());
        refundReqDTO.setWholeOrderCancel(orderRefundReqDTO.getWholeOrderCancel());
        refundReqDTO.setCancelItemList(orderRefundReqDTO.getCancelItemList());
        return this.orderAmountCalculateManager.qcRefundAdvise(refundReqDTO);
    }

    public static void checkPartCancel(QcBatchPrepareBO prepareBO, List<QcResendReqDTO> reqList) {
        if (CollUtil.isEmpty(reqList)) {
            return;
        }
        String reason = checkPartCancelReason(prepareBO);
        if (reason == null) {
            return;
        }
        Map<Long, Integer> realItemQtyMap = prepareBO.getRealItemQtyMap();
        Assert.validateFalse(reqList.size() >= realItemQtyMap.size(), reason + "订单不可部分取消");
        reqList.forEach(i -> {
            FactoryOrder factoryOrder = prepareBO.getOldFactoryOrderMap().get(i.getFactoryOrderId());
            Assert.validateNull(factoryOrder, "生产单不存在");
            OrderItem orderItem = prepareBO.getOldOrderItemMap().get(factoryOrder.getOrderItemId());
            Long orderItemId = NumberUtils.greaterZero(orderItem.getOriginalRflId()) ? orderItem.getOriginalRflId() : orderItem.getId();
            Integer qty = realItemQtyMap.getOrDefault(orderItemId, 0);
            Assert.validateEqual(i.getQty(), qty, reason + "订单不可部分取消");
        });
    }

    /**
     * 校验能否部分取消
     *
     * @return 能则返回空，否则返回原因
     */
    private static String checkPartCancelReason(QcBatchPrepareBO prepareBO) {
        if (!prepareBO.getRefund()) {
            return null;
        }
        DeliveryTypeEnum deliveryType = prepareBO.getDeliveryType();
        OrderDTO orderDTO = prepareBO.getOrderDTO();
        if (!deliveryType.parcelPack) {
            return deliveryType.desc;
        }
        if (orderDTO.getBeAfterServiceOrder()) {
            return "售后单";
        }
        if (MerchantStorePlatformEnum.WALMART == prepareBO.getStorePlatform()) {
            return "沃尔玛";
        }
        if (MerchantStorePlatformEnum.TEMU == prepareBO.getStorePlatform()) {
            return "temu";
        }
        Map<String, String> platformOrderExtendKeyMap = prepareBO.getPlatformOrderExtendKeyMap();
        boolean isSheinSemiOrder = PlatformOrderExtendValueEnum.SHEIN_SEMI_ORDER.isMatch(platformOrderExtendKeyMap);
        if (isSheinSemiOrder && OrderOriginType.isAutoImport(orderDTO.getOriginType())) {
            return "shein半托管自动导入";
        }
        return null;
    }

    public static boolean checkBatchQcParcelPack(QcBatchPrepareBO prepareBO
            , BatchQcSaveDTO reqDTO, List<QcResendReqDTO> resendList) {
        if (CollUtil.isEmpty(resendList)
                // 通过的子单数=订单子单数
                && reqDTO.getCurrentShipItemList().size() == prepareBO.getRealItemQtyMap().size()) {
            // 没有驳回的，且全部通过
            return true;
        }
        if (prepareBO.getOrderDTO().getBeAfterServiceOrder()) {
            return false;
        }
        if (!prepareBO.getDeliveryType().parcelPack) {
            return false;
        }
        if (MerchantStorePlatformEnum.TEMU == prepareBO.getStorePlatform()) {
            return false;
        }
        if (MerchantStorePlatformEnum.WALMART == prepareBO.getStorePlatform()) {
            return false;
        }
        if (MerchantStorePlatformEnum.SHEIN == prepareBO.getStorePlatform()) {
            return false;
        }
        if (MerchantStorePlatformEnum.TIKTOK == prepareBO.getStorePlatform()) {
            return false;
        }
        return true;
    }

    public FastQcFactoryOrderRespDTO fastQcFactoryOrder(String no, Integer isBatch) {
        boolean batch = BasePoConstant.yes(isBatch);
        FastQcFactoryOrderRespDTO respDTO = new FastQcFactoryOrderRespDTO();
        respDTO.setNormalQc(BasePoConstant.YES);
        List<FactoryOrder> factoryOrderList = this.factoryOrderService.findByNoOrMerchantOrderNoForQc(no);
        Assert.validateEmpty(factoryOrderList, "该订单待还未生成生产单，不可质检");
        if (!batch) {
            factoryOrderList = factoryOrderList.stream().filter(i ->
                    !FactoryOrderStatusEnum.isCancel(i.getStatus())).collect(Collectors.toList());
            if (CollUtil.isEmpty(factoryOrderList)) {
                return respDTO.setErrorMsg("该订单待还未生成生产单或已取消");
            }
            if (factoryOrderList.size() > 1) {
                // 多工厂单
                return respDTO;
            }
            // 本次操作的生产单
            FactoryOrder factoryOrder = factoryOrderList.get(0);
            if (factoryOrder.getNum() > 1) {
                // 多件
                return respDTO;
            }
            if (!QcStatusEnum.NONE.equalsCode(factoryOrder.getRefuseType())) {
                // 驳回单
                return respDTO;
            }
            if (factoryOrderList.stream().anyMatch(i -> FactoryOrderStatusEnum.UNCONFIRMED.equalsCode(i.getStatus()))) {
                return respDTO.setErrorMsg("该订单生产单待工厂确认，不可质检");
            }
        }

        OrderDTO orderDTO = this.orderService.findByNo(factoryOrderList.get(0).getMerchantOrderNo()
                , "status,originType,originId,merchantId,isAdvance,logisticsId,customerId,addressId");
        this.qcCheckOrder(orderDTO);
        if (!this.orderParcelService.existsParcel(orderDTO.getId())) {
            // 自动处理异常数据
            this.rocketMQTemplate.sendNormalAfterCommit(RocketMqTopicConst.EVENT_ORDER, OrderTagConst.ORDER_PARCEL_PACK_V2_TOPIC
                    , new OrderParcelPackMessageDTO(orderDTO.getId(), BasePoConstant.YES));
            Assert.wrong("包裹生成中，请稍后");
        }
        if (OrderStatus.isCancel(orderDTO.getStatus())) {
            return respDTO.setErrorMsg("该订单已取消，不可质检");
        }
        DeliveryTypeEnum deliveryType = DeliveryTypeEnum.matchOrder(orderDTO.getOriginType(), orderDTO.getLogisticsCodeId());
        if (deliveryType != DeliveryTypeEnum.NORMAL && deliveryType != DeliveryTypeEnum.TEMU_FULLY) {
            // 非普通订单
            return respDTO;
        }
        if (this.orderRemarkService.countByOrderIdForQc(orderDTO.getId()) > 0) {
            // 有需要提醒的备注
            return respDTO;
        }
        if (!batch) {
            List<OrderItem> orderItemList = this.orderItemService.findRealItemByOrderIds(Collections.singleton(orderDTO.getId()));
            Assert.validateEmpty(orderItemList, "该订单无可质检的子单");
            if (orderItemList.size() > 1) {
                // 多子单
                return respDTO;
            }
        }
        // 质检通过
        this.fastQcPassBatch(orderDTO.getId(), factoryOrderList);
        if (deliveryType == DeliveryTypeEnum.TEMU_FULLY) {
            // temu全托管只质检通过，不发货
            return respDTO;
        }
        List<OrderParcelDTO> parcelList = this.orderParcelService.findDtoByOrderId(orderDTO.getId(), "logisticsId");
        if (parcelList.size() > 1) {
            // 多包裹
            return respDTO;
        }
        OrderParcelDTO parcel = parcelList.get(0);
        Long parcelLogisticsId = parcel.getLogisticsId();
        TenantLogisticsRespDto parcelLogistics = null;
        if (NumberUtils.greaterZero(parcelLogisticsId)) {
            parcelLogistics = this.tenantLogisticsFeign.getDtoById(parcelLogisticsId);
            if (parcelLogistics == null) {
                // 线上物流也允许快速质检发货  || LogisticsServiceProviderEnum.isOnlineServiceProviderId(parcelLogistics.getServiceProviderId())
                return respDTO.setErrorMsg("物流数据异常");
            }
            String country = this.addressService.findCountry(orderDTO.getAddressId());
            if (this.environmentVariables.isImgCheckLogisticsProviderServiceId(parcelLogistics.getServiceProviderId(), country)) {
                if (this.orderExtendInfoMapperManager.isMatch(orderDTO.getId(), OrderExtendValueEnum.IMG_CHECK_STATUS_INFRINGEMENT)) {
                    return respDTO.setErrorMsg("该订单存在侵权图案，请联系客户取消该订单，或更换其他非云途物流发货");
                }
            }
        }
        boolean canShip = this.orderStatusManager.checkParcelCanShip(parcel.getId());
        if (!canShip) {
            return respDTO.setErrorMsg("存在无法质检的生产单");
        }
        OrderCarriageRespDto carriage = this.orderCarriageService.findByParcelId(parcel.getId());
        if (OrderParcelStatusEnum.PACKING.equalsCode(parcel.getStatus())) {
            if (CarriageStatusEnum.STATUS_SUCCESS.equalsCode(carriage.getCarriageStatus())) {
                try {
                    // 发货
                    this.orderShipmentManager.checkLabelUrl(parcel, carriage, parcelLogistics);
                    this.orderShipmentManager.confirmCarriage(parcel, carriage, parcelLogistics);
                    this.orderShipmentManager.singleParcelShipment(orderDTO, parcel, parcelLogistics);
                    // 打面单
                    respDTO.setPrintCarriagePdf(BasePoConstant.YES);
                } catch (Exception e) {
                    respDTO.setErrorMsg(ExceptionUtils.getFeignExceptionMessage(e));
                }
            } else {
                respDTO.setManualShipment(BasePoConstant.YES);
            }
        } else if (!orderDTO.getStatus().equals(OrderStatus.FINISH.getStatus())) {
            // 已发货，订单未完成，直接打面单
            respDTO.setPrintCarriagePdf(BasePoConstant.YES);
        }
        FastQcFactoryOrderRespDTO.ShipmentInfo shipmentInfo = new FastQcFactoryOrderRespDTO.ShipmentInfo();
        shipmentInfo.setParcelId(parcel.getId())
                .setCarriageName(carriage.getCarriageName())
                .setCarriageNo(carriage.getCarriageNo())
                .setLogisticsId(parcelLogisticsId)
                .setCarriageType(BasePoConstant.yesOrNo(parcelLogistics == null))
                .setPdfUrl(carriage.getLaberPdf())
                .setLaberPdf(carriage.getLaberPdf());
        if (parcelLogistics != null) {
            shipmentInfo.setLogisticsCodeId(parcelLogistics.getCodeId())
                    .setServiceProviderId(parcelLogistics.getServiceProviderId());
            if (StrUtil.isBlank(carriage.getLaberPdf())) {
                // 某些物流的面单特殊处理
                if (OrderParcelManager.syncPrintLaberProviderIds().contains(parcelLogistics.getServiceProviderId())) {
                    shipmentInfo.setLaberPdf(parcelLogistics.getName());
                }
            }
        }
        respDTO.setOrderId(orderDTO.getId()).setOrderNo(orderDTO.getNo())
                .setDeliveryType(deliveryType.code)
                .setIssuingBayId(orderDTO.getIssuingBayId())
                .setShipmentInfo(shipmentInfo);
        return respDTO;
    }

    private void fastQcPassBatch(Long orderId, List<FactoryOrder> factoryOrderList) {
        // 可以质检的单子自动通过
        List<FactoryOrder> canQcList = ListUtil.filter(factoryOrderList, i -> FactoryOrderStatusEnum.canQc(i.getStatus()));
        if (CollUtil.isEmpty(canQcList)) {
            return;
        }
        List<BatchQcSaveDTO.CurrentShipItemDTO> dtoList = ListUtil.copyProperties(canQcList
                , BatchQcSaveDTO.CurrentShipItemDTO.class, (s, t) -> t.setFactorOrderId(s.getId())
                        .setRejectQty(0)
                        .setLessQty(0)
                        .setPassQty(s.getNum()));
        BatchQcSaveDTO reqDTO = new BatchQcSaveDTO();
        reqDTO.setOrderId(orderId)
                .setCurrentShipItemList(dtoList)
                .setIsRefund(BasePoConstant.NO)
                .setIsCheckParcel(BasePoConstant.NO);
        ((FactoryOrderQcManager) AopContext.currentProxy()).qcBatch(reqDTO);
    }

    private void qcCheckOrder(OrderDTO orderDTO) {
        Assert.validateNull(orderDTO, "订单不存在!");
        Assert.validateFalse(OrderStatus.isPay(orderDTO.getStatus()), "该订单待付款，不可质检");
        Assert.validateTrue(OrderStatus.SHELVE.equalsStatus(orderDTO.getStatus()), "搁置中订单，不可质检");
        // 校验发货仓权限
        this.checkIssuingBayPermission(orderDTO.getIssuingBayId());
    }
}
