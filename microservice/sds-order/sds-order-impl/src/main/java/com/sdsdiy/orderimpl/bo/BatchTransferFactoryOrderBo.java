package com.sdsdiy.orderimpl.bo;

import com.sdsdiy.issuingbayapi.dto.base.IssuingBayRespDto;
import com.sdsdiy.logisticsdata.dto.base.LogisticsRespDto;
import com.sdsdiy.orderapi.dto.order.FactoryOrderAfterServiceAuditImagesDTO;
import com.sdsdiy.orderapi.dto.order.FactoryOrderAfterServiceImagesDTO;
import com.sdsdiy.orderapi.dto.order.OrderItemTransferHistoryDto;
import com.sdsdiy.orderdata.dto.BatchTransferFactoryMsgRespDTO;
import com.sdsdiy.orderimpl.entity.po.*;
import com.sdsdiy.orderimpl.entity.po.warning.OrderEarlyWarning;
import com.sdsdiy.productapi.dto.ProductDto;
import com.sdsdiy.productapi.dto.ProductSupplyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTransferFactoryOrderBo {

    private FactoryOrder factoryOrder;
    private IssuingBayRespDto issuingBay;
    private boolean bayChange;
    private BatchTransferFactoryMsgRespDTO message;
    private OrderItem orderItem;
    private OrderItemTransferHistoryDto history;
    private List<FactoryOrderAfterServiceImagesDTO> imagesDTOS;
    private List<FactoryOrderAfterServiceAuditImagesDTO> auditImgs;
    private String auditRemark;
    private ProductDto productDto;
    private LogisticsRespDto logistic;
    private List<OrderItemProductionManuscript> manuscripts;
    private Integer commitFactoryOrderNum;
    private List<FactoryOrder> allFactoryOrders;
    private Order order;
    private ProductionLineItem productionLineItem;
    private ProductSupplyDTO supply;
    private boolean reject;
    private Long defaultProductionLineId;
    private OrderItemMap orderItemMap;
    private FactoryOrderExemptCompensationAmountRecord factoryOrderExemptCompensationAmountRecord;
}
