package com.sdsdiy.orderimpl.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sdsdiy.orderimpl.entity.po.AdminPrepaidPaymentHistory;
import com.sdsdiy.orderimpl.mapper.AdminPrepaidPaymentHistoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;


/**
 * 线下付款记录表(AdminPrepaidPaymentHistory)MapperManager表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-24 17:43:46
 */
@Slf4j
@Service
public class AdminPrepaidPaymentHistoryMapperManager extends ServiceImpl<AdminPrepaidPaymentHistoryMapper, AdminPrepaidPaymentHistory>{

    @Resource
    private AdminPrepaidPaymentHistoryMapper adminPrepaidPaymentHistoryMapper;

    public void updateByAdminPrepaidId(Long adminPrepaidId,Long newShippedId){
        lambdaUpdate()
            .set(AdminPrepaidPaymentHistory::getAdminPrepaidId,newShippedId)
            .eq(AdminPrepaidPaymentHistory::getAdminPrepaidId,adminPrepaidId)
            .update();
    }

    public void updateAdminPrepaidIdByIds(List<Long> ids,Long newShippedId){
        if(CollUtil.isEmpty(ids)){
            return;
        }
        lambdaUpdate()
            .set(AdminPrepaidPaymentHistory::getAdminPrepaidId,newShippedId)
            .in(AdminPrepaidPaymentHistory::getId,ids)
            .update();
    }

    public void batchUpdatePrepaidIdByIds(List<Long> ids,Long adminPrepaidId){
        if(CollUtil.isEmpty(ids)){
            return;
        }
        lambdaUpdate()
            .set(AdminPrepaidPaymentHistory::getAdminPrepaidId,adminPrepaidId)
            .in(AdminPrepaidPaymentHistory::getId,ids)
            .update();
    }

    public List<AdminPrepaidPaymentHistory> getByAdminPrepaidId(Long adminPrepaidId){
        return lambdaQuery()
            .eq(AdminPrepaidPaymentHistory::getAdminPrepaidId,adminPrepaidId)
            .list();
    }

    public List<AdminPrepaidPaymentHistory> getByRecordIds(List<Long> recordIds){
        if(CollUtil.isEmpty(recordIds)){
            return Collections.emptyList();
        }
        return lambdaQuery()
            .in(AdminPrepaidPaymentHistory::getOfflinePayRecordId,recordIds)
            .list();
    }
}