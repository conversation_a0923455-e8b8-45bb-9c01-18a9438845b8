package com.sdsdiy.statimpl.service;


import com.baomidou.dynamic.datasource.annotation.DS;

import com.sdsdiy.statapi.dto.base.UrgentDto;
import com.sdsdiy.statimpl.mapper.FactoryOrderOperateStatisticsMapper;

import com.sdsdiy.statimpl.mapper.StatMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 加急订单(FactoryOrderOperateStatistics)表服务实现类
 *
 * <AUTHOR>
 * @since 2020-09-01 16:42:05
 */
@Service
@Log4j2
@DS("commonslave")
public class OrderUrgentService {
	@Resource
	private StatMapper statMapper;


	public UrgentDto list() {
		UrgentDto urgentDto = new UrgentDto();
		urgentDto.setRejectOrderNum(statMapper.getRejectOrderNum());
		urgentDto.setExpressOrderNum(statMapper.getExpressOrderNumNum());
		urgentDto.setSupplementOrderNum(statMapper.getSupplementOrderNum());
		urgentDto.setCustomerServiceOrderNum(statMapper.getCustomerServiceUrgentOrderNumber());

		return urgentDto;
	}
}