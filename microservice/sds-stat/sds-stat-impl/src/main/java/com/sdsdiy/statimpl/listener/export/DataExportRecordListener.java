package com.sdsdiy.statimpl.listener.export;

import com.alibaba.fastjson.JSON;
import com.sdsdiy.common.base.entity.dto.BaseDownloadDTO;
import com.sdsdiy.core.mq.MqListenerRegisterCondition;
import com.sdsdiy.core.mq.annotation.RocketMQMessageListener;
import com.sdsdiy.core.mq.core.RocketMQListener;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.mq.queue.stat.StatConsumerConst;
import com.sdsdiy.core.mq.queue.stat.StatTagConst;
import com.sdsdiy.core.mq.support.RocketMQUtil;
import com.sdsdiy.core.util.ExceptionUtils;
import com.sdsdiy.orderdata.dto.PodOrderQueryDto;
import com.sdsdiy.paymentapi.param.bill.BillQueryParam;
import com.sdsdiy.statdata.constant.DataExportRecordConstant;
import com.sdsdiy.statdata.constant.DataExportRecordTypeEnum;
import com.sdsdiy.statdata.dto.export.DataExportRecordMessageDTO;
import com.sdsdiy.statdata.dto.export.param.PlatformBillExportParam;
import com.sdsdiy.statdata.dto.export.param.TenantToSaasBillParam;
import com.sdsdiy.statimpl.entity.po.export.DataExportRecord;
import com.sdsdiy.statimpl.feign.OrderFeign;
import com.sdsdiy.statimpl.feign.TenantToSaasBillFeign;
import com.sdsdiy.statimpl.feign.TenantTotalBillFeign;
import com.sdsdiy.statimpl.feign.payment.MerchantBillFeign;
import com.sdsdiy.statimpl.manager.DataExportRecordMapperManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/7/26
 */
@Service
@Slf4j
@Conditional(MqListenerRegisterCondition.class)
@RequiredArgsConstructor
@RocketMQMessageListener(topic = RocketMqTopicConst.EVENT_STAT,
        consumerGroup = StatConsumerConst.GID_TENANT_DATA_EXPORT_HANDLER,
        tag = StatTagConst.TENANT_DATA_EXPORT_TOPIC)
public class DataExportRecordListener implements RocketMQListener {
    private final DataExportRecordMapperManager dataExportRecordMapperManager;
    private final OrderFeign orderFeign;
    private final TenantTotalBillFeign tenantTotalBillFeign;
    private final TenantToSaasBillFeign tenantToSaasBillFeign;
    private final MerchantBillFeign merchantBillFeign;


    @Override
    public void consumeMsg(MessageView messageView) {
        DataExportRecordMessageDTO messageDTO = RocketMQUtil.getBodyBean(messageView, DataExportRecordMessageDTO.class);
        this.handle(messageDTO);
    }

    private void handle(DataExportRecordMessageDTO messageDTO) {
        DataExportRecord record = this.dataExportRecordMapperManager.getById(messageDTO.getId());
        if (record == null || DataExportRecordConstant.SUCCESS == record.getStatus()) {
            log.error("无效数据导出id:{}", messageDTO.getId());
            return;
        }
        String downloadUrl = null;
        String errMsg = null;
        BillQueryParam billQueryParam = null;
        int status;
        try {
            DataExportRecordTypeEnum exportType = DataExportRecordTypeEnum.getByExportType(record.getExportType());
            switch (exportType) {
                case ORDER_MANAGEMENT_CONSIGNEE_INFO:
                    PodOrderQueryDto queryDto = JSON.parseObject(record.getExportCondition(), PodOrderQueryDto.class);
                    BaseDownloadDTO downloadDTO = this.orderFeign.exportConsigneeInfo(queryDto);
                    downloadUrl = downloadDTO.getDownloadUrl();
                    break;
                case PLATFORM_BILL:
                    // 平台账单
                    PlatformBillExportParam param = JSON.parseObject(record.getExportCondition(), PlatformBillExportParam.class);
                    BaseDownloadDTO baseDownloadDTO = this.tenantTotalBillFeign.exportAsync(record.getTenantId(), param.getMonthly());
                    downloadUrl = baseDownloadDTO.getDownloadUrl();
                    break;
                case TENANT_BILL:
                    // 租户账单
                    TenantToSaasBillParam tenantToSaasBillParam = JSON.parseObject(record.getExportCondition(), TenantToSaasBillParam.class);
                    BaseDownloadDTO tenantToSaasBillDownloadDTO = this.tenantToSaasBillFeign.exportAsync(record.getTenantId(), tenantToSaasBillParam.getMonthly());
                    downloadUrl = tenantToSaasBillDownloadDTO.getDownloadUrl();
                    break;
                case SAAS_TENANT_DIS_BILL:
                case SAAS_PLATFORM_BILL:
                case POD_TENANT_DIS_BILL_CURRENT_SUP:
                case POD_TENANT_DIS_BILL_CURRENT_DIS:
                case POD_TENANT_NORMAL_BILL:
                case POD_MERCHANT_TO_TENANT_BILL:
                    billQueryParam = JSON.parseObject(record.getExportCondition(), BillQueryParam.class);
                    downloadUrl = this.merchantBillFeign.billQueryExport(billQueryParam, exportType).getDownloadUrl();
                    break;
                default:
                    return;
            }
            status = DataExportRecordConstant.SUCCESS;
        } catch (Exception e) {
            errMsg = ExceptionUtils.getFeignExceptionMessage(e);
            status = DataExportRecordConstant.FAIL;
        }
        DataExportRecord updateRecord = new DataExportRecord();
        updateRecord.setId(record.getId())
                .setDownloadUrl(downloadUrl)
                .setFailureLog(errMsg)
                .setStatus(status);
        this.dataExportRecordMapperManager.updatePo(updateRecord);
    }
}
