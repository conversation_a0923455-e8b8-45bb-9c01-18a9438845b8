package com.sdsdiy.statdata.constant;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.statdata.dto.DataExportRecordTypeDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * 导出类型枚举
 *
 * <AUTHOR>
 * @date 2025/2/18
 */
@AllArgsConstructor
public enum DataExportRecordTypeEnum {
    //
    PLATFORM_BILL(1, "平台账单", null),
    TENANT_BILL(2, "租户账单", null),
    FINANCIAL_ANALYSIS_ORDER(3, "订单财务分析", null),
    ORDER_MANAGEMENT(4, "订单管理", null),
    LOGISTICS_DELIVERY_TRACKING(5, "物流妥投跟踪", null),
    PRODUCTION_ORDER(6, "生产订单", null),
    ORDER_MANAGEMENT_CONSIGNEE_INFO(7, "订单管理-收货信息", null),


    POD_TENANT_DIS_BILL_CURRENT_SUP(10, "POD分销账单-当前租户是供应商", "template/bill/PodTenantDisBillCurrentSup.xlsx"),
    POD_TENANT_DIS_BILL_CURRENT_DIS(11, "POD分销账单-跨租户供应商账单", "template/bill/PodTenantDisBillCurrentDis.xlsx"),

    POD_AFTER_SERVICE_AUDIT(12, "售后订单", null),

    SAAS_TENANT_DIS_BILL(13, "分销账单", "template/bill/TenantDisBillBySaas.xlsx"),
    POD_TENANT_NORMAL_BILL(14, "POD-租户收支明细（非分销订单）", "template/bill/PodTenantNormalBill.xlsx"),

    SAAS_PLATFORM_BILL(15, "SAAS端-平台账单", "template/bill/SaasPlatformBill.xlsx"),
    POD_MERCHANT_TO_TENANT_BILL(16, "POD商户账单", "template/bill/MerchantToTenant.xlsx"),
    ;


    @Getter
    private final Integer exportType;
    @Getter
    private final String desc;
    @Getter
    private final String templatePath;

    public static DataExportRecordTypeEnum getByExportType(Integer exportType) {
        for (DataExportRecordTypeEnum value : values()) {
            if (value.getExportType().equals(exportType)) {
                return value;
            }
        }
        throw new BusinessException("导出来源不存在");
    }

    public static List<DataExportRecordTypeDto> dataList() {
        List<DataExportRecordTypeDto> list = Lists.newArrayList();
        for (DataExportRecordTypeEnum value : values()) {
            DataExportRecordTypeDto data = new DataExportRecordTypeDto();
            data.setDesc(value.desc);
            data.setExportType(value.exportType);
            list.add(data);
        }
        return list;
    }

    public String getFileName() {
        return this.getDesc() + DateUtil.now() + "_" + RandomUtil.randomString(5);
    }
}
