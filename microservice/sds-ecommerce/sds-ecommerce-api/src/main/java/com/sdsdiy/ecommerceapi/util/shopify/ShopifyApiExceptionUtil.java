package com.sdsdiy.ecommerceapi.util.shopify;

import cn.hutool.http.HttpStatus;
import com.sdsdiy.ecommerceapi.exception.ShopifyApiException;

/**
 * <AUTHOR>
 */
public class ShopifyApiExceptionUtil {

    /**
     * 店铺授权过期
     *
     * @param e
     *
     * @return
     */
    public static boolean isAuthExpired(ShopifyApiException e) {
        int httpStatus = e.getHttpStatus();
        return httpStatus == HttpStatus.HTTP_UNAUTHORIZED
            || httpStatus == HttpStatus.HTTP_PAYMENT_REQUIRED
            || httpStatus == HttpStatus.HTTP_FORBIDDEN;
    }


    /**
     * 请求频率限制
     *
     * @param e
     * @return
     */
    public static boolean isRateLimit(ShopifyApiException e) {
        return e.getHttpStatus() == 429;
    }

    public static boolean isNotFound(ShopifyApiException e) {
        return e.getHttpStatus() == 404;
    }

    /**
     * 服务器内部错误
     *
     * @param e
     *
     * @return
     */
    public static boolean isShopifyServerError(ShopifyApiException e) {
        int httpStatus = e.getHttpStatus();
        return httpStatus == HttpStatus.HTTP_UNAVAILABLE
            || httpStatus == HttpStatus.HTTP_INTERNAL_ERROR
            || httpStatus == 522;
    }
}
