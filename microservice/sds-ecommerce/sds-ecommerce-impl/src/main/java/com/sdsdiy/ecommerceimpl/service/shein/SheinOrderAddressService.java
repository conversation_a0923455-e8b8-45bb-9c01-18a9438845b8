package com.sdsdiy.ecommerceimpl.service.shein;

import com.alibaba.fastjson.JSONObject;
import com.sds.platform.sdk.shein.SheinApiClient;
import com.sds.platform.sdk.shein.Shein<PERSON>ey;
import com.sds.platform.sdk.shein.SheinOrderStatusEnum;
import com.sds.platform.sdk.shein.SheinServerException;
import com.sds.platform.sdk.shein.order.OrderExportAddressApi;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.OrderTagConst;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.core.redis.lock.LockUtil;
import com.sdsdiy.ecommercedata.bo.OnlineOrderSyncMsg;
import com.sdsdiy.ecommercedata.bo.SheinOrderAddressTaskMsg;
import com.sdsdiy.ecommerceimpl.entity.SheinOrder;
import com.sdsdiy.ecommerceimpl.entity.SheinOrderAddress;
import com.sdsdiy.ecommerceimpl.entity.SheinOrderAddressTask;
import com.sdsdiy.ecommerceimpl.entity.SheinSupplier;
import com.sdsdiy.ecommerceimpl.mapper.shein.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;

@Service
@Slf4j
@RequiredArgsConstructor
public class SheinOrderAddressService {

    private final SheinSupplierMapper sheinSupplierMapper;
    private final RocketMQTemplate rocketMQTemplate;
    private final SheinOrderMapper sheinOrderMapper;
    private final SheinOrderAddressMapper sheinOrderAddressMapper;
    private final SheinOrderAddressTaskMapper sheinOrderAddressTaskMapper;
    private final LockUtil lockUtil;
    private final SheinCustomInfoService sheinCustomInfoService;

    private static String lockKey(String orderNo) {
        return "ecommerce:shein:order:address:task:" + orderNo;
    }

    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    static class AddressTaskResult {
        private Boolean success;
        private String msg;
    }

    private AddressTaskResult processTask(SheinOrder order, SheinOrderAddressTask task) {
        String orderNo = task.getOrderNo();
        String supplierId = order.getSupplierId();

        SheinSupplier supplier = sheinSupplierMapper.findOneBySupplierId(supplierId);
        if (supplier == null || supplier.authExpired()) {
            return new AddressTaskResult().setSuccess(true).setMsg("店铺失效");
        }

        if (!order.canImportAddress()) {
            return new AddressTaskResult().setSuccess(true).setMsg("不能导入地址");
        }

        SheinOrderAddress orderAddress = new SheinOrderAddress()
            .setOrderNo(orderNo)
            .setSupplierId(supplierId)
            .setMerchantStoreType(order.getMerchantStoreType())
            .setUpdateTime(LocalDateTime.now().toString());

        try {
            Integer orderStatus = order.getOrderStatus();
//            int handleType = SheinOrderStatusEnum.PENDING.getStatus().equals(orderStatus) ? 2 : 1;
            // 永远是1，仅同步地址，不更改订单状态
            int handleType = 1;
            OrderExportAddressApi api = new OrderExportAddressApi(orderNo, handleType);
            SheinKey sheinKey = supplier.generateRelatedKey();
            JSONObject addressJson = SheinApiClient.executeAndParse(sheinKey, api);
            orderAddress.setAddressJson(addressJson.toJSONString());
            sheinOrderAddressMapper.upsertByColumnAndCollectionName("orderNo", orderAddress);
            return new AddressTaskResult().setSuccess(true).setMsg("");
        } catch (SheinServerException sx) {
            return new AddressTaskResult().setSuccess(false).setMsg(sx.getErrorMsg());
        }
    }
    public Boolean updatePendingStatus(String no){
        SheinOrder order = sheinOrderMapper.findOne(no);
        if (order == null) {
            return false;
        }

        String orderNo = no;
        String supplierId = order.getSupplierId();

        SheinSupplier supplier = sheinSupplierMapper.findOneBySupplierId(supplierId);
        if (supplier == null || supplier.authExpired()) {
            return false;
        }

        if (!order.canImportAddress()) {
            return false;
        }

        SheinOrderAddress orderAddress = new SheinOrderAddress()
                .setOrderNo(orderNo)
                .setSupplierId(supplierId)
                .setMerchantStoreType(order.getMerchantStoreType())
                .setUpdateTime(LocalDateTime.now().toString());
        Integer orderStatus = order.getOrderStatus();
        if(!SheinOrderStatusEnum.PENDING.getStatus().equals(orderStatus)){
            return true;
        }
        // 2更改状态
        int handleType = 2;
        try {

            OrderExportAddressApi api = new OrderExportAddressApi(orderNo, handleType);
            SheinKey sheinKey = supplier.generateRelatedKey();
            JSONObject addressJson = SheinApiClient.executeAndParse(sheinKey, api);
            orderAddress.setAddressJson(addressJson.toJSONString());
            sheinOrderAddressMapper.upsertByColumnAndCollectionName("orderNo", orderAddress);
            return true;
        } catch (SheinServerException sx) {
            log.error(no + " update pending error",sx);
        }
        return false;
    }

    public void notifyOnlineOrderUpdate(SheinOrder sheinOrder) {
        OnlineOrderSyncMsg msg = new OnlineOrderSyncMsg();
        msg.setOrderId(sheinOrder.getOrderNo());
        msg.setPlatformCode(MerchantStorePlatformEnum.SHEIN.getCode());
        msg.setShopName(sheinOrder.getSupplierId());
        msg.setType(sheinOrder.getMerchantStoreType());
        rocketMQTemplate.sendDelay(RocketMqTopicConst.EVENT_ORDER_DELAY, OrderTagConst.DELAY_ONLINE_ORDER_SYNC, msg, Duration.ofSeconds(60));
    }

    public void consumeTask(SheinOrderAddressTaskMsg msg) {
        SheinOrderAddressTask task = sheinOrderAddressTaskMapper.findOne(msg.getOrderNo());
        if (task == null) {
            return;
        }
        SheinOrder order = sheinOrderMapper.findOne(task.getOrderNo());
        if (order == null) {
            sheinOrderAddressTaskMapper.removeByOrderNo(msg.getOrderNo());
            return;
        }

//        sheinCustomInfoService.getAndSaveCustomInfo(order);

        log.info("shein order address task consume, orderNo={}", msg.getOrderNo());

        String lockKey = lockKey(msg.getOrderNo());
        boolean lockSuccess = lockUtil.tryLock(lockKey, 1000, 5 * 1000);
        if (!lockSuccess) {
            log.warn("shein order address task consume locked");
            return;
        }

        try {
            AddressTaskResult addressTaskResult = processTask(order, task);
            log.info("shein order address task consume orderNo={},result={}", msg.getOrderNo(), addressTaskResult);
            if (Boolean.TRUE.equals(addressTaskResult.getSuccess())) {
                sheinOrderAddressTaskMapper.removeByOrderNo(msg.getOrderNo());
            } else {
                task.setProcessCount(task.getProcessCount() + 1);
                if (task.getProcessCount() > 3) {
                    task.setProcessTime(0L);
                } else {
                    task.setProcessTime(System.currentTimeMillis() + 30 * 1000 * 60);
                }
                task.setUpdateTime(LocalDateTime.now().toString());
                task.setLastProcessTime(LocalDateTime.now().toString());
                sheinOrderAddressTaskMapper.upsertByOrderNo(task);
            }
            notifyOnlineOrderUpdate(order);
        } finally {
            lockUtil.unlock(lockKey);
        }
    }
}
