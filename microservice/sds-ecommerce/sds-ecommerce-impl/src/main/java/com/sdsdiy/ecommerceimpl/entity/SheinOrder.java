package com.sdsdiy.ecommerceimpl.entity;

import com.sds.platform.sdk.shein.OrderPerformanceTypeEnum;
import com.sds.platform.sdk.shein.OrderPrintOrderStatusEnum;
import com.sdsdiy.core.mongo.BaseMongoEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class SheinOrder extends BaseMongoEntity {

    private String orderNo;
    private String supplierId;
    private String merchantStoreType;

    private Integer orderStatus;
    private Integer performanceType;
    private Integer printOrderStatus;
    private String detailJson;

    private String sheinOrderUpdateTime;
    private String detailJsonMd5;

    private Long updatedAt;
    private String updateTime;


    public boolean canImportAddress() {
        return OrderPerformanceTypeEnum.SELLER_LOGISTICS.getStatus().equals(getPerformanceType())
            && OrderPrintOrderStatusEnum.CAN.getStatus().equals(getPrintOrderStatus());
    }
}
