package com.sdsdiy.ecommerceimpl.feign.publish;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.publishapi.api.AliexpressProductAuthApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "service-publish", contextId = "AliexpressProductAuthFeign", url = MicroServiceEndpointConstant.SERVICE_PUBLISH)
public interface AliexpressProductAuthFeign extends AliexpressProductAuthApi {
}
