package com.sdsdiy.gatewayimpl.support;


import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public enum EnumOutPlatformCode {
    BAY("bay","仓库"),
    FACTORY("factory","工厂")

    ;
    //新增枚举 记录下当下status
    private final String code;

    private final String desc;

    EnumOutPlatformCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
