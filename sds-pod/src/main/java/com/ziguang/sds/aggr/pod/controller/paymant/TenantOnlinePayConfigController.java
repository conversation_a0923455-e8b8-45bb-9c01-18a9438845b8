package com.ziguang.sds.aggr.pod.controller.paymant;

import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.exception.BusinessException;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.paymentapi.dto.FreePayMerchantNameDto;
import com.sdsdiy.paymentapi.dto.TenantOnlinePayDto;
import com.sdsdiy.paymentapi.param.TenantBankCardParam;
import com.sdsdiy.paymentapi.param.TenantOnlinePayConfParam;
import com.sdsdiy.paymentapi.param.TenantOnlinePayParam;
import com.ziguang.sds.aggr.pod.feign.payment.TenantOnlinePayConfigFeign;
import com.ziguang.sds.aggr.pod.shiro.ISecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/pod/tenant")
@Api(tags = "租户线上支付")
public class TenantOnlinePayConfigController {

    private static final Logger log = LoggerFactory.getLogger(TenantOnlinePayConfigController.class);
    @Autowired
    private TenantOnlinePayConfigFeign tenantOnlinePayConfigFeign;

    /**
     * 租户打开线上支付
     */
    @RequiresPermissions("payment:online_pay")
    @PostMapping("/paymentConfig/onlinePay/open")
    public TenantOnlinePayDto openOnlinePay(
        @RequestBody @Validated TenantOnlinePayParam param
    ) {
        long tenantId = ISecurityUtils.getTenantId();
        long userId = ISecurityUtils.getCurrUserId();
        if (tenantId == 1L) {
            throw new BusinessException("SDS请去SaaS配置");
        }

        param.setTenantId(tenantId);
        param.setOperateUserId(userId);
        return tenantOnlinePayConfigFeign.openOnlinePay(param);
    }
    /**
     * 租户打开线上支付
     */
    @RequiresPermissions("payment:online_pay")
    @PutMapping("/paymentConfig/onlinePayConfig")
    @ApiOperation(value = "修改在线支付-支付宝或拉卡拉")
    public Object updateOnlinePayConfig(
            @RequestBody TenantOnlinePayConfParam param
    ) {
        long tenantId = ISecurityUtils.getTenantId();
        long userId = ISecurityUtils.getCurrUserId();
        if (tenantId == 1L) {
            throw new BusinessException("SDS请去SaaS配置");
        }

        param.setTenantId(tenantId);
        param.setOperateUserId(userId);
        param.setOperatorUid(userId);
        return tenantOnlinePayConfigFeign.updateOnlinePayConfig(param);
    }
    /**
     * 租户打开线上支付
     */
    @RequiresPermissions("payment:online_pay")
    @PutMapping("/paymentConfig/onlineBankCardConfig")
    @ApiOperation(value = "修改在线支付-银行卡")
    public Object updateBankCardConfig(
            @RequestBody TenantBankCardParam param
    ) {
        long tenantId = ISecurityUtils.getTenantId();
        long userId = ISecurityUtils.getCurrUserId();
        if (tenantId == 1L) {
            throw new BusinessException("SDS请去SaaS配置");
        }

        param.setTenantId(tenantId);
        param.setOperateUserId(userId);
        param.setOperatorUid(userId);
        return tenantOnlinePayConfigFeign.updateBandCard(param);
    }

    /**
     * 租户打开线上支付
     */
    @RequiresPermissions("payment:online_pay")
    @PutMapping("/paymentConfig/freePayMerchantsConfig")
    @ApiOperation(value = "修改在线支付-免支付商户")
    public Object updateBankCardConfig(
            @RequestBody TenantOnlinePayParam param
    ) {
        long tenantId = ISecurityUtils.getTenantId();
        long userId = ISecurityUtils.getCurrUserId();
        if (tenantId == 1L) {
            throw new BusinessException("SDS请去SaaS配置");
        }

        param.setTenantId(tenantId);
        param.setOperateUserId(userId);
        return tenantOnlinePayConfigFeign.updateFreeMerchants(param);
    }


    /**
     * 租户获取线上支付配置
     */
    @GetMapping("/paymentConfig/onlinePay")
    @ApiOperation(value = "在线支付配置")
    public TenantOnlinePayDto getConfig(@RequestParam(required = false) Long supTenantId) {
        long tenantId = ISecurityUtils.getTenantId();
        boolean getSupTenantConf = NumberUtils.greaterZero(supTenantId);
        if (getSupTenantConf) {
            tenantId = supTenantId;
        } else {
            boolean havaPermission = ISecurityUtils.checkPermission("payment:online_pay");
            if (!havaPermission) {
                throw new BusinessException("您没有该菜单权限，请联系管理员");
            }
        }
        TenantOnlinePayDto tenantOnlinePayDto = tenantOnlinePayConfigFeign.get(tenantId);
        if (getSupTenantConf) {
            tenantOnlinePayDto.setAlipayConf(null);
            tenantOnlinePayDto.setBankCard(null);
            tenantOnlinePayDto.setLakalaConfs(null);
        }

        log.info("get online pay {}",tenantOnlinePayDto);
        return tenantOnlinePayDto;
    }

    /**
     * 获取全部商户名字
     *
     * @return
     */
    @GetMapping("/paymentConfig/onlinePay/totalMerchantName")
    public BaseListDto<FreePayMerchantNameDto> getTotalMerchantName() {
        long tenantId = ISecurityUtils.getTenantId();
        List<FreePayMerchantNameDto> dtos = tenantOnlinePayConfigFeign.getTotalMerchantName(tenantId);
        BaseListDto baseListDto = new BaseListDto();
        baseListDto.setList(dtos);
        return baseListDto;
    }
    
    @PostMapping("/validateMerchantNo")
    @RequiresPermissions("payment:online_pay")
    @ApiOperation(value = "验证商户账号有效性")
    public String validateMerchantNo(@RequestBody TenantOnlinePayConfParam param){
        return tenantOnlinePayConfigFeign.validateMerchantNo(param,ISecurityUtils.getTenantId());
    }
}
