package com.ziguang.sds.aggr.pod.controller.merchantstore;

import com.ps.ps.service.MerchantStoreService;
import com.ziguang.base.dto.StoreTreeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "pod端查商户店铺")
@RestController
@RequestMapping("/merchantStore")
@RequiredArgsConstructor
public class MerchantStoreController {
    private final MerchantStoreService merchantStoreService;

    @ApiOperation("店铺tree")
    @GetMapping("/storeTree")
    public List<StoreTreeDto> storeTree(@RequestParam("merchantId") Long merchantId,
                                        @RequestParam(value = "platformCode", required = false) String platformCode) {

        return this.merchantStoreService.storeTree(null, null, 0, 0, platformCode,
                null, null, null, null, merchantId);
    }
}
