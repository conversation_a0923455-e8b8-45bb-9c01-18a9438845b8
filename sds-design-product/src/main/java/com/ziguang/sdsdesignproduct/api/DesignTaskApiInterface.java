/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */


package com.ziguang.sdsdesignproduct.api;

import com.ziguang.base.support.ListResponse;
import com.ziguang.base.dto.DesignTaskDTO;
import com.ziguang.base.dto.DesignTaskResearchRequest;
import com.ziguang.base.dto.DesignTaskSaveRequest;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;


public interface DesignTaskApiInterface {
    @RequestLine("POST /design_tasks")
    @Headers("Content-Type: application/json")
    DesignTaskDTO save(DesignTaskSaveRequest designTaskSaveRequest) throws Exception ;
    @RequestLine("POST /design_tasks/save_one")
    @Headers("Content-Type: application/json")
    DesignTaskDTO saveOne(DesignTaskSaveRequest designTaskSaveRequest) throws Exception ;

    @RequestLine("POST /design_tasks/list")
    @Headers("Content-Type: application/json")
    ListResponse list(@RequestBody DesignTaskResearchRequest designTaskResearchRequest)  ;

    @RequestLine("DELETE /design_tasks/{id}")
    @Headers("Content-Type: application/json")
    boolean delete(@Param("id") Long id);


}

