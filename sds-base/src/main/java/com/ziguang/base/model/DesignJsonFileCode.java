/*
 * Powered By [rapid-framework]
 * Web Site: http://www.rapid-framework.org.cn
 * Google Code: http://code.google.com/p/rapid-framework/
 * Since 2008 - 2019
 */

package com.ziguang.base.model;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "`design_json_file_code`")
@Data
public class DesignJsonFileCode {

    @Id
    @GeneratedValue(generator = "JDBC")
    Long id;
    String status;
    Long createTime;
    Integer sort;
    Integer failedTime;
    String designJson;
    String fileCode;
    String md5;

}
