package com.sdsdiy.common.base.constant;

import com.google.common.collect.Lists;
import com.sdsdiy.common.base.exception.BusinessException;

import java.util.List;

/**
 * <AUTHOR>
 */
public class BasePoConstant {

    /**
     * 非必填id Long 类型
     */
    public final static Long LONG_ZERO = 0L;
    public final static Long LONG_ONE = 1L;
    public static final Integer DEFAULT_SIZE = 10;
    /**
     * 非必填类别 Integer 类型
     */
    public final static Integer INTEGER_ZERO = 0;
    public final static Integer INTEGER_ONE = 1;

    /**
     * 是否删除  是
     **/
    public static final Integer IS_DELETE_YES = 1;

    /**
     * 是否删除  否
     **/
    public static final Integer IS_DELETE_NO = 0;

    /**
     * 删除状态
     */
    public static final Integer DELETE_STATUS = 99;

    /**
     * 系统用户ID  0
     **/
    public static final Long SYSTEM_UID = 0L;
    /**
     * 不可能id
     */
    public static final Long IMPOSSIBLE_ID = -1L;
    public static final List<Long> IMPOSSIBLE_LIST = Lists.newArrayList(-1L);

    /**
     * 是否开启 是
     **/
    public static final Integer IS_ENABLED_YES = 1;

    /**
     * 是否开启  否
     **/
    public static final Integer IS_ENABLED_NO = 0;

    /**
     * 是
     **/
    public static final Integer YES = 1;

    /**
     * 否
     **/
    public static final Integer NO = 0;

    /**
     * 是
     **/
    public static final String YES_CN = "是";

    /**
     * 否
     **/
    public static final String NO_CN = "否";

    /**
     * 开启
     **/
    public static final Integer OPEN = 1;

    /**
     * 关闭
     **/
    public static final Integer CLOSE = 0;
    public static final Integer CLOSE_2 = 2;

    /**
     * 最上级id
     */
    public static final Long TOP_LEVEL_ID = 0L;

    public static final Integer ES_MAX_QUERY = 1000000;

    public static final String YES_STRING = "yes";
    public static final String NO_STRING = "no";
    public static final String HIDE_STRING = "hide";

    public static final String YES_SIMPLE_STRING = "Y";
    public static final String NO_SIMPLE_STRING = "N";
    public static final String FAILED_SIMPLE_STRING = "FAILED";
    public static final String DOING_SIMPLE_STRING = "doing";
    public static final String SUCCESS_SIMPLE_STRING = "SUCCESS";

    public static final String TRUE_STRING = "true";
    /**
     * 开启
     */
    public static final String STATUS_OPEN = "open";
    /**
     * 关闭
     */
    public static final String STATUS_CLOSE = "close";

    /**
     * 增加
     */
    public static final String ADD = "add";
    /**
     * 减少
     */
    public static final String CUT = "cut";

    /**
     * 开启
     */
    public static final String STATUS_OPEN_2 = "OPEN";
    /**
     * 关闭
     */
    public static final String STATUS_CLOSE_2 = "CLOSE";
    /**
     * 删除
     */
    public static final String STATUS_DELETE = "delete";
    /**
     * 正常
     */
    public static final String STATUS_NORMAL = "normal";
    public static final String MUST_STR = "must";
    public static final String OPTIONAL_STR = "optional";
    /**
     * 取消中
     */
    public static final String STATUS_IN_CANCEL = "in_cancel";
    /**
     * 进行中
     */
    public static final String STATUS_PROCESSING = "PROCESSING";
    /**
     * 完成
     */
    public static final String STATUS_COMPLETED = "COMPLETED";
    /**
     * 无法完成
     */
    public static final String STATUS_NO_COMPLETED = "NO_COMPLETED";
    /**
     * 失败
     */
    public static final String STATUS_FAILED = "FAILED";
    public static final String STATUS_FAIL = "FAIL";


    public static final Integer ONLINE = 1;
    public static final Integer OFFLINE = 0;

    public static void checkStatus(String status) {
        if (!STATUS_OPEN.equals(status) && !STATUS_CLOSE.equals(status)) {
            throw new BusinessException("状态值异常");
        }
    }

    public static Integer yesOrNo(Boolean yes) {
        return yes != null && yes ? YES : NO;
    }

    public static String yesOrNoCn(Boolean yes) {
        return yes != null && yes ? YES_CN : NO_CN;
    }

    public static boolean yes(Integer yes) {
        return YES.equals(yes);
    }
}
