package com.sdsdiy.common.base.enums;

import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;

/**
 * 商户类型
 *
 * <AUTHOR>
 * @date 2021/7/30
 */
public enum MerchantTypeEnum {
    //
    NONE("", ""),
    PLATFORM("platform", "平台商户/sds商户"),
    AGENT("agent", "独立部署/租户"),
    ;
    public final String type;
    public final String desc;

    MerchantTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public boolean equals(String type) {
        return this.type.equalsIgnoreCase(type);
    }

    public static String getType(Long tenantId) {
        return TenantCommonConstant.SDSDIY_TENANT_ID.equals(tenantId)
                ? MerchantTypeEnum.PLATFORM.type : MerchantTypeEnum.AGENT.type;
    }
}
