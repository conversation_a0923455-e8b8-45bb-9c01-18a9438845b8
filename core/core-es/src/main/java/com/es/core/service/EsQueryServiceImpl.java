package com.es.core.service;


import cn.hutool.core.util.StrUtil;
import com.es.core.ScrollHandler;
import com.es.core.chain.EsChainQueryWrapper;
import com.es.core.wrapper.EsQueryWrapper;
import com.es.core.wrapper.EsWrapper;
import com.es.pojo.EsResponse;
import com.es.pojo.PageInfo;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 只做查询，屏蔽掉修改功能
 *
 * @Author: hzh
 * @Date: 2022/1/21 11:10
 */
@SuppressWarnings({"unchecked"})
public class EsQueryServiceImpl<T> extends AbstractEsService<T> implements EsService<T> {

    @Override
    public EsQueryWrapper<T> esQueryWrapper() {
        return new EsQueryWrapper<>(this.clazz);
    }

    @Override
    public EsChainQueryWrapper<T> esChainQueryWrapper() {
        return new EsChainQueryWrapper<>(this);
    }

    @Override
    public void createMapping() {

    }

    @Override
    public boolean save(T entity) {
        return false;
    }

    @Override
    public boolean saveOrUpdate(T entity) {
        return false;
    }

    @Override
    public void saveBatch(Collection<T> entityList) {

    }

    @Override
    public void saveBatch(Collection<T> entityList, int batchSize) {

    }


    /**
     * 根据 ID 删除
     *
     * @param id 主键ID
     */
    @Override
    public boolean removeById(Serializable id) {
        return false;
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        return false;
    }

    @Override
    public boolean remove(EsWrapper<T> esUpdateWrapper) {
        return false;
    }

    @Override
    public boolean removeAll() {
        return false;
    }

    /**
     * 根据 ID 选择修改
     *
     * @param entity 实体对象
     */
    @Override
    public boolean updateById(T entity) {
        return false;
    }

    @Override
    public boolean updateBatch(Collection<T> entityList) {
        return false;
    }

    /**
     * 根据ID 批量更新
     *
     * @param entityList 实体对象集合
     * @param batchSize  更新批次数量
     */
    @Override
    public boolean updateBatch(Collection<T> entityList, int batchSize) {
        return false;
    }

    @Override
    public void updateByWrapper(EsWrapper<T> esUpdateWrapper) {
    }

    @Override
    public void deleteIndex() {
    }

    /**
     * 根据 ID 查询
     *
     * @param id 主键ID
     */
    @Override
    public T getById(Serializable id) {
        List<String> ids = Collections.singletonList(String.valueOf(id));
        EsQueryWrapper<T> esQueryWrapper = new EsQueryWrapper<>();
        esQueryWrapper.ids(ids);
        //查询
        EsResponse<T> esResponse = this.esExecutor.searchByWrapper(esQueryWrapper, this.clazz, this.index);
        List<T> list = esResponse.getList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list.get(0);
        }
    }

    /**
     * 查询（根据ID 批量查询）
     *
     * @param idList 主键ID列表
     */
    @Override
    public List<T> listByIds(Collection<? extends Serializable> idList) {
        List<String> idStr = idList.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
        if (idStr.isEmpty()) {
            return Collections.emptyList();
        }
        EsQueryWrapper<T> esQueryWrapper = new EsQueryWrapper<>();
        esQueryWrapper.ids(idStr);
        //查询
        return this.esExecutor.searchByWrapper(esQueryWrapper, this.clazz, this.index).getList();
    }

    @Override
    public EsResponse<T> list(EsQueryWrapper<T> esQueryWrapper) {
        //默认查询所有
        if (esQueryWrapper == null) {
            esQueryWrapper = this.matchAll();
        }
        return this.esExecutor.searchByWrapper(esQueryWrapper, this.clazz, this.index);
    }

    @Override
    public EsResponse<T> page(PageInfo<T> pageInfo, EsQueryWrapper<T> esQueryWrapper) {
        if (esQueryWrapper == null) {
            esQueryWrapper = this.matchAll();
        }
        return this.esExecutor.searchPageByWrapper(pageInfo, esQueryWrapper, this.clazz, this.index);
    }

    @Override
    public long count(EsQueryWrapper<T> esQueryWrapper) {
        if (esQueryWrapper == null) {
            esQueryWrapper = this.matchAll();
        }
        return this.esExecutor.count(esQueryWrapper, this.index);
    }


    @Override
    public void scroll(EsQueryWrapper<T> esQueryWrapper, int size, int keepTime, ScrollHandler<T> scrollHandler) {
        if (esQueryWrapper == null) {
            esQueryWrapper = this.matchAll();
        }

        this.esExecutor.scrollByWrapper(esQueryWrapper, this.clazz, this.index, size, keepTime, scrollHandler);
    }

    @Override
    public EsResponse<T> aggregation(EsQueryWrapper<T> esQueryWrapper) {
        return esExecutor.search(new PageInfo<>(1, 0), esQueryWrapper, clazz, index);
    }

    private EsQueryWrapper<T> matchAll() {
        EsQueryWrapper<T> esQueryWrapper = new EsQueryWrapper<>(super.clazz);
        esQueryWrapper.must().query(QueryBuilders.matchAllQuery());
        return esQueryWrapper;
    }

    @Override
    public Map<String, Map<String, Long>> groupByAndCount(EsChainQueryWrapper<T> esChainQueryWrapper, String esFields) {
        if (StrUtil.isBlank(esFields)) {
            return Collections.emptyMap();
        }
        String[] esFieldList = esFields.split(StrUtil.COMMA);
        for (String esField : esFieldList) {
            TermsAggregationBuilder builder = AggregationBuilders.terms(esField).field(esField).size(Integer.MAX_VALUE);
            esChainQueryWrapper.addAggregationBuilder(builder);
        }
        Aggregations aggregation = esChainQueryWrapper.aggregation();
        Map<String, Map<String, Long>> esFieldMap = new HashMap<>(esFieldList.length);
        for (String esField : esFieldList) {
            Terms terms = aggregation.get(esField);
            Map<String, Long> countMap = new HashMap<>(terms.getBuckets().size());
            for (Terms.Bucket bucket : terms.getBuckets()) {
                countMap.put(bucket.getKeyAsString(), bucket.getDocCount());
            }
            esFieldMap.put(esField, countMap);
        }
        return esFieldMap;
    }
}
