package com.ps.Listener;

import cn.hutool.core.text.CharSequenceUtil;
import com.google.common.collect.Lists;
import com.ps.Listener.Even.*;
import com.ps.ps.service.ConfigService;
import com.ps.support.utils.StringUtils;
import com.sdsdiy.common.consts.DingtalkRobotEnum;
import com.sdsdiy.common.dtoconvert.exception.ExceptionConstant;
import com.sdsdiy.coreconfig.util.DingDingUtil;
import com.sdsdiy.paymentapi.dto.PaymentDto;
import com.ziguang.base.model.Order;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Async
public class DingDingListener {
    @Autowired
    private ConfigService configService;

    @Value("${spring.profiles.active}")
    private String profiles;

    @EventListener
    @Async
    public void onEven(IndependentDomainEvent independentDomainEvent){
        String dingUrl = "https://oapi.dingtalk.com/robot/send?access_token=00a19267a80a4c87edfd355db84c6b84d40f607ba53fc02e6ad6bf3d1a34bf72";
        String content = "环境:" + configService.getEnv() + " 用户提交了域名" + independentDomainEvent.getIndependentDomain().getDomain();
        DingDingUtil.sendDDMessage(dingUrl,content);
    }

    @EventListener
    @Async
    public void onEven(LogisticsEvent logisticsEvent){
        String dingUrl = "https://oapi.dingtalk.com/robot/send?access_token=777cdd4fc4bf1fe3d68817fa2d6a2ee278734cfb672432dab05dc3a1030bdd61";
        String content = "预警 :环境:" + configService.getEnv() + "," + " 运单号生成失败,订单号:" + logisticsEvent.getOrderNo();
        if(configService.getEnv().equals("production")){
            DingDingUtil.sendDDMessage(dingUrl,content);
        }
    }

    @EventListener
    @Async
    public void onEven(PrototypeLayerChangeEvent prototypeLayerChangeEvent){
        String dingUrl = "https://oapi.dingtalk.com/robot/send?access_token=47e5f6cdc2d655c5b24761a269098bb756156d0f2c068b04415310380c27137c";
        String skuDesc = CollectionUtils.isEmpty(prototypeLayerChangeEvent.getProductSkus()) ? "无" : StringUtils.listToString(prototypeLayerChangeEvent.getProductSkus());
        String content = "模板图层发生变动，涉及的产品sku："  + skuDesc;
        if(configService.getEnv().equals("production")){
            DingDingUtil.sendDDMessage(dingUrl,content);
        }
    }

    @EventListener
    @Async
    public void onEven(ImportOrderErrorEvent importOrderErrorEvent){
        String dingUrl = "https://oapi.dingtalk.com/robot/send?access_token=f681f75e6aa820c0424e4acaa0af0574aac96ec53ce2e66bb467235e6a3b7e88";
        String content = "预警 :环境:" + configService.getEnv() + "," + " 第三方订单号:" + importOrderErrorEvent.getOrderNo() + "导入失败," + importOrderErrorEvent.getMsg();
        DingDingUtil.sendDDMessage(dingUrl,content);

    }
    @EventListener
    @Async
    public void onEven(OrderDesignErrorEvent orderDesignErrorEvent){
        Order order = orderDesignErrorEvent.getOrder();
        String dingUrl = "https://oapi.dingtalk.com/robot/send?access_token=f681f75e6aa820c0424e4acaa0af0574aac96ec53ce2e66bb467235e6a3b7e88";
        String content = "预警 :环境:" + configService.getEnv() +"order_no" +order.getNo() + ",orderID" + order.getId() +" 合图失败 :"  + orderDesignErrorEvent.getMsg();
        DingDingUtil.sendDDMessage(dingUrl, content, Lists.newArrayList("18850568902"), false);
    }

    @Async
    @EventListener
    public void productionManuscriptTaskFailedNotify(ProductionManuscriptEvent productionManuscriptEvent){
        String dingUrl = "https://oapi.dingtalk.com/robot/send?access_token=f681f75e6aa820c0424e4acaa0af0574aac96ec53ce2e66bb467235e6a3b7e88";
        String content = "预警 :环境:" + configService.getEnv() +"order_item_no:" +productionManuscriptEvent.getNo()  +" 生产稿件生成失败 :"  + productionManuscriptEvent.getTime();
        DingDingUtil.sendDDMessage(dingUrl,content);
    }

    public void factoryOrderMultiNotify(String msg){
        String dingUrl = "https://oapi.dingtalk.com/robot/send?access_token=f681f75e6aa820c0424e4acaa0af0574aac96ec53ce2e66bb467235e6a3b7e88";
        String content = "预警 :环境:" + configService.getEnv() + " " + msg;
        DingDingUtil.sendDDMessage(dingUrl, content, Lists.newArrayList("18850568902"), false);
    }

    public void sendExceptionToDingTalk(String title, PaymentDto paymentDto, Exception e) {
        String errorMessage = ExceptionConstant.getRootCauseMessageIfSeata(e);

        String stringBuilder = "环境: " + this.profiles + "\n" +
            "标题: " + title + "\n" +
            "参数：" + paymentDto.getMethod() + ", paymentId:" + paymentDto.getId() + "\n" +
            "异常：" + errorMessage;
        if (CharSequenceUtil.isNotEmpty(this.profiles) && (this.profiles.contains("test") || this.profiles.contains("local"))) {
            DingDingUtil.sendDDMessage(DingtalkRobotEnum.PAYMENT_ERROR.getWebhookUrl(), stringBuilder, null, false);
        } else {
            DingDingUtil.sendDDMessage(DingtalkRobotEnum.PAYMENT_ERROR.getWebhookUrl(), stringBuilder, Lists.newArrayList("18850568902"), false);
        }
    }

}
