package com.ps.ps.service.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ps.bo.order.OrderPaymentCheckBO;
import com.ps.dto.MerchantBalanceDto;
import com.ps.dto.OrderItemProductFactoryDto;
import com.ps.exception.BusinessException;
import com.ps.ps.feign.OrderPreviewFeign;
import com.ps.ps.feign.ProductFeign;
import com.ps.ps.feign.designproduct.DesignProductMCFeign;
import com.ps.ps.feign.logistics.TenantLogisticsFeign;
import com.ps.ps.feign.merchantBlacklist.MerchantBlacklistFeign;
import com.ps.ps.feign.order.*;
import com.ps.ps.feign.payment.TenantDistributionWalletFeign;
import com.ps.ps.feign.platformorder.OnlineOrderFeign;
import com.ps.ps.feign.product.MerchantAuthProductParentFeign;
import com.ps.ps.feign.product.ProductDistributionAuthFactoryFeign;
import com.ps.ps.feign.tenant.TenantFeign;
import com.ps.ps.feign.user.MerchantStoreAuthTokenFeign;
import com.ps.ps.feign.user.OnlineLogisticsWarehouseFeign;
import com.ps.ps.service.*;
import com.ps.ps.service.cache.ProductCacheService;
import com.ps.support.Assert;
import com.ps.support.MapListUtil;
import com.ps.support.OrderPaymentCheckRespDTO;
import com.ps.support.utils.ConvertUtil;
import com.ps.support.utils.StringUtils;
import com.ps.util.EndProductUtil;
import com.sds.platform.sdk.temu.constant.TemuOrderTypeEnum;
import com.sdsdiy.common.base.constant.BasePoConstant;
import com.sdsdiy.common.base.constant.MerchantStoreAuthStatusEnum;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseDTO;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.enums.EuropeUnionEnum;
import com.sdsdiy.common.base.enums.MerchantStoreAuthTypeEnum;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.enums.onlineorder.OnlineOrderStatusMergedEnum;
import com.sdsdiy.common.base.enums.onlineorder.OnlineOrderStatusUtil;
import com.sdsdiy.common.base.enums.onlineorder.TemuOrderStatusEnum;
import com.sdsdiy.common.base.helper.IdsSearchHelper;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.core.mq.core.RocketMQTemplate;
import com.sdsdiy.core.mq.queue.RocketMqTopicConst;
import com.sdsdiy.designproductapi.contant.DesignProductFitLevelConstant;
import com.sdsdiy.designproductapi.contant.OfficialDesignProductConstant;
import com.sdsdiy.designproductapi.dto.DesignProductReqDto;
import com.sdsdiy.designproductapi.dto.base.DesignProductRespDto;
import com.sdsdiy.designproductdata.constant.DesignProductTypeConstant;
import com.sdsdiy.logisticsapi.constant.ServiceProviderConstant;
import com.sdsdiy.logisticsapi.dto.base.LogisticsChannelRespDto;
import com.sdsdiy.logisticsapi.dto.base.TenantAddressRespDto;
import com.sdsdiy.logisticsapi.dto.base.TenantLogisticsRespDto;
import com.sdsdiy.logisticsapi.dto.tenantlogistics.LogisticsAndLogisticsSourceResp;
import com.sdsdiy.logisticsapi.enums.LogisticsServiceProviderEnum;
import com.sdsdiy.logisticsapi.enums.TaxNumberTypeEnum;
import com.sdsdiy.orderapi.constant.*;
import com.sdsdiy.orderapi.constant.event.OrderEventRefreshConstant;
import com.sdsdiy.orderapi.constant.orderitem.OrderItemCustomizeDesignProductRelResultEnum;
import com.sdsdiy.orderapi.dto.OrderAmountRespDTO;
import com.sdsdiy.orderapi.dto.base.blacklist.MerchantBatchDetectDto;
import com.sdsdiy.orderapi.dto.orderitem.OrderItemCustomizeDesignProductRelRespDto;
import com.sdsdiy.orderapi.dto.orderitem.OrderItemFactoryRespDto;
import com.sdsdiy.orderdata.constant.order.OrderExtendValueEnum;
import com.sdsdiy.orderdata.constant.order.PlatformOrderExtendValueEnum;
import com.sdsdiy.orderdata.constant.order.PlatformOrderItemExtendValueEnum;
import com.sdsdiy.orderdata.constant.order.SupplyChainTypeEnum;
import com.sdsdiy.orderdata.dto.*;
import com.sdsdiy.orderdata.dto.create.OrderAmountCreateDTO;
import com.sdsdiy.orderdata.dto.msg.OrderRefreshMsg;
import com.sdsdiy.orderdata.dto.onlinePlatform.PlatformOrderDto;
import com.sdsdiy.orderdata.dto.onlinePlatform.PlatformOrderItemDto;
import com.sdsdiy.orderdata.dto.order.OrderItemSupplyChainDTO;
import com.sdsdiy.orderdata.dto.order.update.TemuOnlineLogisticsCheckDto;
import com.sdsdiy.orderdata.dto.outplatform.PlatformOrderExtendReqDTO;
import com.sdsdiy.orderdata.dto.outplatform.PlatformOrderItemExtendReqDTO;
import com.sdsdiy.orderdata.enums.OrderStatus;
import com.sdsdiy.paymentapi.dto.TenantDistributorWalletDto;
import com.sdsdiy.paymentapi.param.TenantDistributionQueryParam;
import com.sdsdiy.productapi.dto.product.ProductOnlineSupplyChainsDto;
import com.sdsdiy.productdata.dto.auth.MerchantAuthProductParentRespDTO;
import com.sdsdiy.productdata.dto.auth.ProductSmallMinNumDto;
import com.sdsdiy.productdata.dto.distribution.DistributionFactoryProductionDayLimitDTO;
import com.sdsdiy.userapi.dto.tenant.TenantRespDto;
import com.sdsdiy.userdata.dto.MerchantStoreAuthTokenDto;
import com.sdsdiy.userdata.dto.TemuOnlineWarehouseCheckDTO;
import com.sdsdiy.userdata.param.TemuOnlineWarehouseCheckParam;
import com.ziguang.base.dto.GetBalanceBo;
import com.ziguang.base.dto.ProductHolidayDto;
import com.ziguang.base.model.*;
import com.ziguang.base.support.contant.CommonStatus;
import com.ziguang.base.support.contant.EndProductType;
import com.ziguang.base.support.contant.OrderOriginType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentCheckService {
    private final PlatformOrderItemExtendFeign platformOrderItemExtendFeign;
    private final PlatformOrderExtendFeign platformOrderExtendFeign;
    @Autowired
    private MerchantBlacklistFeign merchantBlacklistFeign;
    @Resource
    OnlineOrderFeign onlineOrderFeign;

    @Resource
    ProductCacheService productCacheService;

    @Resource
    OrderAmountService orderAmountService;

    @Resource
    OrderService orderService;

    @Resource
    LogisticsService logisticsService;
    @Resource
    ProductService productService;

    @Resource
    DesignProductMCFeign designProductMCFeign;

    @Resource
    EndProductService endProductService;

    @Resource
    OrderPreviewFeign orderPreviewFeign;

    @Resource
    OrderLogisticsUpdateFeign orderLogisticsUpdateFeign;


    @Resource
    OrderImportExtraInfoFeign orderImportExtraInfoFeign;
    @Resource
    TenantLogisticsOrderFeign tenantLogisticsOrderFeign;
    @Resource
    private OrderItemSupplyChainFeign orderItemSupplyChainFeign;

    @Resource
    private OrderTailLogisticsFeign orderTailLogisticsFeign;
    @Resource
    private MerchantAuthProductParentFeign merchantAuthProductParentFeign;
    @Resource
    private ProductFeign productFeign;
    @Resource
    private OrderExtendInfoFeign orderExtendInfoFeign;
    @Resource
    private MerchantStoreService merchantStoreService;
    @Resource
    private OrderUpdateCheckFeign orderUpdateCheckFeign;
    @Resource
    private OnlineLogisticsWarehouseFeign onlineLogisticsWarehouseFeign;
    @Resource
    private SupplyService supplyService;
    @Resource
    private RocketMQTemplate rocketMQTemplate;
    @Resource
    private MerchantStoreAuthTokenFeign merchantStoreAuthTokenFeign;
    @Resource
    private OrderItemCustomizeDesignProductRelFeign orderItemCustomizeDesignProductRelFeign;
    @Resource
    private TenantDistributionWalletFeign tenantDistributionWalletFeign;
    @Resource
    private TenantFeign tenantFeign;
    @Resource
    private ProductDistributionAuthFactoryFeign productDistributionAuthFactoryFeign;

    public OrderPaymentCheckBO buildOrderPaymentCheckBO(Long merchantId, Long userId, List<Long> orderIds) {
        GetBalanceBo getBalanceBo = new GetBalanceBo(merchantId, userId, orderIds, false);
        MerchantBalanceDto merchantBalanceDto = this.orderService.getBalance(getBalanceBo);
        Long tenantId = merchantBalanceDto.getTenantId();
        List<Order> orders = merchantBalanceDto.getOrders();
        List<Order> autoImportOrders = orders.stream().filter(i -> com.sdsdiy.orderapi.constant.OrderOriginType.isAutoImport(i.getOriginType())).collect(Collectors.toList());

        Map<Long, Map<String, String>> extendInfoMap = this.orderExtendInfoFeign.mapsByOrderIds(BaseListDto.of(orderIds));

        List<String> platformCodes = ListUtil.toValueDistinctList(autoImportOrders, Order::getMerchantStorePlatformCode
                , i -> StrUtil.isNotBlank(i.getMerchantStorePlatformCode()));
        List<String> outOrderNos = ListUtil.toValueDistinctList(autoImportOrders, Order::getOutOrderNo
                , i -> StrUtil.isNotBlank(i.getOutOrderNo()));
        Map<String, Map<String, Map<String, String>>> platformOrderItemExtendMap = this.platformOrderItemExtendFeign
                .mapByPlatformCodesAndOutIds(new PlatformOrderItemExtendReqDTO()
                        .setPlatformCodes(platformCodes).setOutIds(outOrderNos)
                        .setOriginType(com.sdsdiy.orderapi.constant.OrderOriginType.AUTO_IMPORT.getCode()));

        Map<String, Map<String, Map<String, String>>> platformOrderExtendMap = this.platformOrderExtendFeign
                .mapByOutIds(new PlatformOrderExtendReqDTO()
                        .setPlatformCodes(platformCodes).setOutIds(outOrderNos).setOriginType(com.sdsdiy.orderapi.constant.OrderOriginType.AUTO_IMPORT.getCode()));
        List<Long> merchantStoreIds = orders.stream().map(Order::getMerchantStoreId).filter(NumberUtils::greaterZero).distinct().collect(Collectors.toList());
        List<MerchantStoreAuthTokenDto> storeAuthTokenDtos = this.merchantStoreAuthTokenFeign.findAllByMerchantStoreIds(IdsSearchHelper.of(merchantStoreIds));
        Map<Long, List<MerchantStoreAuthTokenDto>> storeIdStoreAuthDtoMap = ListUtil.toMapValueList(MerchantStoreAuthTokenDto::getMerchantStoreId, storeAuthTokenDtos);

        List<MerchantStore> merchantStores = merchantStoreService.findByIds(merchantStoreIds);
        Map<Long, MerchantStore> merchantStoreMap = ListUtil.toMap(MerchantStore::getId, merchantStores);

        Map<String, PlatformOrderDto> outOrderNoPlatformOrderMap = getOutOrderNoPlatformOrderMap(orders, extendInfoMap);
        Map<Long, OrderAmountRespDTO> amountMaps = this.getAmountMaps(orders);
        Map<Long, TenantRespDto> productTenantMap = getProductTenantMap(amountMaps);
        // 租户id不等于产品租户id,获取租户再产品租户的授信钱包
        Map<Long, TenantDistributorWalletDto> supTenantIdWalletMap = getSupTenantIdWalletMap(merchantBalanceDto.getTenantId(), amountMaps);
        List<Long> productIds = orders.stream().flatMap(i -> i.getItems().stream()).filter(i->NumberUtils.greaterZero(i.getProductId())).map(i -> i.getProductId()).distinct().collect(Collectors.toList());
        List<Product> products = productCacheService.findByIds(productIds);
        // 租户授权工厂
        List<Long> productParentIds = products.stream().filter(i->NumberUtils.greaterZero(i.getParentId())).map(i->i.getParentId()).distinct().collect(Collectors.toList());
        Map<Long, List<DistributionFactoryProductionDayLimitDTO>> productParentIdTenantAuthFactoryLimitMap = productDistributionAuthFactoryFeign.mapAuthFactoryByParentIds(
            tenantId, BaseListDto.of(productParentIds));

        OrderPaymentCheckBO checkBO = new OrderPaymentCheckBO();
        checkBO.setMerchantId(merchantId).setUserId(userId)
                .setOrderIds(orderIds)
                .setOrders(orders)
                .setMerchantBalance(merchantBalanceDto)
                .setExtendInfoMap(extendInfoMap)
                .setPlatformOrderItemExtendMap(platformOrderItemExtendMap)
                .setPlatformOrderExtendMap(platformOrderExtendMap)
                .setStoreIdStoreAuthDtoMap(storeIdStoreAuthDtoMap)
                .setMerchantStoreMap(merchantStoreMap)
                .setOutOrderNoPlatformOrderMap(outOrderNoPlatformOrderMap)
                .setOrderAmmountMaps(amountMaps)
                .setProductTenantMap(productTenantMap)
                .setSupTenantIdWalletMap(supTenantIdWalletMap)
                .setProducts(products)
                .setProductParentIdTenantAuthFactoryLimitMap(productParentIdTenantAuthFactoryLimitMap)
                .setCheckInfoListMap(new HashMap<>(orderIds.size()));
        return checkBO;
    }

    private Map<String, PlatformOrderDto> getOutOrderNoPlatformOrderMap(List<Order> orders, Map<Long, Map<String, String>> extendInfoMap) {
        List<Order> temuAutoImportOrders = orders.stream()
            .filter(i -> com.sdsdiy.orderapi.constant.OrderOriginType.isAutoImport(i.getOriginType())&&
                MerchantStorePlatformEnum.TEMU.getCode().equals(i.getMerchantStorePlatformCode()))
            .collect(Collectors.toList());
        List<Long> temuAutoImportSemiOrLocalOrderIds = temuAutoImportOrders.stream()
            .filter(i -> OrderExtendValueEnum.TEMU_SEMI_ORDER.isMatch(extendInfoMap.get(i.getId())) ||
            OrderExtendValueEnum.TEMU_LOCAL_ORDER.isMatch(extendInfoMap.get(i.getId())))
            .map(i -> i.getId())
            .collect(Collectors.toList());

        Map<String, PlatformOrderDto> outOrderNoPlatformOrderMap=Maps.newHashMap();
        if(CollUtil.isNotEmpty(temuAutoImportSemiOrLocalOrderIds)){
            outOrderNoPlatformOrderMap=orderPreviewFeign.getPlatformOrderDtoMap(temuAutoImportSemiOrLocalOrderIds);
        }
        return outOrderNoPlatformOrderMap;
    }

    /**
     * @param merchantId
     * @param orderIds
     */
    public OrderPaymentCheckRespDTO check(Long tenantId, Long merchantId, Long userId, List<Long> orderIds, boolean isFbaPaymentAuth) {
        OrderPaymentCheckBO checkBO = this.buildOrderPaymentCheckBO(merchantId, userId, orderIds);
        this.checkJitStore(checkBO);
        List<Order> orders = checkBO.getOrders();
        MapListUtil<Long, PaymentCheckInfo> enumPaymentCheckMapListUtil = MapListUtil.instance();
        Map<Long, Order> orderMap = Maps.newHashMap();

        Map<Long, MerchantAuthProductParentRespDTO> authProductParentMap = this.getAuthProductParentMap(merchantId, orders);
        Map<Long, DesignProductRespDto> designProductRespDtoMap = this.getDesignProductMaps(merchantId, orders);
        Map<Long, OnlineOrderRespDTO> onlineOrderMap = this.getOnlineOrderMap(orders);
        Map<Long, OrderImportExtraInfoDto> orderImportExtraMap = this.findOrderImportExtraMap(orders);
        Map<Long, Logistics> logisticsMap = this.findOrderLogisticsMap(orders);
        Map<Long, OrderItemCustomizeDesignProductRelRespDto> customDesignRelMap = this.findCustomDesignRel(orders);


        Map<String, ProductHolidayDto> variantIdAndSupplyChainTypeHolidayMap = this.getVariantIdAndSupplyChainTypeHolidayMaps(orders);
        Map<Long, OrderItemSupplyChainDTO> orderItemSupplyChainMap = this.getOrderItemSupplyChainMaps(orders);
        Map<Long, LogisticsChannelRespDto> tailLogisticsChannelIdByOrderMaps = this.getTailLogisticsChannelIdByOrderMaps(orders);

        Map<Long, OrderItemFactoryRespDto> orderItemFactoryRespDtoMap = this.getOrderItemFactoryRespDtoMap(orders);
        this.refreshOrderBlacklistType(merchantId, orderImportExtraMap, orders);
        Boolean checkOrdersQuota = this.checkOrdersQuotas(merchantId,tenantId,orders);
        List<TemuOnlineLogisticsCheckDto> checks = Lists.newArrayList();


        for (Order order : orders) {
            OrderImportExtraInfoDto orderImportExtraInfoDto = orderImportExtraMap.get(order.getId());
            PaymentCheckOneRespDto paymentCheckOneRespDto = this.checkOrder(tenantId, order, logisticsMap.get(order.getLogisticsId()), orderImportExtraInfoDto
                    , enumPaymentCheckMapListUtil, authProductParentMap, designProductRespDtoMap, onlineOrderMap
                    , isFbaPaymentAuth, checkBO, customDesignRelMap, variantIdAndSupplyChainTypeHolidayMap,
                    orderItemSupplyChainMap, tailLogisticsChannelIdByOrderMaps, orderItemFactoryRespDtoMap, checkOrdersQuota
                   );
            orderMap.put(order.getId(), order);
            if(paymentCheckOneRespDto != null && paymentCheckOneRespDto.getCheckDto() != null){
                checks.add(paymentCheckOneRespDto.getCheckDto());
            }
        }
        this.orderUpdateCheckFeign.temuOnlineLogisticsCheckBatchForMerchant(new BaseListDto<>(checks));

        List<PaymentCheckRespDto> respDtos = Lists.newArrayList();
        for (Long orderId : orderIds) {
            PaymentCheckRespDto paymentCheckRespDto = new PaymentCheckRespDto();
            Order order = orderMap.get(orderId);
            Assert.validateNull(order, "订单数据异常");
            paymentCheckRespDto.setId(orderId);
            paymentCheckRespDto.setOutOrderNo(order.getNo());
            List<PaymentCheckInfo> paymentCheckInfos = enumPaymentCheckMapListUtil.getList(orderId);
            EnumPaymentCheckErrorType enumPaymentCheckErrorType = null;
            StringBuilder msg = new StringBuilder();
            for (PaymentCheckInfo paymentCheckInfo : paymentCheckInfos) {
                EnumPaymentCheck enumPaymentCheck = paymentCheckInfo.getEnumPaymentCheck();
                if (enumPaymentCheckErrorType == null) {
                    enumPaymentCheckErrorType = enumPaymentCheck.getErrorType();
                }

                if (enumPaymentCheck.getErrorType() == EnumPaymentCheckErrorType.ERROR) {
                    //错误的优先级最高
                    enumPaymentCheckErrorType = EnumPaymentCheckErrorType.ERROR;
                }

                if (msg.length() != 0) {
                    msg.append(",");
                }
                msg.append(paymentCheckInfo.getMsg());

            }
            paymentCheckRespDto.setMsg(msg.toString());
            paymentCheckRespDto.setTotalAmount(order.getTotalAmount());
            paymentCheckRespDto.setWarnType(enumPaymentCheckErrorType == null ? null : enumPaymentCheckErrorType.getValue());
            respDtos.add(paymentCheckRespDto);
        }

        OrderPaymentCheckRespDTO listResponse = new OrderPaymentCheckRespDTO();
        listResponse.setItems(respDtos);
        listResponse.setPaymentAmount(checkBO.getMerchantBalance().getPaymentAmount());
        return listResponse;
    }

    private Map<Long, TenantRespDto> getProductTenantMap(Map<Long, OrderAmountRespDTO> amountMaps) {
        List<Long> productTenantIds = amountMaps.values().stream().map(i -> i.getProductTenantId()).distinct().collect(Collectors.toList());
        List<TenantRespDto> tenantRespDtos = tenantFeign.getByIds(productTenantIds);
        Map<Long, TenantRespDto> productTenantMap = ListUtil.toMap(BaseDTO::getId, tenantRespDtos);
        return productTenantMap;
    }

    private Map<Long, TenantDistributorWalletDto> getSupTenantIdWalletMap(Long tenantId, Map<Long, OrderAmountRespDTO> amountMaps) {
        List<Long> supplierTenantIds = amountMaps.values().stream().filter(i -> i.getIsDistribution()).map(i -> i.getProductTenantId()).distinct().collect(Collectors.toList());
        TenantDistributionQueryParam tenantDistributionQueryParam = new TenantDistributionQueryParam();
        tenantDistributionQueryParam.setSupTenantId(supplierTenantIds);
        tenantDistributionQueryParam.setDisTenantIds(Collections.singletonList(tenantId));
        List<TenantDistributorWalletDto> tenantDistributorWalletDtos = tenantDistributionWalletFeign.queryWallets(tenantDistributionQueryParam);
        Map<Long, TenantDistributorWalletDto> supTenantIdWalletMap = ListUtil.toMap(TenantDistributorWalletDto::getSupTenantId, tenantDistributorWalletDtos);
        return supTenantIdWalletMap;
    }

    private static boolean isDistribution(Long tenantId,Long productTenantId) {
        return NumberUtils.greaterZero(tenantId) && NumberUtils.greaterZero(productTenantId) && !tenantId.equals(productTenantId);
    }

    public Boolean checkOrdersQuotas(Long merchantId,Long tenantId,List<Order> orders){
        CheckOrderQuotaRequestParam param = new CheckOrderQuotaRequestParam();
        param.setTenantId(tenantId);
        param.setMerchantId(merchantId);
        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        param.setOrderIds(orderIds);
        param.setThrowException(false);
        return this.tenantLogisticsOrderFeign.checkOrdersQuotas(param);
    }

    private Map<Long, OrderItemCustomizeDesignProductRelRespDto> findCustomDesignRel(List<Order> orders) {
        List<Long> orderItemIds = new ArrayList<>();
        for (Order order : orders) {
            if (CollUtil.isNotEmpty(order.getItems())) {
                orderItemIds.addAll(order.getItems().stream().map(OrderItem::getId).collect(Collectors.toList()));
            }
        }
        orderItemIds = orderItemIds.stream().distinct().collect(Collectors.toList());;
        Map<Long, OrderItemCustomizeDesignProductRelRespDto> res = new HashMap<>();
        if (CollUtil.isNotEmpty(orderItemIds)) {
            List<OrderItemCustomizeDesignProductRelRespDto> relRespDtos = this.orderItemCustomizeDesignProductRelFeign.getByOrderItemIds(orderItemIds);
            return relRespDtos.stream().collect(Collectors.toMap(OrderItemCustomizeDesignProductRelRespDto::getOrderItemId, Function.identity(), (a, b) -> b));
        }
        return res;
    }

    /**
     * @param merchantId
     * @param orderId
     */
    public PaymentCheckOneRespDto checkOneOrder(Long tenantId, Long merchantId, Long userId, Long orderId, boolean isFbaPaymentAuth) {
        List<Long> orderIds = Lists.newArrayList(orderId);
        OrderPaymentCheckBO checkBO = this.buildOrderPaymentCheckBO(merchantId, userId, orderIds);
        this.checkJitStore(checkBO);
        List<Order> orders = checkBO.getOrders();
        MapListUtil<Long, PaymentCheckInfo> enumPaymentCheckMapListUtil = MapListUtil.instance();

        Map<Long, MerchantAuthProductParentRespDTO> authProductParentMap = this.getAuthProductParentMap(merchantId, orders);
        Map<Long, DesignProductRespDto> designProductRespDtoMap = this.getDesignProductMaps(merchantId, orders);
        Map<Long, OnlineOrderRespDTO> onlineOrderMap = this.getOnlineOrderMap(orders);
        Map<Long, OrderImportExtraInfoDto> orderImportExtraMap = this.findOrderImportExtraMap(orders);
        Map<Long, OrderItemCustomizeDesignProductRelRespDto> customDesignRelMap = this.findCustomDesignRel(orders);


        Map<String, ProductHolidayDto> variantIdAndSupplyChainTypeHolidayMap = this.getVariantIdAndSupplyChainTypeHolidayMaps(orders);
        Map<Long, OrderItemSupplyChainDTO> orderItemSupplyChainMap = this.getOrderItemSupplyChainMaps(orders);
        Map<Long, LogisticsChannelRespDto> tailLogisticsChannelCodeByOrderMap = this.getTailLogisticsChannelIdByOrderMaps(orders);
        Map<Long, OrderItemFactoryRespDto> orderItemFactoryRespDtoMap = this.getOrderItemFactoryRespDtoMap(orders);
        this.refreshOrderBlacklistType(merchantId, orderImportExtraMap, orders);

        Order order = orders.get(0);
        Logistics logistics = this.logisticsService.findById(order.getLogisticsId());
        Boolean checkOrdersQuota = this.checkOrdersQuotas(merchantId,tenantId,orders);
        PaymentCheckOneRespDto paymentCheckOneRespDto = this.checkOrder(tenantId, orders.get(0), logistics, orderImportExtraMap.get(orderId),
                enumPaymentCheckMapListUtil, authProductParentMap, designProductRespDtoMap, onlineOrderMap
                , isFbaPaymentAuth, checkBO, customDesignRelMap,variantIdAndSupplyChainTypeHolidayMap,orderItemSupplyChainMap
                ,tailLogisticsChannelCodeByOrderMap,orderItemFactoryRespDtoMap,checkOrdersQuota);
        if(paymentCheckOneRespDto != null && paymentCheckOneRespDto.getCheckDto() != null){
            this.orderUpdateCheckFeign.temuOnlineLogisticsCheckBatchForMerchant(new BaseListDto<>(Lists.newArrayList(paymentCheckOneRespDto.getCheckDto())));
        }
        return paymentCheckOneRespDto;

    }

    public void checkFactoryLimit(Order order, List<PaymentCheckInfo> enumPaymentChecks,
                                  OrderPaymentCheckBO checkBO,
                                  Map<Long, OrderItemSupplyChainDTO> orderItemIdChainValueMap,
                                  Map<Long, OrderItemFactoryRespDto> orderItemIdFactoryDtoMap) {
        if (null == order) {
            return;
        }
        List<Product> products = checkBO.getProducts();
        List<OrderItem> orderItems = order.getItems();
        MapListUtil<String, OrderItemProductFactoryDto> supplyChainTypeFactoryDtosMapList = formatSupplyChainTypeFactoryDtosMapList(orderItems, orderItemIdChainValueMap, orderItemIdFactoryDtoMap);
        Set<Long> reachLimitProductIds = this.getReachLimitProductIds(supplyChainTypeFactoryDtosMapList);
        if (CollUtil.isNotEmpty(reachLimitProductIds)) {
            List<Product> reachLimitProducts = products.stream().filter(i->reachLimitProductIds.contains(i.getId())).collect(Collectors.toList());

            // 报错
            StringJoiner sj = new StringJoiner(",");
            for (Product product : reachLimitProducts) {
                sj.add(product.getName() + product.getColorName() + product.getSize());
            }
            String msg = "无法下单，订单内的“" + sj + "”产品无供应关系或订单内产品履约工厂产能已达本日上限";
            enumPaymentChecks.add(PaymentCheckInfo.of(EnumPaymentCheck.PRODUCT_FACTORY_LIMIT, msg));
        }
    }
    public void checkTenantFactoryLimit(Boolean isDistribution,Order order,
                                        List<PaymentCheckInfo> enumPaymentChecks,
                                        OrderPaymentCheckBO checkBO,
                                        Map<Long, OrderItemSupplyChainDTO> orderItemIdChainValueMap,
                                        Map<Long, OrderItemFactoryRespDto> orderItemIdFactoryDtoMap) {
        if (null == order || !isDistribution) {
            return;
        }
        List<Product> products = checkBO.getProducts();
        Map<Long, List<DistributionFactoryProductionDayLimitDTO>> productParentIdTenantAuthFactoryLimitMap = checkBO.getProductParentIdTenantAuthFactoryLimitMap();
        List<OrderItem> orderItems = order.getItems();
        MapListUtil<String, OrderItemProductFactoryDto> supplyChainTypeFactoryDtosMapList = formatSupplyChainTypeFactoryDtosMapList(orderItems, orderItemIdChainValueMap, orderItemIdFactoryDtoMap);
        List<Long> reachLimitProductIds = this.getTenantReachLimitProductIds(productParentIdTenantAuthFactoryLimitMap,products,supplyChainTypeFactoryDtosMapList);
        if (CollUtil.isNotEmpty(reachLimitProductIds)) {
            List<Product> reachLimitProducts = products.stream().filter(i->reachLimitProductIds.contains(i.getId())).collect(Collectors.toList());
            // 报错
            StringJoiner sj = new StringJoiner(",");
            for (Product product : reachLimitProducts) {
                sj.add(product.getName() + product.getColorName() + product.getSize());
            }
            String msg = "无法下单，订单内的“" + sj + "”产品无供应关系或订单内产品履约工厂产能已达本日上限";
            enumPaymentChecks.add(PaymentCheckInfo.of(EnumPaymentCheck.PRODUCT_FACTORY_LIMIT, msg));
        }
    }
    public void checkTenantDistributorWallet(Order order, OrderAmountRespDTO orderAmountRespDTO,
                                             List<PaymentCheckInfo> enumPaymentChecks,
                                             Map<Long, TenantDistributorWalletDto> supTenantIdWalletMap,
                                             Map<Long, TenantRespDto> productTenantMap) {
        if (null == order) {
            return;
        }
        if(!orderAmountRespDTO.getIsDistribution()){
            return;
        }
        Long productTenantId = orderAmountRespDTO.getProductTenantId();
        TenantDistributorWalletDto tenantDistributorWalletDto = supTenantIdWalletMap.get(productTenantId);
        boolean moneyNotEnough = null == tenantDistributorWalletDto || NumberUtil.isLessOrEqual(tenantDistributorWalletDto.getBalance(), BigDecimal.ZERO);
        if (moneyNotEnough) {
            TenantRespDto tenantRespDto = productTenantMap.get(productTenantId);
            String tenantNo = null != tenantRespDto ? tenantRespDto.getTenantNo() : "";
            String msg = "租户（"+tenantNo+"）授信余额不足，请联系客服";
            enumPaymentChecks.add(PaymentCheckInfo.of(EnumPaymentCheck.DISTRIBUTION_TENANT_WALLET_NOT_ENOUGH, msg));
        }
    }


    private void reachLimitSendMsgRefreshOrder(Long userId, Long orderId) {
        OrderRefreshMsg orderRefreshMsg = new OrderRefreshMsg();
        orderRefreshMsg.setId(orderId);
        orderRefreshMsg.setUserId(userId);
        orderRefreshMsg.setTag(OrderEventRefreshConstant.EVENT_ORDER_REFRESH);
        orderRefreshMsg.setUpdateItemFactory(true);
        this.rocketMQTemplate.sendNormal(RocketMqTopicConst.ORDER_SYNC_TOPIC, OrderEventRefreshConstant.EVENT_ORDER_REFRESH, orderRefreshMsg);
    }

    @NotNull
    private Set<Long> getReachLimitProductIds(MapListUtil<String, OrderItemProductFactoryDto> supplyChainTypeFactoryDtosMapList) {
        Map<String, List<OrderItemProductFactoryDto>> supplyChainTypeFactoryDtosMap = supplyChainTypeFactoryDtosMapList.getMaps();
        if (CollUtil.isEmpty(supplyChainTypeFactoryDtosMap)) {
            return Collections.emptySet();
        }
        Set<Long> reachLimitProductIds = Sets.newHashSet();
        for (Map.Entry<String, List<OrderItemProductFactoryDto>> entry : supplyChainTypeFactoryDtosMap.entrySet()) {
            List<OrderItemProductFactoryDto> orderItemProductFactoryDtos = entry.getValue();
            String supplyChainType = entry.getKey();
            List<Long> productIds = orderItemProductFactoryDtos.stream().map(i -> i.getProductId()).distinct().collect(Collectors.toList());
            List<Long> factoryIds = orderItemProductFactoryDtos.stream().map(i -> i.getFactoryId()).distinct().collect(Collectors.toList());
            List<ProductSupply> productSupplies = this.supplyService.findByFactoryIdsProductIdsSupplyChainType(productIds, factoryIds, supplyChainType);

            List<ProductSupply> matchSupplies = getSupplies(orderItemProductFactoryDtos, productSupplies);

            List<ProductSupply> reachLimitSupplies = matchSupplies.stream().filter(i -> BasePoConstant.yes(i.getReachLimit())).collect(Collectors.toList());
            List<Long> reachLimitProductIdList = reachLimitSupplies.stream().map(i -> i.getProductId()).distinct().collect(Collectors.toList());
            reachLimitProductIds.addAll(reachLimitProductIdList);
        }
        return reachLimitProductIds;
    }

    private List<Long> getTenantReachLimitProductIds(Map<Long, List<DistributionFactoryProductionDayLimitDTO>> productParentIdTenantAuthFactoryLimitMap,
                                                     List<Product> products,
                                                     MapListUtil<String, OrderItemProductFactoryDto> supplyChainTypeFactoryDtosMapList) {
        Map<String, List<OrderItemProductFactoryDto>> supplyChainTypeFactoryDtosMap = supplyChainTypeFactoryDtosMapList.getMaps();
        if (CollUtil.isEmpty(supplyChainTypeFactoryDtosMap)) {
            return Collections.emptyList();
        }
        Map<Long, Long> productIdParentIdMap = ListUtil.toMap(Product::getId, Product::getParentId, products);

        List<ProductSupply> allSupplies = Lists.newArrayList();
        for (Map.Entry<String, List<OrderItemProductFactoryDto>> entry : supplyChainTypeFactoryDtosMap.entrySet()) {
            List<OrderItemProductFactoryDto> orderItemProductFactoryDtos = entry.getValue();
            String supplyChainType = entry.getKey();
            List<Long> productIds = orderItemProductFactoryDtos.stream().map(i -> i.getProductId()).distinct().collect(Collectors.toList());
            List<Long> factoryIds = orderItemProductFactoryDtos.stream().map(i -> i.getFactoryId()).distinct().collect(Collectors.toList());
            List<ProductSupply> productSupplies = this.supplyService.findByFactoryIdsProductIdsSupplyChainType(productIds, factoryIds, supplyChainType);

            List<ProductSupply> matchSupplies = getSupplies(orderItemProductFactoryDtos, productSupplies);
            allSupplies.addAll(matchSupplies);
        }
        List<ProductSupply> validProductSupplies = Lists.newArrayList();
        for (ProductSupply productSupply : allSupplies) {
            Long productParentId = productIdParentIdMap.get(productSupply.getProductId());
            List<DistributionFactoryProductionDayLimitDTO> distributionFactoryProductionDayLimitDTOS = productParentIdTenantAuthFactoryLimitMap.get(productParentId);
            if (CollUtil.isEmpty(distributionFactoryProductionDayLimitDTOS)) {
                validProductSupplies.add(productSupply);
                continue;
            }
            boolean notLimitFactory = distributionFactoryProductionDayLimitDTOS.stream().anyMatch(i -> i.getFactoryId() == 0);
            // 不限工厂
            if (notLimitFactory) {
                Boolean isProductionFull = distributionFactoryProductionDayLimitDTOS.get(0).getProductionFull();
                // 产能未满
                if (!isProductionFull) {
                    validProductSupplies.add(productSupply);
                }
            } else {
                // 产能未满的工厂
                List<Long> validFactoryIds = distributionFactoryProductionDayLimitDTOS.stream().filter(i -> !i.getProductionFull()).map(i -> i.getFactoryId()).collect(Collectors.toList());
                if (validFactoryIds.contains(productSupply.getFactoryId())) {
                    validProductSupplies.add(productSupply);
                }
            }
        }
        List<Long> notReachLimitProductIdList = validProductSupplies.stream().map(i -> i.getProductId()).distinct().collect(Collectors.toList());
        List<Long> productIds = supplyChainTypeFactoryDtosMap.values().stream().flatMap(i -> i.stream()).map(i -> i.getProductId()).distinct().collect(Collectors.toList());

        List<Long> reachLimitProductIdLis = productIds.stream().filter(i -> !notReachLimitProductIdList.contains(i)).collect(Collectors.toList());
        return reachLimitProductIdLis;
    }

    @NotNull
    private static List<ProductSupply> getSupplies(List<OrderItemProductFactoryDto> orderItemProductFactoryDtos, List<ProductSupply> productSupplies) {
        List<ProductSupply> matchSupplies = Lists.newArrayList();
        for (OrderItemProductFactoryDto orderItemProductFactoryDto : orderItemProductFactoryDtos) {
            List<ProductSupply> supplies = productSupplies.stream()
                    .filter(i -> i.getProductId().equals(orderItemProductFactoryDto.getProductId()) &&
                            i.getFactoryId().equals(orderItemProductFactoryDto.getFactoryId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(supplies)) {
                matchSupplies.addAll(supplies);
            }
        }
        return matchSupplies;
    }

    @NotNull
    private static MapListUtil<String, OrderItemProductFactoryDto> formatSupplyChainTypeFactoryDtosMapList(List<OrderItem> orderItems,
                                                                                                           Map<Long, OrderItemSupplyChainDTO> orderItemIdChainValueMap,
                                                                                                           Map<Long, OrderItemFactoryRespDto> orderItemIdFactoryDtoMap) {
        MapListUtil<String, OrderItemProductFactoryDto> supplyChainTypeFactoryDtosMapList = MapListUtil.instance();
        for (OrderItem orderItem : orderItems) {
            OrderItemProductFactoryDto orderItemProductFactoryDto = new OrderItemProductFactoryDto();
            orderItemProductFactoryDto.setOrderId(orderItem.getOrderId());
            orderItemProductFactoryDto.setOrderItemId(orderItem.getId());
            orderItemProductFactoryDto.setProductName(orderItem.getProductName());
            orderItemProductFactoryDto.setProductId(orderItem.getProductId());
            OrderItemFactoryRespDto orderItemFactoryRespDto = orderItemIdFactoryDtoMap.get(orderItem.getId());
            Long factoryId = null != orderItemFactoryRespDto ? orderItemFactoryRespDto.getFactoryId() : null;
            orderItemProductFactoryDto.setFactoryId(factoryId);
            OrderItemSupplyChainDTO supplyChainDTO = orderItemIdChainValueMap.get(orderItem.getId());
            String supplyChainType = supplyChainDTO == null ? SupplyChainTypeEnum.ONE_PIECE.name() : supplyChainDTO.getSupplyType();
            orderItemProductFactoryDto.setSupplyChainType(supplyChainType);
            if (NumberUtils.greaterZero(factoryId)) {
                supplyChainTypeFactoryDtosMapList.addDistinctValue(supplyChainType, orderItemProductFactoryDto);
            }
        }
        return supplyChainTypeFactoryDtosMapList;
    }

    public void checkJitStore(OrderPaymentCheckBO checkBO) {
        List<Order> orders = checkBO.getOrders();
        if (CollUtil.isEmpty(orders)) {
            return;
        }
        Map<Long, Map<String, String>> extendInfoMap = checkBO.getExtendInfoMap();
        List<Long> jitOrderIds = orders.stream().map(Order::getId)
                .filter(i -> OrderExtendValueEnum.JIT.isMatch(extendInfoMap.get(i))).collect(Collectors.toList());
        if (CollUtil.isEmpty(jitOrderIds)) {
            return;
        }
        List<Long> jitOrderStoreIds = orders.stream().filter(i -> jitOrderIds.contains(i.getId())).map(Order::getMerchantStoreId).distinct().collect(Collectors.toList());
        List<MerchantStore> merchantStores = this.merchantStoreService.findByIds(jitOrderStoreIds);
        List<Long> notJitStoreIds = merchantStores.stream().filter(i -> i.getIsPopChoice().equals(BasePoConstant.NO)).map(MerchantStore::getId).collect(Collectors.toList());
        String notJitStoreOrderNos = orders.stream().filter(i -> notJitStoreIds.contains(i.getMerchantStoreId())).map(Order::getNo).collect(Collectors.joining(","));

        if (StrUtil.isNotBlank(notJitStoreOrderNos)) {
            throw new BusinessException("订单" + notJitStoreOrderNos + "店铺未授权半托管，请先去“店铺管理”菜单点击“同步半托管店铺”");
        }
    }

    @Resource
    private TenantLogisticsFeign tenantLogisticsFeign;

    /**
     * 修改支付前的校验
     *
     * @param merchantId
     * @param orderId
     * @param orderCreateReqDto
     * @return
     */
    public PaymentCheckOneRespDto putPreparePaymentCheck(Long merchantId, Long orderId, OrderCreateReqDto orderCreateReqDto) {
        MapListUtil<Long, PaymentCheckInfo> enumPaymentCheckMapListUtil = MapListUtil.instance();
        OrderBatchCreateRespDto orderBatchCreateRespDto = this.orderPreviewFeign.updateOrderPreview(merchantId, orderId, orderCreateReqDto);
        OrderCreateRespDto order = orderBatchCreateRespDto.getItems().get(0);
        Integer fitLevel = 0;
        List<PaymentCheckInfo> enumPaymentChecks = Lists.newArrayList();
        List<String> errorMsgs = Lists.newArrayList();
        List<String> warnMsgs = Lists.newArrayList();
        OnlineOrderRespDTO onlineOrderRespDTO = null;

        for (OrderCreateItemRespDto item : order.getItems()) {
            String productName = item.getProduct() == null ? "" : item.getProduct().getName();
            if (item.getFitLevel() != null && item.getFitLevel().equals(DesignProductFitLevelConstant.FIT_LEVEL_TERRIBLE)) {
                warnMsgs.add(productName + "素材尺寸不足");
            }

            if (item.getDesignProductStatus() != null && item.getDesignProductStatus().equals(CommonStatus.DELETE.getStatus())) {
                enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.DESIGN_PRODUCT_DELETE));
            }
            if (item.getProductStatus() != null && !EndProductType.isValidByStatus(item.getProductStatus())) {
                String msg = productName + "停止生产";
                errorMsgs.add(msg);
                enumPaymentChecks.add(PaymentCheckInfo.of(EnumPaymentCheck.PRODUCT_IN_VALID, msg));
            }
        }

        if (onlineOrderRespDTO != null) {
            OnlineOrderStatusMergedEnum enumByPlatformAndStatusCode = OnlineOrderStatusUtil.getMergedEnumByPlatformAndStatusCode(
                    onlineOrderRespDTO.getMerchantStorePlatform(),
                    onlineOrderRespDTO.getStatusCode()
            );
            if (enumByPlatformAndStatusCode != null && enumByPlatformAndStatusCode != OnlineOrderStatusMergedEnum.WaitShipped) {
                enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.ORDER_SEND));
            }
        }
        //子账号fba支付权限
        if (OrderOriginType.FBA.getValue().equals(order.getOrigin()) && !orderCreateReqDto.getIsFbaPayAuth()) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.FBA_PERMISSION_DENIED));
        }
        //素材是否不足
        if (fitLevel.equals(DesignProductFitLevelConstant.FIT_LEVEL_TERRIBLE)) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.MATERIAL_NOT_ENOUGH));
        }

        if (!NumberUtils.greaterZero(order.getIssuingBayId())) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.NO_BAY));
        }
        //校验地址
        Address address = ConvertUtil.dtoConvert(order.getAddress(), Address.class);
        if (!AddressService.checkAddress(address, orderCreateReqDto.getOrigin())) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.ADDRESS));
        }

        //校验物流
        if (!NumberUtils.greaterZero(order.getLogisticsId())) {
            if (!com.sdsdiy.orderapi.constant.OrderOriginType.isFba(orderCreateReqDto.getOrigin())) {
                enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.NO_LOGISTICS));
            }
        }
        // 校验仓映射
        if (null != order.getLogistics()) {
            this.checkTemuIssuingBayMapping(orderId, order.getIssuingBayId(), order.getMerchantStoreId()
                    , order.getLogistics().getServiceProviderId(), enumPaymentChecks, errorMsgs,order.getLogistics().getId());
        }
        OrderAmountCreateDTO orderAmount = order.getOrderAmount();
        if(null!=orderAmount){
            checkTenantSupplyPrice(orderAmount.getProductAmount(),orderAmount.getTenantProductAmount(),enumPaymentChecks);
        }

        checkPreviewTemuQuantity(order, enumPaymentChecks);

        return this.buildErrorMSg(orderId, enumPaymentCheckMapListUtil, enumPaymentChecks, errorMsgs, warnMsgs);
    }

    public void checkPreviewTemuQuantity(OrderCreateRespDto order, List<PaymentCheckInfo> enumPaymentChecks) {
        if (!needCheckTemuQuantity(order.getOriginType(),null!=order.getMerchantStore()?order.getMerchantStore().getMerchantStorePlatformCode():"",order.getExtendMap())) {
            return;
        }
        if(!BooleanUtil.isTrue(order.getTemuNumChange())){
            return;
        }

        String errorMsg = "";
        List<OrderCreateItemRespDto> orderItems = order.getItems();
        for (OrderCreateItemRespDto orderItem : orderItems) {
            PlatformOrderItemDto platformOrderItem = orderItem.getPlatformOrderItem();
            if (null == platformOrderItem) {
                continue;
            }
            BigDecimal actualQuantity = NumberUtil.sub(platformOrderItem.getQuantity(),platformOrderItem.getShipmentInfoQuantity());
            boolean numEqual = NumberUtil.equals(actualQuantity, NumberUtil.toBigDecimal(orderItem.getNum()));
            if (!numEqual) {
                errorMsg = "修改的产品数量不等于线上订单剩余可发货产品件数：" + actualQuantity;
                break;
            }
        }
        if (StrUtil.isNotBlank(errorMsg)) {
            enumPaymentChecks.add(PaymentCheckInfo.of(EnumPaymentCheck.ORDER_ITEM_NOT_EQUAL_ALLOW_QUANTITY, errorMsg));
        }
    }

    private static boolean needCheckTemuQuantity(String originType, String merchantStorePlatformCode, Map<String,String> extendMap) {
        boolean semiOrLocalOrder = OrderExtendValueEnum.TEMU_SEMI_ORDER.isMatch(extendMap) ||
                                   OrderExtendValueEnum.TEMU_LOCAL_ORDER.isMatch(extendMap);
        return com.sdsdiy.orderapi.constant.OrderOriginType.isAutoImport(originType) &&
               MerchantStorePlatformEnum.TEMU.getCode().equals(merchantStorePlatformCode) &&
               semiOrLocalOrder;
    }


    /**
     * 有错误返回错误的 ，没错误返回warn的
     */
    public PaymentCheckOneRespDto checkOrder(Long tenantId, Order order, Logistics logistics,
                                             OrderImportExtraInfoDto orderImportExtraInfoDto,
                                             MapListUtil<Long, PaymentCheckInfo> enumPaymentCheckMapListUtil,
                                             Map<Long, MerchantAuthProductParentRespDTO> authProductParentMap,
                                             Map<Long, DesignProductRespDto> designProductRespDtoMap,
                                             Map<Long, OnlineOrderRespDTO> onlineOrderMap, boolean isFbaPaymentAuth,
                                             OrderPaymentCheckBO checkBO,
                                             Map<Long, OrderItemCustomizeDesignProductRelRespDto> customizeDesignRelMap,
                                             Map<String, ProductHolidayDto> variantIdAndSupplyChainTypeHolidayMap,
                                             Map<Long, OrderItemSupplyChainDTO> orderItemSupplyChainMap,
                                             Map<Long, LogisticsChannelRespDto> tailLogisticsChannelIdByOrderMaps,
                                             Map<Long, OrderItemFactoryRespDto> orderItemFactoryRespDtoMap,
                                             Boolean checkOrdersQuota
    ) {
        Long orderId = order.getId();
        Integer fitLevel = 0;
        Integer validStatus = null;
        Map<Long, OrderAmountRespDTO> amountMaps=checkBO.getOrderAmmountMaps();
        List<PaymentCheckInfo> enumPaymentChecks = checkBO.getCheckInfoList(orderId);
        List<String> errorMsgs = Lists.newArrayList();
        List<String> warnMsgs = Lists.newArrayList();
        //休假中警告
        List<OrderItem> orderItems = order.getItems();
        List<Long> productIds = orderItems.stream().map(OrderItem::getProductId).distinct().collect(Collectors.toList());
        List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());


        Set<String> holidayProductNames = Sets.newHashSet();
        Set<String> prototypeUpdateKeyIds = Sets.newHashSet();
        Set<String> prototypeUpdateOfficialKeyIds = Sets.newHashSet();

        com.sdsdiy.common.base.helper.MapListUtil<Long, Long> productParentItemMapListUtil = com.sdsdiy.common.base.helper.MapListUtil.instance();
        for (OrderItem item : order.getItems()) {
            if (!OrderStatus.noCancel(item.getStatus())) {
                //不校验 取消或删除对订单
                continue;
            }
            this.checkCustomDesignStatus(item, checkBO, customizeDesignRelMap);

            Product product = this.productCacheService.findById(item.getProductId());
            productParentItemMapListUtil.addDistinctValue(product.getParentId(), item.getId());
            String productName = product == null ? "" : product.getName();
            String keyIdMsg = "";
            OrderItemSupplyChainDTO supplyChainDTO = orderItemSupplyChainMap.get(item.getId());
            String chainType = supplyChainDTO == null ? SupplyChainTypeEnum.ONE_PIECE.name() : supplyChainDTO.getSupplyType();
            //休假中
            ProductHolidayDto productHolidayDto = variantIdAndSupplyChainTypeHolidayMap.get(ProductService.getVariantIdIdAndSupplyChainTypeKey(chainType, item.getProductId()));
            if (null != productHolidayDto && null != productHolidayDto.getHolidayType() && productHolidayDto.getHolidayType() == 2) {
                holidayProductNames.add(productName);
            }
            //提取成品素材是否不足
            DesignProductRespDto designProductRespDto = designProductRespDtoMap.get(item.getEndProductId());
            if (designProductRespDto != null) {
                if (designProductRespDto.getFitLevel() != null) {
                    if (designProductRespDto.getFitLevel().equals(DesignProductFitLevelConstant.FIT_LEVEL_TERRIBLE)) {
                        warnMsgs.add(productName + "素材尺寸不足");
                    }
                    if (fitLevel < designProductRespDto.getFitLevel()) {
                        fitLevel = designProductRespDto.getFitLevel();
                    }
                }
                if (designProductRespDto.getStatus().equals(CommonStatus.DELETE.getStatus())) {
                    enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.DESIGN_PRODUCT_DELETE));
                }
                //刺绣设计的模板更新不影响刺绣成品最后的生产稿件，要过滤掉刺绣设计模板类型的成品
                if (designProductRespDto.getBoolPrototypeUpdate() && !DesignProductTypeConstant.isEmbroidery(designProductRespDto.getType())) {
                    if (OfficialDesignProductConstant.OFFICIAL_SPECIAL_USER_ID.equals(designProductRespDto.getUserId())) {
                        prototypeUpdateOfficialKeyIds.add(designProductRespDto.getKeyId());
                    } else {
                        prototypeUpdateKeyIds.add(designProductRespDto.getKeyId());
                    }
                }
                keyIdMsg = " 成品:" + designProductRespDto.getKeyId();
            }
            //校验产品是否停产
            if (NumberUtils.greaterZero(item.getProductId())) {
                if (validStatus == null) {
                    EndProductType endProductType = EndProductUtil.getEndProductType(authProductParentMap.get(item.getProduct().getParentId())
                            , this.productCacheService.findById(item.getProductId()), order.getTenantId());
                    if (EndProductType.isValidByStatus(endProductType.getStatus()) && CommonDesignProductService.isOfficeMaterialValid(designProductRespDto)) {
                        //产品可生产 do nothing
                    } else {
                        //不合法
                        validStatus = EndProductType.inValidStatus();
                        errorMsgs.add(productName + keyIdMsg + "停止生产");
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(prototypeUpdateKeyIds)) {
            String msg = StringUtils.listToString(prototypeUpdateKeyIds) + EnumPaymentCheck.PROTOTYPE_UPDATE.getMsg();
            errorMsgs.add(msg);
            enumPaymentChecks.add(PaymentCheckInfo.of(EnumPaymentCheck.PROTOTYPE_UPDATE, msg));
        }
        if (CollectionUtil.isNotEmpty(prototypeUpdateOfficialKeyIds)) {
            String msg = StringUtils.listToString(prototypeUpdateOfficialKeyIds) + EnumPaymentCheck.OFFICIAL_DESIGN_PRODUCT_PROTOTYPE_UPDATE.getMsg();
            errorMsgs.add(msg);
            enumPaymentChecks.add(PaymentCheckInfo.of(EnumPaymentCheck.PROTOTYPE_UPDATE, msg));
        }
        OnlineOrderRespDTO onlineOrderRespDTO = null;
        if (order.getOriginType().equals(OrderOriginType.AUTO_IMPORT.getValue())) {
            onlineOrderRespDTO = onlineOrderMap.get(order.getOriginId());
        }
        if (onlineOrderRespDTO != null) {
            //这边不需要特别区分速卖通的已取消和已完成，都属于已完成
            OnlineOrderStatusMergedEnum enumByPlatformAndStatusCode = OnlineOrderStatusUtil.getMergedEnumByPlatformAndStatusCode(
                    onlineOrderRespDTO.getMerchantStorePlatform(),
                    onlineOrderRespDTO.getStatusCode()
            );
            if (enumByPlatformAndStatusCode != null && enumByPlatformAndStatusCode != OnlineOrderStatusMergedEnum.WaitShipped) {
                enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.ORDER_SEND));
            }
            if (OrderExtendValueEnum.TEMU_FULLY_ORDER.isMatch(checkBO.getExtendInfoMap().get(orderId)) &&
                    CollUtil.isNotEmpty(onlineOrderRespDTO.getItemRespDTOList())) {
                // temu 全托管
                List<String> canceledOutItemIds = onlineOrderRespDTO.getItemRespDTOList().stream()
                        .filter(i -> TemuOrderStatusEnum.CANCELED_STATUS_VALUE.contains(i.getStatusValue()))
                        .map(OnlineOrderItemRespDTO::getOutItemId).collect(Collectors.toList());
                boolean existsCanceled = orderItems.stream().filter(i -> !OrderStatus.isCancel(i.getStatus()))
                        .anyMatch(i -> canceledOutItemIds.contains(i.getOutOrderItemId()));
                if (existsCanceled) {
                    enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.TEMU_FULLY_ITEM_CANCEL));
                }
            }
        }
        if (validStatus != null && !EndProductType.isValidByStatus(validStatus)) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.PRODUCT_IN_VALID));
        }
        //休假中
        if (CollUtil.isNotEmpty(holidayProductNames)) {
            for (String productName : holidayProductNames) {
                warnMsgs.add(productName + "产品休假中");
            }
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.PRODUCT_HOLIDAY_ONGING));
        }
        //素材是否不足
        if (fitLevel.equals(DesignProductFitLevelConstant.FIT_LEVEL_TERRIBLE)) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.MATERIAL_NOT_ENOUGH));
        }
        if (OrderStatus.NONE.getStatus() == order.getStatus()) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.ORDER_ITEM_NO_MATCH));
        } else if (OrderStatus.UNPAIN.getStatus() == order.getStatus()) {
            if (!NumberUtils.greaterZero(order.getIssuingBayId())) {
                enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.NO_BAY));
            }
        } else {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.PAINED));
        }
        //校验地址
        if (!AddressService.checkAddress(order.getAddress(), order.getOriginType())) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.ADDRESS));
        }
        //子账号fba支付权限
        if (OrderOriginType.FBA.getValue().equals(order.getOrigin()) && !isFbaPaymentAuth) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.FBA_PERMISSION_DENIED));
        }
        //校验物流
        if (!NumberUtils.greaterZero(order.getLogisticsId()) || logistics == null) {
            //有地址，没有物流不可以
            if (null != order.getAddressId() && order.getAddressId() > 0) {
                enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.NO_LOGISTICS));
            } else {
                if (!order.getOriginType().equals(OrderOriginType.FBA.getValue())) {
                    enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.NO_LOGISTICS));
                }
            }
        } else {
            //校验ioss失败
            if (!this.checkIoss(order, logistics, orderImportExtraInfoDto)) {
                enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.IOSS_WRONG));
            }
        }
        checkShein(order, checkBO);
        checkTemuUS2CA(order, checkBO);
        boolean temuSemi = null != logistics && LogisticsServiceProviderEnum.isTemuSemi(logistics.getServiceProviderId());
        TemuOnlineLogisticsCheckDto checkDto = null;
        if (temuSemi) {
            checkDto = new TemuOnlineLogisticsCheckDto();
            LogisticsChannelRespDto logisticsChannelRespDto = tailLogisticsChannelIdByOrderMaps.get(order.getId());
            checkDto.setOrderOriginType(order.getOriginType());
            checkDto.setOrderSyncStatus(order.getSyncStatus());
            checkDto.setPlatformCode(order.getMerchantStorePlatformCode());
            checkDto.setOutOrderNo(order.getOutOrderNo());
            checkDto.setLogisticsId(order.getLogisticsId());
            checkDto.setIsCheckLogistics(true);
            if (logisticsChannelRespDto != null) {
                checkDto.setTailLogisticsChannelCode(logisticsChannelRespDto.getCode());
                checkDto.setTailLogisticsChannelId(logisticsChannelRespDto.getId());
            }
        }
        if (MerchantIsBlacklistConstant.ADDRESS.equals(order.getBlacklistType()) || MerchantIsBlacklistConstant.BUYER.equals(order.getBlacklistType())) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.BLACK_LIST));
        }
        if (!TenantCommonConstant.isSdsdiy(tenantId)) {
            //未付款，租户物流来源发生变动校验
            this.checkTenantLogisticsChange(order, logistics, enumPaymentChecks,amountMaps.get(orderId));
            //校验订单额度
//            Boolean orderQuotaEnough = this.tenantLogisticsOrderFeign.checkOrderQuota(orderId, false);
            Boolean orderQuotaEnough = checkOrdersQuota;
            if (!orderQuotaEnough) {
                enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.ORDER_QUOTA_NOT_ENOUGH));
            }
        }
        //小单起批量
        this.checkSmallOrderMinNum(order, enumPaymentChecks, errorMsgs, orderItems, orderItemSupplyChainMap);
        //校验产能线有效性
        this.checkSupplyChainType(tenantId, enumPaymentChecks, errorMsgs, orderItems, productIds, orderItemSupplyChainMap);

        //同产品，产能线不一致校验
        for (Map.Entry<Long, List<Long>> entry : productParentItemMapListUtil.getMaps().entrySet()) {
            List<Long> itemIds = entry.getValue();
            Set<String> itemSupplyChains = orderItemSupplyChainMap.entrySet().stream().filter(i -> itemIds.contains(i.getKey())).map(j -> j.getValue().getSupplyType()).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(itemSupplyChains) && itemSupplyChains.size() > 1) {
                enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.SUPPLY_CHAIN_TYPE_DIFFERENCE));
                break;
            }
        }
        // temu发货仓映射
        if (null != logistics) {
            this.checkTemuIssuingBayMapping(orderId, order.getIssuingBayId(), order.getMerchantStoreId()
                    , logistics.getServiceProviderId(), enumPaymentChecks, errorMsgs,logistics.getId());
        }
        OrderAmountRespDTO orderAmountRespDTO = amountMaps.get(orderId);
        if(null!=orderAmountRespDTO){
            checkTenantSupplyPrice(orderAmountRespDTO.getProductAmount(),orderAmountRespDTO.getTenantProductAmount(),enumPaymentChecks);
        }

        boolean isDistribution = orderAmountRespDTO.getIsDistribution();
        // 校验产能上限
        this.checkFactoryLimit(order, enumPaymentChecks,checkBO,orderItemSupplyChainMap,orderItemFactoryRespDtoMap);
        // 租户产能上限
        this.checkTenantFactoryLimit(isDistribution,order, enumPaymentChecks,checkBO,orderItemSupplyChainMap,orderItemFactoryRespDtoMap);
        this.checkTenantDistributorWallet(order,amountMaps.get(orderId),enumPaymentChecks,checkBO.getSupTenantIdWalletMap(),checkBO.getProductTenantMap());
        // 未开启线上付款，分销产品，报错
        boolean tenantOpenOnlinePay = checkBO.getMerchantBalance().isTenantOpenOnlinePay();
        Assert.validateTrue(isDistribution&&!tenantOpenOnlinePay,"租户未开启线上收款，请联系管理员");

        // 达上限，发送刷新订单消息
        if (CollUtil.isNotEmpty(enumPaymentChecks)) {
            enumPaymentChecks.stream()
                    .filter(i -> EnumPaymentCheck.reachLimitRefreshOrder(i.getEnumPaymentCheck()))
                    .findFirst()
                    .ifPresent(reachLimitCheckInfo -> this.reachLimitSendMsgRefreshOrder(checkBO.getUserId(), orderId));
        }
        // 合作仓授权校验
        // checkPartnerWarehouseAuth(order, checkBO);

        PaymentCheckOneRespDto paymentCheckOneRespDto = this.buildErrorMSg(orderId, enumPaymentCheckMapListUtil, enumPaymentChecks, errorMsgs, warnMsgs);
        paymentCheckOneRespDto.setCheckDto(checkDto);
        return paymentCheckOneRespDto;
    }

    private void checkCustomDesignStatus(OrderItem item, OrderPaymentCheckBO checkBO, Map<Long, OrderItemCustomizeDesignProductRelRespDto> customizeDesignRelMap) {
        if (Objects.isNull(item)) {
            return;
        }
        OrderItemCustomizeDesignProductRelRespDto customRel = customizeDesignRelMap.get(item.getId());

        if (Objects.isNull(customRel)) {
            return;
        }
        //如果order_item_customize_design_product_rel的old_design_product_id，
        // 跟order_item的end_product_id不同，表示用户已经设计或关联了其他成品
        if (NumberUtils.greaterZero(item.getEndProductId()) && !item.getEndProductId().equals(customRel.getOldDesignProductId())) {
            return;
        }
        String result = customRel.getResult();
        if (OrderItemCustomizeDesignProductRelResultEnum.isFail(result) || OrderItemCustomizeDesignProductRelResultEnum.isWait(result)) {
            checkBO.getCheckInfoList(item.getOrderId()).add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.MICRO_DESIGN_STATUS_WAIT_OR_ERROR));
        }
    }

    public void checkPartnerWarehouseAuth(Order order, OrderPaymentCheckBO checkBO) {
        if (null == order || !MerchantStorePlatformEnum.TEMU.equalsCode(order.getMerchantStorePlatformCode())) {
            return;
        }
        boolean isY2 = isY2(order, checkBO);
        boolean isAutoImport = com.sdsdiy.orderapi.constant.OrderOriginType.isAutoImport(order.getOriginType());
        boolean isTemuSemi = isTemuSemi(order, checkBO);
        boolean isTemuPartnerAuthOk = temuPartnerAuthOk(checkBO, order);
        log.info("判断合作仓是否授权,非isY2={},isAutoImport={},isTemuSemi={},isTemuPartnerAuthOk={}",!isY2,isAutoImport,isTemuSemi,isTemuPartnerAuthOk);
        if (!isY2 && isAutoImport && isTemuSemi && isTemuPartnerAuthOk) {
            MerchantStore merchantStore = checkBO.getMerchantStoreMap().get(order.getMerchantStoreId());
            String storeName = null != merchantStore ? merchantStore.getName() : "";
            String msg = storeName + "店铺还未开启合作仓下单模式";
            checkBO.getCheckInfoList(order.getId()).add(PaymentCheckInfo.of(EnumPaymentCheck.COOPERATIVE_WAREHOUSE_NOT_AUTHORIZE, msg));
        }
    }

    private static boolean isTemuSemi(Order order, OrderPaymentCheckBO checkBO) {
        Map<String, String> extendMap = checkBO.getExtendInfoMap().get(order.getId());
        boolean isTemuSemi=OrderExtendValueEnum.TEMU_SEMI_ORDER.isMatch(extendMap);
        return isTemuSemi;
    }

    private static boolean isY2(Order order, OrderPaymentCheckBO checkBO) {
        Map<String, String> extentMap = checkBO.getPlatformOrderExtendMap()
            .getOrDefault(order.getMerchantStorePlatformCode(), Collections.emptyMap())
            .get(order.getOutOrderNo());
        return PlatformOrderExtendValueEnum.TEMU_Y2_ADVANCE_SALE.isMatch(extentMap);
    }

    public boolean temuPartnerAuthOk(OrderPaymentCheckBO checkBO,Order order) {
        Map<Long, List<MerchantStoreAuthTokenDto>> storeIdStoreAuthDtoMap = checkBO.getStoreIdStoreAuthDtoMap();
        if (CollUtil.isEmpty(storeIdStoreAuthDtoMap)) {
            return false;
        }
        List<MerchantStoreAuthTokenDto> merchantStoreAuthTokenDtos = storeIdStoreAuthDtoMap.get(order.getId());
        if (CollUtil.isEmpty(merchantStoreAuthTokenDtos)) {
            return false;
        }
        MerchantStoreAuthTokenDto authToken = merchantStoreAuthTokenDtos.stream().filter(i -> i.getType().equalsIgnoreCase(MerchantStoreAuthTypeEnum.PARTNER_WAREHOUSE.getCode())).findFirst().orElse(null);
        if (null == authToken) {
            return false;
        }
        return MerchantStoreAuthStatusEnum.AUTH_SUCCESS.getValue().equals(authToken.getAuthStatus());
    }

    private static void checkShein(Order order, OrderPaymentCheckBO checkBO) {
        if (!MerchantStorePlatformEnum.TEMU.equalsCode(order.getMerchantStorePlatformCode())) {
            return;
        }
        Map<String, String> extentMap = checkBO.getPlatformOrderExtendMap()
                .getOrDefault(order.getMerchantStorePlatformCode(), Collections.emptyMap())
                .get(order.getOutOrderNo());
        if (CollUtil.isEmpty(extentMap)) {
            return;
        }
        if (PlatformOrderExtendValueEnum.PACKAGE_NO_MISS.isMatch(extentMap)) {
            checkBO.getCheckInfoList(order.getId()).add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.SHEIN_PACKAGE_NO_MISS));
        }
    }
    private static void checkTemuQuantity(Order order, OrderPaymentCheckBO checkBO) {
        Map<String, String> extendMap = checkBO.getExtendInfoMap().get(order.getId());
        boolean needCheckQuantity = needCheckTemuQuantity(order.getOriginType(), order.getMerchantStorePlatformCode(), extendMap);
        if(!needCheckQuantity){
            return;
        }
        Map<String, PlatformOrderDto> outOrderNoPlatformOrderMap = checkBO.getOutOrderNoPlatformOrderMap();
        PlatformOrderDto platformOrderDto = outOrderNoPlatformOrderMap.get(order.getOutOrderNo());
        if(null == platformOrderDto || CollUtil.isEmpty(platformOrderDto.getItems())){
            return;
        }
        Map<String, PlatformOrderItemDto> ourItemIdMap = ListUtil.toMap(PlatformOrderItemDto::getOutItemId, platformOrderDto.getItems());

        String errorMsg = "";
        List<OrderItem> orderItems = order.getItems();
        for (OrderItem orderItem : orderItems) {
            PlatformOrderItemDto platformOrderItem = ourItemIdMap.get(orderItem.getOutOrderItemId());
            if (null == platformOrderItem) {
                continue;
            }
            BigDecimal actualQuantity = NumberUtil.sub(platformOrderItem.getQuantity(),platformOrderItem.getShipmentInfoQuantity());
            boolean numEqual = NumberUtil.equals(actualQuantity, NumberUtil.toBigDecimal(orderItem.getNum()));
            if (!numEqual) {
                errorMsg = "修改的产品数量不等于线上订单剩余可发货产品件数：" + actualQuantity;
                break;
            }
        }
        if (StrUtil.isNotBlank(errorMsg)) {
            checkBO.getCheckInfoList(order.getId()).add(PaymentCheckInfo.of(EnumPaymentCheck.ORDER_ITEM_NOT_EQUAL_ALLOW_QUANTITY,errorMsg));
        }
    }

    private static void checkTemuUS2CA(Order order, OrderPaymentCheckBO checkBO) {
        if (!MerchantStorePlatformEnum.TEMU.equalsCode(order.getMerchantStorePlatformCode())) {
            return;
        }
        Map<String, Map<String, String>> itemExtentMap = checkBO.getPlatformOrderItemExtendMap()
                .get(order.getMerchantStorePlatformCode());
        if (CollUtil.isEmpty(itemExtentMap)) {
            return;
        }
        // TEMU美发加
        Set<String> us2caValues = new HashSet<>();
        order.getItems().stream().filter(i -> !OrderStatus.isCancel(i.getStatus())
                        && StrUtil.isNotBlank(i.getOutOrderItemId()))
                .forEach(i -> {
                    Map<String, String> map = itemExtentMap.get(i.getOutOrderItemId());
                    if (PlatformOrderItemExtendValueEnum.TEMU_US_TO_CA_BC.isMatch(map)) {
                        us2caValues.add(PlatformOrderItemExtendValueEnum.TEMU_US_TO_CA_BC.getCode());
                    } else if (PlatformOrderItemExtendValueEnum.TEMU_US_TO_CA_BBC.isMatch(map)) {
                        us2caValues.add(PlatformOrderItemExtendValueEnum.TEMU_US_TO_CA_BBC.getCode());
                    }
                });
        if (us2caValues.size() > 1) {
            checkBO.getCheckInfoList(order.getId()).add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.TEMU_US2CA_BC_BBC));
        }
    }

    private void checkTemuIssuingBayMapping(Long orderId, Long issuingBayId, Long merchantStoreId
            , Long serviceProviderId, List<PaymentCheckInfo> enumPaymentChecks, List<String> errorMsgs,Long logisticsId) {
        String msg = getCheckMsg(orderId, issuingBayId, merchantStoreId, serviceProviderId, logisticsId);
        if(StrUtil.isNotBlank(msg)){
            errorMsgs.add(msg);
            enumPaymentChecks.add(PaymentCheckInfo.of(EnumPaymentCheck.TEMU_ISSUING_BAY_NOT_MAPPING, msg));
        }
    }

    private void checkTenantSupplyPrice(Double productAmount, Double tenantProductAmount,
                                        List<PaymentCheckInfo> enumPaymentChecks) {
        boolean priceUpsideDown = null != productAmount && null != tenantProductAmount
            && NumberUtil.isGreater(BigDecimal.valueOf(tenantProductAmount), BigDecimal.valueOf(productAmount));
        if (priceUpsideDown) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.TENANT_PRICE_UPSIDE_DOWN));
        }
    }

    @Nullable
    private String getCheckMsg(Long orderId, Long issuingBayId, Long merchantStoreId, Long serviceProviderId, Long logisticsId) {
        if (!NumberUtils.greaterZero(issuingBayId) || !NumberUtils.greaterZero(serviceProviderId)) {
            return null;
        }
        Boolean isTailOnlineProvider = LogisticsServiceProviderEnum.isTailOnlineProvider(serviceProviderId);
        boolean temuSemi = LogisticsServiceProviderEnum.isTemuSemi(serviceProviderId)||isTailOnlineProvider;
        boolean temuFully = LogisticsServiceProviderEnum.TEMU_FULLY_MANAGED.equalsNumber(serviceProviderId);
        if (!temuSemi && !temuFully) {
            return null;
        }
        List<TemuOnlineWarehouseCheckParam> paramList = Lists.newArrayList();
        TemuOnlineWarehouseCheckParam temuOnlineWarehouseCheckParam = new TemuOnlineWarehouseCheckParam();
        temuOnlineWarehouseCheckParam.setMerchantStoreId(merchantStoreId);
        temuOnlineWarehouseCheckParam.setOrderId(orderId);
        temuOnlineWarehouseCheckParam.setIssuingBayId(issuingBayId);
        temuOnlineWarehouseCheckParam.setType(temuFully ? TemuOrderTypeEnum.TEMU_FULLY.getCode() : TemuOrderTypeEnum.TEMU_SEMI.getCode());

        Long onlineIssuingBayId = null;
        if(isTailOnlineProvider){
            TenantAddressRespDto tenantAddress = tenantLogisticsFeign.getTenantAddress(logisticsId, issuingBayId);
            onlineIssuingBayId = null != tenantAddress ? tenantAddress.getOnlineLogisticsIssuingBayId() : -1L;
        }
        temuOnlineWarehouseCheckParam.setOnlineIssuingBayId(onlineIssuingBayId);
        paramList.add(temuOnlineWarehouseCheckParam);
        List<TemuOnlineWarehouseCheckDTO> temuOnlineWarehouseCheckDTOS = this.onlineLogisticsWarehouseFeign.checkTemuIssuingBayConf(paramList);
        if (CollUtil.isEmpty(temuOnlineWarehouseCheckDTOS)) {
            return null;
        }
        String msg="";
        Map<Long, TemuOnlineWarehouseCheckDTO> orderIdCheckMap = ListUtil.toMap(TemuOnlineWarehouseCheckDTO::getOrderId, temuOnlineWarehouseCheckDTOS);
        TemuOnlineWarehouseCheckDTO temuOnlineWarehouseCheckDTO = orderIdCheckMap.get(orderId);
        if (null != temuOnlineWarehouseCheckDTO && !temuOnlineWarehouseCheckDTO.getHaveMapTemuWarehouse()) {
            String issuingBayName = StrUtil.isEmpty(temuOnlineWarehouseCheckDTO.getOnlineIssuingBayName())
                    ? "" : "(" + temuOnlineWarehouseCheckDTO.getOnlineIssuingBayName() + ")";
            msg = "当前订单发货仓" + issuingBayName + "未映射temu仓库，请去【店铺管理】-【" +
                    (temuSemi ? "半托管/本本发货设置" : "全托管发货设置") + "】进行映射";
        }
        return msg;
    }


    private void checkSupplyChainType(Long tenantId, List<PaymentCheckInfo> enumPaymentChecks, List<String> errorMsgs, List<OrderItem> orderItems, List<Long> productIds, Map<Long, OrderItemSupplyChainDTO> orderItemSupplyChainMap) {
        ProductOnlineSupplyChainsDto dto = new ProductOnlineSupplyChainsDto();
        dto.setTenantId(tenantId);
        dto.setProductIds(productIds);
        Map<Long, List<String>> productIdSupplyChainsMap = this.productFeign.getProductIdOnlineSupplyChainsMap(dto);
        Map<Long, Long> orderItemIdProductIdMap = orderItems.stream().collect(Collectors.toMap(i -> i.getId(), i -> i.getProductId(), (a, b) -> b));

        for (Map.Entry<Long, OrderItemSupplyChainDTO> entry : orderItemSupplyChainMap.entrySet()) {
            OrderItemSupplyChainDTO orderItemSupplyChainDTO = entry.getValue();
            String supplyChainType = orderItemSupplyChainDTO.getSupplyType();
            Long orderItemId = orderItemSupplyChainDTO.getId();
            Long productId = orderItemIdProductIdMap.get(orderItemId);

            List<String> supplyChainTypes = productIdSupplyChainsMap.get(productId);
            if (CollUtil.isNotEmpty(supplyChainTypes) && !supplyChainTypes.contains(supplyChainType)) {
                enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.SUPPLY_CHAIN_TYPE_DIFFERENCE));
                break;
            }
        }
    }

    private void checkSmallOrderMinNum(Order order, List<PaymentCheckInfo> enumPaymentChecks, List<String> errorMsgs, List<OrderItem> orderItems, Map<Long, OrderItemSupplyChainDTO> orderItemSupplyChainMap) {
        List<Long> supplySmallOrderItemIds = orderItemSupplyChainMap.values().stream().filter(i -> SupplyChainTypeEnum.isSmallOrder(i.getSupplyType())).map(i -> i.getId()).collect(Collectors.toList());
        List<OrderItem> smallOrderItems = orderItems.stream().filter(i -> supplySmallOrderItemIds.contains(i.getId())).collect(Collectors.toList());
        List<Long> supplySmallOrderProductIds = smallOrderItems.stream().map(i -> i.getProductId()).distinct().collect(Collectors.toList());

        ProductSmallMinNumDto dto = new ProductSmallMinNumDto();
        dto.setMerchantId(order.getMerchantId());
        dto.setProductIds(supplySmallOrderProductIds);
        Map<Long, Integer> productIdSmallOrderMinNumMap = this.merchantAuthProductParentFeign.finalSmallOrderMinNumMap(dto);
        if (CollUtil.isEmpty(productIdSmallOrderMinNumMap)) {
            return;
        }
        StringJoiner sj = new StringJoiner(",");
        for (OrderItem orderItem : smallOrderItems) {
            if (!OrderStatus.noCancel(orderItem.getStatus())) {
                //不校验 取消或删除对订单
                continue;
            }
            OrderItemSupplyChainDTO orderItemSupplyChainDTO = orderItemSupplyChainMap.get(orderItem.getId());
            if (null == orderItemSupplyChainDTO) {
                continue;
            }

            Integer mergeNum = orderItemSupplyChainDTO.getMergeNum();
            Integer minNum = productIdSmallOrderMinNumMap.get(orderItem.getProductId());
            if (mergeNum < minNum) {
                Product product = this.productCacheService.findById(orderItem.getProductId());
                String productName = product == null ? "" : product.getName();
                sj.add(productName);
            }
        }
        String notMeetNumProductName = sj.toString();
        if (StrUtil.isNotEmpty(notMeetNumProductName)) {
            String msg = "产品[" + sj.toString() + "]不满足起批量";
            errorMsgs.add(msg);
            enumPaymentChecks.add(PaymentCheckInfo.of(EnumPaymentCheck.SMALL_ORDER_NOT_MEET_MIN_NUM, msg));
        }
    }

    private void checkTenantLogisticsChange(Order order, Logistics logistics, List<PaymentCheckInfo> enumPaymentChecks,OrderAmountRespDTO orderAmountRespDTO) {
        if (null == logistics || OrderStatus.isPay(order.getStatus())) {
            return;
        }
        boolean onlyPayTenant = EnumOrderPayType.onlyPayTenant(orderAmountRespDTO.getPaymentType());
        if (onlyPayTenant) {
            return;
        }
        BaseListReqDto baseListReqDto = new BaseListReqDto();
        baseListReqDto.setIdList(Lists.newArrayList(logistics.getId()));
        Map<Long, LogisticsAndLogisticsSourceResp> map = this.tenantLogisticsFeign.getLogisticsAndLogisticsSource(order.getTenantId(), baseListReqDto);
        Assert.validateTrue(null == map.get(logistics.getId()), "物流不存在");
        //未付款，租户物流来源发生变动校验
        log.info("getLogisticsAndLogisticsSourceMAP={}", JSON.toJSONString(map));
        List<LogisticsAndLogisticsSourceResp> logisticsSourceChangeDtos = map.values().stream()
                .filter(l -> !l.getDeliveryMethod().equals(ServiceProviderConstant.DELIVERY_METHOD_FBA) && !l.getDistributionProductLogisticsSource().equals(l.getLogisticsSource()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(logisticsSourceChangeDtos)) {
            enumPaymentChecks.add(PaymentCheckInfo.ofDefault(EnumPaymentCheck.TENANT_LOGISTICS_SOURCE_CHANGE));
        }
    }

    public boolean checkIoss(Order order, Logistics logistics, OrderImportExtraInfoDto orderImportExtraInfoDto) {
        if (EuropeUnionEnum.isEuropeUnion(order.getAddress().getCountry())) {
            boolean checkIoss = this.isCheckIoss(order, logistics, orderImportExtraInfoDto);
            if (checkIoss) {
                if (orderImportExtraInfoDto == null) {
                    return false;
                }
                if (StringUtils.isBlank(orderImportExtraInfoDto.getIoss())) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean isCheckIoss(Order order, Logistics logistics, OrderImportExtraInfoDto orderImportExtraInfoDto) {
        boolean checkIoss = false;

        if (EuropeUnionEnum.isEuropeUnion(order.getAddress().getCountry())) {
            if (logistics.getIsCollectVatAble().equals(com.sdsdiy.common.base.constant.CommonStatus.ONLINE.getStatus())) {
                //没有选择或
                if (orderImportExtraInfoDto == null || StringUtils.isBlank(orderImportExtraInfoDto.getIossType()) || TaxNumberTypeEnum.IOSS.getValue().equalsIgnoreCase(orderImportExtraInfoDto.getIossType())) {
                    checkIoss = true;
                }
            } else {
                if ("must".equalsIgnoreCase(logistics.getIossType())) {
                    checkIoss = true;
                }
            }
        }
        return checkIoss;
    }

    /**
     * @param orderId
     * @param enumPaymentCheckMapListUtil
     * @param enumPaymentChecks
     * @param errorMsgs
     * @param warnMsgs
     * @return
     */
    public PaymentCheckOneRespDto buildErrorMSg(Long orderId, MapListUtil<Long, PaymentCheckInfo> enumPaymentCheckMapListUtil,
                                                List<PaymentCheckInfo> enumPaymentChecks, List<String> errorMsgs, List<String> warnMsgs) {
        PaymentCheckOneRespDto respDto = new PaymentCheckOneRespDto();

        List<PaymentCheckInfo> warnEnumPaymentChecks = Lists.newArrayList();
        List<PaymentCheckInfo> errorEnumPaymentChecks = Lists.newArrayList();
        enumPaymentChecks = ListUtil.distinct(enumPaymentChecks);
        EnumPaymentCheckErrorType enumPaymentCheckErrorType = null;

        for (PaymentCheckInfo paymentCheckInfo : enumPaymentChecks) {
            EnumPaymentCheck enumPaymentCheck = paymentCheckInfo.getEnumPaymentCheck();
            if (enumPaymentCheckErrorType == null) {
                enumPaymentCheckErrorType = enumPaymentCheck.getErrorType();
            }
            if (enumPaymentCheck.getErrorType() == EnumPaymentCheckErrorType.ERROR) {
                //错误的优先级最高
                enumPaymentCheckErrorType = EnumPaymentCheckErrorType.ERROR;
                errorEnumPaymentChecks.add(paymentCheckInfo);
            } else if (enumPaymentCheck.getErrorType() == EnumPaymentCheckErrorType.WARN) {
                warnEnumPaymentChecks.add(paymentCheckInfo);
            }
        }
        List<String> msgs = Lists.newArrayList();
        respDto.setWarnType(enumPaymentCheckErrorType == null ? "" : enumPaymentCheckErrorType.getValue());
        if (enumPaymentCheckErrorType == null) {
            respDto.setMsgs(msgs);
            return respDto;
        }

        if (enumPaymentCheckErrorType == EnumPaymentCheckErrorType.ERROR) {
            msgs = errorMsgs;
            enumPaymentChecks = errorEnumPaymentChecks;
        } else if (enumPaymentCheckErrorType == EnumPaymentCheckErrorType.WARN) {
            msgs = warnMsgs;
            enumPaymentChecks = warnEnumPaymentChecks;
        }

        for (PaymentCheckInfo paymentCheckInfo : enumPaymentChecks) {
            EnumPaymentCheck enumPaymentCheck = paymentCheckInfo.getEnumPaymentCheck();
            if (enumPaymentCheck != EnumPaymentCheck.MATERIAL_NOT_ENOUGH &&
                    enumPaymentCheck != EnumPaymentCheck.PRODUCT_IN_VALID &&
                    enumPaymentCheck != EnumPaymentCheck.PROTOTYPE_UPDATE &&
                    enumPaymentCheck != EnumPaymentCheck.PRODUCT_HOLIDAY_ONGING &&
                    enumPaymentCheck != EnumPaymentCheck.TEMU_ISSUING_BAY_NOT_MAPPING &&
                    enumPaymentCheck != EnumPaymentCheck.COOPERATIVE_WAREHOUSE_NOT_AUTHORIZE &&
                    enumPaymentCheck != EnumPaymentCheck.SMALL_ORDER_NOT_MEET_MIN_NUM) {
                msgs.add(paymentCheckInfo.getMsg());
            }
            enumPaymentCheckMapListUtil.addDistinctValue(orderId, paymentCheckInfo);
        }
        respDto.setMsgs(msgs);
        return respDto;
    }

    /**
     * 刷新订单的黑名单标记
     *
     * @param merchantId
     * @param orderList
     */
    public void refreshOrderBlacklistType(Long merchantId, Map<Long, OrderImportExtraInfoDto> orderImportExtraMap, List<Order> orderList) {
        List<MerchantBatchDetectDto> checkDtoList = new ArrayList<>();
        for (int i = 0; i < orderList.size(); i++) {
            Order item = orderList.get(i);
            MerchantBatchDetectDto dto = new MerchantBatchDetectDto();

            String uuid = String.valueOf(i + 1);
            dto.setUuid(uuid);
            Address address = item.getAddress();
            if (address != null) {
                dto.setProvince(address.getProvince());
                dto.setCity(address.getCity());
                dto.setPostcode(address.getPostcode());
                dto.setDetail(address.getDetail());
            }
            OrderImportExtraInfoDto onlineOrderRespDTO = orderImportExtraMap.get(item.getId());

            if (onlineOrderRespDTO != null) {
                dto.setBuyerId(onlineOrderRespDTO.getBuyerId());
                dto.setPlatform(item.getMerchantStorePlatformCode());
            }

            checkDtoList.add(dto);
        }

        if (CollectionUtil.isEmpty(checkDtoList)) {
            return;
        }


        Map<String, Integer> checkResult = this.merchantBlacklistFeign.batchDetect(merchantId, checkDtoList);
        for (int i = 0; i < orderList.size(); i++) {
            Order item = orderList.get(i);
            String key = String.valueOf(i + 1);
            item.setBlacklistType(checkResult.getOrDefault(key, MerchantIsBlacklistConstant.UNKNOWN));
        }
    }

    /**
     * @param merchantId
     * @param orders
     * @return
     */
    public Map<Long, MerchantAuthProductParentRespDTO> getAuthProductParentMap(Long merchantId, List<Order> orders) {
        Set<Long> parentIds = new HashSet<>();
        for (Order order : orders) {
            for (OrderItem item : order.getItems()) {
                if (item.getProduct() != null && NumberUtils.greaterZero(item.getProduct().getParentId())) {
                    parentIds.add(item.getProduct().getParentId());
                }
            }
        }
        if (CollUtil.isEmpty(parentIds)) {
            return Collections.emptyMap();
        }
//            productCacheService.findByIds(productIds);
        return this.endProductService.getAuthParentMap(parentIds, merchantId);
    }

    /**
     * 获得订单的成品信息
     */
    public Map<Long, DesignProductRespDto> getDesignProductMaps(Long merchantId, List<Order> orders) {
        List<Long> designProductIds = Lists.newArrayList();
        List<Long> designProductMerchantIds = Lists.newArrayList();
        Map<Long, String> designProductCompoundIdMap = Maps.newHashMap();
        for (Order order : orders) {
            for (OrderItem item : order.getItems()) {
                if (NumberUtils.greaterZero(item.getDesignProductMerchantId())) {
                    designProductMerchantIds.add(item.getDesignProductMerchantId());
                }
                if (NumberUtils.greaterZero(item.getEndProductId())) {
                    designProductIds.add(item.getEndProductId());

                    if (StringUtils.isNotBlank(item.getCompoundId())) {
                        designProductCompoundIdMap.put(item.getEndProductId(), item.getCompoundId());
                    }
                }
            }
        }
        Map<Long, DesignProductRespDto> designProductRespDtoMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(designProductIds) && CollUtil.isNotEmpty(designProductMerchantIds)) {
            List<DesignProductRespDto> designProductRespDtos = this.designProductMCFeign.findBoolUpdateOfficialMaterial(new DesignProductReqDto(merchantId, designProductMerchantIds, designProductIds, "id,status,productId,compoundId,keyId", designProductCompoundIdMap));
            for (DesignProductRespDto designProductRespDto : designProductRespDtos) {
                designProductRespDtoMap.put(designProductRespDto.getId(), designProductRespDto);
            }
        }
        return designProductRespDtoMap;
    }
    /**
     *
     */
    public Map<String, ProductHolidayDto> getVariantIdAndSupplyChainTypeHolidayMaps(List<Order> orders) {
        Set<Long> productIds = Sets.newHashSet();
        for (Order order : orders) {
            for (OrderItem item : order.getItems()) {
                if(NumberUtils.greaterZero(item.getProductId())){
                    productIds.add(item.getId());
                }

            }
        }

        return this.productService.getVariantIdAndSupplyChainTypeHolidayMap(productIds);

    }

    /**
     *
     */
    public Map<Long, OrderItemSupplyChainDTO> getOrderItemSupplyChainMaps(List<Order> orders) {

        List<Long> orderItemIds = Lists.newArrayList();
        for (Order order : orders) {
            for (OrderItem item : order.getItems()) {
                orderItemIds.add(item.getId());
            }
        }

        return this.orderItemSupplyChainFeign.getMapByIds(orderItemIds);

    }

    /**
     *
     */
//    public Map<Long, String> getTailLogisticsChannelCodeByOrderMaps(List<Order> orders) {
//        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
//        return orderTailLogisticsFeign.getTailLogisticsChannelCodeByOrderIds(orderIds);
//
//    }
    public Map<Long, LogisticsChannelRespDto> getTailLogisticsChannelIdByOrderMaps(List<Order> orders) {
        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        return orderTailLogisticsFeign.getTailLogisticsChannelCodeByOrderIds(orderIds);
    }

    /**
     *
     */
    public Map<Long, OrderAmountRespDTO> getAmountMaps(List<Order> orders) {
        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        return orderAmountService.getAmountMap(orderIds);

    }
    /**
     *
     */
    public Map<Long, OrderItemFactoryRespDto> getOrderItemFactoryRespDtoMap(List<Order> orders) {
        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        Map<Long, OrderItemFactoryRespDto> orderItemIdFactoryDtoMap = this.orderService.getOrderItemIdFactoryDtoMap(orderIds);
        return orderItemIdFactoryDtoMap;

    }

    /**
     * 获得订单的线上订单信息
     */
    public Map<Long, OnlineOrderRespDTO> getOnlineOrderMap(List<Order> orders) {
        List<Long> onlineOrderIds = Lists.newArrayList();
        for (Order order : orders) {
            if (order.getOriginType().equals(OrderOriginType.AUTO_IMPORT.getValue())) {
                onlineOrderIds.add(order.getOriginId());
            }
        }
        return this.findOnlineMap(onlineOrderIds);

    }

    /**
     * 获得订单的线上订单信息
     */
    public Map<Long, OnlineOrderRespDTO> findOnlineMap(List<Long> onlineOrderIds) {
        if (CollUtil.isNotEmpty(onlineOrderIds)) {
            IdsSearchHelper searchHelper = new IdsSearchHelper();
            searchHelper.setIds(onlineOrderIds);
            searchHelper.setFields("id,customerId,merchantStorePlatform,statusValue,statusCode,itemRespDTOList");
            List<OnlineOrderRespDTO> list = this.onlineOrderFeign.findByIds(searchHelper);
            return list.stream().collect(Collectors.toMap(OnlineOrderRespDTO::getId, Function.identity()));
        }
        return Maps.newHashMap();
    }

    /**
     * 获得订单的线上订单信息
     */
    public Map<Long, OrderImportExtraInfoDto> findOrderImportExtraMap(List<Order> orders) {
        List<Long> ids = orders.stream().map(Order::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(ids)) {
            IdsSearchHelper searchHelper = new IdsSearchHelper();
            searchHelper.setIds(ids);
            searchHelper.setFields("id,customerId,merchantStorePlatform,statusValue,statusCode,ioss,iossType");
            return this.orderImportExtraInfoFeign.findMapByIds(searchHelper);
        }
        return Maps.newHashMap();
    }

    /**
     * 获得订单的线上订单信息
     */
    public Map<Long, Logistics> findOrderLogisticsMap(List<Order> orders) {
        List<Long> ids = orders.stream().map(Order::getLogisticsId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(ids)) {
            return this.logisticsService.findByIds(ids).stream().collect(Collectors.toMap(Logistics::getId, Function.identity()));
        }
        return Maps.newHashMap();
    }

    public void checkTemuIssuingbayRel(Long orderId, Long logisticsId) {
        if(!NumberUtils.greaterZero(logisticsId)){
            return;
        }
        TenantLogisticsRespDto tenantLogistics = tenantLogisticsFeign.getDtoById(logisticsId);
        Assert.validateNull(tenantLogistics,"物流不存在");
        Order order = orderService.findById(orderId);
        Assert.validateNull(order,"订单不存在");
        // 获取最新发货仓
        Long newIssuingbayId = orderLogisticsUpdateFeign.getNewBayIdByOrderId(orderId,logisticsId);
        Assert.validateTrue(!NumberUtils.greaterZero(newIssuingbayId),"所选物流没有发货仓可用");
        // 判断temu发货仓映射配置
        String checkMsg = getCheckMsg(orderId, newIssuingbayId, order.getMerchantStoreId(), tenantLogistics.getServiceProviderId(), logisticsId);
        Assert.validateTrue(StrUtil.isNotBlank(checkMsg),checkMsg);
    }
}
