package com.sds.platform.sdk.tiktok.auth;


import com.sds.platform.sdk.tiktok.TiktokBaseApi;
import com.sds.platform.sdk.tiktok.base.TiktokApiType;

public class GetAccessTokenApi extends Tik<PERSON><PERSON><PERSON><PERSON>pi<TokenInfoResp> {

    public GetAccessTokenApi(String appSecret, String authCode) {
        super("GET", "/api/v2/token/get");
        this.setEndpoint("https://auth.tiktok-shops.com");
        this.addQuery("auth_code", authCode);
        this.addQuery("app_secret", appSecret);
        this.addQuery("grant_type", "authorized_code");
        TiktokApiType apiType = new TiktokApiType()
            .setNeedSign(false)
            .setNeedShopCipher(false)
            .setNeedToken(false);
        this.setApiSignType(apiType);
    }
}
