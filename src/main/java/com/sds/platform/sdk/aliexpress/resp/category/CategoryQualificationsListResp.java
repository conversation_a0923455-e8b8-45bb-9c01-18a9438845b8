package com.sds.platform.sdk.aliexpress.resp.category;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/2
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class CategoryQualificationsListResp implements Serializable {
    private static final long serialVersionUID = 2808586477499228994L;
    /**
     * code
     */
    private String code;
    /**
     * requestId
     */
    @JSONField(name = "request_id")
    private String requestId;
    /**
     * support
     */
    private String support;
    /**
     * qualificationModuleList
     */
    @JSONField(name = "qualification_module_list")
    private List<QualificationModuleListDTO> qualificationModuleList;

    /**
     * QualificationModuleListDTO
     */
    @NoArgsConstructor
    @Data
    public static class QualificationModuleListDTO {
        /**
         * label
         */
        private String label;
        /**
         * type
         */
        private String type;
        /**
         * key
         */
        private String key;
        /**
         * tips
         */
        private String tips;
        /**
         * required
         */
        private String required;
    }
}
