package com.sds.platform.sdk.temu.order;


import com.alibaba.fastjson.JSONObject;
import com.sds.platform.sdk.temu.TemuBaseApi;
import com.sds.platform.sdk.temu.constant.OrderMall;

@OrderMall
public class OrderShippingInfoGet<PERSON>pi extends TemuBaseApi<OrderShippingInfoResp> {

    private static final String TYPE = "bg.order.shippinginfo.get";

    public OrderShippingInfoGetApi(String parentOrderSn, boolean enableLogReq) {
        super(TYPE, OrderShippingInfoResp.class);
        JSONObject req = new JSONObject();
        req.put("parentOrderSn", parentOrderSn);
        this.setRequestBody(req);
        this.setLogReq(enableLogReq);
    }
}
