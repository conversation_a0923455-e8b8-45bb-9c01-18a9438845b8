package com.sds.platform.sdk.temu.good;

import com.alibaba.fastjson.JSONObject;
import com.sds.platform.sdk.temu.TemuBaseApi;
import com.sds.platform.sdk.temu.constant.ProductMall;

@ProductMall
public class GoodListGetApi extends TemuBaseApi<JSONObject> {
    private static final String TYPE = "bg.goods.list.get";

    public GoodListGetApi(GoodListGetReq req) {
        super(TYPE, JSONObject.class);
        this.setRequestBody(req);
    }
}
