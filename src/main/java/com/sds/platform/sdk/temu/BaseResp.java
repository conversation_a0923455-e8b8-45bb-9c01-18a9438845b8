package com.sds.platform.sdk.temu;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class BaseResp<T> {

    @JSONField(name = "requestId")
    private String requestId;

    @JSONField(name = "errorCode")
    private String errorCode;

    @JSONField(name = "success")
    private Boolean success;

    @JSONField(name = "errorMsg")
    private String errorMsg;

    @JSONField(name = "result")
    private T result;
}
