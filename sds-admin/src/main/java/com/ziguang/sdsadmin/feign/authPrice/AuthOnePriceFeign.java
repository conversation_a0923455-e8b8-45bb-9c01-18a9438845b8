package com.ziguang.sdsadmin.feign.authPrice;

import com.sdsdiy.common.base.constant.MicroServiceEndpointConstant;
import com.sdsdiy.productapi.api.authPrice.AuthOnePriceApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "service-product", contextId = "authOnePriceFeign", url = MicroServiceEndpointConstant.SERVICE_PRODUCT)
public interface AuthOnePriceFeign extends AuthOnePriceApi {
}
