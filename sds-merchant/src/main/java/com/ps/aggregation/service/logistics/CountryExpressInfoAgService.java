package com.ps.aggregation.service.logistics;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.ps.ps.feign.ProductFeign;
import com.ps.ps.feign.issuingbay.IssuingBayAreaFeign;
import com.ps.ps.feign.logistics.LogisticsExpensesFeign;
import com.ps.ps.feign.logistics.LogisticsProductFeign;
import com.ps.ps.feign.product.ProductSupplyFeign;
import com.ps.ps.feign.tenant.TenantFeign;
import com.ps.ps.service.CountryExpressInfoService;
import com.ps.ps.service.MerchantExchangeRateService;
import com.ps.ps.service.ProductService;
import com.ps.support.Assert;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseListReqDto;
import com.sdsdiy.common.base.enums.CountryCurrencyCodeEnum;
import com.sdsdiy.common.base.helper.NumberUtils;
import com.sdsdiy.issuingbayapi.dto.base.IssuingBayAreaRespDto;
import com.sdsdiy.logisticsapi.dto.*;
import com.sdsdiy.orderdata.constant.order.SupplyChainTypeEnum;
import com.sdsdiy.productapi.dto.ProductDto;
import com.sdsdiy.productapi.dto.ProductSupplyDTO;
import com.sdsdiy.productapi.dto.product.ProductCalProfitParam;
import com.sdsdiy.productapi.dto.product.ProductCalProfitResp;
import com.sdsdiy.productapi.myenum.EnumProductStatus;
import com.sdsdiy.userapi.constant.enums.DistributionProductLogisticsSourceEnum;
import com.sdsdiy.userapi.dto.tenant.resp.TenantLogisticsSourceResp;
import com.ziguang.base.dto.ExchangeRateConfigDTO;
import com.ziguang.base.model.Product;
import com.ziguang.base.support.contant.SiteCountry;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sdsdiy.common.base.constant.BasePoConstant.YES;

/**
 * @author: bin_lin
 * @date: 2020/8/7 18:02
 * @desc:
 */
@Service
public class CountryExpressInfoAgService {
    @Resource
    private ProductService productService;
    @Resource
    private LogisticsProductFeign logisticsProductFeign;
    @Resource
    private CountryExpressInfoService countryExpressInfoService;
    @Resource
    private MerchantExchangeRateService merchantExchangeRateService;
    @Resource
    private LogisticsExpensesFeign logisticsExpensesFeign;
    @Resource
    private ProductSupplyFeign productSupplyFeign;
    @Resource
    private IssuingBayAreaFeign issuingBayAreaFeign;
    @Resource
    private TenantFeign tenantFeign;
    @Resource
    private ProductFeign productFeign;


    public List<ReferTransferCurrencyRespDto> rangeFreightByProductVariantIds(FreightByProductVariantIdsParam param) {
        List<Product> productList = productService.findByIds(param.getProductVariantIds());
        if (CollectionUtil.isEmpty(productList)) {
            return Lists.newArrayList();
        }
        IssuingBayAreaRespDto issuingArea = getFirstPriorityIssuingArea(param.getProductVariantIds());
        if (issuingArea == null) {
            return Lists.newArrayList();
        }
        ReferTransferCurrencyDto transferCurrencyDto = new ReferTransferCurrencyDto();
        transferCurrencyDto.setCountryCode(param.getCountryCode());
        transferCurrencyDto.setLogisticsType(param.getLogisticsType());
        transferCurrencyDto.setProductIdList(param.getProductVariantIds());
        transferCurrencyDto.setIssuingAreaId(issuingArea.getId());
        transferCurrencyDto.setTenantId(param.getTenantId());
        transferCurrencyDto.setIsCheckWeight(param.getIsCheckWeight());
        List<ReferTransferCurrencyRespDto> freightRange = getFreightRange(transferCurrencyDto, productList);
        if (CollectionUtil.isNotEmpty(freightRange)) {
            Map<Long, List<Product>> productListMap = productList.stream().collect(Collectors.groupingBy(Product::getParentId));
            TenantLogisticsSourceResp logisticsSource = tenantFeign.getDistributionProductLogisticsSource(param.getTenantId());
            List<LogisticsProductRespDto> logisticsProductList;
            if (logisticsSource != null && DistributionProductLogisticsSourceEnum.ORDER_TENANT.name().equals(logisticsSource.getDistributionProductLogisticsSource())
                    && TenantCommonConstant.SDSDIY_TENANT_ID.equals(issuingArea.getTenantId())) {
                logisticsProductList = logisticsProductFeign.getByProductIds(param.getTenantId(), new BaseListReqDto(Lists.newArrayList(productListMap.keySet())));
            } else {
                logisticsProductList = logisticsProductFeign.getByProductIds(issuingArea.getTenantId(), new BaseListReqDto(Lists.newArrayList(productListMap.keySet())));
            }
            List<Long> bindLogisticsId = logisticsProductList.stream().map(LogisticsProductRespDto::getLogisticsId).collect(Collectors.toList());
            freightRange = freightRange.stream()
                    .filter(freight -> freight.getLogistics() != null && (bindLogisticsId.contains(freight.getLogistics().getId()) || YES.equals(freight.getLogistics().getAllProductStatus())))
                    .collect(Collectors.toList());
        }
        return freightRange;
    }


    private IssuingBayAreaRespDto getFirstPriorityIssuingArea(List<Long> variantIds) {
        // 过滤掉产能=0的
        List<ProductSupplyDTO> supplies = productSupplyFeign.findOnlineSupplyByVariantIds(variantIds, null, "id,productId,issuingAreaId,distributionPercent");
        supplies = supplies.stream().filter(s -> NumberUtils.greaterZero(s.getDistributionPercent())).collect(Collectors.toList());
        if (CollUtil.isEmpty(supplies)) {
            return null;
        }
        //求出区域并集
        Map<Long, List<Long>> productIdKeyAreaIdsMap = supplies.stream().collect(Collectors.groupingBy(ProductSupplyDTO::getProductId, Collectors.mapping(ProductSupplyDTO::getIssuingAreaId, Collectors.toList())));
        List<Long> issuingAreaIds = Lists.newArrayList();
        for (int i = 0; i < variantIds.size(); i++) {
            Long variantId = variantIds.get(i);
            List<Long> productIssuingAreaIds = productIdKeyAreaIdsMap.get(variantId);
            if (CollectionUtil.isEmpty(productIssuingAreaIds)) {
                return null;
            }
            if (i == 0) {
                issuingAreaIds = productIssuingAreaIds;
                continue;
            }
            issuingAreaIds.retainAll(productIssuingAreaIds);
            if (CollectionUtil.isEmpty(issuingAreaIds)) {
                return null;
            }
        }
        BaseListReqDto baseListReqDto = new BaseListReqDto();
        baseListReqDto.setIdList(issuingAreaIds);
        baseListReqDto.setFields("priority");
        List<IssuingBayAreaRespDto> issuingBayAreas = issuingBayAreaFeign.findByIds(baseListReqDto);
        if (CollectionUtil.isEmpty(issuingBayAreas)) {
            return null;
        }
        return issuingBayAreas.stream().min(Comparator.comparing(IssuingBayAreaRespDto::getPriority)).get();
    }

    public List<ReferTransferCurrencyRespDto> freight(Long tenantId, Long productId, String countyCode) {
        ReferTransferCurrencyDto referDto = new ReferTransferCurrencyDto();
        referDto.setLogisticsType(2);
        referDto.setProductIdList(Lists.newArrayList(productId));
        referDto.setCountryCode(countyCode);
        Product product = productService.findById(productId);
        IssuingBayAreaRespDto firstPriorityIssuingArea = getFirstPriorityIssuingArea(Lists.newArrayList(productId));
        if (firstPriorityIssuingArea == null) {
            return Lists.newArrayList();
        }
        LogisticsFreightProductReqDto minProductReqDto = BeanUtil.toBean(product, LogisticsFreightProductReqDto.class);
        minProductReqDto.setNum(1);
        minProductReqDto.setWeight(product.getWeight());
        referDto.setProductMinReqDto(minProductReqDto);
        referDto.setTenantId(tenantId);
        LogisticsFreightProductReqDto maxProductReqDto = BeanUtil.toBean(product, LogisticsFreightProductReqDto.class);
        maxProductReqDto.setNum(1);
        maxProductReqDto.setWeight(product.getWeight());
        referDto.setProductMaxReqDto(maxProductReqDto);
        referDto.setIsMatchProductLogistics(YES);
        referDto.setIssuingAreaId(firstPriorityIssuingArea.getId());
        List<ReferTransferCurrencyRespDto> referTransferCurrencyResp = logisticsExpensesFeign.transferCurrency(referDto);
        Map<Long, ReferTransferCurrencyRespDto> currencyRespDtoMap = referTransferCurrencyResp.stream()
                .collect(Collectors.toMap(r -> r.getLogistics().getId(), Function.identity(), (r1, r2) -> r1.getFreightStart() <= r2.getFreightStart()?r1:r2));
        List<Long> currencyIds = currencyRespDtoMap.values().stream().map(ReferTransferCurrencyRespDto::getId).collect(Collectors.toList());
        return referTransferCurrencyResp.stream().filter(r -> currencyIds.contains(r.getId())).collect(Collectors.toList());
    }


    public List<ReferTransferCurrencyRespDto> freightRange(ReferTransferCurrencyDto referDto) {
        return getFreightRange(referDto);
    }

    public List<ReferTransferCurrencyRespDto> transferCurrency(ReferTransferCurrencyDto referDto, Long merchantId) {
        //查询所有产品信息获取最重的重量级最轻的重量
        List<ReferTransferCurrencyRespDto> result = getFreightRange(referDto);
        if (referDto.getCurrencyCode() != null && CountryCurrencyCodeEnum.CN.getCurrencyCode().equals(referDto.getCurrencyCode())) {
            for (ReferTransferCurrencyRespDto infoNew : result) {
                CountryCurrencyInfo currencyInfo = new CountryCurrencyInfo();
                currencyInfo.setCurrencyCode(CountryCurrencyCodeEnum.CN.getCurrencyCode());
                currencyInfo.setCurrencyName(CountryCurrencyCodeEnum.CN.getCurrencyName());
                currencyInfo.setRate(1D);
                currencyInfo.setCurrencySign(CountryCurrencyCodeEnum.CN.getSign());
                infoNew.setCountryCurrencyInfo(currencyInfo);
            }
            return result;
        }
        //获取义人民币为基准的各国费率
        Map<String, ExchangeRateConfigDTO> exchangeRate = merchantExchangeRateService.getExchangeRate(merchantId);
        ExchangeRateConfigDTO rateConfigDTO;
        if (StringUtils.isEmpty(referDto.getCurrencyCode())) {
            SiteCountry siteCountry = SiteCountry.checkByCode(referDto.getCountryCode());
            Assert.validateNull(siteCountry, "未收录对应国家简码信息！！");
            rateConfigDTO = exchangeRate.get(siteCountry.getCurrency());
        } else {
            rateConfigDTO = exchangeRate.get(referDto.getCurrencyCode());
        }
        Assert.validateNull(rateConfigDTO, "未有该对应国家的费率！！");
        //转换成各国费用
        for (ReferTransferCurrencyRespDto infoNew : result) {
            Double startFreight = infoNew.getFreightStart();
            Double endFreight = infoNew.getFreightEnd();
            infoNew.setFreightStart(transformCurrency(rateConfigDTO.getRate(), startFreight));
            infoNew.setFreightEnd(transformCurrency(rateConfigDTO.getRate(), endFreight));
            CountryCurrencyInfo currencyInfo = BeanUtil.toBean(rateConfigDTO, CountryCurrencyInfo.class);
            infoNew.setCountryCurrencyInfo(currencyInfo);
        }
        return result;
    }

    public ProductCalProfitResp getCurrentProductCalProfit(ProductCalProfitParam param) {
        List<Long> productIds = Lists.newArrayList(param.getProductVariantId());
        IssuingBayAreaRespDto firstPriorityIssuingArea = this.getFirstPriorityIssuingArea(productIds);
        Assert.validateNull(firstPriorityIssuingArea, "产品区域异常");
        Product product = productService.findById(param.getProductVariantId());
        Assert.validateNull(product, "产品区域异常");

        List<LogisticsFreightProductReqDto> productList = Lists.newArrayList();

        LogisticsFreightProductReqDto productReqDto = new LogisticsFreightProductReqDto();
        productReqDto.setNum(param.getPurchaseNum());
        productReqDto.setMergeNum(param.getPurchaseNum());
        productReqDto.setSupplyChainType(SupplyChainTypeEnum.ONE_PIECE.name());
        productReqDto.setId(product.getId());
        productReqDto.setWeight(product.getWeight());
        productReqDto.setBoxHeight(product.getBoxHeight());
        productReqDto.setBoxLength(product.getBoxLength());
        productReqDto.setBoxWidth(product.getBoxWidth());
        productList.add(productReqDto);

        LogisticsFreightReqDto logisticsFreightReqDto = new LogisticsFreightReqDto();
        logisticsFreightReqDto.setLogisticsId(param.getLogisticsId());
        logisticsFreightReqDto.setLogisticsType(2);
        logisticsFreightReqDto.setCountryCode(param.getCountryCode());
        logisticsFreightReqDto.setProductList(productList);
        logisticsFreightReqDto.setIssuingBayAreaId(firstPriorityIssuingArea.getId());
        logisticsFreightReqDto.setTenantId(param.getTenantId());
        logisticsFreightReqDto.setMerchantId(param.getMerchantId());
        countryExpressInfoService.formatGoodsTotalPrice(logisticsFreightReqDto);
        List<LogisticsFreightRespDto> refer = this.logisticsExpensesFeign.findRefer(logisticsFreightReqDto);
        Assert.validateEmpty(refer, "未查询物流费用");
        Double freight = refer.get(0).getFreight();
        BigDecimal logisticsCost = BigDecimal.valueOf(freight);
        BigDecimal otherCost = param.getOtherCost().multiply(BigDecimal.valueOf(param.getPurchaseNum()));
        BigDecimal salePriceTotal = param.getSalePrice().multiply(BigDecimal.valueOf(param.getPurchaseNum()));
        BigDecimal commissionTotalCost = param.getCommissionRate().divide(BigDecimal.valueOf(100)).multiply(salePriceTotal);
        BigDecimal costTotal = logisticsFreightReqDto.getGoodsTotalPrice()
                .add(logisticsCost)
                .add(otherCost)
                .add(commissionTotalCost);

        BigDecimal profitTotal = salePriceTotal.subtract(costTotal);
        ProductCalProfitResp productCalProfitResp = new ProductCalProfitResp();
        productCalProfitResp.setCostTotal(costTotal);
        productCalProfitResp.setProfitTotal(profitTotal);
        productCalProfitResp.setProfitTotalRate(NumberUtil.div(profitTotal,costTotal,4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        productCalProfitResp.setLogisticsCost(logisticsCost);
        productCalProfitResp.setProductCost(logisticsFreightReqDto.getGoodsTotalPrice());
        productCalProfitResp.setSalePriceTotal(salePriceTotal);
        productCalProfitResp.setCommissionTotalCost(commissionTotalCost);
        productCalProfitResp.setOtherCost(otherCost);
        return productCalProfitResp;
    }

    public List<ReferTransferCurrencyRespDto> getFreightRange(ReferTransferCurrencyDto referDto) {
        //查询所有产品信息获取最重的重量级最轻的重量
        List<Long> productIds = Lists.newArrayList();
        referDto.getProductIdList().forEach(id -> {
            List<ProductDto> productAllList = productFeign.findByParentId(id, EnumProductStatus.ONLINE.getStatus(), "");
            productIds.addAll(productAllList.stream().map(ProductDto::getId).collect(Collectors.toList()));
        });
        if (CollectionUtil.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        List<Product> productList = productService.findByIds(productIds);
        IssuingBayAreaRespDto firstPriorityIssuingArea = getFirstPriorityIssuingArea(productIds);
        if (firstPriorityIssuingArea == null) {
            return Lists.newArrayList();
        }
        referDto.setIssuingAreaId(firstPriorityIssuingArea.getId());
        return getFreightRange(referDto, productList);
    }

    private List<ReferTransferCurrencyRespDto> getFreightRange(ReferTransferCurrencyDto referDto, List<Product> productList) {
        Product productMin = productList.stream().min(Comparator.comparing(Product::getWeight)).get();
        Product productMax = productList.stream().max(Comparator.comparing(Product::getWeight)).get();
        Product productBulkMax = productList.stream().max(Comparator.comparing(product -> product.getBoxHeight() * product.getBoxLength() * product.getBoxWidth())).get();
        Product productBulkMin = productList.stream().min(Comparator.comparing(product -> product.getBoxHeight() * product.getBoxLength() * product.getBoxWidth())).get();
        LogisticsFreightProductReqDto minProductReqDto = BeanUtil.toBean(productBulkMin, LogisticsFreightProductReqDto.class);
        minProductReqDto.setNum(1);
        minProductReqDto.setWeight(productMin.getWeight());

        referDto.setProductMinReqDto(minProductReqDto);
        LogisticsFreightProductReqDto maxProductReqDto = BeanUtil.toBean(productBulkMax, LogisticsFreightProductReqDto.class);
        maxProductReqDto.setNum(1);
        maxProductReqDto.setWeight(productMax.getWeight());
        referDto.setProductMaxReqDto(maxProductReqDto);
        return logisticsExpensesFeign.transferCurrency(referDto);
    }


    private Double transformCurrency(Double rate, Double freight) {
        if (freight != null) {
            return NumberUtil.div(freight, rate, 2);
        }
        return null;
    }


}

