package com.ps.aggregation.controller.user;

import com.ps.ps.feign.crm.CrmUserFeign;
import com.ps.support.ISecurityUtils;
import com.sdsdiy.userapi.dto.crm.resp.CrmUserContactDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: zmy
 */
@Api("业务员")
@RestController
@RequestMapping("/crmUsers")
public class CrmUserController {

    @Resource
    private CrmUserFeign crmUserFeign;

    @ApiOperation("业务咨询联系人")
    @GetMapping("contact")
    public CrmUserContactDTO contactPerson() {
        Long merchantId=null;
        try {
            merchantId = ISecurityUtils.getMerchantId();
        } catch (Exception e) {
        }
        return crmUserFeign.merchantContact(merchantId);
    }
}