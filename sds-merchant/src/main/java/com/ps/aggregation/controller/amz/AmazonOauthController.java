package com.ps.aggregation.controller.amz;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ps.exception.BusinessException;
import com.ps.param.AmzOauthBo;
import com.ps.ps.feign.amz.AmzAuthFeign;
import com.ps.ps.feign.user.MerchantStoreFeign;
import com.ps.ps.service.LockService;
import com.ps.ps.service.MerchantService;
import com.ps.ps.service.MerchantStoreService;
import com.ps.support.ISecurityUtils;
import com.ps.support.redis.RedisUtil;
import com.ps.system.service.MerchantUserService;
import com.sdsdiy.common.base.enums.MerchantStorePlatformEnum;
import com.sdsdiy.common.base.helper.Assert;
import com.sdsdiy.ecommerceapi.constant.AmazonMarketplaceEnum;
import com.sdsdiy.ecommerceapi.dto.amazon.AmazonOauthUrlVo;
import com.sdsdiy.ecommerceapi.dto.amazon.AmzAuthCallbackDto;
import com.sdsdiy.ecommerceapi.dto.amazon.AmzOauthRecordDto;
import com.sdsdiy.ecommerceapi.param.amz.AmzAuthCallbackParam;
import com.sdsdiy.ecommerceapi.param.amz.AmzAuthUrlParam;
import com.sdsdiy.ecommerceapi.param.amz.AmzOauthLoginParam;
import com.sdsdiy.ecommercedata.param.amazon.AmzOauthLoginVo;
import com.sdsdiy.userapi.param.UpdateStoreAfterOauthSuccessParam;
import com.ziguang.base.model.Merchant;
import com.ziguang.base.model.MerchantStore;
import com.ziguang.base.model.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/amazon")
@Slf4j
@RequiredArgsConstructor
@Api("亚马逊授权")
public class AmazonOauthController {

    private final AmzAuthFeign amzAuthFeign;
    private final MerchantStoreService merchantStoreService;
    private final MerchantStoreFeign merchantStoreFeign;
    private final LockService lockService;
    private final RedisUtil redisUtil;
    private final MerchantService merchantService;
    private final MerchantUserService merchantUserService;

    private static String amzAuthSellingPartnerIdLockKey(String sellerId) {
        return "amz:oauth:selling_id:" + sellerId;
    }
    
    @ApiOperation(value = "生成授权跳转地址")
    @ResponseBody
    @GetMapping("/oauth/{merchantStoreId}/genOauthUrl")
    public AmazonOauthUrlVo genOauthUrl(
        @PathVariable Long merchantStoreId,
        @ApiParam("当前域名") @RequestParam(value = "oauthDomain", required = true) String domain
    ) {
        MerchantStore merchantStore = merchantStoreService.getStore(merchantStoreId);
        Assert.validateNull(merchantStore, "店铺不存在");

        String siteDepartment = merchantStore.getSiteDepartment();
        AmazonMarketplaceEnum marketplaceEnum = AmazonMarketplaceEnum.getGetByCountryCode(siteDepartment);
        if (ObjectUtil.isNull(marketplaceEnum)) {
            throw new BusinessException("店铺站点异常");
        }

        long tenantId = ISecurityUtils.getTenantId();
        long merchantId = ISecurityUtils.getMerchantId();
        Long currentUserId = ISecurityUtils.getCurrUserId();

        AmzAuthUrlParam param = AmzAuthUrlParam.builder()
            .currentUserId(currentUserId)
            .merchantStoreId(merchantStoreId)
            .amzCountryCode(siteDepartment)
            .merchantId(merchantId)
            .tenantId(tenantId)
            .oauthDomain(domain)
            .build();
        return amzAuthFeign.generateOauthUrl(param);
    }
    
    /**
     * 发起授权，前端调用，成功返回亚马逊跳转地址给前端由前端跳转
     */
    @ResponseBody
    @ApiOperation(value = "亚马逊授权-登陆", notes = "这个接口是配置在亚马逊应用后台的地址，需要兼容多租户的场景，所以这里不校验token")
//    @GetMapping("/oauth/oauthLogin")
    public AmzOauthLoginVo oauthLogin(
        @ApiParam("亚马逊参数-跳转地址") @RequestParam(value = "amazon_callback_uri", required = true) String amzCallbackUri,
        @ApiParam("亚马逊参数-state") @RequestParam(value = "amazon_state", required = true) String amazonState,
        @ApiParam("state参数") @RequestParam(value = "state", required = true) String state,
        @ApiParam("亚马逊参数-sellerId") @RequestParam(value = "selling_partner_id", required = true) String sellingPartnerId,
        @ApiParam("亚马逊参数-应用版本") @RequestParam(value = "version", required = false) String version
    ) {
        String sellerIdLockKey = amzAuthSellingPartnerIdLockKey(sellingPartnerId);
        if (!redisUtil.tryLock(sellerIdLockKey, 300L)) {
            return AmzOauthLoginVo.builder()
                .success(false)
                .msg("当前亚马逊店铺正在授权，请稍后再试")
                .build();
        }

        AmzOauthLoginParam param = AmzOauthLoginParam.builder()
            .version(version)
            .sellingPartnerId(sellingPartnerId)
            .state(state)
            .amazonCallbackUri(amzCallbackUri)
            .amazonState(amazonState)
            .build();
        log.info("amz oauthLogin sellingPartnerId={} param={}", param.getSellingPartnerId(), JSON.toJSONString(param));
        AmzOauthRecordDto recordDto = null;
        AmzOauthLoginVo amzOauthLoginVo = new AmzOauthLoginVo();
        amzOauthLoginVo.setState(state);

        try {
            recordDto = amzAuthFeign.oauthLogin(param);

            amzOauthLoginVo = attachAuthUserAndStore(amzOauthLoginVo, recordDto);

            amzOauthLoginVo.setSuccess(true);
            amzOauthLoginVo.setAmzCallbackUri(recordDto.getOauthLoginRedirectUrl());
        } catch (BusinessException e) {
            amzOauthLoginVo.setSuccess(false);
            amzOauthLoginVo.setMsg(e.getMessage());
        }

        redisUtil.unlock(sellerIdLockKey);
        return amzOauthLoginVo;
    }

    public AmzOauthLoginVo attachAuthUserAndStore(AmzOauthLoginVo amzOauthLoginVo, AmzOauthRecordDto recordDto) {
        long oauthMerchantStoreId = recordDto.getMerchantStoreId();
        amzOauthLoginVo.setMerchantStoreId(oauthMerchantStoreId);

        MerchantStore store = checkWantOauthStore(oauthMerchantStoreId, recordDto.getSellerId(), recordDto.getRegion());
        amzOauthLoginVo.setMerchantStoreName(store.getName());

        Merchant merchant = merchantService.findById(recordDto.getMerchantId());
        amzOauthLoginVo.setMerchantName(merchant.getName());
        amzOauthLoginVo.setMerchantId(merchant.getId());

        User user = merchantUserService.findById(recordDto.getAuthUserId());
        amzOauthLoginVo.setUserId(user.getId());
        amzOauthLoginVo.setUserName(user.getUsername());

        amzOauthLoginVo.setSuccess(true);
        amzOauthLoginVo.setAmzCallbackUri(recordDto.getOauthLoginRedirectUrl());

        return amzOauthLoginVo;
    }

    private MerchantStore checkWantOauthStore(long merchantStoreId, String sellerId, String regionCode) throws BusinessException {
        MerchantStore wantAuthMerchantStore = merchantStoreService.getStore(merchantStoreId);
        if (wantAuthMerchantStore == null || !MerchantStorePlatformEnum.AMZ.getCode().equals(wantAuthMerchantStore.getMerchantStorePlatformCode())) {
            throw new BusinessException("授权的店铺异常");
        }

        AmazonMarketplaceEnum marketplaceEnum = AmazonMarketplaceEnum.getGetByCountryCode(wantAuthMerchantStore.getSiteDepartment());
        if (marketplaceEnum == null || !marketplaceEnum.getRegionEnum().getRegionCode().equals(regionCode)) {
            throw new BusinessException("店铺站点异常");
        }
        if (StrUtil.isNotEmpty(wantAuthMerchantStore.getSellerId())
            && !wantAuthMerchantStore.getSellerId().contains(sellerId)) {
            throw new BusinessException("当前店铺已经绑定其他亚马逊账号");
        }
        return wantAuthMerchantStore;
    }
    
    
    /**
     * 授权回调，提供给亚马逊调用
     *
     * @param mwsAuthToken
     * @param spapiOauthCode
     * @param sellingPartnerId
     * @param state
     * @param httpServletRequest
     * @param httpServletResponse
     * @throws IOException
     */
    @ResponseBody
    @ApiOperation(value = "亚马逊授权-重定向", notes = "亚马逊处理完成后重定向地址")
    @GetMapping("/auth/callback")
    public void oauthCallback(
        @ApiParam("亚马逊参数 mwsToken") @RequestParam(value = "mws_auth_token", required = false) String mwsAuthToken,
        @ApiParam("亚马逊参数 spapi_oauth_code") @RequestParam(value = "spapi_oauth_code") String spapiOauthCode,
        @ApiParam("亚马逊参数 selling_partner_id") @RequestParam(value = "selling_partner_id") String sellingPartnerId,
        @ApiParam("亚马逊参数 state") @RequestParam(value = "state") String state,
        HttpServletRequest httpServletRequest,
        HttpServletResponse httpServletResponse
    ) throws IOException {
        log.info("receive amz oauth callback sellingPartnerId={}, state={}", sellingPartnerId, state);

        AmzOauthRecordDto oauthRecord = amzAuthFeign.getOauthRecordWhenCallback(state, sellingPartnerId);
        if (oauthRecord == null) {
            throw new BusinessException("数据异常，授权记录不存在");
        }

        log.info("oauth record={}", JSON.toJSONString(oauthRecord));

        AmzOauthBo oauthBo = AmzOauthBo.builder()
            .sellerId(sellingPartnerId)
            .state(state)
            .oauthFinishRedirectUrl(oauthRecord.getOauthFinishRedirectUrl())
            .build();

        if (oauthRecord.getUserCount() > 1 || !sellingPartnerId.equals(oauthRecord.getSellerId())) {
            oauthBo.setSuccess(false);
            oauthBo.setMsg("授权记录已经过期，请重新发起");
            oauthFinish(httpServletResponse, oauthBo);
            return;
        }

        String sellerIdLockKey = amzAuthSellingPartnerIdLockKey(sellingPartnerId);
        if (!lockService.tryLock(sellerIdLockKey, 0L, 300 * 1000L)) {
            oauthBo.setSuccess(false);
            oauthBo.setMsg("当前亚马逊店铺正在授权");
            oauthFinish(httpServletResponse, oauthBo);
            return;
        }

        AmzAuthCallbackParam param = AmzAuthCallbackParam.builder()
            .mwsAuthToken(mwsAuthToken)
            .state(state)
            .spapiOauthCode(spapiOauthCode)
            .sellingPartnerId(sellingPartnerId)
            .build();
        log.info("receive amz auth callback sellingPartnerId={}, param={}", sellingPartnerId, JSON.toJSONString(param));

        long merchantStoreId = oauthRecord.getMerchantStoreId();
        try {
            checkWantOauthStore(merchantStoreId, oauthRecord.getSellerId(), oauthRecord.getRegion());
        } catch (BusinessException e) {
            oauthBo.setSuccess(false);
            oauthBo.setMsg(e.getMessage());
            oauthFinish(httpServletResponse, oauthBo);
            return;
        }

        AmzAuthCallbackDto callbackDto = amzAuthFeign.callback(param);
        log.info("amz callback ecommerce result={}", JSON.toJSONString(callbackDto));
        if (Boolean.FALSE.equals(callbackDto.getSuccess())) {
            oauthBo.setSuccess(false);
            oauthBo.setMsg(callbackDto.getMsg());
            oauthFinish(httpServletResponse, oauthBo);
            return;
        }

        UpdateStoreAfterOauthSuccessParam updateStoreAfterOauthSuccessParam = UpdateStoreAfterOauthSuccessParam.builder()
            .platformCode(MerchantStorePlatformEnum.AMZ.getCode())
            .sellerId(callbackDto.getAmzMerchant().getSellingPartnerId())
            .wantOauthMerchantStoreId(callbackDto.getAmzMerchant().getMerchantStoreId())
            .build();
        log.info("amz callback begin update store param={}", JSON.toJSONString(callbackDto));
        merchantStoreFeign.updateStoreAfterOauthSuccess(callbackDto.getAmzMerchant().getMerchantId(), updateStoreAfterOauthSuccessParam);
        log.info("amz callback begin update store result={}", JSON.toJSONString(callbackDto));

        oauthBo.setSuccess(true);
        oauthFinish(httpServletResponse, oauthBo);
    }

    public void oauthFinish(HttpServletResponse response, AmzOauthBo amzOauthBo) throws IOException {
        String lockKey = amzAuthSellingPartnerIdLockKey(amzOauthBo.getSellerId());
        lockService.unlockByCurrentThread(lockKey);
        String realUrl = generateRealOauthFinishUrl(amzOauthBo.getOauthFinishRedirectUrl(), amzOauthBo.getSuccess(), amzOauthBo.getMsg());
        response.sendRedirect(realUrl);
    }

    private String generateRealOauthFinishUrl(String url, boolean success, String msg) throws UnsupportedEncodingException {
        url = url + "?success=" + success;
        if (Boolean.FALSE.equals(success)) {
            url += "&msg=" + URLEncoder.encode(msg, "UTF-8");
        }
        return url;
    }
}

