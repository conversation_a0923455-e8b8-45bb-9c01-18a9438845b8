package com.ziguang.sds.aggr.saas.controller.payment;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ps.ps.feign.payment.MerchantBillFeign;
import com.ps.ps.feign.payment.TransactionFeign;
import com.ps.ps.feign.stat.DataExportRecordFeign;
import com.ps.ps.feign.tenant.TenantFeign;
import com.ps.tool.EasyExcelUtil;
import com.sdsdiy.common.base.constant.CommonConstant;
import com.sdsdiy.common.base.constant.tenant.TenantCommonConstant;
import com.sdsdiy.common.base.entity.dto.BaseIdAndNameDTO;
import com.sdsdiy.common.base.entity.dto.BaseListDto;
import com.sdsdiy.common.base.entity.dto.PageResultDto;
import com.sdsdiy.common.base.helper.ListUtil;
import com.sdsdiy.paymentapi.constant.*;
import com.sdsdiy.paymentapi.dto.BillMonthlyAmountOfMerchantVo;
import com.sdsdiy.paymentapi.dto.MerchantBillVo;
import com.sdsdiy.paymentapi.dto.SaasTotalBillMonthlyAmountDto;
import com.sdsdiy.paymentapi.param.bill.BillQueryParam;
import com.sdsdiy.statdata.constant.DataExportRecordTypeEnum;
import com.sdsdiy.statdata.dto.export.DataExportRecordCreateDTO;
import com.ziguang.sds.aggr.saas.shiro.ISecurityUtils;
import com.ziguang.sds.aggr.saas.shiro.ShiroUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/saas/merchantBill")
@Slf4j
@RequiredArgsConstructor
@Api("SAAS-全部账单")
public class SaasBillController {
    private final TenantFeign tenantFeign;
    private final MerchantBillFeign merchantBillFeign;
    private final DataExportRecordFeign dataExportRecordFeign;
    private final TransactionFeign transactionFeign;


    @PostMapping("/admin/refundPayment")
    @ApiOperation(value = "平台账单-金额统计", notes = "")
    public void adminRefundPayment(@RequestParam String tradeNo) {
        long currentId = ISecurityUtils.getCurrUserId();
        transactionFeign.adminSystemRefundPayment(tradeNo, currentId);
    }


    @PostMapping("/platformBill/amountStat")
    @ApiOperation(value = "平台账单-金额统计", notes = "")
    public SaasTotalBillMonthlyAmountDto platformBillAmountStat(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.SAAS.getCode())
                .setCurrentViewRoleId(TenantCommonConstant.SAAS_TENANT_ID);
        return merchantBillFeign.monthlyAmountStatForSaasPlatformBill(queryParam);
    }


    @PostMapping("/platformBill/query")
    @RequiresPermissions("payment:saas_total_bill")
    @ApiOperation(value = "平台账单-查询", notes = "筛选项角色：商户-MERCHANT，租户-TENANT；变动类型：amountChangeTypeOfSaas")
    public PageResultDto<MerchantBillVo> platformBillQuery(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.SAAS.getCode())
                .setCurrentViewRoleId(TenantCommonConstant.SAAS_TENANT_ID);
        return this.merchantBillFeign.billQueryPage(queryParam);
    }

    @PostMapping("/platformBill/export")
    @ApiOperation(value = "平台账单-导出")
    public BaseIdAndNameDTO platformBillExport(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.SAAS.getCode())
                .setCurrentViewRoleId(TenantCommonConstant.SAAS_TENANT_ID);
        ShiroUser currUser = ISecurityUtils.getCurrUser();
        StringBuilder builder = new StringBuilder();
        builder.append("账单月份：")
                .append(DateUtil.formatDate(new Date(queryParam.getBeginCreateTime())))
                .append("~")
                .append(DateUtil.formatDate(new Date(queryParam.getEndCreateTime())));
        if (StrUtil.isNotBlank(queryParam.getKeyword())) {
            builder.append(CommonConstant.NEWLINE).append("订单号：").append(queryParam.getKeyword());
        }
        if (StrUtil.isNotBlank(queryParam.getPurposeType())) {
            builder.append(CommonConstant.NEWLINE).append("用途：")
                    .append(PurposeType.findByCode(queryParam.getPurposeType(), true).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getAmountChangeRole())) {
            builder.append(CommonConstant.NEWLINE).append("角色：")
                    .append(PaymentRoleEnum.getByCodeOrException(queryParam.getAmountChangeRole()).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getPaymentMethod())) {
            builder.append(CommonConstant.NEWLINE).append("支付方式：")
                    .append(PaymentMethodEnum.findByCode(queryParam.getPaymentMethod(), true).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getAmountChangeTypeOfSaas())) {
            builder.append(CommonConstant.NEWLINE).append("变动类型：")
                    .append(AmountChangeType.findByCode(queryParam.getAmountChangeTypeOfSaas(), true).getDesc());
        }
        DataExportRecordCreateDTO createDTO = new DataExportRecordCreateDTO();
        createDTO.setTenantId(TenantCommonConstant.SAAS_TENANT_ID)
                .setUsername(currUser.getUsername())
                .setUserId(currUser.getId())
                .setExportType(DataExportRecordTypeEnum.SAAS_PLATFORM_BILL)
                .setExportCondition(JSON.toJSONString(queryParam))
                .setConditionDesc(builder.toString());
        Long id = this.dataExportRecordFeign.createAndSendMsg(createDTO);
        return new BaseIdAndNameDTO(id);
    }


    @PostMapping("/queryTenantDisBill")
    @ApiOperation(value = "租户分销账单-查询", notes = "")
    public PageResultDto<MerchantBillVo> queryTenantDisBill(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.SAAS.getCode())
                .setCurrentViewRoleId(TenantCommonConstant.SAAS_TENANT_ID);
        return this.merchantBillFeign.billQueryPage(queryParam);
    }

    @PostMapping("/tenantDisBill/export")
    @ApiOperation(value = "租户分销账单-导出")
    public BaseIdAndNameDTO tenantDisBillExport(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.SAAS.getCode())
                .setCurrentViewRoleId(TenantCommonConstant.SAAS_TENANT_ID);
        List<BaseIdAndNameDTO> tenantList = this.tenantFeign.findNameByIds(BaseListDto.of(Arrays
                .asList(queryParam.getRelatedDisTenantId(), queryParam.getRelatedSupTenantId())));
        Map<Long, String> tenantMap = ListUtil.toMapByBaseIdAndName(tenantList);
        StringBuilder builder = new StringBuilder();
        builder.append("供应商：").append(tenantMap.get(queryParam.getRelatedSupTenantId())).append(CommonConstant.NEWLINE)
                .append("分销商：").append(tenantMap.get(queryParam.getRelatedDisTenantId())).append(CommonConstant.NEWLINE)
                .append("账单月份：")
                .append(DateUtil.formatDate(new Date(queryParam.getBeginCreateTime())))
                .append("~")
                .append(DateUtil.formatDate(new Date(queryParam.getEndCreateTime())));
        if (StrUtil.isNotBlank(queryParam.getKeyword())) {
            builder.append(CommonConstant.NEWLINE).append("订单号：").append(queryParam.getKeyword());
        }
        if (StrUtil.isNotBlank(queryParam.getPurposeType())) {
            builder.append(CommonConstant.NEWLINE).append("用途：")
                    .append(PurposeType.findByCode(queryParam.getPurposeType(), true).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getAmountChangeRole())) {
            builder.append(CommonConstant.NEWLINE).append("账单对象类型：")
                    .append(PaymentRoleEnum.getByCodeOrException(queryParam.getAmountChangeRole()).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getPaymentMethod())) {
            builder.append(CommonConstant.NEWLINE).append("支付方式：")
                    .append(PaymentMethodEnum.findByCode(queryParam.getPaymentMethod(), true).getDesc());
        }
        if (StrUtil.isNotBlank(queryParam.getAmountChangeTypeOfSupTenant())) {
            builder.append(CommonConstant.NEWLINE).append("变动类型：")
                    .append(AmountChangeType.findByCode(queryParam.getAmountChangeTypeOfSupTenant(), true).getDesc());
        }
        ShiroUser currUser = ISecurityUtils.getCurrUser();
        DataExportRecordCreateDTO createDTO = new DataExportRecordCreateDTO();
        createDTO.setTenantId(TenantCommonConstant.SAAS_TENANT_ID)
                .setUsername(currUser.getUsername())
                .setUserId(currUser.getId())
                .setExportType(DataExportRecordTypeEnum.SAAS_TENANT_DIS_BILL)
                .setExportCondition(JSON.toJSONString(queryParam))
                .setConditionDesc(builder.toString());
        Long id = this.dataExportRecordFeign.createAndSendMsg(createDTO);
        return new BaseIdAndNameDTO(id);
    }

    @PostMapping("/merchantBill/queryMonthlyAmount")
    @ApiOperation(value = "商户账单-本月金额数据", notes = "查看商户的交易，relatedMerchantId必传")
    public BillMonthlyAmountOfMerchantVo merchantBillQueryMonthlyAmount(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.SAAS.getCode());
        return merchantBillFeign.monthlyAmountStatForMerchant(queryParam);
    }

    @PostMapping("/merchantBill/query")
    @ApiOperation(value = "POD-商户账单", notes = "当前商户的交易，relatedMerchantId必传")
    public PageResultDto<MerchantBillVo> merchantBillQuery(@RequestBody BillQueryParam queryParam) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.SAAS.getCode());
        return merchantBillFeign.billQueryPage(queryParam);
    }

    @GetMapping("/merchantBill/export")
    @ApiOperation(value = "POD-商户账单", notes = "当前商户的交易，relatedMerchantId必传")
    public void merchantBillExport(
        BillQueryParam queryParam,
        HttpServletResponse response
    ) {
        queryParam.setCurrentViewRole(PaymentRoleEnum.SAAS.getCode());

        // 1. 查询月度汇总金额数据
        BillMonthlyAmountOfMerchantVo monthlyAmount = merchantBillFeign.monthlyAmountStatForMerchant(queryParam);
        List<MerchantBillVo> vos = merchantBillFeign.billQueryList(queryParam);

        // 3. 选择导出模板
        BillExcelTemplate template = BillExcelTemplate.MERCHANT_BILL_FOR_TENANT_OR_SAAS;
        String fileName = template.getFileName();
        String templatePath = template.getTemplate();

        // 4. 执行导出
        try {
            EasyExcelUtil.exportExcel(fileName, templatePath, monthlyAmount, vos, response);
        } catch (Exception e) {
            log.error("POD商户账单导出异常", e);
            throw new RuntimeException("导出失败");
        }
    }
}
